package com.cenker.scrm.handler.security;

import com.alibaba.fastjson.JSON;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.util.AuthUtil;
import com.cenker.scrm.util.IpUtils;
import com.cenker.scrm.util.SecurityUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * token过滤器 验证token有效性
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    private final TokenParseUtil tokenService;

    private final H5TokenService h5TokenService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        H5LoginUser loginUser;
        // 判断是否是企微登录
        boolean weComLogin = AuthUtil.isWeComLogin(request);
        if (weComLogin) {
            // APP_PREFIX + "login_tokens:";
            loginUser = tokenService.getWorkLoginUser(request);
            if (loginUser == null) {
                log.warn("认证失败，请求接口：{}，请求IP：{}，请求参数：{}", request.getRequestURI(), IpUtils.getIpAddr(request), JSON.toJSONString(request.getParameterMap()));
            }
        } else {
            // 微信登录对象
            // APP_PREFIX + "workbench_login_tokens:";
            loginUser = tokenService.getH5LoginUser(request);
        }

        if (Objects.nonNull(loginUser) && Objects.isNull(SecurityUtils.getAuthentication())) {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            if (weComLogin) {
                //APP_PREFIX + "login_tokens:";  OauthTokenConfig.getExpireTime()
                tokenService.refreshToken(loginUser);
            } else {
                // APP_PREFIX + "workbench_login_tokens:"; OauthTokenConfig.getWorkbenchExpireTime()
                h5TokenService.refreshToken(loginUser);
            }
        }

        chain.doFilter(request, response);
    }

    private boolean isContainsUrl(String URI){
        String URIList = "/h5/activity";
        String mobile = "/h5/mobile/api";
        return URI.contains(URIList) || URI.contains(mobile);
    }
}
