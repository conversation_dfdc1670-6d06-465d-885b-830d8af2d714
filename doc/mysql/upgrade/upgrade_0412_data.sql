INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('1v1群发客户查询条件类型', 'group_snd_qry_type', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '客户标签', 'USER_TAG', 'group_snd_qry_type', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '群发客户查询条件类型');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, '客户旅程', 'JOURNERY', 'group_snd_qry_type', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '群发客户查询条件类型');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (3, '添加成员', 'USER_ID', 'group_snd_qry_type', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '群发客户查询条件类型');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (4, '添加时间', 'ADD_DATE', 'group_snd_qry_type', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '群发客户查询条件类型');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (5, '业务标签', 'BUSINESS_DATA', 'group_snd_qry_type', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '群发客户查询条件类型');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (6, '自定义', 'USER_DEFINED', 'group_snd_qry_type', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '群发客户查询条件类型');

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('业务标签性别', 'user_tag_sex', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '男', '男', 'user_tag_sex', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, '女', '女', 'user_tag_sex', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (3, '其他', '其他', 'user_tag_sex', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('业务标签年龄段', 'user_tag_age', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '50后', '50后', 'user_tag_age', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, '60后', '60后', 'user_tag_age', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (3, '70后', '70后', 'user_tag_age', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (4, '80后', '80后', 'user_tag_age', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (5, '90后', '90后', 'user_tag_age', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (6, '00后', '00后', 'user_tag_age', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (7, '其他', '其他', 'user_tag_age', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('业务标签公用是/否', 'user_tage_y_n', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '是', 'Y', 'user_tage_y_n', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, '否', 'N', 'user_tage_y_n', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('业务标签风险测评等级', 'user_tage_pg_level', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (0, '最低类别(C0)', '最低类别(C0)', 'user_tage_pg_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '安益型(C1)', '安益型(C1)', 'user_tage_pg_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, '保守型(C2)', '保守型(C2)', 'user_tage_pg_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (3, '稳健型(C3)', '稳健型(C3)', 'user_tage_pg_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (4, '积极型(C4)', '积极型(C4)', 'user_tage_pg_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (5, '激进型(C5)', '激进型(C5)', 'user_tage_pg_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('业务标签是否定投', 'user_tage_is_dt', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '当前定投', '当前定投', 'user_tage_is_dt', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, '当前未定投但曾定投', '当前未定投但曾定投', 'user_tage_is_dt', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (3, '一直未定投', '一直未定投', 'user_tage_is_dt', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('业务标签仓位比例标签', 'user_tage_ratio_label', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, 'R1仓位最多', 'R1仓位最多', 'user_tage_ratio_label', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, 'R2+R3仓位最多', 'R2+R3仓位最多', 'user_tage_ratio_label', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (3, 'R4+R5仓位最多', 'R4+R5仓位最多', 'user_tage_ratio_label', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('资产等级', 'user_tag_asset_level', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '空仓', '0', 'user_tag_asset_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, 'V1', 'V1', 'user_tag_asset_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (3, 'V2', 'V2', 'user_tag_asset_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (4, 'V3', 'V3', 'user_tag_asset_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (5, 'V4', 'V4', 'user_tag_asset_level', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('历史以来盈利情况标签', 'user_tag_his_prft', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '历史盈利', '历史盈利', 'user_tag_his_prft', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, '历史亏损', '历史亏损', 'user_tag_his_prft', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (3, '历史不盈不亏', '历史不盈不亏', 'user_tag_his_prft', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('近三年盈利情况标签', 'user_tag_l3y_prft', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '近三年盈利', '近三年盈利', 'user_tag_l3y_prft', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (2, '近三年亏损', '近三年亏损', 'user_tag_l3y_prft', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (3, '近三年不盈不亏', '近三年不盈不亏', 'user_tag_l3y_prft', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '业务标签字典');

INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`, `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`, `data_format_type`, `dict_type`) VALUES ('其他', '贵宾', '是否贵宾客户', NULL, '2024-03-13 15:33:39', NULL, '2024-03-18 17:53:09', 62, 1, 'c_if_vip_cust', 'MULTI_SELECT', 'user_tage_y_n');
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`, `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`, `data_format_type`, `dict_type`) VALUES ('投诉', '投诉', '是否投诉客户', NULL, '2024-03-13 15:33:39', NULL, '2024-03-18 17:53:09', 63, 1, 'c_if_complaint_cust', 'MULTI_SELECT', 'user_tage_y_n');
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`, `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`, `data_format_type`, `dict_type`) VALUES ('免打扰', '勿扰', '是否免打扰客户', NULL, '2024-03-13 15:33:39', NULL, '2024-03-18 17:53:09', 64, 1, 'c_if_notdisturb_cust', 'MULTI_SELECT', 'user_tage_y_n');
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`, `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`, `data_format_type`, `dict_type`) VALUES ('投资收益', '账号收益', '历史以来盈利情况', NULL, '2024-03-13 15:33:39', NULL, '2024-03-18 17:53:09', 65, 0, 'c_his_ttl_cf_prft_label', 'MULTI_SELECT', 'user_tag_his_prft');
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`, `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`, `data_format_type`, `dict_type`) VALUES ('投资收益', '账号收益', '近三年盈利情况', NULL, '2024-03-13 15:33:39', NULL, '2024-03-18 17:53:09', 66, 0, 'c_l3y_ttl_cf_prft_label', 'MULTI_SELECT', 'user_tag_l3y_prft');
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`, `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`, `data_format_type`, `dict_type`) VALUES ('客服接触', '客服接触', '电话客服话务次数', NULL, '2024-03-13 15:33:39', NULL, '2024-03-18 17:53:09', 67, 0, 'c_mobile_cnt', 'NUMBER', NULL);
INSERT INTO `tb_dc_user_tags`(`primary_category`, `secondary_category`, `tag_name`, `create_by`, `create_time`, `update_by`, `update_time`, `display_order`, `is_show`, `reference_column`, `data_format_type`, `dict_type`) VALUES ('客服接触', '客服接触', '服务订阅数量', NULL, '2024-03-13 15:33:39', NULL, '2024-03-18 17:53:09', 68, 0, 'c_service_cnt', 'NUMBER', NULL);

update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT' , s.dict_type ='user_tag_sex'  where s.reference_column = 'c_gen_name';
update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT' , s.dict_type ='user_tag_age'  where s.reference_column = 'c_age_stra';
update tb_dc_user_tags s set  s.data_format_type ='DATE'   where s.reference_column = 'c_bth_date';
update tb_dc_user_tags s set  s.data_format_type ='TEXT'   where s.reference_column = 'c_city_name';
update tb_dc_user_tags s set  s.data_format_type ='TEXT'   where s.reference_column = 'c_prov_name';
update tb_dc_user_tags s set  s.data_format_type ='TEXT'   where s.reference_column = 'c_occo_name';
update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT' , s.dict_type ='user_tage_y_n'  where s.reference_column = 'c_if_ds_cust';
update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT' , s.dict_type ='user_tage_y_n'  where s.reference_column = 'c_if_cs_cust';
update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT' , s.dict_type ='user_tage_y_n'  where s.reference_column = 'c_if_ia_cust_rul';
update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT'  , s.dict_type ='user_tag_asset_level'   where s.reference_column = 'c_day_hld_ast_stra';
update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT'  , s.dict_type ='user_tag_asset_level'   where s.reference_column = 'c_l1y_avg_hld_ast_stra';
update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT'  , s.dict_type ='user_tag_asset_level'   where s.reference_column = 'c_l3y_avg_hld_ast_stra';
update tb_dc_user_tags s set  s.data_format_type ='DATE'   where s.reference_column = 'c_cust_open_date';
update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT' , s.dict_type ='user_tage_pg_level'   where s.reference_column = 'c_risk_type_name';
update tb_dc_user_tags s set  s.data_format_type ='NUMBER'   where s.reference_column = 'f_l1y_tracnt';
update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT'  , s.dict_type ='user_tage_is_dt'   where s.reference_column = 'c_l3m_if_rfap';
update tb_dc_user_tags s set  s.data_format_type ='DATE'   where s.reference_column = 'c_last_rfap_cfm_date';
update tb_dc_user_tags s set  s.data_format_type ='NUMBER'   where s.reference_column = 'f_his_ttl_cf_prft';
update tb_dc_user_tags s set  s.data_format_type ='NUMBER'   where s.reference_column = 'f_l3y_ttl_cf_prft';
update tb_dc_user_tags s set  s.data_format_type ='NUMBER'   where s.reference_column = 'f_r1_ast_ratio';
update tb_dc_user_tags s set  s.data_format_type ='NUMBER'   where s.reference_column = 'f_r23_ast_ratio';
update tb_dc_user_tags s set  s.data_format_type ='NUMBER'   where s.reference_column = 'f_r45_ast_ratio';
update tb_dc_user_tags s set  s.data_format_type ='MULTI_SELECT'  , s.dict_type ='user_tage_ratio_label'   where s.reference_column = 'c_astr_ratio_label';


INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (2, 'IN', '包含', '2024-03-26 16:12:32', 1, 'SEL_USER_TAG', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (3, 'NOTIN', '不包含', '2024-03-26 16:12:32', 1, 'SEL_USER_TAG', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (4, 'NOTIN', '不包含', '2024-03-26 16:12:32', 1, 'SEL_JOURNERY', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (5, 'IN', '包含', '2024-03-26 16:12:32', 1, 'SEL_JOURNERY', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (6, 'IN', '包含', '2024-03-26 16:12:32', 1, 'SEL_USER_ID', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (7, 'NOTIN', '不包含', '2024-03-26 16:12:32', 1, 'SEL_USER_ID', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (8, 'EQ', '等于', '2024-03-26 16:12:32', 1, 'SEL_ADD_DATE', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (9, 'GT', '大于', '2024-03-26 16:12:32', 1, 'SEL_ADD_DATE', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (10, 'LT', '小于', '2024-03-26 16:17:53', 1, 'SEL_ADD_DATE', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (11, 'GE', '大于等于', '2024-03-26 16:17:53', 1, 'SEL_ADD_DATE', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (12, 'LE', '小于等于', '2024-03-26 16:17:53', 1, 'SEL_ADD_DATE', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (13, 'IN', '包含', '2024-03-26 16:17:53', 1, 'SEL_USER_DEFINED', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (14, 'NOTIN', '不包含', '2024-03-26 16:12:32', 1, 'SEL_USER_DEFINED', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (15, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_gen_name', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (16, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_gen_name', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (17, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_age_stra', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (18, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_age_stra', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (19, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'c_bth_date', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (20, 'GT', '大于', '2024-03-25 11:29:43', 1, 'c_bth_date', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (21, 'LT', '小于', '2024-03-25 11:29:43', 1, 'c_bth_date', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (22, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'c_bth_date', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (23, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'c_bth_date', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (24, 'LIKE', '匹配', '2024-03-25 11:29:43', 1, 'c_city_name', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (25, 'NOTLIKE', '不匹配', '2024-03-25 11:29:43', 1, 'c_city_name', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (26, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'c_city_name', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (27, 'NE', '不等于', '2024-03-25 11:29:43', 1, 'c_city_name', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (28, 'LIKE', '匹配', '2024-03-25 11:29:43', 1, 'c_prov_name', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (29, 'NOTIN', '不匹配', '2024-03-25 11:29:43', 1, 'c_prov_name', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (30, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'c_prov_name', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (31, 'NE', '不等于', '2024-03-25 11:29:43', 1, 'c_prov_name', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (32, 'LIKE', '匹配', '2024-03-25 11:29:43', 1, 'c_occo_name', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (33, 'NOTLIKE', '不匹配', '2024-03-25 11:29:43', 1, 'c_occo_name', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (34, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'c_occo_name', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (35, 'NE', '不等于', '2024-03-25 11:29:43', 1, 'c_occo_name', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (36, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_if_ds_cust', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (37, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_if_ds_cust', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (38, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_if_cs_cust', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (39, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_if_cs_cust', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (40, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_if_ia_cust_rul', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (41, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_if_ia_cust_rul', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (42, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_day_hld_ast_stra', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (43, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_day_hld_ast_stra', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (47, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_l1y_avg_hld_ast_stra', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (48, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_l1y_avg_hld_ast_stra', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (52, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_l3y_avg_hld_ast_stra', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (53, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_l3y_avg_hld_ast_stra', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (57, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'c_cust_open_date', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (58, 'GT', '大于', '2024-03-25 11:29:43', 1, 'c_cust_open_date', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (59, 'LT', '小于', '2024-03-25 11:29:43', 1, 'c_cust_open_date', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (60, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'c_cust_open_date', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (61, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'c_cust_open_date', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (62, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_risk_type_name', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (63, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_risk_type_name', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (64, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'f_l1y_tracnt', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (65, 'GT', '大于', '2024-03-25 11:29:43', 1, 'f_l1y_tracnt', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (66, 'LT', '小于', '2024-03-25 11:29:43', 1, 'f_l1y_tracnt', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (67, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'f_l1y_tracnt', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (68, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'f_l1y_tracnt', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (69, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_l3m_if_rfap', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (70, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_l3m_if_rfap', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (71, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'c_last_rfap_cfm_date', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (72, 'GT', '大于', '2024-03-25 11:29:43', 1, 'c_last_rfap_cfm_date', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (73, 'LT', '小于', '2024-03-25 11:29:43', 1, 'c_last_rfap_cfm_date', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (74, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'c_last_rfap_cfm_date', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (75, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'c_last_rfap_cfm_date', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (76, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'f_his_ttl_cf_prft', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (77, 'GT', '大于', '2024-03-25 11:29:43', 1, 'f_his_ttl_cf_prft', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (78, 'LT', '小于', '2024-03-25 11:29:43', 1, 'f_his_ttl_cf_prft', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (79, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'f_his_ttl_cf_prft', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (80, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'f_his_ttl_cf_prft', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (81, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'f_l3y_ttl_cf_prft', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (82, 'GT', '大于', '2024-03-25 11:29:43', 1, 'f_l3y_ttl_cf_prft', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (83, 'LT', '小于', '2024-03-25 11:29:43', 1, 'f_l3y_ttl_cf_prft', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (84, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'f_l3y_ttl_cf_prft', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (85, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'f_l3y_ttl_cf_prft', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (86, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'f_r1_ast_ratio', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (87, 'GT', '大于', '2024-03-25 11:29:43', 1, 'f_r1_ast_ratio', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (88, 'LT', '小于', '2024-03-25 11:29:43', 1, 'f_r1_ast_ratio', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (89, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'f_r1_ast_ratio', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (90, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'f_r1_ast_ratio', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (91, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'f_r23_ast_ratio', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (92, 'GT', '大于', '2024-03-25 11:29:43', 1, 'f_r23_ast_ratio', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (93, 'LT', '小于', '2024-03-25 11:29:43', 1, 'f_r23_ast_ratio', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (94, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'f_r23_ast_ratio', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (95, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'f_r23_ast_ratio', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (96, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'f_r45_ast_ratio', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (97, 'GT', '大于', '2024-03-25 11:29:43', 1, 'f_r45_ast_ratio', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (98, 'LT', '小于', '2024-03-25 11:29:43', 1, 'f_r45_ast_ratio', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (99, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'f_r45_ast_ratio', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (100, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'f_r45_ast_ratio', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (101, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_astr_ratio_label', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (102, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_astr_ratio_label', 2);

INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (103, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_if_vip_cust', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (104, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_if_vip_cust', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (105, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_if_complaint_cust', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (106, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_if_complaint_cust', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (107, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_if_notdisturb_cust', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (108, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_if_notdisturb_cust', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (109, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_his_ttl_cf_prft_label', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (110, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_his_ttl_cf_prft_label', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (111, 'IN', '包含', '2024-03-25 11:29:43', 1, 'c_l3y_ttl_cf_prft_label', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (112, 'NOTIN', '不包含', '2024-03-25 11:29:43', 1, 'c_l3y_ttl_cf_prft_label', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (113, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'c_mobile_cnt', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (114, 'GT', '大于', '2024-03-25 11:29:43', 1, 'c_mobile_cnt', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (115, 'LT', '小于', '2024-03-25 11:29:43', 1, 'c_mobile_cnt', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (116, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'c_mobile_cnt', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (117, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'c_mobile_cnt', 5);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (118, 'EQ', '等于', '2024-03-25 11:29:43', 1, 'c_service_cnt', 1);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (119, 'GT', '大于', '2024-03-25 11:29:43', 1, 'c_service_cnt', 2);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (120, 'LT', '小于', '2024-03-25 11:29:43', 1, 'c_service_cnt', 3);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (121, 'GE', '大于等于', '2024-03-25 11:29:43', 1, 'c_service_cnt', 4);
INSERT INTO `tb_dc_user_tags_operrel_mapping` VALUES (122, 'LE', '小于等于', '2024-03-25 11:29:43', 1, 'c_service_cnt', 5);

INSERT INTO `tb_wx_mass_message_query_type` VALUES (1, '客户特征', 'USER_TAG', '客户标签', 'userTag', '1', 1, 1, '2024-03-26 15:22:40', 1, '2024-03-26 15:22:45');
INSERT INTO `tb_wx_mass_message_query_type` VALUES (2, '客户特征', 'JOURNERY', '客户旅程', 'journery', '1', 2, 1, '2024-03-26 15:22:40', 1, '2024-03-26 15:22:45');
INSERT INTO `tb_wx_mass_message_query_type` VALUES (3, '客户行为', 'USER_ID', '添加成员', 'userId', '1', 3, 1, '2024-03-26 15:22:40', 1, '2024-03-26 15:22:45');
INSERT INTO `tb_wx_mass_message_query_type` VALUES (4, '客户行为', 'ADD_DATE', '添加时间', 'addDate', '1', 4, 1, '2024-03-26 15:55:06', 1, '2024-03-26 15:55:10');
INSERT INTO `tb_wx_mass_message_query_type` VALUES (5, '业务/自定义', 'BUSINESS_DATA', '业务标签', 'businessData', '1', 5, 1, '2024-03-26 15:55:06', 1, '2024-03-26 15:55:10');
INSERT INTO `tb_wx_mass_message_query_type` VALUES (6, '业务/自定义', 'USER_DEFINED', '自定义', 'userDefined', '1', 6, 1, '2024-03-26 15:55:06', 1, '2024-03-26 15:55:10');

INSERT INTO `tb_wx_mass_message_query_type_tag` VALUES (1, 'USER_TAG', 'SEL_USER_TAG', '客户标签', NULL, 'DIALOG', '1', 1, NULL, 'tag_id', 1, '2024-03-27 12:56:33', 1, '2024-03-27 12:56:37');
INSERT INTO `tb_wx_mass_message_query_type_tag` VALUES (2, 'JOURNERY', 'SEL_JOURNERY', '客户旅程', NULL, 'DIALOG', '1', 1, NULL, 'stage_id', 1, '2024-03-27 12:56:33', 1, '2024-03-27 12:56:37');
INSERT INTO `tb_wx_mass_message_query_type_tag` VALUES (3, 'USER_ID', 'SEL_USER_ID', '添加成员', NULL, 'DIALOG', '1', 1, NULL, 'u.userid', 1, '2024-03-27 12:56:33', 1, '2024-03-27 12:56:37');
INSERT INTO `tb_wx_mass_message_query_type_tag` VALUES (4, 'ADD_DATE', 'SEL_ADD_DATE', '添加时间', NULL, 'DATE', '1', 1, NULL, 'create_time', 1, '2024-03-27 12:56:33', 1, '2024-03-27 12:56:37');
INSERT INTO `tb_wx_mass_message_query_type_tag` VALUES (5, 'BUSINESS_DATA', 'SEL_BUSINESS_DATA', '业务标签', NULL, '', '1', 1, NULL, NULL, 1, '2024-03-27 12:56:33', 1, '2024-03-27 12:56:37');
INSERT INTO `tb_wx_mass_message_query_type_tag` VALUES (6, 'USER_DEFINED', 'SEL_USER_DEFINED', '自定义', NULL, 'TEXT', '1', 1, NULL, '', 1, '2024-03-27 12:56:33', 1, '2024-03-27 12:56:37');

INSERT INTO xxl_job_info ( job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES ( 2, '【获客链接】查询通过获客链接添加的成员', '2024-02-02 14:13:01', '2024-02-02 14:13:03', 'cenker', NULL, 'CRON', '0 0/10 * * * ?', 'DO_NOTHING', 'ROUND', 'qryCustByWxCustLink', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', NULL, 'GLUE代码初始化', '2024-02-02 14:13:01', NULL, 1, 0, 0);

INSERT INTO  sys_dict_type (dict_name, dict_type, status, create_by, update_by, create_time, update_time, del_flag, remark) VALUES ('微信获客链接状态', 'customer_link_status', '0', 'admin', 'admin', NOW(), NOW(), 0, NULL);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (0, '使用中', '0', 'customer_link_status', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '微信获客链接状态');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, del_flag, create_by, update_by, create_time, update_time, remark) VALUES (1, '已废弃', '1', 'customer_link_status', '', '', 'Y', '0', 0, 'admin', '', NOW(), NOW(), '微信获客链接状态');

UPDATE `tb_dc_user_tags` SET primary_category = '客服标签' WHERE id IN ( 25, 26, 27 );
UPDATE `tb_dc_user_tags` SET secondary_category = '免打扰' WHERE id = 27;
UPDATE `tb_dc_user_tags` SET secondary_category = '账户收益（数值）' WHERE id in (19, 20);
UPDATE `tb_dc_user_tags` SET secondary_category = '账户收益（分类）' WHERE id in (28, 29);

INSERT INTO `sys_config`(`config_name`, `config_key`, `config_value`, `config_type`, `start_config`, `dev_env`, `test_env`, `prod_env`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`) VALUES ('企微端水印开关', 'wk.watermark.switch', 'false', 'N', 'N', '', '', '', 'admin', 'admin', '2024-03-16 14:46:08', '2024-03-20 13:29:40', 0, NULL);
INSERT INTO `sys_config`(`config_name`, `config_key`, `config_value`, `config_type`, `start_config`, `dev_env`, `test_env`, `prod_env`, `create_by`, `update_by`, `create_time`, `update_time`, `del_flag`, `remark`) VALUES ('认证订阅表单', 'authentication.subscription.form', 'https://scrm-work.efunds.com.cn/questionnaireModel/f2d511d2a1d34970b5a137bb9a4c4b82', 'N', 'N', '', '', '', 'admin', 'admin', '2024-03-30 11:27:01', '2024-03-30 11:30:57', 0, '认证服务的订阅表单，给用户填写信息');
