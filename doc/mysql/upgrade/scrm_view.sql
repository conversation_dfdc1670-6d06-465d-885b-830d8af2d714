-- 1. 客户维表
CREATE OR REPLACE VIEW v_customer AS
SELECT c.external_user_id,
       c.union_id,
       c.first_add_date,
       c.is_auth,
       ar.first_auth_time,
       c.`status`,
       ut.wx_tag,
       c_gen_name,
       c_age_stra,
       c_bth_date,
       c_city_name,
       c_prov_name,
       c_occo_name,
       c_if_ds_cust,
       c_if_cs_cust,
       c_if_ia_cust_rul,
       c_day_hld_ast_stra,
       c_l1y_avg_hld_ast_stra,
       c_l3y_avg_hld_ast_stra,
       c_cust_open_date,
       c_risk_type_name,
       c_day_ttl_hld_fund,
       f_l1y_tracnt,
       c_l3m_if_rfap,
       c_last_rfap_cfm_date,
       f_his_ttl_cf_prft,
       f_l3y_ttl_cf_prft,
       f_r1_ast_ratio,
       f_r23_ast_ratio,
       f_r45_ast_ratio,
       c_astr_ratio_label,
       c_if_vip_cust,
       c_if_complaint_cust,
       c_if_notdisturb_cust,
       c_his_ttl_cf_prft_label,
       c_l3y_ttl_cf_prft_label,
       f_mobile_cnt,
       f_mmf_ast_ratio,
       f_bond_ast_ratio,
       f_equity_ast_ratio,
       f_blend_ast_ratio,
       f_fof_ast_ratio,
       f_other_ast_ratio,
       c_if_y_share_cust,
       c_if_not_y_pens_cust,
       c_if_etf_cust,
       c_if_etf_cnet_cust,
       c_if_index_cust
FROM tb_wx_ext_customer c
         LEFT JOIN (SELECT custno, min(bind_time) AS first_auth_time FROM tb_wx_customer_auth_record GROUP BY custno) ar
                   ON ar.custno = c.custno
         LEFT JOIN v_f_scrm_label l ON l.custno = c.custno
         LEFT JOIN (SELECT external_user_id,
                           JSON_OBJECTAGG(group_name, tags_list) AS wx_tag
                    FROM (SELECT external_user_id, group_name, GROUP_CONCAT(tag) AS tags_list
                          FROM tb_wx_ext_follow_user_tag
                          WHERE type = 1
                            AND del_flag = 0
                          GROUP BY external_user_id, group_name) AS subquery
                    GROUP BY external_user_id) ut ON ut.external_user_id = c.external_user_id;

-- 2. 员工客户关系表
CREATE OR REPLACE VIEW v_staff_customer_relation AS
SELECT external_user_id,
       user_id as staff_id,
       DATE_FORMAT(create_time, '%Y/%m/%d %H/%i/%s') as add_time,
       DATE_FORMAT(del_time, '%Y/%m/%d %H/%i/%s')    as del_time,
       status,
       chat_status                                   as agree_save_archive,
       add_way
FROM tb_wx_ext_follow_user;

-- 3. 客户统计数据表
CREATE OR REPLACE VIEW v_customer_stat_data AS
SELECT `statistic_date`,
       `external_user_id`,
       `alone_chat_num`,
       `group_chat_num`,
       `customer_group_num`,
       `radar_receive_times`,
       `radar_click_times`,
       `radar_forward_times`,
       `average_read_time`
FROM tb_statistic_customer;

-- 4. 客户总览统计表
CREATE OR REPLACE VIEW v_customer_stat_summary AS
SELECT t.*,
       (t.customerNewTotal - t.customerLossTotal) AS netNewTotal
FROM (SELECT statistic_date,
             COUNT(DISTINCT CASE WHEN `status` = 0 THEN external_user_id END) AS customerTotal,
             COUNT(DISTINCT CASE WHEN is_auth = 1 THEN external_user_id END)  AS customerAuthTotal,
             COUNT(DISTINCT CASE
                                WHEN is_auth = 1 AND date(auth_time) = statistic_date
                                    THEN external_user_id END)                AS customerNewAuthTotal,
             COUNT(DISTINCT CASE
                                WHEN date(add_time) = statistic_date
                                    THEN custno END)                          AS customerNewTotal,
             COUNT(CASE
                       WHEN `status` != 0 AND date(loss_time) = statistic_date
                           THEN external_user_id END)                         AS customerLossTotal,
             IFNULL(SUM(alone_chat_num), 0)                                   AS aloneChatTotal,
             IFNULL(SUM(group_chat_num), 0)                                   AS customerGroupChatTotal,
             COUNT(DISTINCT CASE
                                WHEN alone_chat_num > 0 OR group_chat_num > 0
                                    THEN external_user_id END)                AS customerActiveTotal
      FROM tb_statistic_customer
      GROUP BY statistic_date
      ORDER BY statistic_date) AS t;

-- 群维表
CREATE OR REPLACE VIEW v_customer_group AS
SELECT g.chat_id                                              AS group_id,
       g.create_time,
       g.dismiss_date,
       g.group_name,
       g.`owner`,
       count(DISTINCT gm.user_id)                             AS group_member_cnt,
       count(DISTINCT CASE WHEN type = 1 THEN gm.user_id END) AS group_staff_cnt,
       count(DISTINCT CASE WHEN type = 2 THEN gm.user_id END) AS group_customer_cnt
FROM tb_wx_customer_group g
         LEFT JOIN tb_wx_customer_group_member gm ON gm.group_id = g.chat_id
GROUP BY g.chat_id;

-- 群成员表
CREATE OR REPLACE VIEW v_customer_group_member AS
SELECT group_id,
       user_id,
       type,
       create_time,
       departure_time,
       join_scene,
       quit_scene
FROM tb_wx_customer_group_member;

-- 群数据统计表
CREATE OR REPLACE VIEW v_group_stat_data AS
SELECT statistic_date,
       group_id,
       group_name,
       member_num,
       staff_num,
       member_in_num,
       member_out_num,
       net_new_num,
       active_customer_num,
       group_chat_num,
       count(DISTINCT CASE WHEN u.userid IS NOT NULL THEN u.userid END) AS staff_send_msg_cnt,
       count(DISTINCT CASE WHEN u.userid IS NULL THEN u.userid END)     AS customer_send_msg_cnt
FROM tb_statistic_customer_group g
         LEFT JOIN wk_chat_archive_info wcai ON wcai.room_id = g.group_id
    AND wcai.chat_type = 2
         LEFT JOIN tb_wx_user u ON u.userid = wcai.from_id
GROUP BY g.group_id;

-- 群统计数据表
CREATE OR REPLACE VIEW v_group_stat_summary AS
SELECT statistic_date,
       COUNT(DISTINCT CASE WHEN dismiss_date IS NULL THEN group_id END)                AS groupTotal,
       IFNULL(SUM(member_num), 0)                                                      AS groupMemberTotal,
       IFNULL(SUM(staff_num), 0)                                                       AS groupStaffTotal,
       COUNT(DISTINCT CASE WHEN date(create_time) = statistic_date THEN group_id END)  AS groupNewTotal,
       COUNT(DISTINCT CASE WHEN date(dismiss_date) = statistic_date THEN group_id END) AS groupLossTotal,
       COUNT(DISTINCT CASE WHEN group_chat_num > 0 THEN group_id END)                  AS groupActiveTotal,
       IFNULL(SUM(group_chat_num), 0)                                                  AS chatTotal,
       IFNULL(SUM(member_in_num), 0)                                                   AS memberInTotal,
       IFNULL(SUM(member_out_num), 0)                                                  AS memberOutTotal,
       IFNULL(SUM(active_customer_num), 0)                                             AS customerActiveTotal,
       IFNULL(SUM(net_new_num), 0)                                                     AS netNewMemberTotal
FROM tb_statistic_customer_group
GROUP BY statistic_date
ORDER BY statistic_date;

-- 员工维表
CREATE OR REPLACE VIEW v_staff AS
SELECT u.`userid` as staff_id,
       u.`NAME`,
       u.`main_department `,
       count(DISTINCT CASE WHEN efu.STATUS = 0 THEN efu.external_user_id END)  AS customer_cnt,
       count(DISTINCT efu.external_user_id)                                    AS his_customer_cnt,
       count(DISTINCT CASE WHEN efu.STATUS != 0 THEN efu.external_user_id END) AS his_lost_customer_cnt,
       count(DISTINCT g.chat_id)                                               AS manage_group_cnt,
       count(DISTINCT CASE WHEN gm.STATUS = 0 THEN gm.group_id END)            AS join_group_cnt
FROM tb_wx_user u
         LEFT JOIN tb_wx_ext_follow_user efu ON efu.user_id = u.userid
         LEFT JOIN tb_wx_customer_group g ON g.`owner` = u.userid
    AND g.STATUS = 0
    AND g.dismiss_status = 0
         LEFT JOIN tb_wx_customer_group_member gm ON gm.user_id = u.userid
    AND gm.STATUS = 0
GROUP BY u.userid;

-- 员工客户服务统计表
CREATE OR REPLACE VIEW v_staff_stat_data AS
SELECT s.`statistic_date`,
       s.`userid` as staff_id,
       s.`customer_add_num`,
       s.`customer_del_num`,
       (s.customer_add_num - s.customer_del_num)                        AS net_increase_customer_num,
       s.`customer_chat_num`,
       count(CASE WHEN wcai.consume_id = s.userid THEN wcai.msg_id END) AS customer_sent_msg_cnt,
       s.`alone_chat_num`,
       s.`reply_timely_num`,
       (CASE
            WHEN s.total_conversation_num IS NULL OR s.total_conversation_num = 0 THEN 0
            ELSE ROUND(s.reply_timely_num * 100 / SUM(s.total_conversation_num))
           END)                                                         AS reply_timely_ratio,
       (CASE
            WHEN s.total_conversation_num IS NULL OR s.total_conversation_num = 0 THEN 0
            ELSE ROUND(s.total_conversation_duration / s.total_conversation_num)
           END)                                                         AS average_reply_duration,
       s.`staff_group_manage_num`,
       s.`staff_group_join_num`,
       s.`staff_group_chat_num`
FROM tb_statistic_staff s
         LEFT JOIN wk_chat_archive_info wcai ON wcai.msg_day = s.statistic_date
    AND (wcai.from_id = s.userid OR wcai.consume_id = s.userid)
GROUP BY s.statistic_date,
         s.userid;

-- 物料维度统计
CREATE OR REPLACE VIEW v_radar_stat_data AS
SELECT r.statistic_date,
       r.radar_id,
       r.title,
       r.radar_type,
       count(DISTINCT CASE WHEN cr.forward_to = 1 THEN cr.customer_id END) AS forward_num,
       sum(cr.read_time)                                                   AS total_read_duration,
       r.send_times,
       r.click_times,
       r.forward_times,
       count(DISTINCT cr.customer_id)                                      AS click_num
FROM tb_statistic_radar r
         LEFT JOIN tb_wx_radar_content c ON c.radar_id = r.radar_id
         LEFT JOIN tb_wx_radar_content_record cr ON cr.content_id = c.id AND date(cr.create_time) = r.statistic_date
GROUP BY r.statistic_date,
         r.radar_id;

-- 客户行为-物料触达
CREATE OR REPLACE VIEW v_radar_customer_data AS
SELECT CURRENT_DATE  AS statistic_date,
       wu.union_id,
       r.id                                                           AS radar_id,
       r.title,
       count(DISTINCT cr.id)                                          AS click_num,
       GROUP_CONCAT(DATE_FORMAT(cr.create_time, '%Y-%m-%d %H:%i:%s')) as click_time,
       sum(cr.read_time)                                              AS total_read_duration,
       sum(cr.forward_num)                                            AS forward_num
FROM tb_wx_radar_content_record cr
         JOIN mp_wx_user wu ON wu.id = cr.customer_id
         JOIN tb_wx_radar_content c ON c.id = cr.content_id
         JOIN tb_wx_radar_interact r ON r.id = c.radar_id
GROUP BY cr.customer_id,
         r.id;

-- 会话内容表
CREATE OR REPLACE VIEW v_chat_archive_info AS
SELECT `msg_id`,
       `chat_type`,
       `from_id`,
       `consume_id`,
       `room_id`,
       `room_consume_id`,
       IF(u.userid IS NULL, 1, 0) AS sender_type,
       `msg_time`,
       `msg_content`,
       `msg_type`
FROM wk_chat_archive_info wcai
         LEFT JOIN tb_wx_user u ON u.userid = wcai.from_id;

-- 单聊会话表
CREATE OR REPLACE VIEW v_chat_conversation AS
SELECT `statistic_date`,
       `customer_id`,
       `origin_msg_id`,
       `origin_msg_time`,
       `origin_msg_content`,
       `staff_id`,
       `reply_msg_id`,
       `reply_msg_time`,
       `reply_msg_content`,
       `duration`,
       `timeout`
FROM `wk_chat_conversation`;

-- 单聊统计表
CREATE OR REPLACE VIEW v_alone_chat_stat_data AS
SELECT t.statistic_date,
       t.customer_id,
       t.staff_id,
       min(t.start_time) as start_time,
       max(t.end_time) as end_time,
       sum(t.customer_sent_msg_cnt) as customer_sent_msg_cnt,
       sum(t.staff_sent_msg_cnt) as staff_sent_msg_cnt,
       count(CASE WHEN wcc.timeout = 1 THEN wcc.id END)           AS reply_timely_num,
       round(
               sum(wcc.duration) / count(DISTINCT origin_msg_id)) AS avg_reply_duration
FROM (SELECT o.msg_day                                                        AS statistic_date,
             IF
                 (u.userid IS NULL, o.from_id, o.consume_id)                  AS customer_id,
             IF
                 (u.userid IS NULL, o.consume_id, o.from_id)                  AS staff_id,
             min(o.msg_time)                                                  AS start_time,
             max(o.msg_time)                                                  AS end_time,
             count(DISTINCT CASE WHEN u.userid IS NULL THEN o.msg_id END)     AS customer_sent_msg_cnt,
             count(DISTINCT CASE WHEN u.userid IS NOT NULL THEN o.msg_id END) AS staff_sent_msg_cnt
      FROM wk_chat_archive_info o
               LEFT JOIN tb_wx_user u ON u.userid = o.from_id
      WHERE o.chat_type = 1
      GROUP BY o.msg_day,
               o.from_id,
               o.consume_id) t
         LEFT JOIN wk_chat_conversation wcc ON wcc.statistic_date = t.statistic_date
    AND t.customer_id = wcc.customer_id
    AND wcc.staff_id = t.staff_id
GROUP BY t.statistic_date,
         t.customer_id,
         t.staff_id;

-- 敏感词触发记录
CREATE OR REPLACE VIEW v_sens_word_alarm_record AS
SELECT ar.`sensitive_word`,
       ar.`rule_id`,
       sri.rule_name,
       sri.intercept_type,
       ar.`msg_id`,
       ar.`content_value`,
       ar.`send_user_id`,
       IF
           (u.userid IS NULL, 1, 0) AS sender_type,
       ar.`accept_user_id`,
       ar.`trigger_time`
FROM `ck_session_sens_word_alarm_record` ar
         LEFT JOIN ck_session_sens_rule_info sri ON sri.rule_id = ar.rule_id
         LEFT JOIN tb_wx_user u ON u.userid = ar.send_user_id;


-- 敏感词触发统计
CREATE OR REPLACE VIEW v_sens_word_stat_data AS
SELECT sw.`statistic_date`,
       sw.`userid` as staff_id,
       sw.`user_name`,
       sw.`dept_id`,
       sw.`sensitive_word`,
       sw.`rule_id`,
       sri.`rule_name`,
       sri.intercept_type,
       sw.`trigger_times`
FROM `tb_statistic_sens_word` sw
         LEFT JOIN ck_session_sens_rule_info sri ON sri.rule_id = sw.rule_id;

-- 敏感行为触发记录
CREATE OR REPLACE VIEW v_sens_act_alarm_record AS
SELECT ar.act_type,
       ar.`rule_id`,
       sri.rule_name,
       sri.intercept_type,
       ar.`msg_id`,
       ar.`send_user_id`,
       IF
           (u.userid IS NULL, 1, 0) AS sender_type,
       ar.`accept_user_id`,
       ar.`trigger_time`
FROM `ck_session_sens_act_alarm_record` ar
         LEFT JOIN ck_session_sens_rule_info sri ON sri.rule_id = ar.rule_id
         LEFT JOIN tb_wx_user u ON u.userid = ar.send_user_id;

-- 敏感行为触发统计
CREATE OR REPLACE VIEW v_sens_act_stat_data AS
SELECT sa.`statistic_date`,
       sa.`userid` as staff_id,
       sa.`user_name`,
       sa.`dept_id`,
       sa.dept_name,
       sa.`act_type`,
       sa.`rule_id`,
       sri.`rule_name`,
       sri.intercept_type,
       sa.`trigger_times`
FROM `tb_statistic_sens_act` sa
         LEFT JOIN ck_session_sens_rule_info sri ON sri.rule_id = sa.rule_id;

-- 热词触发记录
CREATE OR REPLACE VIEW v_hot_word_trigger_record AS
SELECT tr.`word`,
       tr.`rule_id`,
       tr.`msg_id`,
       tr.`hot_word`,
       tr.`msg_content`,
       tr.`from_id`,
       IF
           (u.userid IS NULL, 1, 0) AS sender_type,
       tr.`consume_id`,
       tr.`msg_time`
FROM `ck_session_hot_word_trigger_record` tr
         LEFT JOIN tb_wx_user u ON u.userid = tr.from_id;

-- 热词触发统计
CREATE OR REPLACE VIEW v_hot_word_stat_data AS
SELECT `statistic_date`,
       `hot_word`,
       `synon_words`,
       `staff_trigger_times`,
       `staff_trigger_num`,
       `customer_trigger_times`,
       `customer_trigger_num`
FROM `tb_statistic_hot_word`;

-- 回复超时统计
CREATE OR REPLACE VIEW v_reply_timeout_stat_data AS
SELECT `statistic_date`,
       `userid` as staff_id,
       `user_name`,
       `dept_id`,
       `timeout_times`
FROM `tb_statistic_reply_timeout`;


