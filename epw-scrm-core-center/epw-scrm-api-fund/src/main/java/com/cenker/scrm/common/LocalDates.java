package com.cenker.scrm.common;

import com.google.common.base.Strings;
import org.joda.time.DateTimeFieldType;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.joda.time.LocalTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.DateTimeFormatterBuilder;
import org.joda.time.format.ISODateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2015/8/2
 */
public class LocalDates {

    private static final Logger logger = LoggerFactory.getLogger(LocalDates.class);

    private static final DateTimeFormatter uspTime = uspTime();

    public static String toMMDashDD(String date) {
        if (Strings.isNullOrEmpty(date) || date.length() != 8) {
            logger.warn("日期格式不正确");
            return date;
        }

        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyyMMdd");
        LocalDate parsedDate;
        try {
            parsedDate = formatter.parseLocalDate(date);
        } catch (Exception e) {
            logger.warn("日期转换失败", e);
            return date;
        }

        DateTimeFormatter outputFormatter = DateTimeFormat.forPattern("MM-dd");
        return outputFormatter.print(parsedDate);
    }


    public static String toYearMonthDayDash(String date) {
        if (Strings.isNullOrEmpty(date) || date.length() != 8) {
            logger.warn("日期格式不正确");
            return date;
        }

        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyyMMdd");
        LocalDate parsedDate;
        try {
            parsedDate = formatter.parseLocalDate(date);
        } catch (Exception e) {
            logger.warn("日期转换失败", e);
            return date;
        }

        DateTimeFormatter outputFormatter = DateTimeFormat.forPattern("yyyy-MM-dd");
        return outputFormatter.print(parsedDate);
    }


    /**
     * format to yyyyMMdd
     */
    public static String toDateString(LocalDate date) {
        return date == null ? null : ISODateTimeFormat.basicDate().print(date);
    }

    public static String toDateString(LocalDate date, String format) {
        if(Strings.isNullOrEmpty(format)){
            return toDateString(date);
        }
        return date == null ? null : DateTimeFormat.forPattern(format).print(date);
    }

    /**
     *  format to yyyy-MM-dd
     */
    public static String toISODateString(LocalDate date) {
        return date == null ? null : ISODateTimeFormat.date().print(date);
    }

    public static String toDateTimeString(LocalDateTime dateTime) {
        return dateTime == null ? null : DateTimeFormat.forPattern("yyyyMMddHHmmss").print(dateTime);
    }

    public static String toDateTimeString(String format , LocalDateTime dateTime) {
        if(Strings.isNullOrEmpty(format)){
            return null;
        }

        return dateTime == null ? null : DateTimeFormat.forPattern(format).print(dateTime);
    }

    public static LocalDate parseLocalDate(String date) {
        return Strings.isNullOrEmpty(date) || "--".equals(date) ? null : ISODateTimeFormat.basicDate().parseLocalDate(date);
    }

    /**
     * yyyy-MM-dd格式日期字符串转LocalDate
     * @param date
     * @return
     */
    public static LocalDate parseNormalLocalDate(String date) {
        LocalDate localDate = null;

        if ("--".equals(date)) {
            return null;
        }

        try {
            localDate = Strings.isNullOrEmpty(date) ? null : LocalDate.parse(date);
        } catch (Exception e) {
            logger.warn("日期不合法");
        }
        return localDate;
    }

    public static LocalDate parseLocalDateCoerced(String date) {
        LocalDate localDate = null;
        try {
            localDate = Strings.isNullOrEmpty(date) ? null : ISODateTimeFormat.basicDate().parseLocalDate(date);
        } catch (Exception e) {
            logger.warn("日期不合法");
        }
        return localDate;
    }

    public static LocalDateTime parseLocalDateTime(String dateTime) {
        if (Strings.isNullOrEmpty(dateTime)) {
            return null;
        }
        String format = "yyyyMMddHHmmss".substring(0, dateTime.length());
        if (dateTime.length() == 6) {
            format = "HHmmss";
        }

        return DateTimeFormat.forPattern(format).parseLocalDateTime(dateTime);
    }

    public static LocalDate parseLocalDate(String format,String dateTime) {
        if (Strings.isNullOrEmpty(dateTime)) {
            return null;
        }

        if(Strings.isNullOrEmpty(format)){
            return null;
        }

        String _format = format.substring(0,dateTime.length());

        return DateTimeFormat.forPattern(_format).parseLocalDate(dateTime);
    }

    public static LocalDateTime parseLocalDateTime(String format,String dateTime) {
        if (Strings.isNullOrEmpty(dateTime)) {
            return null;
        }

        if(Strings.isNullOrEmpty(format)){
            return null;
        }

        String _format = format.substring(0,dateTime.length());

        return DateTimeFormat.forPattern(_format).parseLocalDateTime(dateTime);
    }

    public static LocalTime parseLocalTime(String localTime) {
        if (Strings.isNullOrEmpty(localTime)) {
            return null;
        }
        return LocalTime.parse(localTime, uspTime);
    }


    private static DateTimeFormatter uspTime() {
        if (uspTime == null) {
            return new DateTimeFormatterBuilder()
                    .appendFixedDecimal(DateTimeFieldType.hourOfDay(), 2)
                    .appendFixedDecimal(DateTimeFieldType.minuteOfHour(), 2)
                    .appendFixedDecimal(DateTimeFieldType.secondOfMinute(), 2)
                    .toFormatter();
        }
        return uspTime;
    }

}
