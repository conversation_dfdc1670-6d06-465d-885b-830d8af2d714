package com.cenker.scrm.common;

import com.efunds.market.usp.entity.*;
import com.efunds.market.usp.uic.vo.response.*;
import com.google.common.base.Function;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.*;

/**
 * 资产转换类
 * Created by zhengcb on 2015/5/20.
 */
public class AssetConvert {

    /**
     * 属于某种币种的所有基金的资产汇总
     *
     * @param uspTotalFundAsset
     * @return
     */
    public static FundTotalAsset convertToTotalFundAsset(com.efunds.market.usp.entity.FundAsset uspTotalFundAsset) {

        FundTotalAsset eetTotalFundAsset = new FundTotalAsset();
        eetTotalFundAsset.setAssetType(uspTotalFundAsset.getAssetType());
        eetTotalFundAsset.setAssetTypeName(uspTotalFundAsset.getAssetTypeName());
        eetTotalFundAsset.setCurrency(uspTotalFundAsset.getCurrency());    //币别

        eetTotalFundAsset.setTotalProfits(uspTotalFundAsset.getTotalProfit()); //总累计收益
        eetTotalFundAsset.setIncomeDate(uspTotalFundAsset.getIncomeDate());
        eetTotalFundAsset.setTotalAssets(uspTotalFundAsset.getTotalAssets());  //总资产
        eetTotalFundAsset.setYesterdayIncome(uspTotalFundAsset.getTotalYesterdayIncome()); //总昨日收益

        return eetTotalFundAsset;
    }

    /**
     * 基金维度的资产
     *
     * @param uspFundAsset
     * @return
     */
    public static AssetItem convertToFundAssets(FundAssetDetail uspFundAsset) {
        AssetItem fundAsset = new AssetItem();

        fundAsset.setAssetType(uspFundAsset.getAssetType());
        fundAsset.setAssetTypeName(uspFundAsset.getAssetTypeName());
        fundAsset.setChannelCode(uspFundAsset.getChannelCode());
        fundAsset.setChannelName(uspFundAsset.getChannelName());
        fundAsset.setFundType(uspFundAsset.getFundType());
        fundAsset.setFundTypeDesc(uspFundAsset.getFundTypeDesc());
        fundAsset.setCurrency(uspFundAsset.getCurrency());
        fundAsset.setFundCode(uspFundAsset.getFundCode());
        fundAsset.setFundShortName(uspFundAsset.getFundShortName());
        fundAsset.setShareType(uspFundAsset.getShareType());
        fundAsset.setBonusType(uspFundAsset.getBonusType());
        fundAsset.setBonusTypeDesc(uspFundAsset.getBonusTypeDesc());
        fundAsset.setNetValue(uspFundAsset.getNetValue());
        fundAsset.setNavDate(uspFundAsset.getNavDate());
        fundAsset.setProportion(uspFundAsset.getAssetRatio());  //这只基金资产占比
        fundAsset.setDayInc(uspFundAsset.getDayInc());   //净值涨跌幅
        fundAsset.setDailyYieldBp(uspFundAsset.getIncomeUnit());    //万份收益
        fundAsset.setAssets(uspFundAsset.getAsset());
        fundAsset.setAvailableAssets(uspFundAsset.getAvailableAsset());
        fundAsset.setTotalShares(uspFundAsset.getTotalShares());
        fundAsset.setAvailableShares(uspFundAsset.getAvailableShares());
        fundAsset.setTotalProfits(uspFundAsset.getTotalProfit());
        fundAsset.setYesterdayIncome(uspFundAsset.getYesterdayIncome());
        fundAsset.setIncome(uspFundAsset.getIncome());

        return fundAsset;
    }

    /**
     * 银行卡维度的资产列表
     *
     * @param uspFundAsset
     * @return
     */
    public static List<AssetItem> convertToFundBankAssets(FundAssetDetail uspFundAsset) {
        List<AssetItem> eetFundBankAssets = new ArrayList<AssetItem>(uspFundAsset.getDetails().size());

        for (FundBankAssetDetail uspFundBankAsset : uspFundAsset.getDetails()) {
            AssetItem item = new AssetItem();

            //单只基金汇总信息
            item.setAssetType(uspFundAsset.getAssetType());
            item.setAssetTypeName(uspFundAsset.getAssetTypeName());
//            item.setChannelName(uspFundAsset.getChannelName());
            item.setFundType(uspFundAsset.getFundType());
            item.setFundTypeDesc(uspFundAsset.getFundTypeDesc());
            item.setCurrency(uspFundAsset.getCurrency());
            item.setShareType(uspFundAsset.getShareType());
            item.setFundCode(uspFundBankAsset.getFundCode());
            item.setFundShortName(uspFundAsset.getFundShortName());
            item.setNetValue(uspFundAsset.getNetValue());   //净值
            item.setNavDate(uspFundAsset.getNavDate());  //净值日期
            item.setDayInc(uspFundAsset.getDayInc());   //净值涨跌幅
            item.setDailyYieldBp(uspFundAsset.getIncomeUnit());    //万份收益
            item.setSevenDayYield(uspFundAsset.getIncomeRatio());
            item.setIsDsAvailable(Booleans.fromString(uspFundAsset.getHasDsAvailableShares()));

            item.setProportion(uspFundAsset.getAssetRatio());  //基金资产占比
            item.setIsDailyYield(Booleans.fromString(uspFundAsset.getIsBFSY()));

            //单只基金里某张银行卡资产信息
            item.setTradeAccount(uspFundBankAsset.getTradeAccount());
            item.setBankName(uspFundBankAsset.getBankShortName());
            item.setBankCardNoShort(uspFundBankAsset.getLast4OfBankCard());
            item.setChannelCode(uspFundBankAsset.getChannelCode());
            item.setBankIndex(uspFundBankAsset.getBankIndex());
            item.setTradeChannel(uspFundBankAsset.getTradeChannel());
            item.setBonusType(uspFundBankAsset.getBonusType()); //TODO 是否要转换成enum
            item.setBonusTypeDesc(uspFundBankAsset.getBonusTypeDesc());
            item.setAssets(uspFundBankAsset.getAsset());
            item.setAvailableAssets(uspFundBankAsset.getAvailableAsset());
            item.setTotalShares(uspFundBankAsset.getTotalShares());
            item.setAvailableShares(uspFundBankAsset.getAvailableShares());
            item.setTotalProfits(uspFundBankAsset.getTotalProfit());
            item.setYesterdayIncome(uspFundBankAsset.getYesterdayIncome());
            item.setIncome(uspFundBankAsset.getIncome());
            item.setAvailableFastRedemptionAmount(uspFundBankAsset.getAvailableFastAsset());//快赎可用金额
            item.setAvailableFastRedemptionShares(uspFundBankAsset.getAvailableFastShares());//快赎可用份额
            item.setProductType(ProductType.parse(uspFundBankAsset.getProductType()));
            item.setIsNeedSupplyBank(Booleans.fromStringOrEmpty(uspFundBankAsset.getNeedSupplyBank()));
            eetFundBankAssets.add(item);
        }
        return eetFundBankAssets;
    }

    /**
     * "一个月汇总资产信息"对象转换（汇总资产走势图）
     *
     * @return
     */
    public static List<com.cenker.scrm.common.SimpleAsset> convertToSimpleAsset(List<com.efunds.market.usp.entity.SimpleAsset> respAssets, String currency) {
        List<com.cenker.scrm.common.SimpleAsset> simpleAssetList = new ArrayList<com.cenker.scrm.common.SimpleAsset>();
        for (com.efunds.market.usp.entity.SimpleAsset respAsset : respAssets) {
            com.cenker.scrm.common.SimpleAsset simpleAsset = new com.cenker.scrm.common.SimpleAsset();
            simpleAsset.setAsset(respAsset.getAssets());
            simpleAsset.setDate(respAsset.getDate());
            simpleAsset.setCurrency(currency);
            simpleAssetList.add(simpleAsset);
        }
        return simpleAssetList;
    }

    /**
     * "一个月汇总收益信息"对象转换（汇总收益走势图）
     *
     * @return
     */
    public static List<SimpleProfit> convertToSimpleProfit(List<com.efunds.market.usp.entity.SimpleAssetProfit> uspProfit, String currency) {
        List<SimpleProfit> simpleProfitList = new ArrayList<SimpleProfit>();
        for (com.efunds.market.usp.entity.SimpleAssetProfit profit : uspProfit) {
            SimpleProfit simpleProfit = new SimpleProfit();
            simpleProfit.setDate(profit.getDate());//收益日期
            simpleProfit.setAsset(profit.getTotalProfit());//累计收益
            simpleProfit.setProfit(profit.getProfit());//当天收益
            simpleProfit.setCurrency(currency);//币种
            simpleProfitList.add(simpleProfit);
        }
        return simpleProfitList;
    }

    public static List<com.cenker.scrm.common.SimpleAssetProfit> convertSimpleAssetProfit(List<SimpleAssetAndProfit> uspData) {
        List<com.cenker.scrm.common.SimpleAssetProfit> assetProfits = new ArrayList<com.cenker.scrm.common.SimpleAssetProfit>();
        if (uspData == null) {
            return assetProfits;
        }
        for (SimpleAssetAndProfit uspDatum : uspData) {
            com.cenker.scrm.common.SimpleAssetProfit assetProfit = new com.cenker.scrm.common.SimpleAssetProfit();
            assetProfit.setAsset(uspDatum.getAssets());
            assetProfit.setProfit(uspDatum.getProfit());
            assetProfit.setAsset(uspDatum.getTotalProfit());
            assetProfit.setDate(uspDatum.getDate());
            assetProfits.add(assetProfit);
        }
        return assetProfits;
    }

    /**
     * "历史收益列表信息"对象转换
     *
     * @param hisProfits
     * @return
     */
    public static List<ProfitGroup> convertToHisProfit(List<SimpleAssetPeriodProfit> hisProfits) {

        Map<String, ProfitGroup> groupMap = new HashMap<String, ProfitGroup>(2);

        for (SimpleAssetPeriodProfit profit : hisProfits) {
            ProfitItem profitItem = new ProfitItem();
            profitItem.setFundCode(profit.getFundCode());
            profitItem.setFundShortName(profit.getFundShortName());
            profitItem.setShareType(profit.getShareType());
            profitItem.setBeginAsset(new BigDecimal(profit.getBeignAsset()));
            profitItem.setEndAsset(new BigDecimal(profit.getEndAsset()));
            profitItem.setPurchaseAmt(new BigDecimal(profit.getPurchaseAmt()));//申购金额
            profitItem.setSubscribeAmt(new BigDecimal(profit.getSubscribeAmt()));//认购金额
            profitItem.setDividendAmt(new BigDecimal(profit.getDividendAmt()));
            profitItem.setRedeemAmt(new BigDecimal(profit.getRedeemAmt()));//赎回金额
            profitItem.setTransferInAmt(new BigDecimal(profit.getTransferInAmt()));
            profitItem.setTransferOutAmt(new BigDecimal(profit.getTransferOutAmt()));

            profitItem.setSideBagFlag(profit.getSideBagFlag());
            profitItem.setMainBagFundCode(profit.getMainBagFundCode());
            profitItem.setMainBagFundName(profit.getMainBagFundName());
            profitItem.setSideBagBeginDate(profit.getSideBagBeginDate());
            profitItem.setSideBagEndDate(profit.getSideBagEndDate());
            profitItem.setSideBagShareFlag(profit.getSideBagShareFlag());

            profitItem.setPeriodProfit(BigDecimalUtils.toBigDecimal(profit.getPeriodProfit()));
            profitItem.setIsValidProfit(profit.getValidProfit());

            String currency = profit.getCurrency();
            if (!groupMap.containsKey(currency)) {
                ProfitGroup group = new ProfitGroup();
                group.setCurrency(currency);
                groupMap.put(currency, group);
            }

            groupMap.get(currency).add(profitItem);
        }

        ArrayList<ProfitGroup> profitGroups = Lists.newArrayList(groupMap.values());
        Collections.sort(profitGroups);
        return profitGroups;
    }

    /**
     * "指定币种历史收益列表信息" 对象转换
     *
     * @param hisProfits
     * @return
     */
    public static List<FundProfit> convertToHisFundProfit(List<SimpleAssetPeriodProfit> hisProfits, String currency) {
        List<FundProfit> fundProfits = new ArrayList<FundProfit>();
        if (Strings.isNullOrEmpty(currency)) {
            currency = "156";
        }

        for (SimpleAssetPeriodProfit profit : hisProfits) {
            if (currency.equals(profit.getCurrency())) {
                FundProfit fundProfit = new FundProfit();
                fundProfit.setAccProfit(new BigDecimal(profit.getPeriodProfit()));
                fundProfit.setAssets(new BigDecimal(profit.getEndAsset()));
                fundProfit.setFundCode(profit.getFundCode());
                fundProfit.setFundName(profit.getFundShortName());
                fundProfits.add(fundProfit);
            }
        }
        return fundProfits;
    }

    /**
     * 单只基金详细对象转换
     * 基金维度的资产详情
     *
     * @param uspFundAsset
     * @return
     */
    public static com.cenker.scrm.common.FundAsset convertToFundAssetDetail(FundAssetDetail uspFundAsset) {
        com.cenker.scrm.common.FundAsset fundAsset = new com.cenker.scrm.common.FundAsset();
        fundAsset.setFundCode(uspFundAsset.getFundCode());
        fundAsset.setFundName(uspFundAsset.getFundShortName());
        fundAsset.setFundType(uspFundAsset.getFundType());

        fundAsset.setFundTypeDesc(uspFundAsset.getFundTypeDesc());
        fundAsset.setFundWebType(uspFundAsset.getFundType());
        fundAsset.setFundWebTypeDesc(uspFundAsset.getFundTypeDesc());
        fundAsset.setShareType(uspFundAsset.getShareType());
        fundAsset.setFundState(uspFundAsset.getFundState());
        fundAsset.setCurrency(uspFundAsset.getCurrency());//币种

        fundAsset.setChannelCode(uspFundAsset.getChannelCode());
        fundAsset.setChannelName(uspFundAsset.getChannelName());

        fundAsset.setDistributionMethod(DistributionMethod.parse(uspFundAsset.getBonusType()));
        fundAsset.setDistributionMethodDesc(uspFundAsset.getBonusTypeDesc());

        fundAsset.setFundRisk(uspFundAsset.getFundRisk());
        fundAsset.setFundRiskDesc(uspFundAsset.getFundRiskDesc());

        fundAsset.setNetValue(uspFundAsset.getNetValue());
        String navDate = uspFundAsset.getNavDate();
        fundAsset.setNavDate(navDate);

        fundAsset.setAccProfits(uspFundAsset.getTotalProfit());
        fundAsset.setYesterdayIncome(uspFundAsset.getYesterdayIncome());
        fundAsset.setDayInc(uspFundAsset.getDayInc());//净值涨跌幅
        fundAsset.setDailyYieldBp(uspFundAsset.getIncomeUnit());//七日年化收益率
        fundAsset.setIsDailyYield(Booleans.fromString(uspFundAsset.getIsBFSY()));//是否为百份收益
        fundAsset.setSevenDayYield(uspFundAsset.getIncomeRatio());//七日年化收益率
        fundAsset.setAccruedIncome(uspFundAsset.getIncome());//未付收益
        fundAsset.setLatestDailyIncome(uspFundAsset.getOrgYesterdayIncome());//3期新增,资产首页汇总栏,统一使用该字段显示日收益
        fundAsset.setIsDsAvailable(Booleans.fromString(uspFundAsset.getHasDsAvailableShares()));
        fundAsset.setTotalShares(uspFundAsset.getTotalShares());
        fundAsset.setAvailableShares(uspFundAsset.getAvailableShares());
        fundAsset.setAssets(uspFundAsset.getAsset());
        fundAsset.setAvailableAssets(uspFundAsset.getAvailableAsset());
        fundAsset.setExtendedFundType(uspFundAsset.getExtendedFundType());
        fundAsset.setExtendedFundTypeDesc(uspFundAsset.getExtendedFundTypeDesc());
        fundAsset.setTotalDsShares(uspFundAsset.getDsTotalShares());
        fundAsset.setLockType(uspFundAsset.getLockType());
        fundAsset.setCycleStep(uspFundAsset.getCycleStep());
        fundAsset.setIsFloatFinancial(uspFundAsset.getFloatFinancialFlag());

        fundAsset.setSideBagFlag(uspFundAsset.getSideBagFlag());
        fundAsset.setMainBagFundCode(uspFundAsset.getMainBagFundCode());
        fundAsset.setMainBagFundName(uspFundAsset.getMainBagFundName());
        fundAsset.setSideBagBeginDate(uspFundAsset.getSideBagBeginDate());
        fundAsset.setSideBagEndDate(uspFundAsset.getSideBagEndDate());
        fundAsset.setSideBagShareFlag(uspFundAsset.getSideBagShareFlag());

        return fundAsset;
    }

    /**
     * 短期理财基金到期日列表对象转换
     *
     * @param list
     * @return
     */
    public static List<ShortTermFund> convertToShortTermFund(List<com.efunds.market.usp.entity.ShortTermFund> list) {
        List<ShortTermFund> shortTermFundList = new ArrayList<ShortTermFund>();
        for (com.efunds.market.usp.entity.ShortTermFund termFund : list) {
            ShortTermFund shortTermFund = new ShortTermFund();
            shortTermFund.setFundCode(termFund.getFundCode());
            shortTermFund.setFundName(termFund.getFundShortName());
            shortTermFund.setShareType(termFund.getShareType());
            shortTermFund.setSalesChannel(termFund.getPlatform());//交易渠道
            shortTermFund.setShareAmount(termFund.getShare());
            shortTermFund.setAccruedIncome(termFund.getIncome());
            String registerDate = termFund.getRegister();
            shortTermFund.setRegistrationDate(LocalDates.parseLocalDate(registerDate));//份额注册日期
            shortTermFund.setSettlementAccount(termFund.getTradeAcco());//付款账户
            String maturityDate = termFund.getMaturityDate();
            shortTermFund.setMaturityDate(LocalDates.parseLocalDate(maturityDate));

            shortTermFundList.add(shortTermFund);
        }
        return shortTermFundList;
    }

    /**
     * 锁定期基金的到期日列表对象转换
     * @param list
     * @return
     */
    public static List<ShortTermFund> convertToMaturityDateList(List<com.efunds.market.usp.entity.MaturityDateRecord> list) {
        List<ShortTermFund> shortTermFundList = new ArrayList<ShortTermFund>();
        for (com.efunds.market.usp.entity.MaturityDateRecord item : list) {
            ShortTermFund shortTermFund = new ShortTermFund();
            shortTermFund.setFundCode(item.getFundCode());
            shortTermFund.setFundName(item.getFundShortName());
            shortTermFund.setShareType(item.getShareType());
            shortTermFund.setSalesChannel(item.getPlatform());//交易渠道
            shortTermFund.setShareAmount(item.getShare());
            shortTermFund.setAccruedIncome(item.getIncome());
            String registerDate = item.getRegisterDate();
            if(!"--".equals(registerDate)){
                shortTermFund.setRegistrationDate(LocalDates.parseLocalDate(registerDate));//份额注册日期
            }
            shortTermFund.setSettlementAccount(item.getTradeAcco());//付款账户
            String maturityDate = item.getMaturityDate();
            if(!"已到期".equals(maturityDate)){
                shortTermFund.setMaturityDate(LocalDates.parseLocalDate(maturityDate));
            }

            shortTermFundList.add(shortTermFund);
        }
        return shortTermFundList;
    }


    /**
     * 专户资产-产品历史净值对象转换
     *
     * @param marketInfos
     * @return
     */
    public static List<com.cenker.scrm.common.FundPerformance> convertToFundPerformance(List<CustomerMutiProductMarketInfoResponse.MutiFundProductMarketInfo> marketInfos) {
        List<com.cenker.scrm.common.FundPerformance> fundPerformances = new ArrayList<com.cenker.scrm.common.FundPerformance>();
        for (CustomerMutiProductMarketInfoResponse.MutiFundProductMarketInfo info : marketInfos) {
            com.cenker.scrm.common.FundPerformance performance = new FundPerformance();
            performance.setFundCode(info.getFundCode());
            performance.setFundShortName(info.getFundShortName());
            performance.setFundType(info.getFundType());
            String netDate = info.getNetDate();
            performance.setNavDate(LocalDates.parseLocalDate(netDate));
            performance.setDailyYieldBp(info.getIncomeOf10Thousand());
            performance.setSevenDayYield(info.getSevenYearRateOfReturn());
            performance.setNavps(info.getNetValue());
            performance.setAccNav(info.getTotalNetValue());
            fundPerformances.add(performance);
        }
        return fundPerformances;
    }

    /**
     * "专户资产Tab表格内容"对象转换(通知公告,定期报告,投资说明书,资产管理合同)
     *
     * @param mutiFundInfos
     * @return
     */
    public static List<FundIntro> convertToFundIntro(List<MutiFundInfo> mutiFundInfos) {
        List<FundIntro> fundIntros = new ArrayList<FundIntro>();
        for (MutiFundInfo info : mutiFundInfos) {
            FundIntro intro = new FundIntro();
            intro.setContentUrl(info.getBody());
            String publishDate = info.getPublishDate();
            intro.setPublishDate(LocalDates.parseLocalDate(publishDate));
            intro.setTitle(info.getTitle());
            fundIntros.add(intro);
        }
        return fundIntros;
    }

    /**
     * "专户资产-专户产品信息列表"对象转换
     *
     * @param response
     * @return
     */
    public static List<SimpleFund> convertToSimpleFund(CustomerMutiProductsResponse response) {
        List<SimpleFund> fundList = new ArrayList<SimpleFund>();
        for (CustomerMutiProductsResponse.MutiFundProduct product : response.getMutiFundProducts()) {
            SimpleFund fund = new SimpleFund();
            fund.setFundCode(product.getMprodCode());
            fund.setFundName(product.getMprodName());
            fund.setFundType(product.getMprodType());
            fundList.add(fund);
        }
        return fundList;
    }

    /**
     * "客户最新直销短期理财基金资产"对象转换
     *
     * @param
     * @return
     */
    public static List<com.cenker.scrm.common.FundAsset> convertToShortTermFundAssetDetail(List<FundAssetDetail> data) {
        List<com.cenker.scrm.common.FundAsset> detailList = new ArrayList<com.cenker.scrm.common.FundAsset>();
        for (com.efunds.market.usp.entity.FundAssetDetail fundAssetDetail : data) {
            com.cenker.scrm.common.FundAsset fundAsset = new com.cenker.scrm.common.FundAsset();
            fundAsset.setFundCode(fundAssetDetail.getFundCode());
            fundAsset.setFundName(fundAssetDetail.getFundShortName());
            fundAsset.setFundType(fundAssetDetail.getFundType());
            fundAsset.setFundTypeDesc(fundAssetDetail.getFundTypeDesc());
            fundAsset.setShareType(fundAssetDetail.getShareType());
            fundAsset.setChannelCode(fundAssetDetail.getChannelCode());

            fundAsset.setAccruedIncome(fundAssetDetail.getIncome());

            fundAsset.setTotalShares(fundAssetDetail.getTotalShares());
            fundAsset.setAvailableShares(fundAssetDetail.getAvailableShares());
            fundAsset.setAssets(fundAssetDetail.getAsset());
            fundAsset.setAvailableAssets(fundAssetDetail.getAvailableAsset());
            fundAsset.setIsDsAvailable(Booleans.fromString(fundAssetDetail.getHasDsAvailableShares()));

            fundAsset.setAccProfits(fundAssetDetail.getTotalProfit());
            fundAsset.setDistributionMethod(DistributionMethod.parse(fundAssetDetail.getBonusType()));
            fundAsset.setDistributionMethodDesc(fundAssetDetail.getBonusTypeDesc());
            String maturityDate = fundAssetDetail.getMaturityDate();
            fundAsset.setMaturityDate(maturityDate);
            fundAsset.setTotalDsShares(fundAssetDetail.getDsTotalShares());
            /* if (Constants.EWALLET_FUND_CODE.equals(fundAssetDetail.getFundCode())) {  //均为自有平台资产
                fundAsset.setChannelCode(Constants.ASSETS_EW);
            } else {
                fundAsset.setChannelCode(Constants.ASSETS_GM);
            }*/
            detailList.add(fundAsset);
        }
        return detailList;
    }

    /**
     * e钱包资产转换
     *
     * @return
     */
    public static FundTotalAsset convertToEWalletAsset(GetLatestAssetAndProfitResponse uspRes) {

        boolean isEWalletAssetExists = false;
        FundTotalAsset fundTotalAsset = new FundTotalAsset();

        if (uspRes.getData() != null && uspRes.getData().size() > 0) {
            isEWalletAssetExists = true;
        }
        if (isEWalletAssetExists) {
            com.efunds.market.usp.entity.FundAsset fundAsset = uspRes.getData().get(0);
            fundTotalAsset.setAssetType(fundAsset.getAssetType());
            fundTotalAsset.setAssetTypeName(fundAsset.getAssetTypeName());
            fundTotalAsset.setCurrency(fundAsset.getCurrency());
            fundTotalAsset.setYesterdayIncome(fundAsset.getTotalYesterdayIncome());   //昨日收益
            fundTotalAsset.setIncomeDate(fundAsset.getIncomeDate());   //昨日收益日期
            fundTotalAsset.setTotalProfits(fundAsset.getTotalProfit()); //总收益
            fundTotalAsset.setTotalAssets(fundAsset.getTotalAssets());   //总资产
        } else {
            fundTotalAsset.setAssetType("EW");
            fundTotalAsset.setAssetTypeName("e钱包");
            fundTotalAsset.setCurrency("156");
            fundTotalAsset.setYesterdayIncome("0.00");
            fundTotalAsset.setIncomeDate(null);
            fundTotalAsset.setTotalProfits("0.00");
            fundTotalAsset.setTotalAssets("0.00");
        }
        return fundTotalAsset;
    }


    private static List<AssetProportion> convertToFundRatio(List<FundTypeAssetRatio> ratioList) {
        if (ratioList == null || ratioList.isEmpty()) {
            return null;
        }
        List<AssetProportion> ratios = new ArrayList<AssetProportion>();
        for (FundTypeAssetRatio fundTypeAssetRatio : ratioList) {
            AssetProportion assetProportion = new AssetProportion();
            assetProportion.setFundType(fundTypeAssetRatio.getFundWebType());
            assetProportion.setFundTypeDesc(fundTypeAssetRatio.getFundWebTypeDesc());
            assetProportion.setProportion(fundTypeAssetRatio.getRatio());
            ratios.add(assetProportion);
        }
        return ratios;
    }

    public static AssetSummary convertToAssetSummary(CustomerLatestTotalDetailAssetResponse uspRes) {
        List<com.efunds.market.usp.entity.FundAsset> data = uspRes.getData();
        List<FundTotalAsset> totalAssets = new ArrayList<FundTotalAsset>();

        for (com.efunds.market.usp.entity.FundAsset fundAsset : data) {
            FundTotalAsset fundTotalAsset = convertToTotalFundAsset(fundAsset);
            totalAssets.add(fundTotalAsset);
        }


        List<AssetDetails> assets = getAssetDetails(uspRes.getAssetDetails());

        AssetSummary assetSummary = new AssetSummary();
        assetSummary.setAssetCategories(convertToAssetCategory(uspRes.getCategoryAssetAndProfits()));
        // 货基资产
        if (assetSummary.getAssetCategories() != null) {
            for (AssetCategory assetCategory : assetSummary.getAssetCategories()) {
                if (AssetCategoryEnum.MONEY_MARKET_FUND.name().equals(assetCategory.getCategory())) {
                    assetSummary.setMoneyFundAsset(convertToMoneyFundAsset(assetCategory));
                }
            }
        }

        assetSummary.setRmbAssetProportions(convertToFundRatio(uspRes.getRmbRatios()));
        assetSummary.setUasAssetProportions(convertToFundRatio(uspRes.getUsdRatios()));
        assetSummary.setTotalAssets(totalAssets);
        assetSummary.setAssetDetails(assets);

        return assetSummary;
    }

    private static List<AssetDetails> getAssetDetails(List<FundAssetDetail> uspAssetDetails) {
        List<AssetDetails> assets =  new ArrayList<AssetDetails>();
        Map<String, AssetDetails> assetCurrencyMap = new HashMap<String, AssetDetails>();
        for (FundAssetDetail detail : uspAssetDetails) {
            AssetItem item = convertToAssetItem(detail);
            com.cenker.scrm.common.FundAsset fundAsset = convertToFundAsset(detail);
            FundAssetsDetail fundAssetsDetail = new FundAssetsDetail();
            fundAssetsDetail.setAssetItems(convertToFundBankAssets(detail.getDetails()));
            fundAssetsDetail.setFundAsset(fundAsset);

            String currency = item.getCurrency();
            if (assetCurrencyMap.get(currency) == null) {
                AssetDetails assetDetails = new AssetDetails();
                assetDetails.setCurrency(currency);
                assetCurrencyMap.put(currency, assetDetails);

                assets.add(assetDetails);
            }
            assetCurrencyMap.get(currency).getAssetItems().add(item);
            assetCurrencyMap.get(currency).getFundAssetsDetails().add(fundAssetsDetail);

        }return assets;

    }


    private static List<AssetCategory> convertToAssetCategory(List<CategoryAssetAndProfit> uspCategories) {
        List<AssetCategory> assetCategories = new ArrayList<AssetCategory>();
        for (CategoryAssetAndProfit uspCategory : uspCategories) {
            if (uspCategory != null) {
                AssetCategory assetCategory = new AssetCategory();
                assetCategory.setCategory(uspCategory.getCategory());
                assetCategory.setCategoryDesc(uspCategory.getCategoryDesc());
                assetCategory.setIncomeDate(uspCategory.getIncomeDate());
                assetCategory.setNavDate(uspCategory.getNavDate());
                assetCategory.setTotalAssets(uspCategory.getTotalAssets());
                assetCategory.setTotalProfit(uspCategory.getTotalProfit());
                assetCategory.setTotalYesterdayIncome(uspCategory.getTotalYesterdayIncome());
                assetCategories.add(assetCategory);
            }
        }
        return assetCategories;
    }

    public static FundTotalAsset convertToFundTotalAsset(AssetCategory assetCategory) {
        FundTotalAsset fundTotalAsset = new FundTotalAsset();
        fundTotalAsset.setTotalAssets(assetCategory.getTotalAssets());
        fundTotalAsset.setTotalProfits(assetCategory.getTotalProfit());
        fundTotalAsset.setYesterdayIncome(assetCategory.getTotalYesterdayIncome());
        return fundTotalAsset;
    }

    public static com.cenker.scrm.common.FundAsset convertToFundAsset(String fundCode, AssetCategory assetCategory,
                                                                      List<AssetItem> assetItems) {
        com.cenker.scrm.common.FundAsset fundAsset = new com.cenker.scrm.common.FundAsset();
        fundAsset.setFundCode(fundCode);
        fundAsset.setFundNickName(assetCategory.getCategoryDesc());
        fundAsset.setAssets(assetCategory.getTotalAssets());
        fundAsset.setAccProfits(assetCategory.getTotalProfit());
        fundAsset.setLatestDailyIncome(assetCategory.getTotalYesterdayIncome());
        fundAsset.setIsDsAvailable(false);
        for (AssetItem assetItem : assetItems) {
            if (fundCode.equals(assetItem.getFundCode())) {
                fundAsset.setIsDsAvailable(assetItem.getIsDsAvailable());
            }
        }
        return fundAsset;
    }

    /**
     * 基金持仓成本对象转换
     */
    public static List<UnitCost> convertToUnitCost(List<com.efunds.market.usp.entity.FundKeepCost> fundKeepCosts) {
        if (fundKeepCosts.isEmpty()) {
            return null;
        }

        List<UnitCost> unitCosts = new ArrayList<UnitCost>();
        for (com.efunds.market.usp.entity.FundKeepCost fundKeepCost : fundKeepCosts) {
            UnitCost unitCost = new UnitCost();
            unitCost.setFundCode(fundKeepCost.getFundCode());
            unitCost.setUnitCost(new BigDecimal(fundKeepCost.getUnitCosts()));
            unitCosts.add(unitCost);
        }
        return unitCosts;
    }

    /**
     * 单只基金持仓信息
     */
    public static FundKeepCost convertToFundKeepCost(List<com.efunds.market.usp.entity.FundKeepCost> fundKeepCostList, String fundCode) {
        if (fundKeepCostList == null) {
            return null;
        }
        FundKeepCost fundKeepCost = new FundKeepCost();
        for (com.efunds.market.usp.entity.FundKeepCost fundKeepCosted : fundKeepCostList) {
            if (fundCode.equalsIgnoreCase(fundKeepCosted.getFundCode())) {
                fundKeepCost.setFundCode(fundKeepCosted.getFundCode());
                fundKeepCost.setUnitCosts(fundKeepCosted.getUnitCosts());
                fundKeepCost.setHoldCost(fundKeepCosted.getHoldCost());
                fundKeepCost.setHoldProfit(fundKeepCosted.getHoldProfit());
                fundKeepCost.setRate(fundKeepCosted.getRate());
            }
        }
        return fundKeepCost;
    }

    /**
     * 基金持仓信息列表
     */
    public static List<FundKeepCost> convertToFundKeepCostList(List<com.efunds.market.usp.entity.FundKeepCost> fundKeepCostList) {
        if (fundKeepCostList == null) {
            return null;
        }
        List<FundKeepCost> result = new ArrayList<FundKeepCost>();
        for (com.efunds.market.usp.entity.FundKeepCost fundKeepCosted : fundKeepCostList) {
            if (fundKeepCosted == null) {
                continue;
            }
            FundKeepCost fundKeepCost = new FundKeepCost();
            fundKeepCost.setFundCode(fundKeepCosted.getFundCode());
            fundKeepCost.setUnitCosts(fundKeepCosted.getUnitCosts());
            fundKeepCost.setHoldCost(fundKeepCosted.getHoldCost());
            fundKeepCost.setHoldProfit(fundKeepCosted.getHoldProfit());
            fundKeepCost.setRate(fundKeepCosted.getRate());
            result.add(fundKeepCost);
        }
        return result;
    }

    /**
     * 资产持仓信息列表（按币种分）
     */
    public static List<TotalKeepCost> convertToTotalKeepCostList(List<com.efunds.market.usp.entity.TotalKeepCost> uspTotalKeepCostList) {
        if (uspTotalKeepCostList == null) {
            return null;
        }
        List<TotalKeepCost> totalKeepCostList = new ArrayList<TotalKeepCost>();
        for (com.efunds.market.usp.entity.TotalKeepCost uspTotalKeepCost : uspTotalKeepCostList) {
            TotalKeepCost totalKeepCost = new TotalKeepCost();
            totalKeepCost.setCurrency(uspTotalKeepCost.getCurrency());
            totalKeepCost.setHoldCost(uspTotalKeepCost.getHoldCost());
            totalKeepCost.setHoldProfit(uspTotalKeepCost.getHoldProfit());
            totalKeepCostList.add(totalKeepCost);
        }
        return totalKeepCostList;
    }

    /**
     * 转换收益信息
     *
     * @param profitAndRate
     * @return
     */
    public static AccProfit converToAccProfit(ProfitAndRate profitAndRate) {
        AccProfit accProfit = new AccProfit();
        BeanUtils.copyProperties(profitAndRate, accProfit);
        accProfit.setFundName(profitAndRate.getFundShortName());
        accProfit.setProfitRate(profitAndRate.getRate());
        return accProfit;
    }

    private static MoneyFundAsset convertToMoneyFundAsset(AssetCategory assetCategory) {
        MoneyFundAsset moneyFundAsset = new MoneyFundAsset();
        moneyFundAsset.setTotalAssets(assetCategory.getTotalAssets());
        moneyFundAsset.setTotalYesterdayIncome(assetCategory.getTotalYesterdayIncome());
        moneyFundAsset.setTotalProfit(assetCategory.getTotalProfit());
        moneyFundAsset.setIncomeDate(assetCategory.getIncomeDate());
        moneyFundAsset.setNavDate(assetCategory.getNavDate());
        return moneyFundAsset;
    }

    /**
     * 资产明细转换
     *
     * @param detail
     * @return
     */
    public static AssetItem convertToAssetItem(com.efunds.market.usp.entity.FundAssetDetail detail) {
        AssetItem item = new AssetItem();
        item.setAssetType(detail.getAssetType());
        item.setAssetTypeName(detail.getAssetTypeName());

        item.setChannelCode(detail.getChannelCode());
        item.setChannelName(detail.getChannelName());
        item.setFundType(detail.getFundType());
        item.setFundTypeDesc(detail.getFundTypeDesc());
        item.setFundCode(detail.getFundCode());
        item.setFundShortName(detail.getFundShortName());
        item.setFundState(detail.getFundState());
        item.setShareType(detail.getShareType());
        item.setBonusType(detail.getBonusType());
        item.setBonusTypeDesc(detail.getBonusTypeDesc());
        item.setNetValue(detail.getNetValue());
        item.setNavDate(detail.getNavDate());
        item.setIncomeDate(detail.getIncomeDate());
        item.setDailyYieldBp(detail.getIncomeUnit());   // 万份收益
        item.setIsDailyYield(Booleans.fromString(detail.getIsBFSY()));
        item.setSevenDayYield(detail.getIncomeRatio()); // 七日年化收益率

        item.setIsDsAvailable(Booleans.fromString(detail.getHasDsAvailableShares()));
        item.setTotalShares(detail.getTotalShares());
        item.setAvailableShares(detail.getAvailableShares());
        item.setAvailableAssets(detail.getAvailableAsset());
        item.setAssets(detail.getAsset());
        item.setTotalDsShares(detail.getDsTotalShares());//直销总份额 (3期新增 判断理财基金能否卖出)
        item.setTotalDsAsset(detail.getDsTotalAssets());//直销总资产（用于判断快赎按钮显示）
        item.setTotalProfits(detail.getTotalProfit());

        item.setIncome(detail.getIncome());//未付收益

        item.setYesterdayIncome(detail.getYesterdayIncome());

        item.setProportion(detail.getAssetRatio());
        item.setCurrency(detail.getCurrency());
        item.setDayInc(detail.getDayInc());
        item.setAssetClass(detail.getAssetClass()); // 资产分类（4期新增 FUND基金 PORTFOLIO组合)
        item.setExtendedFundType(detail.getExtendedFundType()); // 专户基金类别（4期新增 用于专户）
        item.setExtendedFundTypeDesc(detail.getExtendedFundTypeDesc()); // 扩展类型描述
        return item;
    }

    /**
     * 基金明细
     * @param detail
     * @return
     */
    public static com.cenker.scrm.common.FundAsset convertToFundAsset(com.efunds.market.usp.entity.FundAssetDetail detail) {
        com.cenker.scrm.common.FundAsset item = new com.cenker.scrm.common.FundAsset();
        item.setAssetTypeName(detail.getAssetTypeName());

        item.setChannelCode(detail.getChannelCode());
        item.setChannelName(detail.getChannelName());
        item.setFundType(detail.getFundType());
        item.setFundTypeDesc(detail.getFundTypeDesc());
        item.setFundCode(detail.getFundCode());
        item.setFundName(detail.getFundShortName());
        item.setFundState(detail.getFundState());
        item.setShareType(detail.getShareType());
        item.setNetValue(detail.getNetValue());
        item.setNavDate(detail.getNavDate());
        item.setIncomeDate(detail.getIncomeDate());
        item.setDailyYieldBp(detail.getIncomeUnit());   // 万份收益
        item.setIsDailyYield(Booleans.fromString(detail.getIsBFSY()));
        item.setSevenDayYield(detail.getIncomeRatio()); // 七日年化收益率

        item.setIsDsAvailable(Booleans.fromString(detail.getHasDsAvailableShares()));
        item.setTotalShares(detail.getTotalShares());
        item.setAvailableShares(detail.getAvailableShares());
        item.setAvailableAssets(detail.getAvailableAsset());
        item.setAssets(detail.getAsset());
        item.setTotalDsShares(detail.getDsTotalShares());//直销总份额 (3期新增 判断理财基金能否卖出)
        item.setTotalDsAsset(detail.getDsTotalAssets());//直销总资产（用于判断快赎按钮显示）
        item.setAccProfits(detail.getTotalProfit());
        item.setDistributionMethod(DistributionMethod.parse(detail.getBonusType()));

        item.setAccruedIncome(detail.getIncome());//未付收益

        item.setYesterdayIncome(detail.getYesterdayIncome());

        item.setSideBagFlag(detail.getSideBagFlag());
        item.setMainBagFundCode(detail.getMainBagFundCode());
        item.setMainBagFundName(detail.getMainBagFundName());
        item.setSideBagBeginDate(detail.getSideBagBeginDate());
        item.setSideBagEndDate(detail.getSideBagEndDate());
        item.setSideBagShareFlag(detail.getSideBagShareFlag());

        item.setProportion(detail.getAssetRatio());
        item.setCurrency(detail.getCurrency());
        item.setDayInc(detail.getDayInc());
        item.setAssetClass(detail.getAssetClass()); // 资产分类（4期新增 FUND基金 PORTFOLIO组合)
        item.setExtendedFundType(detail.getExtendedFundType()); // 专户基金类别（4期新增 用于专户）
        item.setExtendedFundTypeDesc(detail.getExtendedFundTypeDesc()); // 扩展类型描述
        item.setIsUpdating(Booleans.fromStringOrEmpty(detail.getUpdating())); // 资产收益更新标识,投顾才返回
        item.setCycleStep(detail.getCycleStep());
        item.setIsFloatFinancial(detail.getFloatFinancialFlag());
        return item;
    }

    /**
     * 基金银行卡明细资产
     * @param details
     * @return
     */
    public static List<AssetItem> convertToFundBankAssets(List<com.efunds.market.usp.entity.FundBankAssetDetail> details){
        List<AssetItem> list = new ArrayList<AssetItem>();
        for (com.efunds.market.usp.entity.FundBankAssetDetail detail : details) {
            AssetItem item = convertToFundBankAssetItem(detail);
            list.add(item);
        }
        return list;
    }

    public static AssetItem convertToFundBankAssetItem(com.efunds.market.usp.entity.FundBankAssetDetail detail){
        AssetItem item = new AssetItem();

        item.setChannelCode(detail.getChannelCode());
        item.setBonusType(detail.getBonusType());
        item.setBonusTypeDesc(detail.getBonusTypeDesc());

        item.setTotalShares(detail.getTotalShares());
        item.setAvailableShares(detail.getAvailableShares());
        item.setAvailableAssets(detail.getAvailableAsset());
        item.setAssets(detail.getAsset());
        item.setTotalProfits(detail.getTotalProfit());
        item.setFundCode(detail.getFundCode());
        item.setIncome(detail.getIncome());//未付收益

        item.setYesterdayIncome(detail.getYesterdayIncome());

        return item;
    }

    /**
     * 资产区间盈亏
     *
     * @return
     */
    public static List<TotalProfitSum> convertToTotalProfitSum(ListFundProfitsAndRatesResponse response) {
        List<TotalProfitSum> totalProfitSums = new ArrayList<TotalProfitSum>();
        // 设置人民币区间总收益
        for (com.efunds.market.usp.entity.TotalProfitAndRate total : response.getTotalData()) {
            TotalProfitSum totalProfitSum = new TotalProfitSum();
            totalProfitSum.setCurrency(total.getCurrency());
            totalProfitSum.setTotalProfit(total.getProfit());
            totalProfitSum.setListProfitSum(new ArrayList<ProfitSum>());
            totalProfitSums.add(totalProfitSum);
        }
        Map<String, TotalProfitSum> sumMap = Maps.uniqueIndex(totalProfitSums, new Function<TotalProfitSum, String>() {
            @Nullable
            @Override
            public String apply(@Nullable TotalProfitSum input) {
                return input.getCurrency();
            }
        });
        for (ProfitAndRate item : response.getData()) {
            ProfitSum profitSum = new ProfitSum();
            profitSum.setProfit(item.getProfit());
            profitSum.setProfitRate(item.getRate());
            profitSum.setFundCode(item.getFundCode());
            profitSum.setFundName(item.getFundShortName());
            profitSum.setFundType(item.getFundType());

            profitSum.setSideBagFlag(item.getSideBagFlag());
            profitSum.setMainBagFundCode(item.getMainBagFundCode());
            profitSum.setMainBagFundName(item.getMainBagFundName());
            profitSum.setSideBagBeginDate(item.getSideBagBeginDate());
            profitSum.setSideBagEndDate(item.getSideBagEndDate());
            profitSum.setSideBagShareFlag(item.getSideBagShareFlag());

            if (sumMap.get(item.getCurrency()) != null) {
                sumMap.get(item.getCurrency()).getListProfitSum().add(profitSum);
            }
        }
        return totalProfitSums;
    }

    public static List<AssetItem> getRMBAssetItem(List<AssetDetails> assetDetails) {
        for (AssetDetails assets : assetDetails) {
            if (Constants.CURRENCY_CNY.equals(assets.getCurrency())) {
                return assets.getAssetItems();
            }
        }
        return new ArrayList<AssetItem>();
    }

    /**
     * 查找是否有货基直销资产
     * @param assetDetails
     * @return
     */
    public static AssetItem findMoneyDsAssets(List<AssetDetails> assetDetails) {
        List<AssetItem> items = getRMBAssetItem(assetDetails);
        for (AssetItem item : items) {
            if ( Constants.FUNDTYPE_CURRENCY.equals(item.getFundType()) && BigDecimal.ZERO.compareTo(BigDecimalUtils.toDefault(item.getTotalDsAsset(), "0")) < 0) {
                return item;
            }
        }
        return null;
    }

    /**
     * 由收益明细列表和资产明细列表转FundProfit列表
     *
     * @param listProfitSum
     * @param assetItems
     * @return
     */
    public static List<FundProfit> toFundProfitList(List<ProfitSum> listProfitSum, List<AssetItem> assetItems) {
        List<FundProfit> fundProfits = new ArrayList<FundProfit>();

        for (ProfitSum profitSum : listProfitSum) {
            FundProfit fundProfit = new FundProfit();
            fundProfit.setFundCode(profitSum.getFundCode());
            fundProfit.setFundName(profitSum.getFundName());
            if (!"--".equals(profitSum.getProfit()) && !"-".equals(profitSum.getProfit())) {
                fundProfit.setAccProfit(BigDecimalUtils.toBigDecimal(profitSum.getProfit()));
            }

            for (AssetItem item : assetItems) {
                if (fundProfit.getFundCode().equals(item.getFundCode())) {
                    fundProfit.setAssets(BigDecimalUtils.toBigDecimal(item.getAssets()));
                }
            }
            fundProfits.add(fundProfit);
        }
        return fundProfits;
    }

    /**
     * 计算总收益
     * @return
     */
    public static BigDecimal getProfitSum(List<FundProfit> fundProfits) {
        BigDecimal result = BigDecimal.ZERO;
        boolean isNull = true; // 判断是否全部收益都为空，如果是则返回空而不是零

        for (FundProfit fundProfit : fundProfits) {
            if (null != fundProfit.getAccProfit()) {
                result = result.add(fundProfit.getAccProfit());
                isNull = false;
            }
        }
        return isNull ? null : result;
    }

    public static List<com.cenker.scrm.common.FundAssetDetailWithAgencyAndChannel> convertToFundAssetDetailWithAgencyAndChannel(List<com.efunds.market.usp.entity.FundAssetDetailWithAgencyAndChannel> fundAssetChannels){
        List <com.cenker.scrm.common.FundAssetDetailWithAgencyAndChannel> assetChannels = new ArrayList<com.cenker.scrm.common.FundAssetDetailWithAgencyAndChannel>();
        for (com.efunds.market.usp.entity.FundAssetDetailWithAgencyAndChannel fundAssetChannel : fundAssetChannels){
            com.cenker.scrm.common.FundAssetDetailWithAgencyAndChannel assetChannel = new com.cenker.scrm.common.FundAssetDetailWithAgencyAndChannel();
            assetChannel.setChannelCode(fundAssetChannel.getChannelCode());
            assetChannel.setAgencyNo(fundAssetChannel.getAgencyNo());
            assetChannel.setAgencyName(fundAssetChannel.getAgencyName());
            assetChannel.setChannelName(fundAssetChannel.getChannelName());
            assetChannel.setAssetItems(convertToAssetTtemDetails(fundAssetChannel.getFundAssetDetails()));
            assetChannels.add(assetChannel);
        }

        return assetChannels;
    }

    public static List<AssetItem> convertToAssetTtemDetails( List<com.efunds.market.usp.entity.FundAssetDetail> uspFundAssetDetails){
        List<AssetItem> fundAssetDetails = new ArrayList<AssetItem>();
        for (com.efunds.market.usp.entity.FundAssetDetail uspFundAssetDetail : uspFundAssetDetails) {
            fundAssetDetails.add(convertToAssetItem(uspFundAssetDetail));
        }
        return fundAssetDetails;
    }

    /**
     * 基金持仓信息列表（区分渠道）
     */
    public static List<KeepCost> convertToKeepCostList(List<com.efunds.market.usp.entity.KeepCost> uspKeepCostList) {
        if (uspKeepCostList == null) {
            return null;
        }
        List<KeepCost> result = new ArrayList<KeepCost>();
        for (com.efunds.market.usp.entity.KeepCost uspKeepCost : uspKeepCostList) {
            if (uspKeepCost == null) {
                continue;
            }
            KeepCost keepCost = new KeepCost();
            keepCost.setFundCode(uspKeepCost.getFundCode());
            keepCost.setAgencyNo(uspKeepCost.getAgencyNo());
            keepCost.setAgencyName(uspKeepCost.getAgencyName());
            keepCost.setChannelCode(uspKeepCost.getChannelCode());
            keepCost.setChannelName(uspKeepCost.getChannelName());
            keepCost.setUnitCost(uspKeepCost.getUnitCosts());
            keepCost.setHoldCost(uspKeepCost.getHoldCost());
            keepCost.setHoldProfit(uspKeepCost.getHoldProfit());
            keepCost.setRate(uspKeepCost.getRate());
            result.add(keepCost);
        }
        return result;
    }

    public static List<com.cenker.scrm.common.SimpleAsset> convertSimpleAssets(List<com.efunds.market.usp.entity.SimpleAsset> uspAssets) {
        List<com.cenker.scrm.common.SimpleAsset> simpleAssets = new ArrayList<com.cenker.scrm.common.SimpleAsset>();
        for (com.efunds.market.usp.entity.SimpleAsset simpleAsset : uspAssets) {
            com.cenker.scrm.common.SimpleAsset webAsset = new com.cenker.scrm.common.SimpleAsset();
            webAsset.setAsset(simpleAsset.getAssets());
            webAsset.setDate(simpleAsset.getDate());
            simpleAssets.add(webAsset);
        }
        return simpleAssets;
    }

    public static List<SimpleProfit> convertSimpleProfit(List<com.efunds.market.usp.entity.SimpleAssetProfit> uspProfits){
        List<SimpleProfit> simpleProfits = new ArrayList<SimpleProfit>();
        for (com.efunds.market.usp.entity.SimpleAssetProfit uspProfit : uspProfits) {
            SimpleProfit simpleProfit = new SimpleProfit();
            simpleProfit.setAsset(uspProfit.getTotalProfit());
            simpleProfit.setProfit(uspProfit.getProfit());
            simpleProfit.setDate(uspProfit.getDate());

            simpleProfits.add(simpleProfit);
        }

        return simpleProfits;
    }

    public static ProfitsAndRateDetailsBill convertProfitsAndRateDetailsBill(GetProfitsAndRateDetailsResponse response) {
        ProfitsAndRateDetailsBill bill = new ProfitsAndRateDetailsBill();
        bill.setBillQueryStartDate(LocalDates.parseLocalDate(response.getBillQueryStartDate()));
        bill.setBillQueryEndDate(LocalDates.parseLocalDate(response.getBillQueryEndDate()));
        if (response.getProfitAndRateDetails() != null) {
            List<ProfitAndRatDetail> profitAndRatDetails = new ArrayList<ProfitAndRatDetail>();
            for (BillProfitAndRatDetail billProfitAndRatDetail : response.getProfitAndRateDetails()) {
                ProfitAndRatDetail detail = new ProfitAndRatDetail();
                detail.setFundCode(billProfitAndRatDetail.getFundCode());
                detail.setFundShortName(billProfitAndRatDetail.getFundShortName());
                detail.setEndAsset(billProfitAndRatDetail.getEndAsset());
                detail.setEndShare(billProfitAndRatDetail.getEndShare());
                detail.setProfit(billProfitAndRatDetail.getProfit());
                detail.setRate(billProfitAndRatDetail.getRate());
                detail.setCurrency(billProfitAndRatDetail.getCurrency());
                detail.setCurrencyDesc(billProfitAndRatDetail.getCurrencyDesc());
                profitAndRatDetails.add(detail);
            }
            bill.setProfitAndRateDetails(profitAndRatDetails);
        }
        if (response.getTotalProfitAndRates() != null) {
            List<com.cenker.scrm.common.TotalProfitAndRate> totalProfitAndRates = new ArrayList<com.cenker.scrm.common.TotalProfitAndRate>();
            for (BillTotalProfitAndRate billTotalProfitAndRate : response.getTotalProfitAndRates()) {
                com.cenker.scrm.common.TotalProfitAndRate totalProfitAndRate = new com.cenker.scrm.common.TotalProfitAndRate();
                totalProfitAndRate.setCurrency(billTotalProfitAndRate.getCurrency());
                totalProfitAndRate.setEndAsset(billTotalProfitAndRate.getEndAsset());
                totalProfitAndRate.setProfit(billTotalProfitAndRate.getProfit());
                totalProfitAndRate.setHoldProfit(billTotalProfitAndRate.getHoldProfit());
                totalProfitAndRate.setRate(billTotalProfitAndRate.getRate());
                totalProfitAndRate.setCurrencyDesc(billTotalProfitAndRate.getCurrencyDesc());
                totalProfitAndRate.setPercentage(billTotalProfitAndRate.getPercentage());
                totalProfitAndRates.add(totalProfitAndRate);
            }
            bill.setTotalProfitAndRates(totalProfitAndRates);
        }
        bill.setHoldingStatus(bill.getProfitAndRateDetails() != null & !bill.getProfitAndRateDetails().isEmpty());
        return bill;
    }

    public static TotalAssetsAndProfitsBill convertTotalAssetsAndProfitsBill(GetTotalAssetsAndProfitsResponse response) {
        TotalAssetsAndProfitsBill bill = new TotalAssetsAndProfitsBill();
        bill.setBillQueryStartDate(LocalDates.parseLocalDate(response.getBillQueryStartDate()));
        bill.setBillQueryEndDate(LocalDates.parseLocalDate(response.getBillQueryEndDate()));
        bill.setEndTotalProfit(response.getEndTotalProfit());
        if (response.getSimpleAssetAndProfit() != null) {
            List<BillSimpleAssetAndProfit> billSimpleAssetAndProfits = new ArrayList<BillSimpleAssetAndProfit>();
            for (SimpleAssetAndProfit simpleAssetAndProfit : response.getSimpleAssetAndProfit()) {
                BillSimpleAssetAndProfit billSimpleAssetAndProfit = new BillSimpleAssetAndProfit();
                billSimpleAssetAndProfit.setDate(LocalDates.parseLocalDate(simpleAssetAndProfit.getDate()));
                billSimpleAssetAndProfit.setAssets(simpleAssetAndProfit.getAssets());
                billSimpleAssetAndProfit.setProfit(simpleAssetAndProfit.getProfit());
                billSimpleAssetAndProfit.setTotalProfit(simpleAssetAndProfit.getTotalProfit());
                billSimpleAssetAndProfits.add(billSimpleAssetAndProfit);
            }
            bill.setSimpleAssetAndProfit(billSimpleAssetAndProfits);
        }
        return bill;
    }

    public static TotalAssetsAndProfitsByRecentYearBill convertTotalAssetsAndProfitsByRecentYearBill(GetTotalAssetsAndProfitsByRecentYearResponse response) {
        TotalAssetsAndProfitsByRecentYearBill bill = new TotalAssetsAndProfitsByRecentYearBill();
        if (response.getProfitList() != null) {
            List<TotalAssetsAndProfitsByRecentYearBill.AssetsAndProfits> assetsAndProfitList = new ArrayList<TotalAssetsAndProfitsByRecentYearBill.AssetsAndProfits>();
            for (GetTotalAssetsAndProfitsByRecentYearResponse.AssetsAndProfits uspAssetsAndProfits : response.getProfitList()) {
                TotalAssetsAndProfitsByRecentYearBill.AssetsAndProfits assetsAndProfits = new TotalAssetsAndProfitsByRecentYearBill.AssetsAndProfits();
                assetsAndProfits.setAssets(uspAssetsAndProfits.getAssets());
                assetsAndProfits.setTotalProfit(uspAssetsAndProfits.getTotalProfit());
                assetsAndProfits.setDate(uspAssetsAndProfits.getDate());
                assetsAndProfits.setCurrency(uspAssetsAndProfits.getCurrency());
                assetsAndProfits.setCurrencyDesc(uspAssetsAndProfits.getCurrencyDesc());
                assetsAndProfitList.add(assetsAndProfits);
            }
            bill.setProfitList(assetsAndProfitList);
        }
        return bill;
    }

    public static List<FundShareDetail> convertFundShareDetailList(com.cenker.scrm.common.CustomerLatestFundSharesDetailResponse response) {
        List<FundShareDetail> fundShareDetails = new ArrayList<FundShareDetail>();
        for (CustomerLatestFundSharesDetailResponse.FundShareDetail shareDetail : response.getShareDetails()) {
            FundShareDetail fundShareDetail = new FundShareDetail();
            fundShareDetail.setAgencyNo(shareDetail.getAgencyNo());
            fundShareDetail.setAgencyName(shareDetail.getAgencyName());
            fundShareDetail.setTradeAcco(shareDetail.getTradeAcco());
            fundShareDetail.setBankAcco(shareDetail.getBankAcco());
            fundShareDetail.setBankName(shareDetail.getBankName());
            fundShareDetail.setChDate(LocalDates.parseLocalDate(shareDetail.getChDate()));
            fundShareDetail.setFeeRatio(shareDetail.getFeeRatio());
            fundShareDetail.setHoldRange(shareDetail.getHoldRange());
            fundShareDetail.setRemainShares(shareDetail.getRemainShares());
            fundShareDetail.setIsSelfChannel(shareDetail.getIsSelfChannel());
            fundShareDetail.setChannelCode(shareDetail.getChannelCode());
            fundShareDetail.setChannelName(shareDetail.getChannelName());
            fundShareDetail.setChannelDecs(shareDetail.getChannelDesc());
            fundShareDetails.add(fundShareDetail);
        }
        return fundShareDetails;
    }
}
