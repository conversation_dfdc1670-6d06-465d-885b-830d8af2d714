package com.cenker.scrm.common;

import lombok.Data;

import java.util.List;

@Data
public class CustomerLatestFundSharesDetailResponse {
    /**
     * 份额明细列表
     */
    private List<FundShareDetail> shareDetails;

    @Data
    public static class FundShareDetail {
        private String tradeAcco;
        private String agencyNo;
        private String agencyName;
        private String bankAcco;
        private String bankName;
        private String chDate;
        private String feeRatio;
        private String holdRange;
        private String remainShares;
        private Boolean isSelfChannel;
        private String channelCode;
        private String channelName;
        private String channelDesc;
    }
}
