<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>epw-scrm-core-center</artifactId>
        <groupId>com.cenker.scrm</groupId>
        <version>2.6.0</version>
    </parent>

    <properties>
        <hessian.version>4.0.7</hessian.version>
        <usp.version>5.23.1-RELEASE</usp.version>
        <joda.time>2.8.2</joda.time>
    </properties>
    <artifactId>epw-scrm-api-fund</artifactId>


    <dependencies>
        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
            <version>${hessian.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.5</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>18.0</version> <!-- Use the appropriate version -->
        </dependency>

        <dependency>
            <groupId>EEI</groupId>
            <artifactId>EEI-Common</artifactId>
            <version>5.17.4.1-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/EEI-Common-5.17.4.1-SNAPSHOT.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>USP</groupId>
            <artifactId>USP-Entity</artifactId>
            <version>${usp.version}</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/USP-Entity-5.23.1-RELEASE.jar</systemPath>
            <exclusions>
                <!--<exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>-->
            </exclusions>
        </dependency>
        <dependency>
            <groupId>USP</groupId>
            <artifactId>USP-Interface</artifactId>
            <version>${usp.version}</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/USP-Interface-5.23.1-RELEASE.jar</systemPath>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>USP</groupId>
            <artifactId>USP-Support</artifactId>
            <version>${usp.version}</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/USP-Support-5.23.1-RELEASE.jar</systemPath>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>USP</groupId>
            <artifactId>USP-Client</artifactId>
            <version>${usp.version}</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/USP-Client-5.23.1-RELEASE.jar</systemPath>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-pool</groupId>
                    <artifactId>commons-pool</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>EEI</groupId>
                    <artifactId>EEI-Jedis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>USP</groupId>
                    <artifactId>USP-Cache</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda.time}</version>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
            <version>2.6.2</version>
        </dependency>

        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-remote</artifactId>
        </dependency>

        <!--服务调用-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
    </dependencies>
</project>