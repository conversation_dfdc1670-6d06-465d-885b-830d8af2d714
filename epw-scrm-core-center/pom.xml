<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>epw-scrm</artifactId>
        <groupId>com.cenker.scrm</groupId>
        <version>2.6.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <description>整合服务</description>
    <modules>
        <module>epw-scrm-oauth-center</module>
        <module>epw-scrm-system-center</module>
        <module>epw-scrm-ai-center</module>
        <module>epw-scrm-api-center</module>
        <module>epw-scrm-wx-center</module>
        <module>epw-scrm-questionnaire</module>
        <module>epw-scrm-knowledgebaseai-center</module>
<!--        <module>epw-scrm-eureka-center</module>-->
        <module>epw-scrm-api-fund</module>
    </modules>
    <artifactId>epw-scrm-core-center</artifactId>

    <dependencies>
        <!-- 公共模块 -->
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-general</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-remote</artifactId>
        </dependency>

        <!--<dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-security</artifactId>
        </dependency>-->

        <!-- 服务间调用转换服务名的负载功能 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>