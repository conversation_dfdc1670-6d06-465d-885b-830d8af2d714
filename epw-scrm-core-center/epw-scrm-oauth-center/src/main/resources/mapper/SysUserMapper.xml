<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.SysUserMapper">

    <resultMap type="com.cenker.scrm.pojo.entity.system.SysUser" id="SysUserResult">
        <id property="userId" column="user_id" />
        <result property="deptId" column="dept_id" />
        <result property="userName" column="user_name" />
        <result property="nickName" column="nick_name" />
        <result property="email" column="email" />
        <result property="phonenumber" column="phonenumber" />
        <result property="sex" column="sex" />
        <result property="avatar" column="avatar" />
        <result property="password" column="password" />
        <result property="status" column="status" />
        <result property="delFlag" column="del_flag" />
        <result property="loginIp" column="login_ip" />
        <result property="loginDate" column="login_date" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
        <result property="corpId" column="corp_id" />
        <result property="corpUserId" column="corp_user_id" />
        <result property="domainAccount" column="domain_account" />
        <result property="userType" column="user_type" />
        <association property="dept" column="dept_id" javaType="com.cenker.scrm.pojo.entity.system.SysDept" resultMap="deptResult" />
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult" />
    </resultMap>

    <resultMap id="RoleResult" type="com.cenker.scrm.pojo.entity.system.SysRole">
        <id property="roleId" column="role_id" />
        <result property="roleName" column="role_name" />
        <result property="roleKey" column="role_key" />
        <result property="roleSort" column="role_sort" />
        <result property="dataScope" column="data_scope" />
        <result property="status" column="role_status" />
    </resultMap>

    <resultMap id="deptResult" type="com.cenker.scrm.pojo.entity.system.SysDept">
        <id property="deptId" column="dept_id" />
        <result property="parentId" column="parent_id" />
        <result property="deptName" column="dept_name" />
        <result property="orderNum" column="order_num" />
        <result property="leader" column="leader" />
        <result property="status" column="dept_status" />
    </resultMap>

    <insert id="insertUser" parameterType="com.cenker.scrm.pojo.entity.system.SysUser" useGeneratedKeys="true" keyProperty="userId">
        insert into sys_user(
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="userName != null and userName != ''">user_name,</if>
        <if test="nickName != null and nickName != ''">nick_name,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="avatar != null and avatar != ''">avatar,</if>
        <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
        <if test="sex != null and sex != ''">sex,</if>
        <if test="password != null and password != ''">password,</if>
        <if test="status != null and status != ''">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="corpId != null and corpId != ''">corp_id,</if>
        <if test="corpUserId != null and corpUserId != ''">corp_user_id,</if>
        <if test="userType != null and userType != ''">user_type,</if>
        <if test="corpOpenUserId != null and corpOpenUserId != ''">corp_open_user_id,</if>
        <if test="domainAccount != null and domainAccount != ''">domain_account,</if>
        create_time
        )values(
        <if test="userId != null and userId != ''">#{userId},</if>
        <if test="deptId != null and deptId != ''">#{deptId},</if>
        <if test="userName != null and userName != ''">#{userName},</if>
        <if test="nickName != null and nickName != ''">#{nickName},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="avatar != null and avatar != ''">#{avatar},</if>
        <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
        <if test="sex != null and sex != ''">#{sex},</if>
        <if test="password != null and password != ''">#{password},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="corpId != null and corpId != ''">#{corpId},</if>
        <if test="corpUserId != null and corpUserId != ''">#{corpUserId},</if>
        <if test="userType != null and userType != ''">#{userType},</if>
        <if test="corpOpenUserId != null and corpOpenUserId != ''">#{corpOpenUserId},</if>
        <if test="domainAccount != null and domainAccount != ''">#{domainAccount},</if>
        sysdate()
        )
    </insert>

    <sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag,
         u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.corp_id, u.corp_user_id,u.user_type,u.domain_account,
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>

    <select id="checkUserIsValidByName"  resultType="int">
        SELECT
            count(1)
        FROM
            sys_user u
        where
            u.user_name = #{userName}
        And u.del_flag = 0
        and u.status = 0
    </select>

    <select id="selectUserByUserName" resultMap="SysUserResult">
        <include refid="selectUserVo" />
        where u.user_name = #{userName}
        and u.del_flag = 0
    </select>

    <select id="selectUserByDomainAccount" resultMap="SysUserResult">
        <include refid="selectUserVo" />
        where u.domain_account = #{domainAccount} and u.del_flag = 0 limit 1
    </select>

    <select id="selectUserByUserCorpInfo" resultMap="SysUserResult">
        <include refid="selectUserVo" />
        where u.corp_id = #{corpId}
        and u.corp_user_id =#{corpUserId}
        and u.del_flag = 0
    </select>

    <select id="checkUserIsValid" parameterType="String" resultType="int">
        SELECT
            count(1)
        FROM
            sys_user u
            -- INNER JOIN sys_dept d ON d.dept_id = u.dept_id
            -- AND d.del_flag = '0'
            -- AND d.STATUS = '0'
        where
            u.corp_user_id = #{corpUserId} and u.corp_id = #{corpId}
            AND u.del_flag = '0'
            AND u.STATUS = '0'
    </select>

    <select id="selectUserByUserCorpInfoV2" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status, u.del_flag,
               u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.corp_id, u.corp_user_id,u.user_type,
               d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,
               r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role ur on u.user_id = ur.user_id
                 left join sys_role r on r.role_id = ur.role_id
                 left join tb_wx_corp_config c on u.corp_id = c.corp_id and c.`status` = 0
        /* 关联企业配置匹配加密id */
        where c.open_corp_id = #{corpId}
        and u.corp_open_user_id =#{corpUserId}
        limit 1
    </select>

    <select id="checkUserIsCorpAdmin" resultType="java.lang.Integer">
        select administrator from tb_wx_user where userid = #{corpUserId} and corp_id = #{corpId}
    </select>

    <select id="checkCorpProviderInstall" resultType="java.lang.Integer">
        select count(1) from tb_wx_corp_config where (corp_id = #{corpId} or open_corp_id = #{corpId}) and  `status` = 0 and del_flag = 0
    </select>

    <select id="selectCorpIdByOpenCorpId" resultType="java.lang.String">
        select corp_id from tb_wx_user_map where open_corp_id = #{openCorpId} limit 1
    </select>
    <select id="selectUserIdByOpenUserId" resultType="java.lang.String">
        select user_id from tb_wx_user_map where open_user_id = #{userId} and corp_id = #{corpId} limit 1
    </select>
    <select id="selectOpenUserIdUserIdByUserId" resultType="java.lang.String">
        select open_user_id from tb_wx_user_map where user_id = #{userId} and corp_id = #{corpId} limit 1
    </select>
    <select id="selectTbUserByUserId" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxUser">
        select * from tb_wx_user where corp_id = #{corpId} and userid = #{userId}
    </select>
    <select id="selectDeptIdByCorpId" resultType="java.lang.String">
        select dept_id from sys_dept where corp_id = #{corpId} and dept_id != 100 limit 1
    </select>

    <select id="selectTbWxConfigId" resultType="Long">
        select id from tb_wx_corp_config where corp_id = #{corpId} limit 1
    </select>
</mapper> 