package com.cenker.scrm.api.web.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.cenker.scrm.api.annotation.NoRepeatSubmit;
import com.cenker.scrm.api.util.HttpUtils;
import com.cenker.scrm.common.constant.CommonConstants;
import com.cenker.scrm.common.util.RedisUtils;
import com.cenker.scrm.common.util.Result;
import com.cenker.scrm.common.validator.ValidatorUtils;
import com.cenker.scrm.project.constant.ProjectRedisKeyConstants;
import com.cenker.scrm.project.entity.UserProjectResultEntity;
import com.cenker.scrm.project.entity.UserProjectSettingEntity;
import com.cenker.scrm.project.request.QueryProjectResultRequest;
import com.cenker.scrm.project.service.ProjectDashboardService;
import com.cenker.scrm.project.service.UserProjectResultService;
import com.cenker.scrm.project.service.UserProjectService;
import com.cenker.scrm.project.service.UserProjectSettingService;
import com.cenker.scrm.project.vo.ExportProjectResultVO;
import com.cenker.scrm.project.vo.ProjectReportVO;
import com.cenker.scrm.wx.mp.service.WxMpUserMsgService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> chengkeyun
 * @description : 项目
 * @create : 2020-11-18 18:17
 **/

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/project/result")
public class UserProjectResultController {

    private final UserProjectService projectService;
    private final UserProjectResultService projectResultService;
    private final UserProjectSettingService userProjectSettingService;
//    private final MailService mailService;
    private final WxMpUserMsgService userMsgService;
    private final RedisUtils redisUtils;

    private final ProjectDashboardService projectDashboardService;

    /***
     * 查看项目
     *  记录查看的IP 统计查看用户数
     * @return
     */
    @PostMapping("view/{projectKey}")
    public Result viewProject(HttpServletRequest request, @PathVariable("projectKey") String projectKey) {
        String ip = HttpUtils.getIpAddr(request);
        Integer count = Convert.toInt(redisUtils.hmGet(StrUtil.format(ProjectRedisKeyConstants.PROJECT_VIEW_IP_LIST, projectKey), ip), CommonConstants.ConstantNumber.ZERO);
        redisUtils.hmSet(StrUtil.format(ProjectRedisKeyConstants.PROJECT_VIEW_IP_LIST, projectKey), ip, count + CommonConstants.ConstantNumber.ONE);
        return Result.success();
    }


    /**
     * 填写
     *
     * @param entity
     * @param request
     * @return
     */
    @NoRepeatSubmit
    @PostMapping("/create")
    public Result createProjectResult(@RequestBody UserProjectResultEntity entity, HttpServletRequest request) {
        log.info("【客户提交表单】客户【{}】，提交参数：{}", entity.getWxOpenName(), JSON.toJSONString(entity));
        ValidatorUtils.validateEntity(entity);
        entity.setSubmitRequestIp(HttpUtils.getIpAddr(request));
        Result<Map> userProjectSettingStatus = userProjectSettingService.getUserProjectSettingStatus(entity.getProjectKey(), entity.getSubmitRequestIp(), entity.getWxOpenId());
        if (ObjectUtil.equal(userProjectSettingStatus.getData().get("status"), 0)) {
            return Result.failed(userProjectSettingStatus.getMsg());
        }
        if(null != entity.getWxUserInfo()){
            if(entity.getWxUserInfo().containsKey("unionId")) {
                entity.setWxUnionId(entity.getWxUserInfo().get("unionId").toString());
            }
            if(entity.getWxUserInfo().containsKey("headImgUrl")) {
                entity.setWxHeadImgUrl(entity.getWxUserInfo().get("headImgUrl").toString());
            }

        }
        projectResultService.saveProjectResult(entity);

        log.info("【客户提交表单】客户【{}】提交成功", entity.getWxOpenName());
        return Result.success();
    }

    /**
     * 查询结果
     * @param request
     * @return
     */
    @PostMapping("/result")
    public Result queryProjectResult(@RequestBody QueryProjectResultRequest request){
        ExportProjectResultVO exportProjectResultVO = projectResultService.exportProjectResult(request);
        return Result.success(exportProjectResultVO);
    }

    /**
     * 填写结果excel导出
     *
     * @param request
     * @return
     */
    @GetMapping("/export")
    public void exportProjectResult(QueryProjectResultRequest request, HttpServletResponse response) throws IOException {
        ExportProjectResultVO exportProjectResultVO = projectResultService.exportProjectResult(request);
        // 通过工具类创建writer，默认创建xls格式
        ExcelWriter writer = ExcelUtil.getWriter();
        //自定义标题别名
        exportProjectResultVO.getTitleList().forEach(item -> {
            writer.addHeaderAlias(item.getFieldKey(), item.getTitle());
        });
        //设置每列默认宽度
        writer.setColumnWidth(-1, 20);
        // 一次性写出内容，使用默认样式，强制输出标题
        writer.write(exportProjectResultVO.getResultList(), true);
        //out为OutputStream，需要写出到的目标流
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=test.xls");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);
        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }

    /**
     * 填写附件导出
     *
     * @param request
     * @return
     */
    @GetMapping("/download/file")
    public Result downloadProjectResultFile(QueryProjectResultRequest request) {
        return projectResultService.downloadProjectResultFile(request);
    }


    /**
     * 结果分页
     *
     * @param request
     * @return
     */
    @GetMapping("/page")
    public Result queryProjectResults(QueryProjectResultRequest request) {
        return Result.success(projectResultService.listByQueryConditions(request));
    }


    /**
     * 查询公开结果
     *
     * @param request
     * @return
     */
    @GetMapping("/public/page")
    public Result queryProjectPublicResults(QueryProjectResultRequest request) {
        UserProjectSettingEntity settingEntity = userProjectSettingService.getByProjectKey(request.getProjectKey());
        if (null != settingEntity && !settingEntity.getPublicResult()) {
            return Result.success();
        }
        return Result.success(projectResultService.listByQueryConditions(request));
    }

//    private void sendWriteResultNotify(UserProjectSettingEntity settingEntity, UserProjectResultEntity entity) {
//        if (ObjectUtil.isNull(settingEntity)) {
//            return;
//        }
//        String projectKey = entity.getProjectKey();
//        UserProjectEntity project = projectService.getByKey(projectKey);
//        if (StrUtil.isNotBlank(settingEntity.getNewWriteNotifyEmail())) {
//            mailService.sendTemplateHtmlMail(settingEntity.getNewWriteNotifyEmail(), "新回复通知", "mail/project-write-notify", MapUtil.of("projectName", project.getName()));
//        }
//
//        if (StrUtil.isNotBlank(settingEntity.getNewWriteNotifyWx())) {
//            List<String> openIdList = StrUtil.splitTrim(settingEntity.getNewWriteNotifyWx(), ";");
//            openIdList.stream().forEach(openId -> {
//                userMsgService.sendKfTextMsg("", openId, "收到新的反馈，请去Pc端查看");
//            });
//        }
//    }

    /**
     * 查询反馈数据
     * @param projectKey
     * @return
     */
    @GetMapping("/analysis")
    public Result queryProjectAnalysisResult(String projectKey){
        List<ProjectReportVO.Analysis> analyseList = projectDashboardService.projectReportAnalysis(projectKey);
        return Result.success(analyseList);
    }

    /**
     * 填写结果excel导出
     *
     * @param
     * @return
     */
    @GetMapping("/export_analysis")
    public void exportProjectAnalysisResult(String projectKey, HttpServletResponse response) throws IOException {
        XSSFWorkbook workbook = projectDashboardService.exportProjectAnalysisResult(projectKey);
        //response为HttpServletResponse对象
        response.setContentType("application/octet-stream;charset=UTF-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=analysis.xlsx");
        response.addHeader("Cache-Control", "no-cache");
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);

        workbook.close();
        outputStream.close();
    }

}