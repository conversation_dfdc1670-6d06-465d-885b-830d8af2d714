<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.SysNoticeMapper">

    <resultMap type="SysNotice" id="SysNoticeResult">
        <result property="noticeId" column="notice_id" />
        <result property="noticeTitle" column="notice_title" />
        <result property="noticeType" column="notice_type" />
        <result property="noticeContent" column="notice_content" />
        <result property="status" column="status" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="isNoticeAllUser" column="is_notice_all_user" />
        <result property="noticeUser" column="notice_user" />
        <result property="noticeMode" column="notice_mode" />
        <result property="timedTask" column="timed_task" />
        <result property="sendTime" column="send_time" />

    </resultMap>

    <sql id="selectNoticeVo">
        select notice_id, notice_title, notice_type, cast(notice_content as char) as notice_content,
               status, create_by, create_time, update_by, update_time, is_notice_all_user,
               notice_user, notice_mode, timed_task, send_time
		from sys_notice
    </sql>

    <select id="selectNoticeById" parameterType="Long" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo" />
        where notice_id = #{noticeId}
    </select>

    <select id="selectNoticeList" parameterType="com.cenker.scrm.pojo.request.system.SysNoticeRequest" resultMap="SysNoticeResult">
        select n.notice_id, n.notice_title, n.notice_type, cast(n.notice_content as char) as notice_content,
        n.status, u.nick_name create_by, n.create_time, n.update_by, n.update_time,
        n.is_notice_all_user, n.notice_user, n.notice_mode, n.timed_task, n.send_time
        from sys_notice n
        left join sys_user u on u.user_id = n.create_by
        <where>
            <if test="corpId != null and corpId != ''">
                AND n.corp_id = #{corpId}
            </if>
            <if test="noticeTitle != null and noticeTitle != ''">
                AND n.notice_title like concat('%', #{noticeTitle}, '%')
            </if>
            <if test="noticeType != null and noticeType != ''">
                AND n.notice_type = #{noticeType}
            </if>
            <if test="userId != null and userId != ''">
                AND n.create_by = #{userId}
            </if>
            <if test="userIds != null  and userIds.size > 0">
                AND n.create_by in
                <foreach item="userId" collection="userIds" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="status != null and status != ''">
                AND n.status = #{status}
            </if>
            <if test="beginTime != null and beginTime != ''">
                AND n.create_time >= CONCAT(#{beginTime}, ' 00:00:00')
            </if>
            <if test="endTime != null and endTime != ''">
                AND n.create_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
            </if>
        </where>
        order by n.create_time desc
    </select>

    <insert id="insertNotice" parameterType="SysNotice">
        insert into sys_notice (
        <if test="noticeTitle != null and noticeTitle != '' ">notice_title,</if>
        <if test="noticeType != null and noticeType != '' ">notice_type,</if>
        <if test="noticeContent != null and noticeContent != '' ">notice_content,</if>
        <if test="status != null">status,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="corpId != null and corpId != ''">corp_id,</if>
        <if test="isNoticeAllUser != null">is_notice_all_user,</if>
        <if test="sendTime != null">send_time,</if>
        <if test="noticeUser != null and noticeUser != ''">notice_user,</if>
        <if test="noticeMode != null and noticeMode != ''">notice_mode,</if>
        <if test="timedTask != null">timed_task,</if>
        create_time
        )values(
        <if test="noticeTitle != null and noticeTitle != ''">#{noticeTitle},</if>
        <if test="noticeType != null">#{noticeType},</if>
        <if test="noticeContent != null and noticeContent != ''">#{noticeContent},</if>
        <if test="status != null">#{status},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="corpId != null and corpId != ''">#{corpId},</if>
        <if test="isNoticeAllUser != null">#{isNoticeAllUser},</if>
        <if test="sendTime != null">#{sendTime},</if>
        <if test="noticeUser != null and noticeUser != ''">#{noticeUser},</if>
        <if test="noticeMode != null and noticeMode != ''">#{noticeMode},</if>
        <if test="timedTask != null">#{timedTask},</if>
        sysdate()
        )
    </insert>

    <update id="updateNotice" parameterType="SysNotice">
        update sys_notice
        <set>
            <if test="noticeTitle != null and noticeTitle != ''">notice_title = #{noticeTitle},</if>
            <if test="noticeType != null">notice_type = #{noticeType},</if>
            <if test="noticeContent != null">notice_content = #{noticeContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="isNoticeAllUser != null">is_notice_all_user = #{isNoticeAllUser},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="noticeUser != null and noticeUser != ''">notice_user = #{noticeUser},</if>
            <if test="noticeMode != null and noticeMode != ''">notice_mode = #{noticeMode},</if>
            <if test="timedTask != null">timed_task = #{timedTask},</if>
            update_time = sysdate()
        </set>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteNoticeById" parameterType="Long">
        delete from sys_notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteNoticeByIds" parameterType="Long">
        delete from sys_notice where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

    <select id="selectNoticeListTop" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo" />
        WHERE
          `status` = 1
          and notice_type = #{noticeType}
        ORDER BY create_time desc
        limit 6
    </select>

</mapper>