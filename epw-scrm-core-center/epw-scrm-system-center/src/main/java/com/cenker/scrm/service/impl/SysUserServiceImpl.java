package com.cenker.scrm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.annotation.DataScope;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.UserConstants;
import com.cenker.scrm.enums.UserStatus;
import com.cenker.scrm.mapper.*;
import com.cenker.scrm.pojo.entity.system.*;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.service.ISysConfigService;
import com.cenker.scrm.service.ISysUserOnlineService;
import com.cenker.scrm.service.ISysUserService;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.SecurityUtils;
import com.cenker.scrm.util.StringUtils;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    private SysRoleMapper roleMapper;
    private SysPostMapper postMapper;
    private SysUserRoleMapper userRoleMapper;
    private SysUserPostMapper userPostMapper;
    private ISysConfigService configService;
    private ISysUserOnlineService userOnlineService;
    private RedisCache redisCache;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {
        return baseMapper.selectUserList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return baseMapper.selectUserByUserName(userName);
    }

    @Override
    public SysUser selectUserByCorpInfo(String corpId, String corpUserId) {
        return baseMapper.selectUserByUserCorpInfo(corpId, corpUserId);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return baseMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        StringBuffer idsStr = new StringBuffer();
        for (SysRole role : list) {
            idsStr.append(role.getRoleName()).append(",");
        }
        if (StringUtils.isNotEmpty(idsStr.toString())) {
            return idsStr.substring(0, idsStr.length() - 1);
        }
        return idsStr.toString();
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        StringBuffer idsStr = new StringBuffer();
        for (SysPost post : list) {
            idsStr.append(post.getPostName()).append(",");
        }
        if (StringUtils.isNotEmpty(idsStr.toString())) {
            return idsStr.substring(0, idsStr.length() - 1);
        }
        return idsStr.toString();
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(String userName) {
        int count = baseMapper.checkUserNameUnique(userName);
        if (count > 0) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysUser user) {
        String userId = StringUtils.isNull(user.getUserId()) ? "-1" : user.getUserId();
        SysUser info = baseMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && !info.getUserId().equals(userId)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkEmailUnique(SysUser user) {
        String userId = StringUtils.isNull(user.getUserId()) ? "-1" : user.getUserId();
        SysUser info = baseMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && !info.getUserId().equals(userId)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new CustomException("不允许操作超级管理员用户");
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = baseMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user) {
        Long userId = Long.valueOf(user.getUserId());
        recordRoleChangeLog(user);

        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);

        // 修改用户信息，需更新缓存
        String tokenKey = CacheKeyConstants.LOGIN_TOKEN_KEY + user.getDomainAccount();
        redisCache.expire(tokenKey, 0);

        return baseMapper.updateUser(user);
    }

    /**
     * 记录用户角色变更日志
     * @param user
     */
    private void recordRoleChangeLog(SysUser user) {
        List<SysRole> currentRoles = roleMapper.selectRolePermissionByUserId(Long.valueOf(user.getUserId()));
        List<Long> currentRoleIds = currentRoles.stream().map(SysRole::getRoleId).collect(Collectors.toList());
        List<Long> newRoleIds = Arrays.asList(user.getRoleIds());

        StringBuilder operDesc = new StringBuilder(String.format("修改用户【%s】的信息。", user.getNickName())).append(StrUtil.LF);
        // 角色未变更
        if (CollectionUtil.isEmpty(currentRoleIds) || !new HashSet<>(currentRoleIds).equals(new HashSet<>(newRoleIds))) {
            operDesc.append("授予角色权限:").append(StrUtil.LF);

            List<SysRole> newRoles = roleMapper.selectRolesById(Arrays.asList(user.getRoleIds()));
            String currentRoleNames = currentRoles.stream().map(role -> "【" + role.getRoleName() + "】").collect(Collectors.joining(","));
            String newRoleNames = newRoles.stream().map(role -> "【" + role.getRoleName() + "】").collect(Collectors.joining(","));
            operDesc.append("原角色：").append(currentRoleNames).append(StrUtil.LF);
            operDesc.append("现角色：").append(newRoleNames).append(StrUtil.LF);
        }

        LogUtil.recordOperLog(operDesc.toString());
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return baseMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return baseMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return baseMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return baseMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return baseMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        Long[] roles = user.getRoleIds();
        if (StringUtils.isNotNull(roles)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roles) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(Long.valueOf(user.getUserId()));
                ur.setRoleId(roleId);
                list.add(ur);
            }
            if (list.size() > 0) {
                userRoleMapper.batchUserRole(list);
            }
        }
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotNull(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(Long.valueOf(user.getUserId()));
                up.setPostId(postId);
                list.add(up);
            }
            if (list.size() > 0) {
                userPostMapper.batchUserPost(list);
            }
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return baseMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId + ""));
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        return baseMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new CustomException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysUser user : userList) {
            try {
                // 验证是否存在这个用户
                SysUser u = baseMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u)) {
                    BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
                    // todo 这个加密对象试试
                    //user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setPassword(passwordEncoder.encode(password));
                    user.setCreateBy(operName);
                    this.insertUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport) {
                    user.setUpdateBy(operName);
                    this.updateUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new CustomException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public int checkUserIsValid(String corpId, String corpUserId) {
        if (StringUtils.isNotBlank(corpUserId)) {
            return baseMapper.checkUserIsValid(corpId, corpUserId);
        }
        return 0;
    }

    @Override
    public int updateCorpUserId(SysUser sysUser) {
        return baseMapper.updateUser(sysUser);
    }

    @Override
    public int stopUserByCorpId(String authCorpId) {
        SysUser user = new SysUser();
        user.setCorpId(authCorpId);
        user.setStatus(UserStatus.DISABLE.getCode());
        user.setDelFlag(UserStatus.DELETED.getCode());
        user.setUpdateBy(Constants.DEFAULT_USER);
        // 强制下线所有登录用户
        userOnlineService.forceLogoutByCorpId(authCorpId);
        return baseMapper.stopUserByCorpId(user);
    }

    @Override
    public int checkUserIsValidByName(String userName) {
        if (StringUtils.isNotBlank(userName)) {
            return baseMapper.checkUserIsValidByName(userName);
        }
        return 0;
    }

    @Override
    public List<SysUser> selectUserByCorpId(String corpId) {
        return baseMapper.selectUserByCorpId(corpId);
    }

    @Override
    public void updateUserName(SysUser sysUser) {
        baseMapper.updateUserName(sysUser);
    }

    @Override
    public int recoverUserByCorpId(String corpId) {
        SysUser user = new SysUser();
        user.setCorpId(corpId);
        // 人工开启账号
        user.setStatus(UserStatus.OK.getCode());
        user.setDelFlag(UserStatus.OK.getCode());
        user.setUpdateBy(Constants.DEFAULT_USER);
        return baseMapper.stopUserByCorpId(user);
    }

    @Override
    public String selectSysUserByCorpUserId(String corpUserId, String corpId) {
        return baseMapper.selectSysUserByCorpUserId(corpUserId, corpId);
    }

    @Override
    public Integer checkUserIsCorpAdmin(String corpUserId, String corpId) {
        return baseMapper.checkUserIsCorpAdmin(corpUserId, corpId);
    }

    @Override
    public SysUser selectUserByOpenUserId(String openUserId, String corpId) {
        return baseMapper.selectUserByOpenUserId(openUserId, corpId);
    }

    @Override
    public String checkCorpUserUnique(SysUser user) {
        String userId = StringUtils.isNull(user.getUserId()) ? "-1" : user.getUserId();
        SysUser info = baseMapper.checkCorpUserUnique(user);
        if (StringUtils.isNotNull(info) && !info.getUserId().equals(userId)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public void createSysUserByTbWxUser(TbWxUser tbWxUser) {
        String corpId = tbWxUser.getCorpId();

        SysUser sysUser = new SysUser();
        // 部门（商户) 私有化默认
        sysUser.setDeptId("100");
        sysUser.setCorpId(corpId);
        sysUser.setCorpUserId(tbWxUser.getUserid());
        sysUser.setCorpOpenUserId(tbWxUser.getUserid());
        sysUser.setDomainAccount(tbWxUser.getUserid());
        String userName = generateUserName(tbWxUser.getUserid());
        sysUser.setUserName(userName);
        sysUser.setAvatar(tbWxUser.getAvatar());
        sysUser.setNickName(tbWxUser.getName());
        String userPassword = generatePassword();
        sysUser.setPassword(userPassword);

        if (StrUtil.isNotBlank(tbWxUser.getRoleId())) {
            Long[] roleIds = Arrays.stream(tbWxUser.getRoleId().split(","))
                    .map(Long::valueOf)
                    .collect(Collectors.toList())
                    .toArray(new Long[0]);

            sysUser.setRoleIds(roleIds);
        }

        String status = tbWxUser.getAdministrator()? UserStatus.OK.getCode() : UserStatus.DISABLE.getCode();
        sysUser.setStatus(status);
        sysUser.setDelFlag(UserStatus.OK.getCode());
        sysUser.setRemark("设置角色自动创建账号");

        // 企业配置id
        sysUser.setCorpConfigId(baseMapper.selectTbWxConfigId(null, corpId));
        insertUser(sysUser);
    }

    @Override
    public void saveUserByWxUser(TbWxUser tbWxUser) {
        SysUser sysUser = this.selectUserByWxUserId(tbWxUser.getUserid());
        if (sysUser == null) {
            log.info("【员工企微账号开通权限】不存在账号，开始创建账号! 企微账号：{}，是否开通管理后台权限：{}", tbWxUser.getUserid(), tbWxUser.getAdministrator());
            this.createSysUserByTbWxUser(tbWxUser);
        } else {
            log.info("【员工企微账号开通权限】已存在账号，修改账号权限! 企微账号：{}，是否开通管理后台权限：{}", tbWxUser.getUserid(), tbWxUser.getAdministrator());
            String status = tbWxUser.getAdministrator()? UserStatus.OK.getCode() : UserStatus.DISABLE.getCode();
            sysUser.setStatus(status);
            sysUser.setUpdateTime(new Date());
            sysUser.setUpdateBy(Constants.DEFAULT_USER);

            // 角色ID
            if (StrUtil.isNotBlank(tbWxUser.getRoleId())) {
                Long[] roleIds = StrUtil.splitTrim(tbWxUser.getRoleId(), StrUtil.COMMA)
                        .stream().map(Long::valueOf).collect(Collectors.toList()).toArray(new Long[0]);
                sysUser.setRoleIds(roleIds);
            }

            this.updateUser(sysUser);
        }
    }

    private String generatePassword() {
        // 获取参数初始化密码
        String userPassword = redisCache.getCacheObject(CacheKeyConstants.SYS_CONFIG_KEY + "sys.user.initPassword");
        // 若未设置初始化密码时，自动生成随机密码
        if (StringUtils.isEmpty(userPassword)) {
            String passBaseString = RandomUtil.BASE_CHAR_NUMBER + "ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$_";
            userPassword = RandomUtil.randomString(passBaseString,8);
        }
        // 与前端加密一致
        for (int j = 0; j < 5; j++) {
            userPassword = DigestUtils.md5DigestAsHex(userPassword.getBytes());
            log.info("密码：{}，md5加密当前次数:{}，加密结果为{}", userPassword, j + 1, userPassword);
        }

        return SecurityUtils.encryptPassword(userPassword);
    }

    private String generateUserName(String userName) {
        // 校验账号是否存在
        int i;
        do {
            i = baseMapper.checkUserIsValidByName(userName);
            if (i > 0) {
                userName = userName + RandomUtil.randomString(1);
            }
        } while (i > 0);
        return userName;
    }

    @Override
    public void stopUserById(String userId) {
        SysUser user = new SysUser();
        user.setUserId(userId);
        user.setStatus(UserStatus.DISABLE.getCode());
        user.setDelFlag(UserStatus.DELETED.getCode());
        user.setUpdateBy(Constants.DEFAULT_USER);
        // 强制下线所有登录用户
        userOnlineService.forceLogoutBySysUserId(userId);
        baseMapper.updateUser(user);
    }

    @Override
    public SysUser selectUserByDomainAccount(String domainAccount) {
        return baseMapper.selectUserByDomainAccount(domainAccount);
    }

    @Override
    public SysUser selectUserByWxUserId(String wxUserId) {
        return baseMapper.selectUserByWxUserId(wxUserId);
    }


    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> selectAllocatedList(SysUser user)
    {
        return baseMapper.selectAllocatedList(user);
    }

    @Override
    public List<SysUser> selectUserNoDeptList() {
        return baseMapper.selectUserNoDeptList();
    }
}
