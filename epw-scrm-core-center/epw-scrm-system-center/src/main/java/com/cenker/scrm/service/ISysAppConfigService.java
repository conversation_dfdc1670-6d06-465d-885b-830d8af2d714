package com.cenker.scrm.service;

import com.cenker.scrm.pojo.dto.system.SysAppConfigListDto;
import com.cenker.scrm.pojo.entity.system.SysAppConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.vo.system.SysAppConfigListVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_app_config(应用配置表)】的数据库操作Service
* @createDate 2025-03-25 17:19:51
*/
public interface ISysAppConfigService extends IService<SysAppConfig> {

    /**
     * 查询应用配置列表
     * @param param
     * @return
     */
    List<SysAppConfigListVo> listAppConfig(SysAppConfigListDto param);

}
