package com.cenker.scrm.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.enums.NamingRuleScope;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.system.*;
import com.cenker.scrm.pojo.entity.system.BuNamingRule;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.system.NamingRuleVo;
import com.cenker.scrm.service.IBuNamingRuleService;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.wx.MaterialFeign;
import com.cenker.scrm.wx.RadarFeign;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/naming/rule")
public class NamingRuleController extends BaseController {

    private final IBuNamingRuleService namingRuleService;
    private final RadarFeign radarFeign;
    private final MaterialFeign materialFeign;

    @PostMapping()
    public AjaxResult add(@Valid @RequestBody NamingRuleAddDto namingRuleAddDto) {
        BuNamingRule buNamingRule = new BuNamingRule();
        buNamingRule.setRuleName(namingRuleAddDto.getRuleName());
        buNamingRule.setScope(namingRuleAddDto.getScope());
        buNamingRule.setRuleContent(namingRuleAddDto.getRuleContent());
        buNamingRule.setStatus(CommonConstants.NO);
        buNamingRule.setCreateBy(namingRuleAddDto.getLoginUserId());
        buNamingRule.setCreateTime(new Date());

        if (existRuleName(buNamingRule)) {
            return AjaxResult.error("规则名称已存在");
        }

        namingRuleService.save(buNamingRule);

        String operDesc = String.format("新增命名规则[%s]", buNamingRule.getRuleName());
        LogUtil.recordOperLog(ModuleEnum.NAMING_RULE, BusinessType.INSERT, operDesc);
        return AjaxResult.success();
    }

    @PutMapping()
    public AjaxResult update(@Valid @RequestBody NamingRuleUpdateDto namingRuleUpdateDto) {
        BuNamingRule buNamingRule = namingRuleService.getById(namingRuleUpdateDto.getRuleId());
        if (buNamingRule == null) {
            return AjaxResult.error("规则不存在");
        }
        buNamingRule.setRuleName(namingRuleUpdateDto.getRuleName());
        buNamingRule.setScope(namingRuleUpdateDto.getScope());
        buNamingRule.setRuleContent(namingRuleUpdateDto.getRuleContent());
        buNamingRule.setUpdateBy(namingRuleUpdateDto.getLoginUserId());
        buNamingRule.setUpdateTime(new Date());

        if (existRuleName(buNamingRule)) {
            return AjaxResult.error("规则名称已存在");
        }

        namingRuleService.updateById(buNamingRule);

        String operDesc = String.format("修改命名规则[%s]", buNamingRule.getRuleName());
        LogUtil.recordOperLog(ModuleEnum.NAMING_RULE, BusinessType.UPDATE, operDesc);
        return AjaxResult.success();
    }

    @PutMapping("/enable")
    public AjaxResult enable(@Valid @RequestBody NamingRuleEnableDto namingRuleEnableDto) {
        String statusDesc = CommonConstants.YES.equals(namingRuleEnableDto.getRuleStatus()) ? "启用" : "停用";
        log.info("{}命名规则，规则ID：[{}]", statusDesc,namingRuleEnableDto.getRuleId());

        BuNamingRule buNamingRule = namingRuleService.getById(namingRuleEnableDto.getRuleId());
        if (buNamingRule == null) {
            return AjaxResult.error("规则不存在");
        }

        StringBuilder desc = new StringBuilder();

        if (CommonConstants.YES.equals(namingRuleEnableDto.getRuleStatus())) {
            BuNamingRule oldNamingRule = namingRuleService.getOne(new LambdaQueryWrapper<BuNamingRule>()
                    .eq(BuNamingRule::getScope, buNamingRule.getScope())
                    .eq(BuNamingRule::getStatus, CommonConstants.YES), false);
            if (Objects.nonNull(oldNamingRule)) {
                log.info("停用命名规则【{}】", oldNamingRule.getRuleName());
                namingRuleService.lambdaUpdate()
                        .set(BuNamingRule::getStatus, CommonConstants.NO)
                        .set(BuNamingRule::getUpdateBy, namingRuleEnableDto.getLoginUserId())
                        .set(BuNamingRule::getUpdateTime, new Date())
                        .eq(BuNamingRule::getScope, buNamingRule.getScope())
                        .ne(BuNamingRule::getRuleId, namingRuleEnableDto.getRuleId())
                        .update();
                desc.append(String.format("停用命名规则[%s]", oldNamingRule.getRuleName())).append("\n");
            }

            updateNamingRuleByScope(namingRuleEnableDto.getApplyToOld(), buNamingRule.getScope(), buNamingRule.getRuleContent());
        }

        namingRuleService.lambdaUpdate()
                .set(BuNamingRule::getStatus, namingRuleEnableDto.getRuleStatus())
                .set(BuNamingRule::getUpdateBy, namingRuleEnableDto.getLoginUserId())
                .set(BuNamingRule::getUpdateTime, new Date())
                .eq(BuNamingRule::getRuleId, namingRuleEnableDto.getRuleId())
                .update();

        desc.append(String.format("%s命名规则[%s]", statusDesc,buNamingRule.getRuleName()));
        LogUtil.recordOperLog(ModuleEnum.NAMING_RULE, BusinessType.UPDATE, desc.toString());

        log.info("{}命名规则[{}]成功", statusDesc, buNamingRule.getRuleName());
        return AjaxResult.success();
    }

    /**
     * 更新应用范围已创建内容的命名规则
     * @param applyToOld
     * @param scope
     * @param namingRule
     */
    private void updateNamingRuleByScope(Integer applyToOld, String scope, String namingRule) {
        if (!CommonConstants.NUM_1.equals(applyToOld)) {
            return;
        }

        log.info("更新应用范围【{}】已创建内容的命名规则！", scope);
        Map<String, String> params = new HashMap<>();
        params.put("namingRule", namingRule);

        if (NamingRuleScope.INTELLIGENT_MATERIAL.name().equalsIgnoreCase(scope)) {
            radarFeign.updateNamingRule(params);
        } else if (NamingRuleScope.NORMAL_MATERIAL.name().equalsIgnoreCase(scope)) {
            materialFeign.updateNamingRule(params);
        }
    }

    @DeleteMapping()
    public AjaxResult delete(@Valid @RequestBody NamingRuleRemoveDto namingRuleRemoveDto) {
        BuNamingRule buNamingRule = namingRuleService.getById(namingRuleRemoveDto.getRuleId());
        if (buNamingRule == null) {
            return AjaxResult.error("规则不存在");
        }

        namingRuleService.removeById(namingRuleRemoveDto.getRuleId());

        String operDesc = String.format("删除命名规则[%s]", buNamingRule.getRuleName());
        LogUtil.recordOperLog(ModuleEnum.NAMING_RULE, BusinessType.DELETE, operDesc);
        return AjaxResult.success();
    }


    @GetMapping("/list")
    public AjaxResult list(NamingRuleListDto namingRuleListDto) {
        startPage();
        List<NamingRuleVo> list = namingRuleService.listByCondition(namingRuleListDto);
        TableDataInfo tableDataInfo = getDataTable(list);

        return AjaxResult.success(tableDataInfo);
    }

    @GetMapping()
    public AjaxResult get(@RequestParam("scope") String scope) {
        LambdaQueryWrapper<BuNamingRule> queryWrapper = new LambdaQueryWrapper<BuNamingRule>()
                .eq(BuNamingRule::getScope, scope)
                .eq(BuNamingRule::getStatus, CommonConstants.YES);

        BuNamingRule namingRule = namingRuleService.getOne(queryWrapper, false);
        NamingRuleVo namingRuleVo = new NamingRuleVo();

        if (Objects.nonNull(namingRule)) {
            namingRuleVo.setRuleId(String.valueOf(namingRule.getRuleId()));
            namingRuleVo.setRuleName(namingRule.getRuleName());
            namingRuleVo.setRuleContent(namingRule.getRuleContent());
        }

        return AjaxResult.success(namingRuleVo);
    }

    /**
     * 校验规则名称是否存在
     * @param buNamingRule
     * @return
     */
    private boolean existRuleName(BuNamingRule buNamingRule) {
        Integer count = namingRuleService.lambdaQuery()
                .eq(BuNamingRule::getRuleName, buNamingRule.getRuleName())
                .eq(BuNamingRule::getStatus, CommonConstants.YES)
                .ne(Objects.nonNull(buNamingRule.getRuleId()), BuNamingRule::getRuleId, buNamingRule.getRuleId())
                .count();

        if (count > 0) {
            return true;
        }

        return false;
    }

}
