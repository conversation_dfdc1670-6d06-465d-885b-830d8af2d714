package com.cenker.scrm.service.impl.group;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.mapper.group.GroupCodeInfoMapper;
import com.cenker.scrm.pojo.entity.wechat.group.GroupCodeInfo;
import com.cenker.scrm.pojo.exception.DataNotExistException;
import com.cenker.scrm.pojo.exception.DataNotExistOrDeletedException;
import com.cenker.scrm.pojo.request.data.GroupCodeStatisticQuery;
import com.cenker.scrm.pojo.request.group.GroupCodeRequest;
import com.cenker.scrm.pojo.request.group.GroupCodeStatisticsRequest;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.pojo.vo.group.*;
import com.cenker.scrm.service.group.IGroupCodeInfoService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.StringUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.cenker.scrm.constants.CommonConstants.NUM_0;
import static com.cenker.scrm.constants.CommonConstants.NUM_24;

/**
 * <AUTHOR>
 * @Date 2023/8/22
 * @Description
 */
@Service
public class GroupCodeInfoServiceImpl extends ServiceImpl<GroupCodeInfoMapper, GroupCodeInfo> implements IGroupCodeInfoService {
    @Override
    public GroupCodeInfo saveGroupCodeInfo(GroupCodeRequest request) {
        GroupCodeInfo groupCodeInfo;
        if (ObjectUtil.isNull(request.getCodeId())) {
            groupCodeInfo = GroupCodeInfo.builder()
                    .codeType(request.getCodeType())
                    .codeName(request.getCodeName())
                    .autoCreateRoom(request.getAutoCreateRoom())
                    .roomBaseName(request.getRoomBaseName())
                    .roomBaseId(request.getRoomBaseId())
                    .spareCodeId(request.getSpareCodeId())
                    .spareCodeUrl(request.getSpareCodeUrl())
                    .deptId(request.getDeptId())
                    .build();
            groupCodeInfo.setCreateBy(request.getCreateBy());
            groupCodeInfo.setCorpId(request.getCorpId());
            groupCodeInfo.setTag(JSON.toJSONString(request.getTagList()));
            groupCodeInfo.setUseStatus(CommonConstants.NUM_1);
            save(groupCodeInfo);

            // 用于事件回调判断是哪个活码添加的成员
            this.lambdaUpdate()
                    .set(GroupCodeInfo::getState, groupCodeInfo.getId())
                    .eq(GroupCodeInfo::getId, groupCodeInfo.getId())
                    .update();

            request.setCodeId(groupCodeInfo.getId());
            return groupCodeInfo;
        }
        groupCodeInfo = Optional.ofNullable(getById(request.getCodeId())).orElseThrow(DataNotExistOrDeletedException::new);
        update(Wrappers.lambdaUpdate(GroupCodeInfo.class)
                .set(!groupCodeInfo.getCodeName().equals(request.getCodeName()), GroupCodeInfo::getCodeName, request.getCodeName())
                .set(!groupCodeInfo.getAutoCreateRoom().equals(request.getAutoCreateRoom()), GroupCodeInfo::getAutoCreateRoom, request.getAutoCreateRoom())
                .set(GroupCodeInfo::getRoomBaseName, request.getRoomBaseName())
                .set(GroupCodeInfo::getRoomBaseId, request.getRoomBaseId())
                .set(GroupCodeInfo::getSpareCodeId, request.getSpareCodeId())
                .set(GroupCodeInfo::getSpareCodeUrl, request.getSpareCodeUrl())
                .set(GroupCodeInfo::getUpdateBy, request.getUpdateBy())
                .set(GroupCodeInfo::getTag, JSON.toJSONString(request.getTagList()))
                .eq(GroupCodeInfo::getId, groupCodeInfo.getId()));
        request.setCodeId(groupCodeInfo.getId());
        return groupCodeInfo;
    }

    @Override
    public List<GroupCodeListVO> listGroupCode(GroupCodeRequest request) {
        return baseMapper.listGroupCode(request);
    }

    @Override
    public GroupCodeDetailVO detailGroupCode(GroupCodeRequest request) {
        GroupCodeDetailVO groupCodeDetailVO = baseMapper.detailGroupCode(request);
        Optional.ofNullable(groupCodeDetailVO).orElseThrow(DataNotExistOrDeletedException::new);
        if (StrUtil.isNotBlank(groupCodeDetailVO.getTag())) {
            groupCodeDetailVO.setTagList(JSON.parseArray(groupCodeDetailVO.getTag(), TagVO.class));
        }

        return groupCodeDetailVO;
    }

    @Override
    public GroupCodeDataStatisticsVO getDataStatistics(GroupCodeRequest request) {
        GroupCodeDataStatisticsVO dataStatistics = baseMapper.getDataStatistics(request);
        Optional.ofNullable(dataStatistics).orElseThrow(() -> new DataNotExistException(ErrCodeEnum.DATA_NOT_EXIST.getMessage()));
        dataStatistics.setTotalNetAddGroupCnt(dataStatistics.getTotalAddGroupCnt() - dataStatistics.getTotalQuitGroupCnt());
        return dataStatistics;
    }

    @Override
    public List<GroupCodeDataStatisticsDetailGroupVO> getDataStatisticsDetailByGroup(GroupCodeStatisticsRequest request) {
        return baseMapper.getDataStatisticsDetailByGroup(request);
    }

    @Override
    public List<GroupCodeDataStatisticsDetailDateVO> getDataStatisticsDetailByDate(GroupCodeStatisticsRequest request) {
        Date startTime = null;
        Date endTime = DateUtils.getNowDate();
        if (StringUtils.isNotEmpty(request.getBeginTime()) && StringUtils.isNotEmpty(request.getEndTime())) {
            try {
                startTime = DateUtils.parseDate(request.getBeginTime() + " 00:00:00", DateUtils.YYYY_MM_DD_HH_MM_SS);
                endTime = DateUtils.parseDate(request.getEndTime() + " 00:00:00", DateUtils.YYYY_MM_DD_HH_MM_SS);
            } catch (ParseException e) {
                log.error("错误信息:{}", e);
            }
        } else {
            startTime = DateUtils.addDays(endTime, -30);
        }
        request.setQueryBeginTime(startTime);
        request.setQueryEndTime(endTime);
        List<GroupCodeDataStatisticsDetailDateVO> result = Lists.newArrayList();
        // 入群
        request.setChangeType(1);
        Map<String, List<GroupCodeDataStatisticsDetailDateVO>> addGroupMap = mapDataStatisticsDetail(request, false);
        // 退群
        request.setChangeType(2);
        Map<String, List<GroupCodeDataStatisticsDetailDateVO>> quitGroupMap = mapDataStatisticsDetail(request, false);
        DateUtils.findDates(startTime, endTime).stream()
                .map(d -> DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, d)).forEach(date -> {
            GroupCodeDataStatisticsDetailDateVO detailDateVO = new GroupCodeDataStatisticsDetailDateVO();
            detailDateVO.setStatisticsDate(date);
            detailDateVO.setTotalAddGroupCnt(CollectionUtil.isNotEmpty(addGroupMap.get(date)) ? addGroupMap.get(date).get(NUM_0).getTotalAddGroupCnt() : NUM_0);
            detailDateVO.setTotalQuitGroupCnt(CollectionUtil.isNotEmpty(quitGroupMap.get(date)) ? quitGroupMap.get(date).get(NUM_0).getTotalAddGroupCnt() : NUM_0);
            detailDateVO.setTotalNetAddGroupCnt(detailDateVO.getTotalAddGroupCnt() - detailDateVO.getTotalQuitGroupCnt());
            result.add(detailDateVO);
        });
        return result;
    }

    @Override
    public GroupCodeDataStatisticsVO getDataStatisticsTendency(Date startTime, Date endTime, GroupCodeStatisticQuery statisticQuery) {
        GroupCodeRequest request = GroupCodeRequest.builder().codeId(statisticQuery.getCodeId()).queryBeginTime(startTime).queryEndTime(endTime).build();
        GroupCodeDataStatisticsVO codeDataStatisticsVO = new GroupCodeDataStatisticsVO();
        List<GroupCodeDataStatisticsDailyVO> list = Lists.newArrayList();
        // 扫码、入群、退群
        Map<String, List<GroupCodeDataStatisticsDetailDateVO>> scanCodeMap;
        Map<String, List<GroupCodeDataStatisticsDetailDateVO>> addGroupMap;
        Map<String, List<GroupCodeDataStatisticsDetailDateVO>> quitGroupMap;
        if (statisticQuery.getToday()) {
            scanCodeMap = baseMapper.getDataStatisticsDetail4ScanByHour(request).parallelStream().filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(GroupCodeDataStatisticsDetailDateVO::getStatisticsDate));
            request.setChangeType(1);
            addGroupMap = mapDataStatisticsDetail(request, true);
            request.setChangeType(2);
            quitGroupMap = mapDataStatisticsDetail(request, true);
            for (int i = NUM_0; i < NUM_24; i++) {
                String iStr = String.valueOf(i);
                addEveryTime(list, scanCodeMap, addGroupMap, quitGroupMap, iStr);
            }
            codeDataStatisticsVO.setData(list);
            sumTotal4RangeDate(codeDataStatisticsVO, list);
            return codeDataStatisticsVO;
        }
        GroupCodeStatisticsRequest statisticsRequest = new GroupCodeStatisticsRequest();
        statisticsRequest.setCodeId(statisticQuery.getCodeId());
        statisticsRequest.setQueryBeginTime(startTime);
        statisticsRequest.setQueryEndTime(endTime);
        scanCodeMap = baseMapper.getDataStatisticsDetail4ScanByDate(request).parallelStream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(GroupCodeDataStatisticsDetailDateVO::getStatisticsDate));
        statisticsRequest.setChangeType(1);
        addGroupMap = mapDataStatisticsDetail(statisticsRequest, false);
        statisticsRequest.setChangeType(2);
        quitGroupMap = mapDataStatisticsDetail(statisticsRequest, false);
        DateUtils.findDates(startTime, endTime).stream().map(d -> DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, d))
                .forEach(date -> addEveryTime(list, scanCodeMap, addGroupMap, quitGroupMap, date));
        codeDataStatisticsVO.setData(list);

        // 原本是查询数据库 后面使用代码相加数据 不用去查询数据库
        sumTotal4RangeDate(codeDataStatisticsVO, list);
        return codeDataStatisticsVO;
    }

    private void sumTotal4RangeDate(GroupCodeDataStatisticsVO codeDataStatisticsVO, List<GroupCodeDataStatisticsDailyVO> list) {
        int scanCodeCnt = 0;
        int totalAddGroupCnt = 0;
        int totalQuitGroupCnt = 0;
        for (GroupCodeDataStatisticsDailyVO groupCodeDataStatisticsDailyVO : list) {
            scanCodeCnt += groupCodeDataStatisticsDailyVO.getScanCodeCnt();
            totalAddGroupCnt += groupCodeDataStatisticsDailyVO.getAddGroupCnt();
            totalQuitGroupCnt += groupCodeDataStatisticsDailyVO.getQuitGroupCnt();
        }
        codeDataStatisticsVO.setScanCodeCnt(scanCodeCnt);
        codeDataStatisticsVO.setTotalAddGroupCnt(totalAddGroupCnt);
        codeDataStatisticsVO.setTotalQuitGroupCnt(totalQuitGroupCnt);
        codeDataStatisticsVO.setTotalNetAddGroupCnt(codeDataStatisticsVO.getTotalAddGroupCnt() - codeDataStatisticsVO.getTotalQuitGroupCnt());
    }

    /**
     * 进群群数据返回
     *
     * @param request        请求参数
     * @param statisticsHour 是否按小时统计
     * @return 对应存在的天数/小时数据
     */
    private Map<String, List<GroupCodeDataStatisticsDetailDateVO>> mapDataStatisticsDetail(GroupCodeRequest request, boolean statisticsHour) {
        if (statisticsHour) {
            return baseMapper.getDataStatisticsDetailByHour(request).parallelStream().filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(GroupCodeDataStatisticsDetailDateVO::getStatisticsDate));
        }
        GroupCodeStatisticsRequest statisticsRequest = (GroupCodeStatisticsRequest) request;
        return baseMapper.getDataStatisticsDetailByDate(statisticsRequest).parallelStream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(GroupCodeDataStatisticsDetailDateVO::getStatisticsDate));
    }

    /**
     * 加入每时/天的数据
     *
     * @param list         返回列表
     * @param scanCodeMap  扫码
     * @param addGroupMap  进群
     * @param quitGroupMap 退群
     * @param date         小时或日期
     */
    private void addEveryTime(List<GroupCodeDataStatisticsDailyVO> list, Map<String, List<GroupCodeDataStatisticsDetailDateVO>> scanCodeMap, Map<String, List<GroupCodeDataStatisticsDetailDateVO>> addGroupMap, Map<String, List<GroupCodeDataStatisticsDetailDateVO>> quitGroupMap, String date) {
        GroupCodeDataStatisticsDailyVO detailDateVO = new GroupCodeDataStatisticsDailyVO();
        detailDateVO.setDay(date);
        detailDateVO.setScanCodeCnt(CollectionUtil.isNotEmpty(scanCodeMap.get(date)) ? scanCodeMap.get(date).get(NUM_0).getScanCodeCnt() : NUM_0);
        detailDateVO.setAddGroupCnt(CollectionUtil.isNotEmpty(addGroupMap.get(date)) ? addGroupMap.get(date).get(NUM_0).getTotalAddGroupCnt() : NUM_0);
        detailDateVO.setQuitGroupCnt(CollectionUtil.isNotEmpty(quitGroupMap.get(date)) ? quitGroupMap.get(date).get(NUM_0).getTotalAddGroupCnt() : NUM_0);
        detailDateVO.setNetAddGroupCnt(detailDateVO.getAddGroupCnt() - detailDateVO.getQuitGroupCnt());
        list.add(detailDateVO);
    }


}
