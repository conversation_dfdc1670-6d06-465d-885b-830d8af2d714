package com.cenker.scrm.service.statistic;


import com.cenker.scrm.pojo.entity.statistic.TbStatisticStaff;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticStaffListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.statistic.StatisticGraphVo;
import com.cenker.scrm.pojo.vo.statistic.StatisticStaffSummaryVo;

import java.util.List;

/**
 * 数据统计-员工数据 服务类接口
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
public interface ITbStatisticStaffService {

    /**
     * 保存统计数据
     * @param statDate
     */
    void saveStatisticData(String statDate);

    /**
     * 汇总数据
     * @param query
     * @return
     */
    StatisticStaffSummaryVo summary(StatisticSummaryQuery query);

    /**
     * 图表
     * @param query
     * @return
     */
    List<StatisticGraphVo> graph(StatisticGraphQuery query);

    /**
     * 明细
     * @param query
     * @return
     */
    List<TbStatisticStaff> list(StatisticStaffListQuery query);
}
