package com.cenker.scrm.controller.subscr;

import cn.hutool.core.util.StrUtil;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.subscr.BuSubscriptionMenuSub;
import com.cenker.scrm.pojo.entity.subscr.BuSubscriptionSection;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionQuery;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionSectionDetail;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionSectionVO;
import com.cenker.scrm.service.subscr.IBuSubscriptionMenuSubService;
import com.cenker.scrm.service.subscr.IBuSubscriptionSectionService;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/bu/subscription/section")
@RequiredArgsConstructor
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class BuSubscriptionSectionController extends BaseController {

    private final IBuSubscriptionSectionService buSubscriptionSectionService;
    private final IBuSubscriptionMenuSubService buSubscriptionMenuSubService;
    private final TokenParseUtil tokenService;

    /**
     * 获取订阅栏目列表
     *
     * @param query 查询条件
     * @return 订阅栏目列表
     */
    @GetMapping("/list")
    public TableDataInfo<BuSubscriptionSectionVO> list(@SpringQueryMap BuSubscriptionQuery query) {
        log.info("【查询订阅栏目列表】开始查询，参数：{}", query);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);
        startPage();
        List<BuSubscriptionSectionVO> voList = buSubscriptionSectionService.getSectionList(query);
        log.info("【查询订阅栏目列表】查询完成，结果数量：{}", voList.size());

        return getDataTable(voList);
    }

    /**
     * 新增订阅栏目
     *
     * @param detail 订阅栏目详情
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<Void> add(@RequestBody @Validated(InsertGroup.class) BuSubscriptionSectionDetail detail) {
        log.info("【新增订阅栏目】开始新增，参数：{}", detail);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, detail);
        detail.setCreateBy(loginUser.getUser().getUserId());
        // 验证名称是否重复
        boolean isUnique = buSubscriptionSectionService.isUnique(detail.getSectionName(), detail.getId());
        if (!isUnique) {
            log.warn("【新增订阅栏目】栏目名称已存在，栏目名称：{}", detail.getSectionName());
            return Result.error(500, "栏目名称已存在，请重新输入！");
        }
        log.info("【新增订阅栏目】开始保存栏目信息");
        Result<Void> result = buSubscriptionSectionService.saveSection(detail);
        if (result.isSuccess()) {
            log.info("【新增订阅栏目】新增成功，栏目名称：{}", detail.getSectionName());
        } else {
            log.warn("【新增订阅栏目】新增失败，栏目名称：{}，错误信息：{}", detail.getSectionName(), result.getMsg());
        }
        return result;
    }

    /**
     * 删除订阅栏目
     *
     * @param id 订阅栏目ID
     * @return 操作结果
     */
    @DeleteMapping("/remove")
    public Result<Void> remove(@RequestParam("id") Long id) {
        log.info("【删除订阅栏目】开始删除，id：{}", id);
        // 逻辑删除，将delFlag设置为1
        BuSubscriptionSection section = buSubscriptionSectionService.getById(id);
        if (section == null) {
            log.warn("【删除订阅栏目】订阅栏目不存在，id：{}", id);
            return Result.error(404, "订阅栏目不存在");
        }

        log.info("【删除订阅栏目】开始逻辑删除，栏目名称：{}", section.getSectionName());
        section.setDelFlag(StatusConstants.DEL_FLAG_TRUE_INT); // 设置为已删除
        section.setUpdateTime(new Date());
        section.setUpdateBy(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserId());
        boolean success = buSubscriptionSectionService.updateById(section);

        if (success) {
            log.info("【删除订阅栏目】删除成功，id：{}，栏目名称：{}", id, section.getSectionName());
            // 同时清空订阅菜单中的关联栏目
            buSubscriptionMenuSubService.lambdaUpdate().set(BuSubscriptionMenuSub::getSectionId, null)
                   .eq(BuSubscriptionMenuSub::getSectionId, id).update();
            return Result.success("删除成功");
        } else {
            log.warn("【删除订阅栏目】删除失败，id：{}", id);
            return Result.error(500, "删除失败");
        }
    }

    /**
     * 根据id获取订阅栏目详情
     *
     * @param id 订阅栏目ID
     * @return 订阅栏目详情
     */
    @GetMapping("/detail")
    public Result<BuSubscriptionSectionDetail> detail(@RequestParam("id") String id) {
        log.info("【查询订阅栏目详情】开始查询，id：{}", id);
        if (StrUtil.isBlank(id)) {
            log.warn("【查询订阅栏目详情】参数错误，id为空");
            return Result.error(500, "参数错误");
        }
        Result<BuSubscriptionSectionDetail> result = buSubscriptionSectionService.getSectionDetailResult(id);
        if (result.isSuccess()) {
            log.info("【查询订阅栏目详情】查询成功，id：{}", id);
        } else {
            log.warn("【查询订阅栏目详情】查询失败，id：{}，错误信息：{}", id, result.getMsg());
        }
        return result;
    }

    /**
     * 修改订阅栏目
     *
     * @param detail 订阅栏目详情
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<Void> update(@RequestBody @Validated(UpdateGroup.class) BuSubscriptionSectionDetail detail) {
        log.info("【修改订阅栏目】开始修改，参数：{}", detail);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, detail);
        // 验证名称是否重复
        boolean isUnique = buSubscriptionSectionService.isUnique(detail.getSectionName(), detail.getId());
        if (!isUnique) {
            log.warn("【修改订阅栏目】栏目名称重复，栏目名称：{}", detail.getSectionName());
            return Result.error(500, "栏目名称重复，请重新输入");
        }
        log.info("【修改订阅栏目】开始更新栏目信息，id：{}", detail.getId());
        Result<Void> result = buSubscriptionSectionService.updateSection(detail);
        if (result.isSuccess()) {
            log.info("【修改订阅栏目】修改成功，id：{}，栏目名称：{}", detail.getId(), detail.getSectionName());
        } else {
            log.warn("【修改订阅栏目】修改失败，id：{}，错误信息：{}", detail.getId(), result.getMsg());
        }
        return result;
    }


}