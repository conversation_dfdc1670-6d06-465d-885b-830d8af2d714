package com.cenker.scrm.handler.message;

import com.cenker.scrm.pojo.dto.message.MassMessageChainDTO;

/**
 * <AUTHOR>
 * @Date 2022/6/16
 * @Description 群发消息链处理器
 */
public abstract class MassMessageHandler {

    private MassMessageHandler massMessageHandler;


    protected abstract void doHandler(MassMessageChainDTO messageChainDTO);


    protected abstract boolean before(MassMessageChainDTO messageChainDTO);

    public void addNextHandler(MassMessageHandler massMessageHandler) {
        this.massMessageHandler = massMessageHandler;
    }

    public void handler(MassMessageChainDTO messageChainDTO){
        // 判断是否需要进入当前处理器
        if (before(messageChainDTO)) {
            doHandler(messageChainDTO);
        }
        if (massMessageHandler != null) {
            // 继续下一个处理器
            massMessageHandler.handler(messageChainDTO);
        }else {
            System.out.println("【群发消息】链式调用结束");
        }
    }

}
