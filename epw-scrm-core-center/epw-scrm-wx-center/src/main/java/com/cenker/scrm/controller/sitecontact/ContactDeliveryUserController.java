package com.cenker.scrm.controller.sitecontact;

import cn.hutool.core.util.ObjectUtil;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.ContactArea;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.ContactDeliveryUser;
import com.cenker.scrm.pojo.request.ContactAreaRequest;
import com.cenker.scrm.pojo.request.ContactDeliveryUserRequest;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.sitecontact.AreaTreeSelect;
import com.cenker.scrm.pojo.vo.sitecontact.ContactDeliveryUserEditVO;
import com.cenker.scrm.pojo.vo.sitecontact.ContactDeliveryUserVO;
import com.cenker.scrm.pojo.vo.sitecontact.DeliveryUserInfoVO;
import com.cenker.scrm.service.sitecontact.IContactAreaService;
import com.cenker.scrm.service.sitecontact.IContactDeliveryUserService;
import com.cenker.scrm.service.sitecontact.ITbWxDeliveryContactService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/24
 * @Description 配送员
 */
@RestController
@RequestMapping("/contact/deliveryUser")
@RequiredArgsConstructor
public class ContactDeliveryUserController extends BaseController {

    private final IContactAreaService contactAreaService;
    private final IContactDeliveryUserService contactDeliveryUserService;
    private final ITbWxDeliveryContactService tbWxDeliveryContactService;

    /**
     * 配送员列表城市门店树
     */
    @RequestMapping("/treeAreaList")
    public AjaxResult treeAreaList(@RequestBody ContactAreaRequest contactAreaRequest) {
        List<ContactArea> list = contactAreaService.selectNormalAreaStoreList(contactAreaRequest);
        List<AreaTreeSelect> areaTreeSelects = contactAreaService.buildAreaTreeSelect(list);
        // 排除未在可见范围内的门店
        return AjaxResult.success(areaTreeSelects.stream().filter(a -> "0".equals(a.getId())).collect(Collectors.toList()));

    }

    /**
     * 新增配送员
     */
    @RequestMapping("/add")
    public AjaxResult add(@RequestBody ContactDeliveryUserRequest contactDeliveryUserRequest) {
        contactDeliveryUserService.add(contactDeliveryUserRequest);
        return AjaxResult.success();
    }

    /**
     * 配送员列表
     *
     * @param contactDeliveryUserRequest
     * @return
     */
    @RequestMapping("/list")
    public TableDataInfo list(@RequestBody ContactDeliveryUserRequest contactDeliveryUserRequest) {
        startPage();
        List<ContactDeliveryUserVO> result = contactDeliveryUserService.selectDeliveryUserList(contactDeliveryUserRequest);
        return getDataTable(result);
    }

    /**
     * 修改配送员
     *
     * @param contactDeliveryUserRequest
     * @return
     */
    @RequestMapping("/update")
    public AjaxResult update(@RequestBody ContactDeliveryUserRequest contactDeliveryUserRequest) {
        ContactDeliveryUser contactDeliveryUser = contactDeliveryUserService.edit(contactDeliveryUserRequest);
        if (ObjectUtil.isNotNull(contactDeliveryUser)) {
            // 发生门店变更或者停用配送员需要清空当前的配送员活码
            tbWxDeliveryContactService.removeBySignId(contactDeliveryUser.getSignId(), TypeConstants.SIGN_DELIVERY);
        }
        return AjaxResult.success();
    }

    /**
     * 回显配送员
     */
    @RequestMapping("/getDeliveryUserById")
    public AjaxResult getDeliveryUserById(@RequestBody ContactDeliveryUserRequest contactDeliveryUserRequest) {
        ContactDeliveryUserEditVO contactDeliveryUserEditVO = contactDeliveryUserService.getDeliveryUserById(contactDeliveryUserRequest);
        return AjaxResult.success(contactDeliveryUserEditVO);
    }

    /**
     * 批量迁移门店
     */
    @RequestMapping("/batchTransferStore")
    public AjaxResult batchTransferStore(@RequestBody ContactDeliveryUserRequest contactDeliveryUserRequest) {
        return AjaxResult.success(contactDeliveryUserService.batchTransferStore(contactDeliveryUserRequest));
    }

    /**
     * 删除配送员
     */
    @RequestMapping("/remove")
    public AjaxResult removeDeliveryUser(@RequestBody ContactDeliveryUserRequest contactDeliveryUserRequest) {
        contactDeliveryUserService.removeDeliveryUser(contactDeliveryUserRequest);
        return AjaxResult.success();
    }

    /**
     * 配送员工作台个人信息及查询数据范围
     */
    @RequestMapping("/getDeliveryUserInfo")
    public AjaxResult getDeliveryUserInfo(@RequestBody ContactDeliveryUserRequest contactDeliveryUserRequest) {
        DeliveryUserInfoVO deliveryUserInfoVO = contactDeliveryUserService.getDeliveryUserInfo(contactDeliveryUserRequest);
        return AjaxResult.success(deliveryUserInfoVO);
    }

}
