package com.cenker.scrm.service.impl.statistic;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.constants.StatisticConstants;
import com.cenker.scrm.enums.DataScopeEnum;
import com.cenker.scrm.mapper.statistic.TbStatisticHotWordMapper;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticHotWord;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticHotWordListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.statistic.StatisticGraphVo;
import com.cenker.scrm.pojo.vo.statistic.StatisticHotWordSummaryVo;
import com.cenker.scrm.service.statistic.ITbStatisticBaseService;
import com.cenker.scrm.service.statistic.ITbStatisticHotWordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据统计-热词统计 服务类接口
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Service
@Slf4j
@AllArgsConstructor
public class TbStatisticHotWordServiceImpl extends ServiceImpl<TbStatisticHotWordMapper, TbStatisticHotWord> implements ITbStatisticHotWordService, ITbStatisticBaseService {

    private TbStatisticHotWordMapper tbStatisticHotWordMapper;

    @Override
    public StatisticHotWordSummaryVo summary(StatisticSummaryQuery query) {
        StatisticHotWordSummaryVo vo = tbStatisticHotWordMapper.summary(query);
        // 时间范围搜索，部分指标返回null
        if (StringUtils.isNotEmpty(query.getBeginTime()) && StringUtils.isNotEmpty(query.getEndTime())) {
            if (!query.getBeginTime().equals(query.getEndTime())) {
                vo.setHotWordTotal(null);
            }
        }
        return vo;
    }

    @Override
    public List<StatisticGraphVo> graph(StatisticGraphQuery query) {
        List<StatisticHotWordSummaryVo> lstVo = tbStatisticHotWordMapper.graph(query);
        List<StatisticGraphVo> lstGraphVo = new ArrayList<>();

        switch (query.getType()){
            case StatisticConstants.HOTWORD_HOTWORDTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getHotWordTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.HOTWORD_TRIGGERTOTAL:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getTriggerTotal().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.HOTWORD_STAFFTRIGGERTIMES:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getStaffTriggerTimes().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.HOTWORD_STAFFTRIGGERNUM:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getStaffTriggerNum().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.HOTWORD_CUSTOMERTRIGGERTIMES:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getCustomerTriggerTimes().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            case StatisticConstants.HOTWORD_CUSTOMERTRIGGERNUM:
                lstGraphVo = lstVo.stream().map(summaryVo -> {
                    StatisticGraphVo graphVo = new StatisticGraphVo();
                    graphVo.setDate(summaryVo.getStatisticDate());
                    graphVo.setNum(summaryVo.getCustomerTriggerNum().doubleValue());
                    return graphVo;
                }).collect(Collectors.toList());
                break;
            default:
                break;
        }

        return lstGraphVo;
    }

    @Override
    public List<TbStatisticHotWord> list(StatisticHotWordListQuery query) {
        LambdaQueryWrapper<TbStatisticHotWord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(TbStatisticHotWord::getStatisticDate, query.getBeginTime());
        queryWrapper.le(TbStatisticHotWord::getStatisticDate, query.getEndTime());

        if (StringUtils.isNotBlank(query.getHotWord())) {
            queryWrapper.and(q -> q.like(TbStatisticHotWord::getHotWord, query.getHotWord()).or().like(TbStatisticHotWord::getSynonWords, query.getHotWord()));
        }
        if (StringUtils.isEmpty(query.getOrderByColumn()) ) {
            queryWrapper.orderByDesc(TbStatisticHotWord::getStatisticDate);
        }

        if (query.getDataScope() == null || !DataScopeEnum.ALL.getValue().equals(query.getDataScope())) {
            // 用户有权限查看的部门
            if (CollectionUtil.isNotEmpty(query.getPermissionDeptIds())) {
                queryWrapper.in(TbStatisticHotWord::getDeptId, query.getPermissionDeptIds());
            }

            // 用户为仅本人权限，只能查看热词创建人是当前登录账号且主部门为归属部门的数据，且主部门为归属部门
            if (CollectionUtil.isEmpty(query.getPermissionDeptIds())) {
                queryWrapper.eq(TbStatisticHotWord::getCreateBy, query.getUserId())
                        .eq(TbStatisticHotWord::getDeptId, query.getDeptId());
            }
        }

        List<TbStatisticHotWord> lstHotWord = tbStatisticHotWordMapper.selectList(queryWrapper);
        return lstHotWord;
    }

    @Override
    public void saveStatisticData(String statDate) {
        log.info("【数据统计更新数据】开始统计热词数据, 日期: {}", statDate);

        this.lambdaUpdate().eq(TbStatisticHotWord::getStatisticDate, statDate).remove();
        baseMapper.saveStatisticDateByDay(statDate);

        log.info("【数据统计更新数据】热词数据统计完成, 日期: {}", statDate);
    }

    @Override
    public void synData(Date statDate) {
        this.saveStatisticData(DateUtil.formatDate(statDate));
    }
}
