package com.cenker.scrm.controller.chatarchive;

import com.cenker.scrm.constants.ChatArchiveConstants;
import com.cenker.scrm.enums.ChatArchiveMsgTypeEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.PageDomain;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.base.TableSupport;
import com.cenker.scrm.pojo.request.chatarchive.ChatArchiveQuery;
import com.cenker.scrm.pojo.request.chatarchive.PermitUserQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveConsumeVo;
import com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveMessageVo;
import com.cenker.scrm.pojo.vo.chatarchive.DepartUserTreeVo;
import com.cenker.scrm.service.chatarchive.IWkChatArchiveInfoService;
import com.cenker.scrm.service.chatarchive.IWkChatArchivePermitUserRecordService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

@RestController
@RequestMapping("/chatarchive")
@Slf4j
@RequiredArgsConstructor
public class ChatArchiveController extends BaseController {

    private final IWkChatArchiveInfoService chatArchiveInfoService;

    private final IWkChatArchivePermitUserRecordService permitUserRecordService;

    @GetMapping("/userTreeList")
    public AjaxResult userTreeList(@RequestBody PermitUserQuery query){
        DepartUserTreeVo treeVo = permitUserRecordService.userTreeList(query);
        return AjaxResult.success(treeVo);
    }

    @GetMapping("/user/chatListByPage/{userId}")
    public TableDataInfo userChatList(@PathVariable("userId") String userId, @RequestBody ChatArchiveQuery query){
        query.setChatId(userId);
        List<ChatArchiveConsumeVo> extCustomerVos = Lists.newArrayList();
        if(ChatArchiveConstants.CHAT_TYPE_PRIVATE.equals(query.getChatType())){
            extCustomerVos = chatArchiveInfoService.selectChatExtCustomerListByUser(query);

        }else if(ChatArchiveConstants.CHAT_TYPE_ROOM.equals(query.getChatType())){
            extCustomerVos = chatArchiveInfoService.selectChatRoomList(query);
        }
        if(!CollectionUtils.isEmpty(extCustomerVos)){
            extCustomerVos.sort(new Comparator<ChatArchiveConsumeVo>() {
                @Override
                public int compare(ChatArchiveConsumeVo o1, ChatArchiveConsumeVo o2) {
                    if(o1.getCreateTime() != null && o2.getCreateTime() != null){
                        return o1.getCreateTime().compareTo(o2.getCreateTime());
                    }else{
                        return 0;
                    }
                }
            });
        }

        return getDataTable(extCustomerVos);
    }

    @GetMapping("/extcustomer/extcustomerListByPage")
    public TableDataInfo extCustomerList(@RequestBody ChatArchiveQuery query){
        startPage();
        List<ChatArchiveConsumeVo> extCustomerVos = chatArchiveInfoService.selectExtCustomerListByPage(query);
        return getDataTable(extCustomerVos);
    }

    @GetMapping("/extcustomer/chatListByPage/{extUserId}")
    public TableDataInfo extCustomerChatList(@PathVariable("extUserId") String extUserId, @RequestBody ChatArchiveQuery query){
        startPage();

        query.setChatId(extUserId);

        log.info("检查企业ID值：{}", query.getCorpId());
        List<ChatArchiveConsumeVo> extCustomerVos = Lists.newArrayList();
        if(ChatArchiveConstants.CHAT_TYPE_PRIVATE.equals(query.getChatType())){
            extCustomerVos = chatArchiveInfoService.selectChatUserListByExtCustomer(query);
        }else if(ChatArchiveConstants.CHAT_TYPE_ROOM.equals(query.getChatType())){
            extCustomerVos = chatArchiveInfoService.selectChatRoomList(query);
        }
        return getDataTable(extCustomerVos);
    }

    @GetMapping("/chatMessageListByPage")
    public TableDataInfo chatMessageListByPage(@RequestBody ChatArchiveQuery query){
//        startPage();
        PageDomain pageDomain = TableSupport.buildPageRequest();

        ChatArchiveMsgTypeEnum chatArchiveMsgTypeEnum = ChatArchiveMsgTypeEnum.getChatArchiveMsgTypeEnum(query.getMsgTypeValue());
        if(null != chatArchiveMsgTypeEnum){
            query.setMsgType(chatArchiveMsgTypeEnum.getCode());
        }
        List<ChatArchiveMessageVo> chatArchiveMessageVoList = chatArchiveInfoService.findChatMessageListByPage(query, pageDomain);
//        BigDecimal count = chatArchiveInfoService.selectChatArchiveMessageCount(query);
        TableDataInfo tableDataInfo = getDataTable(chatArchiveMessageVoList);
//        tableDataInfo.setTotal(count.longValue());
        return tableDataInfo;
    }

    @GetMapping("/chatMessageListBySendType")
    public TableDataInfo chatMessageListBySendType(@RequestBody ChatArchiveQuery query){
//        startPage();
        ChatArchiveMsgTypeEnum chatArchiveMsgTypeEnum = ChatArchiveMsgTypeEnum.getChatArchiveMsgTypeEnum(query.getMsgTypeValue());
        if(null != chatArchiveMsgTypeEnum){
            query.setMsgType(chatArchiveMsgTypeEnum.getCode());
        }
        PageDomain pageDomain = TableSupport.buildPageRequest();
        List<ChatArchiveMessageVo> chatArchiveMessageVoList = chatArchiveInfoService.findChatMessageListBySendTypeAndPage(query, pageDomain);
        Long count = chatArchiveInfoService.findChatMessageListBySendTypeCount(query);

        TableDataInfo tableDataInfo = getDataTable(chatArchiveMessageVoList);
        tableDataInfo.setTotal(count);

        return tableDataInfo;
    }

    @GetMapping("/chatTimeList")
    public AjaxResult chatTimeList(@RequestBody ChatArchiveQuery query){
        List<String> chatTimeList = chatArchiveInfoService.selectChatTimeList(query);
        return AjaxResult.success(chatTimeList);
    }


    @GetMapping("/chatMessageListExport")
    public List<ChatArchiveMessageVo> chatMessageListExport(@RequestBody ChatArchiveQuery query){
        ChatArchiveMsgTypeEnum chatArchiveMsgTypeEnum = ChatArchiveMsgTypeEnum.getChatArchiveMsgTypeEnum(query.getMsgTypeValue());
        if(null != chatArchiveMsgTypeEnum){
            query.setMsgType(chatArchiveMsgTypeEnum.getCode());
        }
        List<ChatArchiveMessageVo> chatArchiveMessageVoList = chatArchiveInfoService.findChatMessageListByPage(query, null);
        return chatArchiveMessageVoList;
    }

    @GetMapping("/chatMessageListBySendTypeExport")
    public List<ChatArchiveMessageVo> chatMessageListBySendTypeExport(@RequestBody ChatArchiveQuery query){
        ChatArchiveMsgTypeEnum chatArchiveMsgTypeEnum = ChatArchiveMsgTypeEnum.getChatArchiveMsgTypeEnum(query.getMsgTypeValue());
        if(null != chatArchiveMsgTypeEnum){
            query.setMsgType(chatArchiveMsgTypeEnum.getCode());
        }
        List<ChatArchiveMessageVo> chatArchiveMessageVoList = chatArchiveInfoService.findChatMessageListBySendTypeAndPage(query, null);
        return chatArchiveMessageVoList;
    }
}
