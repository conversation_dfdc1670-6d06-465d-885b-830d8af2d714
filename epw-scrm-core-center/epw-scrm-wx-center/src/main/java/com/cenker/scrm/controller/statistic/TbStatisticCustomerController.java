package com.cenker.scrm.controller.statistic;

import com.cenker.scrm.enums.StatisticTypeEnum;
import com.cenker.scrm.event.StatisticUpdateEvent;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticCustomer;
import com.cenker.scrm.pojo.request.statistic.StatisticCustomerListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.statistic.StatisticCustomerSummaryVo;
import com.cenker.scrm.pojo.vo.statistic.StatisticGraphVo;
import com.cenker.scrm.service.statistic.ITbStatisticCustomerService;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据统计-客户数据 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@RestController
@RequestMapping("/statistic/customer")
@RequiredArgsConstructor
public class TbStatisticCustomerController extends BaseController {

    private final ITbStatisticCustomerService tbStatisticCustomerService;
    private final TokenParseUtil tokenService;
    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * 统计
     */
    @GetMapping("/summary")
    public AjaxResult summary(@RequestBody StatisticSummaryQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        StatisticCustomerSummaryVo vo = tbStatisticCustomerService.summary(query);
        return AjaxResult.success(vo);
    }

    /**
     * 图表
     */
    @GetMapping("/graph")
    public AjaxResult graph(@RequestBody StatisticGraphQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        List<StatisticGraphVo> lstGraph = tbStatisticCustomerService.graph(query);
        return AjaxResult.success(lstGraph);
    }

    /**
     * 明细
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestBody StatisticCustomerListQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        this.startPage();
        List<TbStatisticCustomer> lstCustomer = tbStatisticCustomerService.list(query);
        return getDataTable(lstCustomer);
    }

    /**
     * 导出
     */
    @GetMapping("/export")
    public List<TbStatisticCustomer> export(@RequestBody StatisticCustomerListQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        List<TbStatisticCustomer> lstCustomer = tbStatisticCustomerService.list(query);
        return lstCustomer;
    }

    /**
     * 更新数据
     */
    @RequestMapping("/synData")
    public Result synData(@RequestBody StatisticSummaryQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        StatisticUpdateEvent event = new StatisticUpdateEvent(StatisticTypeEnum.CUSTOMER, query.getBeginTime(), query.getEndTime(), ITbStatisticCustomerService.class, loginUser.getUserId());
        applicationEventPublisher.publishEvent(event);
        return Result.success("更新操作成功，数据同步中");
    }

}
