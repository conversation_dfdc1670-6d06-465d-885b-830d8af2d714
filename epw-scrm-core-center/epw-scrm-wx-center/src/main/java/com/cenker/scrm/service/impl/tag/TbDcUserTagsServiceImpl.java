package com.cenker.scrm.service.impl.tag;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.mapper.tag.TbDcUserTagsMapper;
import com.cenker.scrm.mapper.tag.TbDcUserTagsOperrelMappingMapper;
import com.cenker.scrm.mapper.tag.TbWxMassMessageQueryTypeMapper;
import com.cenker.scrm.mapper.tag.TbWxMassMessageQueryTypeTagMapper;
import com.cenker.scrm.pojo.dto.tag.QueryCustomerTagDTO;
import com.cenker.scrm.pojo.dto.tag.SetTagDTO;
import com.cenker.scrm.pojo.entity.enums.DataFormatType;
import com.cenker.scrm.pojo.entity.enums.QryWhereEnum;
import com.cenker.scrm.pojo.entity.wechat.TStaff;
import com.cenker.scrm.pojo.entity.wechat.TbDcUserTags;
import com.cenker.scrm.pojo.entity.wechat.TbDcUserTagsOperrelMapping;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.entity.wechat.wxmp.TbWxMassMessageQueryType;
import com.cenker.scrm.pojo.entity.wechat.wxmp.TbWxMassMessageQueryTypeTag;
import com.cenker.scrm.pojo.vo.tag.*;
import com.cenker.scrm.service.ISysDictDataService;
import com.cenker.scrm.service.external.ITStaffService;
import com.cenker.scrm.service.external.ITbWxExtCustomerWorkService;
import com.cenker.scrm.service.tag.ITbDcUserTagsService;
import com.cenker.scrm.util.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class TbDcUserTagsServiceImpl extends ServiceImpl<TbDcUserTagsMapper, TbDcUserTags> implements ITbDcUserTagsService {

    private TbDcUserTagsOperrelMappingMapper tbDcUserTagsOperrelMappingMapper;
    private final TbWxMassMessageQueryTypeMapper tbWxMassMessageQueryTypeMapper;
    private final TbWxMassMessageQueryTypeTagMapper tbWxMassMessageQueryTypeTagMapper;
    private final ISysDictDataService dictDataService;
    private final ITbWxExtCustomerWorkService tbWxExtCustomerWorkService;
    private final ITStaffService sensitiveCustInfoService;

    @Override
    public List<TbDcUserTags> selectTbDcUserTagsList(TbDcUserTags tbDcUserTags) {
        List<TbDcUserTags> list = this.lambdaQuery()
                .eq(StrUtil.isNotBlank(tbDcUserTags.getTagName()), TbDcUserTags::getTagName, tbDcUserTags.getTagName())
                .orderByAsc(TbDcUserTags::getDisplayOrder).list();
        return list;
    }

    @Override
    public void setShow(SetTagDTO tbDcUserTags) {
        //只更新展示字段
        TbDcUserTags entity=baseMapper.selectById(tbDcUserTags.getId());
        entity.setIsShow(tbDcUserTags.getIsShow());
        entity.setUpdateTime(new Date());

        LogUtil.logOperDesc("展示/隐藏业务标签：" + entity.getTagName());
        baseMapper.updateById(entity);
    }


    @Override
    public List<CategoryNodeDTO.PrimaryCategoryDTO> getUserTagsByCustomerId(QueryCustomerTagDTO dto) {
        Map<String, Object> tagDataMap = getTagData(dto.getExternalUserId());

        // 过滤是否显示
        Integer isShow = Objects.nonNull(dto.getShowTag()) ? BooleanUtil.toInteger(dto.getShowTag()) : null;

        List<TbDcUserTags> userTags = this.lambdaQuery()
                .eq(Objects.nonNull(isShow), TbDcUserTags::getIsShow, isShow)
                .isNotNull(TbDcUserTags::getReferenceColumn)
                .orderByAsc(TbDcUserTags::getDisplayOrder).list();

        Map<String, List<TbDcUserTags>> primaryGroupedTags = userTags.stream()
                .collect(Collectors.groupingBy(TbDcUserTags::getPrimaryCategory, LinkedHashMap::new, Collectors.toList()));

        return primaryGroupedTags.entrySet().stream()
                .map(entry -> processPrimaryCategory(entry.getKey(), entry.getValue(), tagDataMap))
                .collect(Collectors.toList());
    }

    /**
     * 查询客户业务标签数据
     * @param externalUserId
     * @return
     */
    private Map<String, Object> getTagData(String externalUserId) {
        Map<String, Object> tagDataMap = new HashMap<>();

        TbWxExtCustomer customer = tbWxExtCustomerWorkService.getOne(new LambdaQueryWrapper<TbWxExtCustomer>()
                .eq(TbWxExtCustomer::getExternalUserId, externalUserId), false);

        // 客户不存在或未认证
        if (Objects.isNull(customer) || StrUtil.isBlank(customer.getCustno())) {
            return tagDataMap;
        }

        // 判断是否是内部员工
        Integer count = sensitiveCustInfoService.lambdaQuery()
                .eq(TStaff::getCustno, customer.getCustno()).count();

        if (count > 0) {
            // 内部员工不查询业务标签
            return tagDataMap;
        }

        tagDataMap = Optional.ofNullable(baseMapper.getCustomerTagRecordByCustomerId(externalUserId))
                .orElse(Collections.emptyMap());
        return tagDataMap;
    }

    private CategoryNodeDTO.PrimaryCategoryDTO processPrimaryCategory(String primaryCategory, List<TbDcUserTags> userTags,
                                                                      Map<String, Object> tagDataMap) {
        CategoryNodeDTO.PrimaryCategoryDTO primaryCategoryDTO = new CategoryNodeDTO.PrimaryCategoryDTO();
        primaryCategoryDTO.setCategoryName(primaryCategory);

        Map<String, List<TbDcUserTags>> secondaryGroupedTags = userTags.stream()
                .collect(Collectors.groupingBy(TbDcUserTags::getSecondaryCategory, LinkedHashMap::new, Collectors.toList()));

        primaryCategoryDTO.setSecondaryCategories(secondaryGroupedTags.entrySet().stream()
                .map(entry -> processSecondaryCategory(entry.getKey(), entry.getValue(), tagDataMap))
                .collect(Collectors.toList()));

        return primaryCategoryDTO;
    }

    private CategoryNodeDTO.SecondaryCategoryDTO processSecondaryCategory(String secondaryCategory, List<TbDcUserTags> userTags,
                                                                          Map<String, Object> tagDataMap) {
        CategoryNodeDTO.SecondaryCategoryDTO secondaryCategoryDTO = new CategoryNodeDTO.SecondaryCategoryDTO();

        // 判断一级分类和二级分类是否相同，如果相同表示只有一级分类，没有二级分类
        boolean sameAsPrimary = secondaryCategory.equals(userTags.get(0).getPrimaryCategory());
        if (!sameAsPrimary) {
            secondaryCategoryDTO.setCategoryName(secondaryCategory);
        }

        List<CategoryNodeDTO> tagDTOs = userTags.stream().map(tag -> {
            CategoryNodeDTO tagDTO = new CategoryNodeDTO();
            tagDTO.setTagName(tag.getTagName());
            Object tagValue = tagDataMap.get(tag.getReferenceColumn());
            tagDTO.setTagValue(translateValue(tag, tagValue));
            return tagDTO;
        }).collect(Collectors.toList());

        secondaryCategoryDTO.setTags(tagDTOs);
        return secondaryCategoryDTO;
    }

    /**
     * 业务标签值格式转换
     * @param tag
     * @param value
     * @return
     */
    private String translateValue(TbDcUserTags tag, Object value) {
        if (Objects.isNull(value)) {
            return "--";
        }

        String tagValue = value.toString();

        if (DataFormatType.TEXT.getCode().equals(tag.getDataFormatType())) {
            return tagValue;
        }

        if (DataFormatType.MULTI_SELECT.getCode().equals(tag.getDataFormatType())) {
            String dictLabel =dictDataService.selectDictLabel(tag.getDictType(), tagValue);
            return dictLabel;
        }

        // 数据格式转换逻辑
        String formattedValue = DataFormatType.format(tagValue, tag.getDataFormatType());
        return formattedValue;
    }

    /**
     * 查询树形，业务标签
     * @return
     */
    public List<ExtQryWhereVO.Condition> getTreeUserTags() {
        QueryWrapper<TbDcUserTags> query = new QueryWrapper<TbDcUserTags>()
                .isNotNull("reference_column")
                .orderBy(true, true, "display_order");
          // query.eq("is_show", 1);
        // 获取所有的用户标签，并根据Id排序
        List<TbDcUserTags> userTags = baseMapper.selectList(query);
        // 将用户标签根据一级和二级分类进行分组，并保证分组顺序
        Map<String, List<TbDcUserTags>> primaryGroupedTags = userTags.stream()
                .collect(Collectors.groupingBy(TbDcUserTags::getPrimaryCategory,
                        LinkedHashMap::new, // 保持查询结果的顺序
                        Collectors.toList()));

        List<ExtQryWhereVO.Condition> primaryCategories = new ArrayList<>();
        primaryGroupedTags.forEach((primaryCategory, tagsUnderPrimary) -> {
            ExtQryWhereVO.Condition primaryCategoryDTO = new ExtQryWhereVO.Condition();
            //设置一级分组名称
            primaryCategoryDTO.setLabel(primaryCategory);
            primaryCategoryDTO.setValue(primaryCategory);
            // 再次根据二级分类进行分组
            Map<String, List<TbDcUserTags>> secondaryGroupedTags = tagsUnderPrimary.stream()
                    .collect(Collectors.groupingBy(TbDcUserTags::getSecondaryCategory, LinkedHashMap::new, Collectors.toList()));

            List<ExtSecCategoryNodeVO> secondaryCategories = new ArrayList<>();
            secondaryGroupedTags.forEach((secondaryCategory, tagsUnderSecondary) -> {
                ExtSecCategoryNodeVO secondaryCategoryDTO = new ExtSecCategoryNodeVO();
               //设置二级分组名称
                secondaryCategoryDTO.setValue(secondaryCategory);
                secondaryCategoryDTO.setLabel(secondaryCategory);
                List<ExtUserTagNodeVO> listTags = tagsUnderSecondary.stream().map(tag -> {
                    ExtUserTagNodeVO tagDTO = new ExtUserTagNodeVO();
                    tagDTO.setLabel(tag.getTagName());
                    tagDTO.setDictType(tag.getDictType());
                    tagDTO.setDataFormatType(tag.getDataFormatType());
                    tagDTO.setValue(tag.getReferenceColumn());

                    QueryWrapper<TbDcUserTagsOperrelMapping> queryMapp = new QueryWrapper<TbDcUserTagsOperrelMapping>();
                    queryMapp.orderByAsc("display_order");
                    queryMapp.eq("tag_type_id", tag.getReferenceColumn());
                    // 获取所有的用户标签，并根据Id排序
                    List<TbDcUserTagsOperrelMapping> relList = tbDcUserTagsOperrelMappingMapper.selectList(queryMapp);
                    List<ExtUserTagNodeVO.OperRel> listOper = new ArrayList<>();
                    for (TbDcUserTagsOperrelMapping opermapp:relList) {
                        ExtUserTagNodeVO.OperRel  or = new ExtUserTagNodeVO.OperRel();
                        or.setOperRelId(opermapp.getOperRel());
                        or.setOperRelName(opermapp.getOperRelName());
                        listOper.add(or);
                    }
                    tagDTO.setOperRelList(listOper);
                    return tagDTO;
                }).collect(Collectors.toList());

                secondaryCategoryDTO.setChildren(listTags);
                secondaryCategories.add(secondaryCategoryDTO);
            });

            primaryCategoryDTO.setChildren(secondaryCategories);
            primaryCategories.add(primaryCategoryDTO);
        });

        return primaryCategories;
    }


    /**
     * 1v1群发查询，筛选用户的条件列表
     * @return
     */
    @Override
    public List<ExternalQryVO>  getQueryList(){
        //查询用户筛选条件：第一层
        QueryWrapper<TbWxMassMessageQueryType> query = new QueryWrapper<TbWxMassMessageQueryType>()
                .orderBy(true, true, "display_order");
        query.eq("show_flag", 1);

        // 获取所有的用户标签，并根据Id排序
        List<TbWxMassMessageQueryType> userTags = tbWxMassMessageQueryTypeMapper.selectList(query);
        // 将用户标签根据一级和二级分类进行分组，并保证分组顺序
        Map<String, List<TbWxMassMessageQueryType>> groupMap = userTags.stream()
                .collect(Collectors.groupingBy(TbWxMassMessageQueryType::getGroupName,
                        LinkedHashMap::new, // 保持查询结果的顺序
                        Collectors.toList()));
        List<ExternalQryVO> backArray = new ArrayList<>();
        groupMap.forEach((groupName, secondTypeList) -> {
            ExternalQryVO externalQryVO = new ExternalQryVO();
            //一级分组名称
            externalQryVO.setGroupName(groupName);
            List<ExtQryWhereVO> listTags = secondTypeList.stream().map(tag -> {
                ExtQryWhereVO tagDTO = new ExtQryWhereVO();
                tagDTO.setQryType(tag.getQryType());
                tagDTO.setQryTypeName(tag.getQryTypeName());
                tagDTO.setQryTypeIcon(tag.getQryTypeIcon());
                //查询分组下面的标签列表
                List<ExtQryWhereVO.Condition> conditionList = new ArrayList<>();
                QueryWrapper<TbWxMassMessageQueryTypeTag> qryTypeTag = new QueryWrapper<TbWxMassMessageQueryTypeTag>()
                        .orderBy(true, true, "display_order");
                qryTypeTag.eq("show_flag", 1);

                qryTypeTag.eq("qry_type",tag.getQryType());
                List<TbWxMassMessageQueryTypeTag> listTag = tbWxMassMessageQueryTypeTagMapper.selectList(qryTypeTag);
                for (TbWxMassMessageQueryTypeTag typeTag:listTag) {
                    ExtQryWhereVO.Condition condition = new ExtQryWhereVO.Condition();
                    condition.setDictType(typeTag.getDictType());
                    condition.setValue(typeTag.getQryTagId());
                    condition.setLabel(typeTag.getQryTagName());
                    condition.setDataFormatType(typeTag.getDataFormatType());

                    //查询标签对应的运算符列表
                    QueryWrapper<TbDcUserTagsOperrelMapping> queryMapp = new QueryWrapper<TbDcUserTagsOperrelMapping>();
                    queryMapp.orderByAsc("display_order");
                    queryMapp.eq("tag_type_id", typeTag.getQryTagId());
                    List<TbDcUserTagsOperrelMapping> relList = tbDcUserTagsOperrelMappingMapper.selectList(queryMapp);
                    List<ExtQryWhereVO.OperRel> listOper = new ArrayList<>();
                    for (TbDcUserTagsOperrelMapping opermapp:relList) {
                        ExtQryWhereVO.OperRel  or = new ExtQryWhereVO.OperRel();
                        or.setOperRelId(opermapp.getOperRel());
                        or.setOperRelName(opermapp.getOperRelName());
                        listOper.add(or);
                    }
                    condition.setOperRelList(listOper);

                    List children = new ArrayList();
                    if(QryWhereEnum.QRY_BUSINESS_DATA.getValue().equals(tag.getQryType())){
                        //condition.setChildren(getTreeUserTags());
                        conditionList.addAll(getTreeUserTags());
                    }else{
                        condition.setChildren(children);
                        conditionList.add(condition);
                    }
                    tagDTO.setConditionList(conditionList);

                }

                return tagDTO;
            }).collect(Collectors.toList());
            externalQryVO.setDataList(listTags);
            backArray.add(externalQryVO);
        });
       return backArray;
    }

}
