package com.cenker.scrm.service.subscr;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.entity.subscr.BuSectionStatistics;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.BuSectionStatisticsQuery;
import com.cenker.scrm.pojo.vo.subscr.BuSectionStatisticsVO;

public interface IBuSectionStatisticsService extends IService<BuSectionStatistics> {

    BuSectionStatisticsVO list(BuSectionStatisticsQuery query);

    void saveStatisticData(String statDate);

    Result synData(BuSectionStatisticsQuery query);
}