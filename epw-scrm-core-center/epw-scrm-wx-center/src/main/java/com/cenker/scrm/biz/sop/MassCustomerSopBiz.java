package com.cenker.scrm.biz.sop;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cenker.scrm.biz.ApprovalMsgNotify;
import com.cenker.scrm.biz.customer.CustomerConditionBizHandler;
import com.cenker.scrm.client.system.SysRoleFeign;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.HttpStatus;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.constants.XxlJobContant;
import com.cenker.scrm.enums.ApprovalTypeEnum;
import com.cenker.scrm.enums.DateStyle;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.enums.SeparatorEnum;
import com.cenker.scrm.enums.sop.SopRepeatTypeEnum;
import com.cenker.scrm.handler.JacksonTypeHandler;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.dto.sop.SopMassCustomerContentDTO;
import com.cenker.scrm.pojo.entity.wechat.sop.SopConditionInfo;
import com.cenker.scrm.pojo.entity.wechat.sop.SopContentInfo;
import com.cenker.scrm.pojo.entity.wechat.sop.SopCustomerInfo;
import com.cenker.scrm.pojo.entity.wechat.sop.SopInfo;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.exception.ParameterException;
import com.cenker.scrm.pojo.request.sop.BaseSopRequest;
import com.cenker.scrm.pojo.request.sop.MassCustomerSopRequest;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.sop.ConditionSopListVO;
import com.cenker.scrm.pojo.vo.sop.MassCustomerSopDetailVO;
import com.cenker.scrm.pojo.vo.sop.SopInfoVO;
import com.cenker.scrm.service.approval.IApprovalService;
import com.cenker.scrm.service.corp.ITbWxCorpConfigService;
import com.cenker.scrm.service.journey.ITbWxExtJourneyCustomerStageService;
import com.cenker.scrm.service.journey.ITbWxExtJourneyInfoService;
import com.cenker.scrm.service.message.ITbWxMassMessageInfoService;
import com.cenker.scrm.service.sop.ISopConditionInfoService;
import com.cenker.scrm.service.sop.ISopContentInfoService;
import com.cenker.scrm.service.sop.ISopCustomerInfoService;
import com.cenker.scrm.service.sop.ISopInfoService;
import com.cenker.scrm.util.CronUtils;
import com.cenker.scrm.util.DateUtils;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.entity.XxlJobInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.util.XxlJobUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/8/3
 * @Description 1V1sop
 */
@Component
@Slf4j
public class MassCustomerSopBiz extends BaseSopBiz {
    private final static String TASK_PREFIX = "RW";
    private final static String FIRST_VERSION = "-V0001";

    public MassCustomerSopBiz(ISopInfoService sopInfoService, ISopContentInfoService sopContentInfoService,
                              XxlJobUtil xxlJobUtil, ISopCustomerInfoService sopCustomerInfoService,
                              ITbWxMassMessageInfoService tbWxMassMessageInfoService, ITbWxExtJourneyInfoService tbWxExtJourneyInfoService,
                              ISopConditionInfoService sopConditionInfoService, ITbWxExtJourneyCustomerStageService tbWxExtJourneyCustomerStageService,
                              CustomerConditionBizHandler customerConditionBizHandler,
                              IApprovalService approvalService, ApprovalMsgNotify approvalMsgNotify, RedissonClient redissonClient) {
        super(sopInfoService, sopContentInfoService,
                xxlJobUtil, sopCustomerInfoService,
                tbWxMassMessageInfoService, tbWxExtJourneyInfoService,
                sopConditionInfoService, tbWxExtJourneyCustomerStageService,
                customerConditionBizHandler, approvalService, approvalMsgNotify, redissonClient);
    }

    @Override
    @SuppressWarnings(value = "all")
    public void saveSopContentInfo(BaseSopRequest baseSopRequest, boolean update) {
        MassCustomerSopRequest request = (MassCustomerSopRequest) baseSopRequest;
        ISopContentInfoService sopContentInfoService = getSopContentInfoService();
        List<SopMassCustomerContentDTO> sopContentList = request.getSopContentList();
        if (CollectionUtils.isEmpty(sopContentList)) {
            throw new ParameterException(ErrCodeEnum.SOP_CONTENT_EMPTY_ERROR);
        }
        String nowDate = DateUtils.getDate(DateUtils.getNowDate(), DateStyle.YYYYMMDD);
        Long sopId = request.getSopId();
        // 查看今天版本创建号是多少（被删除的也算，防止冲突）  如有RW202307120002的版本创建号就是2
        int todaySignCount = sopContentInfoService.getTodaySignCount(TASK_PREFIX + nowDate, sopId);
        log.info("【1V1sop】当前sop:【{}】当前日期【{}】已创建{}条内容序列", request.getSopName(), sopId, todaySignCount);
        List<SopContentInfo> saveList = Lists.newArrayList();
        if (!update) {
            int j = 0;
            for (SopMassCustomerContentDTO sopMassCustomerContentDTO : sopContentList) {
                log.info("【1V1sop】新增内容序列：{}", sopMassCustomerContentDTO);
                addContentTask(request, nowDate, saveList, todaySignCount, j, sopMassCustomerContentDTO);
                j++;
            }
            if (CollectionUtils.isNotEmpty(saveList)) {
                sopContentInfoService.saveBatch(saveList);
            }
            return;
        }
        // 查询出所有的内容序列 更新对比
        List<SopContentInfo> sopContentInfoList = Optional.ofNullable(getSopContentInfoService()
                .list(Wrappers.lambdaQuery(SopContentInfo.class).eq(SopContentInfo::getSopId, sopId))).orElse(Lists.newArrayList());
        Map<Long, SopContentInfo> sopContentInfoMap = sopContentInfoList.stream().collect(Collectors.toMap(SopContentInfo::getId, Function.identity()));
        List<Long> contentIdList = sopContentList.stream().map(SopMassCustomerContentDTO::getContentId).collect(Collectors.toList());
        List<Long> updateIdList = Lists.newArrayList();
        List<Long> removeIdList = Lists.newArrayList();
        for (int i = 0; i < sopContentList.size(); i++) {
            SopMassCustomerContentDTO sopMassCustomerContentDTO = sopContentList.get(i);
            // 更新逻辑 识别有没有什么产生了变更 如果没有则不更新
            SopContentInfo contentInfo = sopContentInfoMap.get(sopMassCustomerContentDTO.getContentId());
            if (ObjectUtil.isNull(contentInfo)) {
                // 这个内容序列可能被删除了 视为新增
                addContentTask(request, nowDate, saveList, todaySignCount, i, sopMassCustomerContentDTO);
                continue;
            }
            SopMassCustomerContentDTO sopConditionContentFromDatabaseDTO = buildSopConditionContentFromDatabaseDTO(contentInfo);
            // 使用两个json直接对比 来判断是否更新 排除影响项 这里使用JacksonTypeHandler是为了json保持一致
            filterInfluenceFactor(sopConditionContentFromDatabaseDTO, sopMassCustomerContentDTO);
            String sourceContentDTO = JacksonTypeHandler.toJsonString(sopConditionContentFromDatabaseDTO);
            String targetContentDTO = JacksonTypeHandler.toJsonString(sopMassCustomerContentDTO);
            if (!sourceContentDTO.equals(targetContentDTO)) {
                updateContentInfo(request, saveList, i, sopMassCustomerContentDTO, contentInfo, sopConditionContentFromDatabaseDTO);
                // 将老版本设置为已删除并运行状态
                updateIdList.add(sopMassCustomerContentDTO.getContentId());
            }
        }

        if (CollectionUtils.isNotEmpty(contentIdList)) {
            if (CollectionUtils.isNotEmpty(updateIdList)) {
                getSopContentInfoService().update(Wrappers.lambdaUpdate(SopContentInfo.class)
                        .set(SopContentInfo::getDeleted, true)
                        .set(SopContentInfo::getAliveVersion, true)
                        .in(SopContentInfo::getId, updateIdList)
                );
            }
        }

        // 对比 如果前端没有数据库里的内容id 则视为删除
        List<Long> removeContentIdList = sopContentInfoList.stream().filter(s -> !contentIdList.contains(s.getId())).map(SopContentInfo::getId).collect(Collectors.toList());
        log.info("【1V1sop】当前sop:【{}】检测到内容序列删除内容序列id:{}", request.getSopName(), removeIdList);
        removeIdList.addAll(removeContentIdList);
        if (CollectionUtils.isNotEmpty(removeIdList)) {
            getSopContentInfoService().update(Wrappers.lambdaUpdate(SopContentInfo.class)
                    .set(SopContentInfo::getAliveVersion, false)
                    .set(SopContentInfo::getDeleted, true)
                    .in(SopContentInfo::getId, removeIdList)
            );
        }

        if (CollectionUtils.isNotEmpty(saveList)) {
            sopContentInfoService.saveBatch(saveList);
        }
    }

    private void updateContentInfo(MassCustomerSopRequest request, List<SopContentInfo> saveList, int i, SopMassCustomerContentDTO sopMassCustomerContentDTO, SopContentInfo contentInfo, SopMassCustomerContentDTO sopConditionContentFromDatabaseDTO) {
        String contentVersion = contentInfo.getContentVersion();
        log.info("【1V1sop】sop【{}】发现内容序列id:{}发生变更，当前版本号【{}】", request.getSopName(), sopConditionContentFromDatabaseDTO.getContentId(), contentVersion);
        String[] versionSplit = contentVersion.split("-V");
        int newVersion = Integer.valueOf(versionSplit[1]) + 1;
        StringBuilder contentSignBuilder = new StringBuilder(versionSplit[0]);
        SopContentInfo sopContentInfo = SopContentInfo.builder()
                .sopId(request.getSopId())
                .contentSign(contentSignBuilder.toString())
                .contentVersion(contentSignBuilder.append("-V").append(String.format("%04d", newVersion)).toString())
                .contentSort(i)
                .repeatType(sopMassCustomerContentDTO.getRepeatType())
                .repeatExpire(sopMassCustomerContentDTO.getRepeatExpire())
                .repeatUnit(SopRepeatTypeEnum.getSopRepeatTypeEnum(sopMassCustomerContentDTO.getRepeatType()).getRepeatUnit())
                .delAttributeSopMinute(sopMassCustomerContentDTO.getDelAttributeSopMinute())
                .stopTaskHour(sopMassCustomerContentDTO.getStopTaskHour())
                .contentText(sopMassCustomerContentDTO.getContentText())
                .startTime(sopMassCustomerContentDTO.getStartTime())
                .endTime(sopMassCustomerContentDTO.getEndTime())
                .contentAttachment(sopMassCustomerContentDTO.getAttachments())
                .corpId(request.getCorpId())
                .createBy(request.getCreateBy())
                .build();
        saveList.add(sopContentInfo);
    }

    private void filterInfluenceFactor(SopMassCustomerContentDTO sopConditionContentFromDatabaseDTO, SopMassCustomerContentDTO sopMassCustomerContentDTO) {
        // ps:这里会有一种情况数据库内容序列版本已经更新了 这样这里就会重复覆盖版本
        sopMassCustomerContentDTO.setContentVersion(null);
        sopConditionContentFromDatabaseDTO.setJobId(null);
        sopMassCustomerContentDTO.setJobId(null);
    }

    private SopMassCustomerContentDTO buildSopConditionContentFromDatabaseDTO(SopContentInfo contentInfo) {
        SopMassCustomerContentDTO sopConditionContentFromDatabaseDTO = new SopMassCustomerContentDTO();
        sopConditionContentFromDatabaseDTO.setContentId(contentInfo.getId());
        sopConditionContentFromDatabaseDTO.setContentVersion(null);
        sopConditionContentFromDatabaseDTO.setStartTime(contentInfo.getStartTime());
        sopConditionContentFromDatabaseDTO.setDelAttributeSopMinute(contentInfo.getDelAttributeSopMinute());
        sopConditionContentFromDatabaseDTO.setStopTaskHour(contentInfo.getStopTaskHour());
        sopConditionContentFromDatabaseDTO.setContentSort(contentInfo.getContentSort());
        sopConditionContentFromDatabaseDTO.setAttachments(contentInfo.getContentAttachment());
        sopConditionContentFromDatabaseDTO.setContentText(contentInfo.getContentText());
        return sopConditionContentFromDatabaseDTO;
    }

    private void addContentTask(MassCustomerSopRequest request, String nowDate, List<SopContentInfo> saveList, int todaySignCount, int j, SopMassCustomerContentDTO sopMassCustomerContentDTO) {
        // 生成任务标识（多版本唯一标识）以及内容版本号 格式：RWYYYYMMDD0001-V001 格式 新增时默认第一个版本
        StringBuilder contentSignBuilder = new StringBuilder(TASK_PREFIX).append(nowDate).append(String.format("%04d", todaySignCount + j + 1));
        SopRepeatTypeEnum sopRepeatTypeEnum = SopRepeatTypeEnum.getSopRepeatTypeEnum(sopMassCustomerContentDTO.getRepeatType());
        if (ObjectUtil.isNull(sopRepeatTypeEnum)) {
            throw new CustomException(ErrCodeEnum.SOP_REPEAT_EXPIRE_NOT_SELECT_ERROR);
        }
        SopContentInfo sopContentInfo = SopContentInfo.builder()
                .sopId(request.getSopId())
                .contentSign(contentSignBuilder.toString())
                .contentVersion(contentSignBuilder.append(FIRST_VERSION).toString())
                .aliveVersion(true)
                .contentSort(ObjectUtil.isNotEmpty(sopMassCustomerContentDTO.getContentSort()) ? sopMassCustomerContentDTO.getContentSort() : j)
                .repeatType(sopMassCustomerContentDTO.getRepeatType())
                .repeatExpire(sopMassCustomerContentDTO.getRepeatExpire())
                .repeatUnit(sopRepeatTypeEnum.getRepeatUnit())
                .delAttributeSopMinute(sopMassCustomerContentDTO.getDelAttributeSopMinute())
                .stopTaskHour(sopMassCustomerContentDTO.getStopTaskHour())
                .contentText(sopMassCustomerContentDTO.getContentText())
                .startTime(sopMassCustomerContentDTO.getStartTime())
                .endTime(sopMassCustomerContentDTO.getEndTime())
                .contentAttachment(sopMassCustomerContentDTO.getAttachments())
                .corpId(request.getCorpId())
                .createBy(request.getCreateBy())
                .build();
        saveList.add(sopContentInfo);
    }

    @Override
    public void createOrStartContentTask(SopInfoVO sopInfoVO) {
        MassCustomerSopDetailVO detailVO = (MassCustomerSopDetailVO) sopInfoVO;
        List<SopMassCustomerContentDTO> sopContentList = detailVO.getSopContentList();
        if (CollectionUtils.isEmpty(sopContentList)) {
            throw new ParameterException(ErrCodeEnum.SOP_CONTENT_EMPTY_ERROR);
        }
        Long sopId = detailVO.getSopId();
        XxlJobUtil xxlJobUtil = getXxlJobUtil();
        for (SopMassCustomerContentDTO sopContentDTO : sopContentList) {
            Long jobId = sopContentDTO.getJobId();
            String contentVersion = sopContentDTO.getContentVersion();
            if (ObjectUtil.isNull(jobId)) {
                addJob4Content(detailVO, sopId, xxlJobUtil, sopContentDTO);
                continue;
            }
            // 如果版本号没变就启动任务 否则就新增任务 返回200默认有值
            ReturnT<XxlJobInfo> jobDetailResult = xxlJobUtil.getJobById(Integer.valueOf(String.valueOf(jobId)));
            if (ObjectUtil.isNull(jobDetailResult) || jobDetailResult.getCode() == HttpStatus.ERROR) {
                log.error("【1V1sop】查询定时任务失败，调用定时任务返回：{}，sop名：【{}】，sopId:{}，内容id：{}，版本号：{}，任务jobId：{}",
                        jobDetailResult, detailVO.getSopName(), sopId, sopContentDTO.getContentId(), contentVersion, jobId);
                throw new CustomException(ErrCodeEnum.SOP_JOB_QUERY_ERROR);
            }
            XxlJobInfo jobInfo = jobDetailResult.getContent();
            String stopContentVersion = jobInfo.getExecutorParam().split(SeparatorEnum.UNDERLINE.getSeparator())[2];
            if (contentVersion.equals(stopContentVersion)) {
                // 直接启动
                ReturnT<String> startResult = xxlJobUtil.startJobById(Integer.valueOf(String.valueOf(jobId)));
                if (ObjectUtil.isNull(startResult) || startResult.getCode() == HttpStatus.ERROR) {
                    log.error("【1V1sop】重新启动定时任务失败，调用定时任务返回：{}，sop名：【{}】，sopId:{}，内容id：{}，版本号：{}，任务jobId：{}",
                            startResult, detailVO.getSopName(), sopId, sopContentDTO.getContentId(), contentVersion, jobId);
                    // throw new CustomException(ErrCodeEnum.SOP_START_ERROR);
                    // 如果定期的话存在时间过去了 报非法的cron参数
                }
                continue;
            }
            // 重新创建并启动
            addJob4Content(detailVO, sopId, xxlJobUtil, sopContentDTO);
        }
    }

    /**
     * 添加定时任务
     *
     * @param detailVO      sop数据库
     * @param sopId         sopId
     * @param xxlJobUtil    定时任务工具
     * @param sopContentDTO 内容数据库
     */
    private void addJob4Content(MassCustomerSopDetailVO detailVO, Long sopId, XxlJobUtil xxlJobUtil, SopMassCustomerContentDTO sopContentDTO) {
        Long contentId = sopContentDTO.getContentId();
        String contentVersion = sopContentDTO.getContentVersion();
        Long jobId;
        String cron = "";
        Integer repeatType = sopContentDTO.getRepeatType();
        Date startTime = sopContentDTO.getStartTime();
        Date nowDate = DateUtils.getNowDate();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        // 获取cron表达式 todo cron表达式无法直接指定每两周 暂时不做
        if (SopRepeatTypeEnum.NEVER_REPEAT.getRepeatType().equals(repeatType)) {
            if (nowDate.after(startTime)) {
                // 定时时间已经过去的时候 此时创建定时任务没有意义 并且在启动定时任务时会报错所以这里直接拦截此类定时任务的创建
                return;
            }
            cron = CronUtils.getCron(sopContentDTO.getStartTime(), CronUtils.DATE_FORMAT_ASSIGN_DAY);
        } else if (SopRepeatTypeEnum.EVERY_DAY.getRepeatType().equals(repeatType)) {
            cron = CronUtils.getCron(sopContentDTO.getStartTime(), CronUtils.DATE_FORMAT_EVERYDAY);
        } else if (SopRepeatTypeEnum.EVERY_WEEK.getRepeatType().equals(repeatType)) {
            // 获取开始时间是星期几 每周就是从这个时间开始
            cron = CronUtils.getCron(sopContentDTO.getStartTime(), String.format(CronUtils.DATE_FORMAT_WEEK, calendar.get(Calendar.DAY_OF_WEEK)));
        } else if (SopRepeatTypeEnum.EVERY_MONTH.getRepeatType().equals(repeatType)) {
            // 获取开始时间是几号 每月就是从这个时间开始
            cron = CronUtils.getCron(sopContentDTO.getStartTime(), CronUtils.DATEFORMAT_MONTH);
        } else if (SopRepeatTypeEnum.EVERY_YEAR.getRepeatType().equals(repeatType)) {
            // 获取开始时间是几月几号 每年就是从这个时间开始
            cron = CronUtils.getCron(sopContentDTO.getStartTime(), CronUtils.DATEFORMAT_YEAR);
        }else if (SopRepeatTypeEnum.WORK_DAY.getRepeatType().equals(repeatType)) {
            cron = CronUtils.getCron(sopContentDTO.getStartTime(), CronUtils.DATE_FORMAT_WORK).replace("%%","MON-FRI");
        }
        log.info("【1V1sop】sop名：【{}】，sopId:{}，任务id：{}，重复类型，{}，版本号：{}，生成cron表达式：{}，下次执行时间：{}",
                detailVO.getSopName(), sopId, contentId, SopRepeatTypeEnum.getSopRepeatTypeEnum(repeatType).getRepeatTypeDesc(), contentVersion, cron, DateUtils.getDate(CronUtils.getNextExecution(cron), DateStyle.YYYY_MM_DD_HH_MM_SS));
        // 加入定时任务 任务描述：标识sopId + 任务id + 任务版本号
        String sign = sopId + SeparatorEnum.UNDERLINE.getSeparator() + contentId + SeparatorEnum.UNDERLINE.getSeparator() + contentVersion;
        ReturnT<String> addResult = xxlJobUtil.addJob("【1V1sop】" + sign, cron, sign, XxlJobContant.MASS_CUSTOMER_SOP_TASK_HANDLER);
        if (ObjectUtil.isNull(addResult) || addResult.getCode() == HttpStatus.ERROR) {
            log.error("【1V1sop】建立定时任务失败，调用定时任务返回：{}，sop名：【{}】，sopId:{}，任务id：{}，版本号：{}",
                    addResult, detailVO.getSopName(), sopId, contentId, contentVersion);
            throw new CustomException(ErrCodeEnum.SOP_START_ERROR);
        }
        // 启动该定时任务
        jobId = Long.valueOf(addResult.getContent());
        ReturnT<String> startResult = xxlJobUtil.startJobById(Integer.valueOf(addResult.getContent()));
        if (ObjectUtil.isNull(startResult) || startResult.getCode() == HttpStatus.ERROR) {
            log.error("【1V1sop】启动定时任务失败，调用定时任务返回：{}，sop名：【{}】，sopId:{}，内容id：{}，版本号：{}，任务jobId：{}",
                    startResult, detailVO.getSopName(), sopId, contentId, contentVersion, jobId);
            throw new CustomException(ErrCodeEnum.SOP_START_ERROR);
        }
        // 更新jobId
        sopContentDTO.setJobId(jobId);
        getSopContentInfoService().update(Wrappers.lambdaUpdate(SopContentInfo.class)
                .set(SopContentInfo::getJobId, jobId).eq(SopContentInfo::getId, sopContentDTO.getContentId()));
    }

    @Override
    public void stopContentTask(SopInfoVO sopInfoVO) {
        MassCustomerSopDetailVO detailVO = (MassCustomerSopDetailVO) sopInfoVO;
        List<SopMassCustomerContentDTO> sopContentList = detailVO.getSopContentList();
        if (CollectionUtils.isEmpty(sopContentList)) {
            throw new ParameterException(ErrCodeEnum.SOP_CONTENT_EMPTY_ERROR);
        }
        for (SopMassCustomerContentDTO sopMassCustomerContentDTO : sopContentList) {
            // 停用时默认所有的内容定时任务都是启用状态
            Long jobId = sopMassCustomerContentDTO.getJobId();
            if (ObjectUtil.isNotNull(jobId)) {
                ReturnT<String> stopResult = getXxlJobUtil().stopJobById(Integer.valueOf(String.valueOf(jobId)));
                if (ObjectUtil.isNull(stopResult) || stopResult.getCode() == HttpStatus.ERROR) {
                    log.error("【1V1sop】停用定时任务失败，调用定时任务返回：{}，sop名：【{}】，sopId:{}，内容id：{}，版本号：{}，任务jobId：{}",
                            stopResult, detailVO.getSopName(), detailVO.getSopId(), sopMassCustomerContentDTO.getContentId(), sopMassCustomerContentDTO.getContentVersion(), jobId);
                    throw new CustomException(ErrCodeEnum.SOP_START_ERROR);
                }
                continue;
            }
            log.error("【1V1sop】停用定时任务未找到jobId【{}】，其内容id为【{}】", jobId, sopMassCustomerContentDTO.getContentId());
        }
    }

    @Override
    public void createOrStartSopScanTask(SopInfoVO sopInfoVO) {

    }

    @Override
    public void stopSopScanTask(SopInfoVO sopInfoVO) {

    }

    @Override
    public void saveSopConditionInfo(BaseSopRequest baseSopRequest) {
        log.info("【1V1SOP】开始保存sop条件信息，请求原文体：{}", baseSopRequest);
        Long sopId = baseSopRequest.getSopId();
        MassCustomerSopRequest request = (MassCustomerSopRequest) baseSopRequest;
        SopConditionInfo sopConditionInfo = SopConditionInfo.builder().sopId(sopId)
                .sendCondition(request.getSendCondition())
                .viewSendCondition(request.getSendCondition())
                .corpId(baseSopRequest.getCorpId()).createBy(baseSopRequest.getCreateBy()).build();
        getSopConditionInfoService().save(sopConditionInfo);
    }

    @Override
    public void createOrStartSop(SopInfoVO sopInfoVO, BaseSopRequest baseSopRequest) {

    }

    public List<ConditionSopListVO> listMassCustomerSop(MassCustomerSopRequest request) {
        return getSopInfoService().listConditionSop(request);
    }

    public MassCustomerSopDetailVO detailMassCustomerSop(MassCustomerSopRequest request) {
        return getSopInfoService().detailMassCustomerSop(request);
    }

    @Override
    public void runConditionContent(SopContentInfo contentInfo) {
        Date startTime = contentInfo.getStartTime();
        Date nowDate = DateUtils.getNowDate();
        Integer repeatExpire = contentInfo.getRepeatExpire();
        if (nowDate.before(startTime)) {
            log.info("【1V1sop】执行sopId:【{}】,开始执行时间:【{}】,contentId:【{}】，未到开始执行时间不做处理",
                    contentInfo.getSopId(), DateUtils.getDate(startTime, DateStyle.YYYY_MM_DD_HH_MM_SS), contentInfo.getId());
            return;
        }
        if (TypeConstants.SOP_REPEAT_EXPIRE_TYPE_1.equals(repeatExpire) && nowDate.after(contentInfo.getEndTime())) {
            log.info("【1V1sop】执行sopId:【{}】,结束执行时间:【{}】,contentId:【{}】，已到期不再处理",
                    contentInfo.getSopId(), DateUtils.getDate(contentInfo.getEndTime(), DateStyle.YYYY_MM_DD_HH_MM_SS), contentInfo.getId());
            return;
        }
        // 获取当前sop
        MassCustomerSopRequest request = new MassCustomerSopRequest();
        request.setSopId(contentInfo.getSopId());
        MassCustomerSopDetailVO massCustomerSopDetailVO = detailMassCustomerSop(request);
        // 筛选人群
        List<SopCustomerInfo> sopCustomerInfoList = getSopCustomerInfoService()
                .list(Wrappers.lambdaQuery(SopCustomerInfo.class)
                        .eq(SopCustomerInfo::getSopId, contentInfo.getSopId())
                        .eq(SopCustomerInfo::getAccord, true));
        if (CollectionUtils.isEmpty(sopCustomerInfoList)) {
            log.info("【1V1sop】执行sopId:【{}】,sopName:【{}】,contentId:【{}】，未找到匹配数据", contentInfo.getSopId(), massCustomerSopDetailVO.getSopName(), contentInfo.getId());
            return;
        }
        // todo 1V1有选择员工发送的逻辑
        List<String> externalUserIdList = sopCustomerInfoList.stream().map(SopCustomerInfo::getExternalUserId).collect(Collectors.toList());
        toMessageMass(contentInfo, externalUserIdList, false);
    }

    @Override
    protected void createOrStartSopAfterApproval(String id) {
        MassCustomerSopRequest request = new MassCustomerSopRequest();
        request.setSopId(Long.valueOf(id));
        MassCustomerSopDetailVO massCustomerSopDetailVO = detailMassCustomerSop(request);
        request.setAlive(massCustomerSopDetailVO.getAlive());
        log.info("【1V1sop】开始创建或启动sopId:【{}】,sopName:【{}】", massCustomerSopDetailVO.getSopId(), massCustomerSopDetailVO.getSopName());
        createOrStartContentTask(massCustomerSopDetailVO);
        // 修改sopInfo的状态
        log.info("【1V1sop】修改sopId:【{}】,sopName:【{}】的状态为已发布", massCustomerSopDetailVO.getSopId(), massCustomerSopDetailVO.getSopName());
        changeSopStatus(request);
    }

    public void startMassCustomerSopScan(MassCustomerSopRequest request) {
//        SopMassSelectDTO sopMassSelectCondition = request.getSopMassSelectCondition();
        ISopCustomerInfoService sopCustomerInfoService = getSopCustomerInfoService();
//        SopMassCustomerChainDTO sopMassCustomerChainDTO = sopCustomerInfoService.getMessagePredictedNum(sopMassSelectCondition);
//        if (sopMassCustomerChainDTO.getMessagePredictedNum() == 0 || CollectionUtils.isEmpty(sopMassCustomerChainDTO.getCustomerList())) {
//            throw new CustomException(ErrCodeEnum.SOP_MASS_NOT_CUSTOMER_ERROR);
//        }
        List<String> externalUserIdList = getCustomerConditionBizHandler().qrySendMsgCustUserList(request.getCorpId(), request.getSendCondition());
        if (CollectionUtil.isEmpty(externalUserIdList)) {
            throw new CustomException(ErrCodeEnum.SOP_MASS_NOT_CUSTOMER_ERROR);
        }

        // 保存客户信息
        Date nowDate = DateUtils.getNowDate();
        List<SopCustomerInfo> saveList = Lists.newLinkedList();
        for (String externalUserId : externalUserIdList) {
            SopCustomerInfo sopCustomerInfo = SopCustomerInfo.builder()
                    .sopId(request.getSopId())
                    .externalUserId(externalUserId)
                    .accord(true)
                    .remark("初次添加条件筛选加入")
                    .joinTime(nowDate)
                    .corpId(CorpInfoProperties.getCorpId())
                    .build();
            saveList.add(sopCustomerInfo);
        }
        sopCustomerInfoService.saveBatch(saveList);
    }

    public Result approve(ApprovalVO approvalVO, LoginUser loginUser) {
        String type = ApprovalTypeEnum.ONESEPARATEONE.getType();
        return approveCommon(approvalVO, loginUser, type);
    }
}
