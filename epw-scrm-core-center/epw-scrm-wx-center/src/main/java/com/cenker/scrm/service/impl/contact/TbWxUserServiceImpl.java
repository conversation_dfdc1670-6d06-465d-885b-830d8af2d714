package com.cenker.scrm.service.impl.contact;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.annotation.RedisLockAspect;
import com.cenker.scrm.client.system.SysUserFeign;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.DefaultConstants;
import com.cenker.scrm.constants.EventConstants;
import com.cenker.scrm.enums.UserStatus;
import com.cenker.scrm.mapper.contact.TbWxUserMapper;
import com.cenker.scrm.mapper.group.TbWxCustomerGroupMapper;
import com.cenker.scrm.mapper.system.SysUserWxMapper;
import com.cenker.scrm.model.login.MobileUser;
import com.cenker.scrm.pojo.dto.WechatUser;
import com.cenker.scrm.pojo.dto.WxLeaveUserDto;
import com.cenker.scrm.pojo.dto.kf.FeedbackContentDTO;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.*;
import com.cenker.scrm.pojo.vo.TbWxUserCardVO;
import com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO;
import com.cenker.scrm.pojo.vo.contact.MemberVo;
import com.cenker.scrm.pojo.vo.contact.WxLeaveUserVO;
import com.cenker.scrm.pojo.vo.external.CorpRealTimeDataVO;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.cenker.scrm.pojo.vo.workbench.WorkbenchLoginInfo;
import com.cenker.scrm.service.IWorkCorpCpService;
import com.cenker.scrm.service.contact.ITbWxContactRelService;
import com.cenker.scrm.service.contact.ITbWxDepartmentService;
import com.cenker.scrm.service.contact.ITbWxUserRelService;
import com.cenker.scrm.service.contact.ITbWxUserService;
import com.cenker.scrm.service.corp.IAppWxCorpPermanentTokenService;
import com.cenker.scrm.service.corp.ITbWxCorpConfigService;
import com.cenker.scrm.service.corp.ITbWxCorpDimissionAllocateService;
import com.cenker.scrm.service.external.TbWxExtFollowUserService;
import com.cenker.scrm.util.*;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.bean.WxCpAgent;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.tpcp.impl.WxTpCpServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 通讯录相关客户Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Service
@Slf4j
public class TbWxUserServiceImpl extends ServiceImpl<TbWxUserMapper, TbWxUser> implements ITbWxUserService {

    public static final int SIZE = 100;
    @Autowired
    private TbWxExtFollowUserService tbWxExtFollowUserService;
    @Autowired
    private TbWxCustomerGroupMapper tbWxCustomerGroupMapper;
    @Autowired
    private ITbWxCorpDimissionAllocateService tbWxCorpDimissionAllocateService;
    @Autowired
    private SysUserFeign sysUserFeign;
    @Autowired
    private ITbWxContactRelService tbWxContactRelService;
    @Autowired
    private ITbWxUserRelService tbWxUserRelService;
    @Autowired(required = false)
    private SysUserWxMapper userMapper;


    /**
     * 企微接口注入优化
     */
    @Resource
    private IWorkCorpCpService<WxCpServiceImpl> workCorpCpService;
    @Autowired
    private IAppWxCorpPermanentTokenService appWxCorpPermanentTokenService;
    @Autowired
    private ITbWxCorpConfigService corpConfigService;
    @Autowired
    private RedisCache redisCache;

    private final static String CHAT_TRANSFER = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/transfer";

    /**
     * 查询通讯录相关客户
     *
     * @param userId 通讯录相关客户ID
     * @return 通讯录相关客户
     */
    @Override
    public TbWxUser selectTbWxUserById(String corpId, String userId) {
        return baseMapper.selectTbWxUserById(corpId, userId);
    }

    /**
     * 查询通讯录相关客户列表 2022-08-15 空数据时不自动同步
     *
     * @param tbWxUser 通讯录相关客户
     * @return 通讯录相关客户
     */
    @Override
    public List<TbWxUser> selectTbWxUserList(TbWxUser tbWxUser, String corpId) {
        if (StringUtils.isBlank(corpId)) {
            return new ArrayList<>();
        }
        tbWxUser.setDelFlag("1");
        List<TbWxUser> list = baseMapper.getTbWxUserList(tbWxUser);
        // 安装人一定是企业管理员
        String userId = appWxCorpPermanentTokenService.selectAppCorpAdmin(tbWxUser.getCorpId());
        // 查询企业管理是否在通讯录表为管理 不是则设置 同理设置企微端权限
        TbWxUser adminUser = selectTbWxUserById(tbWxUser.getCorpId(), userId);
        if (adminUser != null && (!adminUser.getAdministrator() || !adminUser.getSideAble())) {
            for (TbWxUser wxUser : list) {
                if (StringUtils.isNoneBlank(userId) && userId.equals(wxUser.getUserid())) {
                    wxUser.setAdministrator(true);
                    wxUser.setSideAble(true);
                    wxUser.setCorpId(corpId);
                    setCorpAdminByUserId(wxUser);
                    setSideAbleByUserId(wxUser);
                    wxUser.setCorpId(null);
                    break;
                }
            }
        }
        return list;
    }

    @Override
    @RedisLockAspect(key = "synchronizationWxUser", value = "corpId", waitTime = 6)
    public void synchronizationWxUserByDeptId(String corpId, Long deptId) {
        if (StringUtils.isBlank(corpId)) {
            return;
        }

        try {
            WxCpServiceImpl wxCpService = workCorpCpService.getWxCpBookServiceByCorpId(corpId);
            // 将所有成员逻辑删除
            this.lambdaUpdate()
                    .set(TbWxUser::getDelFlag, UserStatus.DELETED.getCode())
                    .eq(TbWxUser::getCorpId, corpId).update();

            WxCpAgent wxCpAgent = wxCpService.getAgentService().get(wxCpService.getWxCpConfigStorage().getAgentId());
            if (Objects.isNull(wxCpAgent)) {
                return;
            }

            // 根据部门ID同步部门下的员工信息
            List<Long> deptIds = wxCpAgent.getAllowParties().getPartyIds();
            syncUserByDept(corpId, wxCpService, deptIds);

            // 根据员工ID同步员工信息
            List<WxCpAgent.User> agentUsers = wxCpAgent.getAllowUserInfos().getUsers();
            syncUser(corpId, wxCpService, agentUsers);

        } catch (Exception ex) {
            log.error("同步失败，失败原因：", ex);
        }
    }


    /**
     * 根据部门id，同步部门下的员工
     * @param corpId
     * @param wxCpService
     * @param deptIds
     * @throws WxErrorException
     */
    private void syncUserByDept(String corpId, WxCpServiceImpl wxCpService, List<Long> deptIds) throws WxErrorException {
        if (CollectionUtil.isEmpty(deptIds)) {
            return;
        }

        List<WxCpUser> wxUserList = new ArrayList<>();
        for (Long deptId : deptIds) {
            List<WxCpUser> userList = wxCpService.getUserService().listByDepartment(deptId, true, 1);
            wxUserList.addAll(userList);
        }

        if (CollectionUtils.isNotEmpty(wxUserList)) {
            updateWxUser(corpId, wxUserList);
        }
    }

    /**
     * 根据userId同步员工信息
     * @param corpId
     * @param wxCpService
     * @param agentUsers
     * @throws WxErrorException
     */
    private void syncUser(String corpId, WxCpServiceImpl wxCpService, List<WxCpAgent.User> agentUsers) throws WxErrorException {
        if (CollectionUtil.isEmpty(agentUsers)) {
            return;
        }

        List<WxCpUser> wxCpUserList = Lists.newArrayList();

        for (WxCpAgent.User user : agentUsers) {
            WxCpUser wxCpUser = wxCpService.getUserService().getById(user.getUserId());
            wxCpUserList.add(wxCpUser);
        }

        if (CollectionUtils.isNotEmpty(wxCpUserList)) {
            updateWxUser(corpId, wxCpUserList);
        }
    }

    private void updateWxUser(String corpId, List<WxCpUser> wxUserList) {
        // 先查询该企业
        List<TbWxUser> existUserList = this.list(new LambdaQueryWrapper<TbWxUser>().eq(TbWxUser::getCorpId, corpId));
        Map<String, TbWxUser> userMap = existUserList.stream().collect(Collectors.toMap(TbWxUser::getUserid, Function.identity(), (o, n) -> n));
        // 从参数表获取默认用户头像
        String defaultUserAvatar = redisCache.getCacheObject(DefaultConstants.DEFAULT_USER_AVATAR);
        // 将企微接口同步过来的员工数据转为TbWxUser对象
        List<TbWxUser> tbWxUserList = ConvertUtils.wxUserConvert(corpId, wxUserList, defaultUserAvatar);
        // 员工信息不存在，则新增
        List<TbWxUser> saveUserList = new ArrayList<>();
        // 员工信息已存在，则修改
        List<TbWxUser> editUserList = new ArrayList<>();

        for (TbWxUser tbWxUser : tbWxUserList) {
            if (StringUtils.isAnyBlank(tbWxUser.getCorpId(), tbWxUser.getUserid())) {
                continue;
            }

            if (userMap.containsKey(tbWxUser.getUserid())) {
                if (tbWxUser.getStatus() == 1) {
                    // 被删除员工重新激活, 1表示未删除，字段定义和其他表不一样，这里以后要优化 todo
                    tbWxUser.setDelFlag("1");
                }

                tbWxUser.setSideAble(null);  // 设置为null，表示不修改该字段值
                tbWxUser.setId(userMap.get(tbWxUser.getUserid()).getId()); // 方便后面按id进行更新
                editUserList.add(tbWxUser);
            } else {
                saveUserList.add(tbWxUser);
            }
        }

        if (CollectionUtil.isNotEmpty(saveUserList)) {
            this.saveBatch(saveUserList);
        }

        if (CollectionUtil.isNotEmpty(editUserList)) {
            this.updateBatchById(editUserList);
        }
    }


    @Override
    public void addWxUser(WxCpUser wxCpUser, String corpId) {
        if (null != wxCpUser && StringUtils.isNotBlank(corpId)) {
            List<WxCpUser> wxUserList = new ArrayList<>();
            wxUserList.add(wxCpUser);
            // 获取默认用户头像
            String defaultUserAvatar = redisCache.getCacheObject(DefaultConstants.DEFAULT_USER_AVATAR);
            // 对象转换
            List<TbWxUser> tbWxUserList = ConvertUtils.wxUserConvert(corpId, wxUserList, defaultUserAvatar);

            List<TbWxUser> saveUserList = new ArrayList<>();
            List<TbWxUser> editUserList = new ArrayList<>();
            for (TbWxUser tbWxUser : tbWxUserList) {
                if (null != tbWxUser && StringUtils.isNotBlank(tbWxUser.getCorpId()) && StringUtils.isNotBlank(tbWxUser.getUserid())) {
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq("corp_id", tbWxUser.getCorpId());
                    queryWrapper.eq("userid", tbWxUser.getUserid());

                    TbWxUser t = this.getOne(queryWrapper);
                    if (null != t) {
                        editUserList.add(tbWxUser);
                    } else {
                        saveUserList.add(tbWxUser);
                    }
                }
            }

            if (saveUserList.size() > 0) {
                this.saveBatch(saveUserList, 500);
            }

            if (editUserList.size() > 0) {
                for (TbWxUser tbWxUser : editUserList) {
                    UpdateWrapper<TbWxUser> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("corp_id", tbWxUser.getCorpId());
                    updateWrapper.eq("userid", tbWxUser.getUserid());
                    tbWxUser.setDelFlag("1");
                    // 不更新企微端权限
                    tbWxUser.setSideAble(null);
                    Integer rows = this.baseMapper.update(tbWxUser, updateWrapper);
                }
            }
        }
    }

    /**
     * 根据企业ID，用户Id获取用户信息
     *
     * @param corpId  企业ID
     * @param userIds 用户Id列表
     * @return 结果
     */
    @Override
    public List<TbWxUser> getTbWxUser(String corpId, List<String> userIds) {
        return baseMapper.getTbWxUser(corpId, userIds);
    }

    /**
     * 删除成员
     *
     * @param corpId 企业ID
     * @param userId 成员Id
     */
    @Override
    public void delWxUser(String corpId, String userId) {
        if (StringUtils.isNotBlank(userId) && StringUtils.isNotBlank(corpId)) {
            TbWxUser tbWxUser = new TbWxUser();
            tbWxUser.setDimissionTime(new Date());
            tbWxUser.setIsAllocate(Integer.valueOf(UserStatus.OK.getCode()));
            tbWxUser.setDelFlag("2");
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("corp_id", corpId);
            wrapper.eq("userid", userId);
            baseMapper.update(tbWxUser, wrapper);
        }
    }

    @Override
    public List<String> listAllUserIdByCorpId(String corpId) {
        return baseMapper.getAllUserIdByCorpId(corpId);
    }

    @Override
    public List<WechatUser> getWxUserByDeptId(String corpId, String deptId) {
        List<WechatUser> list = null;
        if (StringUtils.isNotBlank(corpId)) {
            if (null != deptId) {
                list = this.baseMapper.getWxUserByDeptId(corpId, deptId);
            }
        }
        return list;
    }

    @Override
    public List<WechatUser> getWxDimssionUserByDeptId(String corpId, String deptId) {
        List<WechatUser> list = null;
        if (StringUtils.isNotBlank(corpId)) {
            if (null != deptId) {
                list = this.baseMapper.getWxDimssionUserByDeptId(corpId, deptId);
            }
        }
        return list;
    }

    @Override
    public List<WxLeaveUserVO> leaveNoAllocateUserList(WxLeaveUserDto dto) {
        return baseMapper.leaveNoAllocateUserList(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allocate(String takeOverUserId, String handoverUserId, String corpId, String userName) throws Exception {
        // 获取企业成员对应的群和外部客户
        int allocateTotal = 0;
        QueryWrapper<TbWxCustomerGroup> wrapper = new QueryWrapper<>();
        wrapper.eq("corp_id", corpId);
        wrapper.isNull("dismiss_date");
        wrapper.eq("owner", handoverUserId);
        WxCpServiceImpl wxCpService = workCorpCpService.getWxCpBookServiceByCorpId(corpId);
        List<TbWxExtFollowUser> sucessUsers = new ArrayList<>();
        List<String> successChatIdList = new ArrayList<>();
        List<TbWxCorpDimissionAllocate> allocateList = new ArrayList<>();
        List<TbWxCustomerGroup> groups = tbWxCustomerGroupMapper.selectList(wrapper);
        int total = groups.size();
        allocateTotal = allocateTotal + total;
        if (total > 0) {
            // 设置这些群的状态
            for (TbWxCustomerGroup group : groups) {
                // 离职继承完成
                group.setStatus(3);
                if (StringUtils.isBlank(group.getChatId())) {
                    tbWxCustomerGroupMapper.insert(group);
                    continue;
                }
                tbWxCustomerGroupMapper.updateById(group);
            }

            List<String> groupIds = groups.stream().map(TbWxCustomerGroup::getChatId).collect(Collectors.toList());
            if (total > SIZE) {
                int i;
                for (i = 0; i < (total / SIZE); i++) {
                    List<String> list = groupIds.subList(i * SIZE, (i + 1) * SIZE);
                    successChatIdList.addAll(postWxAllocateChats(takeOverUserId, handoverUserId, wxCpService, list));
                }
                List<String> list = groupIds.subList(i * SIZE, total);
                successChatIdList.addAll(postWxAllocateChats(takeOverUserId, handoverUserId, wxCpService, list));
            } else {
                successChatIdList.addAll(postWxAllocateChats(takeOverUserId, handoverUserId, wxCpService, groupIds));
            }
            if (successChatIdList.size() > 0) {
                allocateList.addAll(buildAllocateList(takeOverUserId, handoverUserId, corpId, userName, successChatIdList, true));
                log.info("风萧萧兮易水寒");
            }
        }
        QueryWrapper<TbWxExtFollowUser> wrapper1 = new QueryWrapper<>();
        wrapper1.eq("corp_id", corpId);
        wrapper1.eq("status", UserStatus.OK.getCode());
        wrapper1.eq("user_id", handoverUserId);
        List<TbWxExtFollowUser> followUserList = tbWxExtFollowUserService.list(wrapper1);
        if (followUserList.size() > 0) {
            // 设置这些关系记录
            for (TbWxExtFollowUser tbWxExtFollowUser : followUserList) {
                tbWxExtFollowUser.setStatus("2");
                tbWxExtFollowUser.setDelTime(new Date());
                tbWxExtFollowUserService.saveOrUpdate(tbWxExtFollowUser);
            }
            allocateTotal = allocateTotal + followUserList.size();
            //调用分配在职或离职成员的客户
            for (TbWxExtFollowUser followUser : followUserList) {
                try {
                    wxCpService.getExternalContactService().transferExternalContact(followUser.getExternalUserId(), handoverUserId, takeOverUserId);
                    sucessUsers.add(followUser);
                } catch (WxErrorException e) {
                    log.error("调用分配在职或离职成员的客户异常:{}", e.getError().getErrorMsg(),
                            followUser.getExternalUserId(), handoverUserId, takeOverUserId);
                }
            }
            if (sucessUsers.size() > 0) {
                allocateList.addAll(buildAllocateList(takeOverUserId, handoverUserId, corpId, userName, sucessUsers.stream()
                                .map(TbWxExtFollowUser::getExternalUserId).collect(Collectors.toList()),
                        false));
            }
        }
        if (allocateList.size() > 0) {
            tbWxCorpDimissionAllocateService.saveBatch(allocateList, 200);
            if (allocateTotal == allocateList.size()) {
                TbWxUser wxUser = new TbWxUser();
                wxUser.setUserid(handoverUserId);
                wxUser.setCorpId(corpId);
                wxUser.setIsAllocate(1);
                baseMapper.updateTbWxUser(wxUser);
            }
        }
        if (successChatIdList.size() <= 0 && sucessUsers.size() <= 0) {
            throw new Exception("离职继承分配失败");
        }
    }





    private List<TbWxCorpDimissionAllocate> buildAllocateList(String takeOverUserId, String handoverUserId, String corpId, String userName, List<String> successChatIdList, boolean chatFlag) {
        List<TbWxCorpDimissionAllocate> allocates = new ArrayList<>();
        for (String chatId : successChatIdList) {
            TbWxCorpDimissionAllocate allocate = new TbWxCorpDimissionAllocate();
            allocate.setCreateBy(userName);
            allocate.setCorpId(corpId);
            allocate.setAllocateId(chatId);
            allocate.setAllocateTime(new Date());
            allocate.setStatus(Constants.ALLOCATED);
            if (chatFlag) {
                allocate.setType("2");
            } else {
                allocate.setType("1");
            }
            allocate.setHandoverUserid(handoverUserId);
            allocate.setTakeOverUserId(takeOverUserId);
            allocates.add(allocate);
        }
        return allocates;
    }

    private List<String> postWxAllocateChats(String takeOverUserId, String handoverUserId, WxCpServiceImpl wxCpService, List<String> list) {
        JsonObject json = new JsonObject();
        List<String> chatIds = new ArrayList<>();
        json.add("chat_id_list", new Gson().toJsonTree(list).getAsJsonArray());
        json.addProperty("new_owner", takeOverUserId);
        try {
            wxCpService.post(CHAT_TRANSFER, json);
            chatIds = list;
        } catch (WxErrorException e) {
            log.error("调用分配离职成员的客户群异常:{}", e.getError().getErrorMsg(),
                    list, handoverUserId, takeOverUserId);
        }
        return chatIds;
    }

    /**
     * 批量保存或者修改成员信息
     *
     * @param tbWxUserList
     * @return
     */
    private int batchSaveOrUpdateTbWxUser(List<TbWxUser> tbWxUserList) {
        // 根据TbWxUser对象中的CorpId、UserId、OpenUserId查询是否存在记录，存在则进行更新操作否则插入
        if (CollectionUtils.isEmpty(tbWxUserList)) {
            return 0;
        }
        // 扩展信息列表
        List<TbWxUserExtattr> extattrList = new ArrayList<>();
        // 对外信息列表
        List<TbWxUserExternalProfile> profileList = new ArrayList<>();

        int resultCount = 0;
        int listSize = tbWxUserList.size();
        if (listSize > 500) {
            List<TbWxUser> tempList = new ArrayList<>();
            for (int i = 0; i <= tbWxUserList.size(); i++) {
                TbWxUser tbWxUser = tbWxUserList.get(i);
                extattrList.addAll(tbWxUser.getTbWxUserExtattrList());
                profileList.addAll(tbWxUser.getTbWxUserExternalProfileList());
                tempList.add(tbWxUserList.get(i));
                if (tempList.size() >= 500) {
                    resultCount = resultCount + baseMapper.insertOrUpdateTbWxUser(tempList);
                    tempList.clear();
                }
            }
            resultCount = resultCount + baseMapper.insertOrUpdateTbWxUser(tempList);
        } else {
            resultCount = baseMapper.insertOrUpdateTbWxUser(tbWxUserList);
        }

        // 初始化对应密文userId
        // userMapService.saveOrUpdateByCorpId(tbWxUserList.get(0).getCorpId());

            /* 非第三方创建的用户没有返回
            // 扩展信息
            if(null != extattrList && extattrList.size() > 0){
                int extattrListSize = extattrList.size();
                if(extattrListSize > 500){
                    List<TbWxUserExtattr> tempExtattrList = new ArrayList<>();
                    for(int i = 0; i <= extattrListSize; i++){
                        tempExtattrList.add(extattrList.get(i));
                        if (tempExtattrList.size() >= 500) {
                            // 删除扩展信息
                            tbWxUserMapper.delExtattr(tempExtattrList);

                            // 添加扩展信息
                            tbWxUserMapper.batchTbWxUserExtattr(tempExtattrList);

                            // 清空临时扩展列表
                            tempExtattrList.clear();
                        }
                    }

                    if (tempExtattrList.size() >= 0) {
                        // 删除扩展信息
                        tbWxUserMapper.delExtattr(tempExtattrList);

                        // 添加扩展信息
                        tbWxUserMapper.batchTbWxUserExtattr(tempExtattrList);
                    }
                }else{
                    // 删除扩展信息
                    tbWxUserMapper.delExtattr(extattrList);

                    // 添加扩展信息
                    tbWxUserMapper.batchTbWxUserExtattr(extattrList);
                }
            }

            // 对外信息
            if(null != profileList && profileList.size() > 0){
                int profileListSize = profileList.size();
                if(profileListSize > 500){
                    List<TbWxUserExternalProfile> tempProfileList = new ArrayList<>();
                    for(int i = 0; i <= profileListSize; i++){
                        tempProfileList.add(profileList.get(i));
                        if (tempProfileList.size() >= 500) {
                            // 删除对外信息
                            tbWxUserMapper.delProfile(tempProfileList);

                            // 添加对外信息
                            tbWxUserMapper.batchTbWxUserExternalProfile(tempProfileList);

                            // 清空临时对外列表
                            tempProfileList.clear();
                        }
                    }

                    if (tempProfileList.size() >= 0) {
                        // 删除对外信息
                        tbWxUserMapper.delProfile(tempProfileList);

                        // 添加对外信息
                        tbWxUserMapper.batchTbWxUserExternalProfile(tempProfileList);
                    }
                }else{
                    // 删除对外信息
                    tbWxUserMapper.delProfile(profileList);

                    // 添加对外信息
                    tbWxUserMapper.batchTbWxUserExternalProfile(profileList);
                }
            }*/

        return resultCount;

    }

    /**
     * 第三方通讯录回调
     *
     * @param wxMessage
     */
    @Override
    public void corpContactHandler(WxCpTpXmlMessage wxMessage) {
        String corpId = wxMessage.getAuthCorpId();
        // WxTpCpServiceImpl wxTpCpService = wxMultiTpCorpService.getWxCpServiceByCorpId(corpId, 1);
        WxTpCpServiceImpl wxTpCpService = null;
        if (EventConstants.CHANGE_CONTACT_CREATE_USER.equals(wxMessage.getChangeType())) {
            log.info("【服务商应用】通讯录回调：添加企业ID{}的成员", corpId);
            try {
                // 添加成员
                String userId = wxMessage.getUserID();
                log.info("【服务商应用】通讯录回调：获取到成员ID为：{}", userId);

                // 获取成员详情
                WxCpUser wxCpUser = wxTpCpService.getUserService().getById(userId);
                log.info("【服务商应用】通讯录回调：根据成员ID{}调用企业微信接口获取到结果为：{}", userId, JSONObject.toJSONString(wxCpUser));

                // 保存成员信息
                addWxUser(wxCpUser, corpId);
            } catch (Exception ex) {
                log.error("【服务商应用】通讯录回调：添加成员异常，接收参数为：{}；异常信息为：{}", JSONObject.toJSONString(wxMessage), ex);
            }
            log.info("【服务商应用】通讯录回调：添加企业ID{}的成员结束", corpId);
        }

        if (EventConstants.CHANGE_CONTACT_DELETE_USER.equals(wxMessage.getChangeType())) {
            log.info("【服务商应用】通讯录回调：删除企业ID{}的成员", corpId);
            try {
                // 删除成员
                String userId = wxMessage.getUserID();
                log.info("【服务商应用】通讯录回调：获取到成员ID为：{}", userId);

                delWxUser(corpId, userId);

                log.info("【服务商应用】通讯录回调：禁用企业Id{}的员工ID{}开始", corpId, userId);
                // 禁用员工账号
                // SysUser sysUser = sysUserFeign.selectUserByCorpInfo(corpId, userId);
                // if (null != sysUser) {
                //     sysUser.setStatus("1");
                //     sysUserFeign.updateUser(sysUser);
                // }
                log.info("【服务商应用】通讯录回调：禁用企业Id{}的员工ID{}结束", corpId, userId);
            } catch (Exception ex) {
                log.error("【服务商应用】通讯录回调：删除成员异常，接收参数为：{}；异常信息为：{}", JSONObject.toJSONString(wxMessage), ex);
            }
            log.info("【服务商应用】通讯录回调：删除企业ID{}的成员结束", corpId);
        }

        if (EventConstants.CHANGE_CONTACT_UPDATE_USER.equals(wxMessage.getChangeType())) {
            log.info("【服务商应用】通讯录回调：修改企业ID{}的成员", corpId);
            try {
                // todo 企业更换userid事件 xml解析没有NewUserId
                /**
                 * <xml>
                 *     <SuiteId>
                 *         <![CDATA[ww82ba5b3acb0d05ad]]>
                 *     </SuiteId>
                 *     <AuthCorpId>
                 *         <![CDATA[wwf8c66a3dd08ae5aa]]>
                 *     </AuthCorpId>
                 *     <InfoType>
                 *         <![CDATA[change_contact]]>
                 *     </InfoType>
                 *     <TimeStamp>1637207152</TimeStamp>
                 *     <ChangeType>
                 *         <![CDATA[update_user]]>
                 *     </ChangeType>
                 *     <UserID>
                 *         <![CDATA[LuJiaCan]]>
                 *     </UserID>
                 *     <NewUserID>
                 *         <![CDATA[Lua]]>
                 *     </NewUserID>
                 *     <OpenUserID>
                 *         <![CDATA[woRRa3DQAAfz_ARDQdB1ulDqWi6wwCKQ]]>
                 *     </OpenUserID>
                 * </xml>
                 */
                // String newUserId = wxMessage.getNewUserId();
                String newUserId = "";
                String userId = wxMessage.getUserID();
                if (StringUtils.isBlank(newUserId)) {
                    // 修改成员
                    log.info("【服务商应用】通讯录回调：获取到成员ID为：{}", userId);

                    // 获取成员详情
                    WxCpUser wxCpUser = wxTpCpService.getUserService().getById(userId);
                    log.info("【服务商应用】通讯录回调：根据成员ID{}调用企业微信接口获取到结果为：{}", userId, JSONObject.toJSONString(wxCpUser));

                    // 修改成员信息
                    addWxUser(wxCpUser, corpId);
                } else {
                    // 修改员工活码
                    log.info("【服务商应用】通讯录回调：修改成员UserID同步修改员工活码");
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq("corp_id", corpId);
                    queryWrapper.eq("user_id", userId);
                    List<TbWxContactRel> tbWxContactRelList = tbWxContactRelService.list(queryWrapper);
                    if (CollectionUtils.isNotEmpty(tbWxContactRelList)) {
                        for (TbWxContactRel tbWxContactRel : tbWxContactRelList) {
                            tbWxContactRel.setUserId(newUserId);
                            tbWxContactRelService.update(tbWxContactRel, queryWrapper);
                        }
                    }
                    log.info("【服务商应用】通讯录回调：修改成员UserID同步修改员工活码结束");

                    log.info("【服务商应用】通讯录回调：修改成员UserID同步员工登录账号");
                    // 修改登录账号
                    SysUser sysUser = sysUserFeign.selectUserByCorpInfo(corpId, userId);
                    if (null != sysUser) {
                        SysUser su = new SysUser();
                        su.setCorpUserId(newUserId);
                        su.setCorpId(corpId);
                        su.setUserId(sysUser.getUserId());
                        sysUserFeign.updateCorpUserId(su);
                    }
                    log.info("【服务商应用】通讯录回调：修改成员UserID同步员工登录账号结束");

                    log.info("【服务商应用】通讯录回调：修改成员UserID同步新增员工信息");
                    // 新增成员
                    // 获取成员详情
                    WxCpUser wxCpUser = wxTpCpService.getUserService().getById(newUserId);
                    log.info("【服务商应用】通讯录回调：根据成员ID{}调用企业微信接口获取到结果为：{}", newUserId, JSONObject.toJSONString(wxCpUser));

                    // 保存成员信息
                    addWxUser(wxCpUser, corpId);
                    log.info("【服务商应用】通讯录回调：修改成员UserID同步新增员工信息结束");

                    // 废除老成员
                    log.info("【服务商应用】通讯录回调：修改成员UserID同步废除原有员工信息");
                    TbWxUser tbWxUser = new TbWxUser();
                    tbWxUser.setDelFlag("2");
                    QueryWrapper<TbWxUser> userQueryWrapper = new QueryWrapper<>();
                    userQueryWrapper.eq("userid", userId);
                    update(tbWxUser, userQueryWrapper);
                    log.info("【服务商应用】通讯录回调：修改成员UserID同步废除原有员工信息结束");

                    log.info("【服务商应用】通讯录回调：修改成员UserID同步建立新旧UserId关系");
                    // 新旧UserId建立关联关系表
                    TbWxUserRel tbWxUserRel = new TbWxUserRel();
                    tbWxUserRel.setCreateTime(new Date());
                    tbWxUserRel.setNewUserId(newUserId);
                    tbWxUserRel.setOldUserId(userId);
                    tbWxUserRel.setCorpId(corpId);
                    tbWxUserRelService.save(tbWxUserRel);
                    log.info("【服务商应用】通讯录回调：修改成员UserID同步建立新旧UserId关系结束");
                }
            } catch (Exception ex) {
                log.error("【服务商应用】通讯录回调：修改成员异常，接收参数为：{}；异常信息为：{}", JSONObject.toJSONString(wxMessage), ex);
            }
            log.info("【服务商应用】通讯录回调：修改企业ID{}的成员结束", corpId);
        }
        if (EventConstants.CHANGE_CONTACT_DELETE_USER.equals(wxMessage.getChangeType())) {
            log.info("通讯录回调：删除企业ID{}的成员", corpId);
            try {
                // 删除成员
                String userId = wxMessage.getUserID();
                log.info("【服务商应用】通讯录回调：获取到成员ID为：{}", userId);

                delWxUser(corpId, userId);

                log.info("【服务商应用】通讯录回调：禁用企业Id{}的员工ID{}开始", corpId, userId);
                // 禁用员工账号
                SysUser sysUser = userMapper.selectUserByUserCorpInfoV2(corpId, userId);
                if (null != sysUser) {
                    sysUser.setStatus("1");
                    int count = sysUserFeign.updateUser(sysUser);
                }
                // todo 删除其他信息


                log.info("【服务商应用】通讯录回调：禁用企业Id{}的员工ID{}结束", corpId, userId);
            } catch (Exception ex) {
                log.error("【服务商应用】通讯录回调：删除成员异常，接收参数为：{}；异常信息为：{}", JSONObject.toJSONString(wxMessage), ex);
            }
            log.info("【服务商应用】通讯录回调：删除企业ID{}的成员结束", corpId);
        }
        if (EventConstants.CHANGE_CONTACT_CREATE_PARTY.equals(wxMessage.getChangeType())) {
            log.info("【服务商应用】通讯录回调：添加企业ID{}的部门信息", corpId);
            try {
                // 添加部门
                log.info("【服务商应用】通讯录回调：添加企业Id{}的部门信息开始，接收参数{}", corpId, JSONObject.toJSONString(wxMessage));
                TbWxDepartment tbWxDepartment = new TbWxDepartment();
                tbWxDepartment.setId(wxMessage.getId() + "");
                tbWxDepartment.setCorpId(corpId);
                tbWxDepartment.setName(wxMessage.getName());
                tbWxDepartment.setParentId(wxMessage.getParentId());
                tbWxDepartment.setDelFlag("1");
                // tbWxDepartmentService.save(tbWxDepartment);
                log.info("【服务商应用】通讯录回调：添加企业Id{}的部门信息结束", corpId);
            } catch (Exception ex) {
                log.error("【服务商应用】通讯录回调：添加企业ID{}的部门信息异常，接收参数为：{}；异常信息为：{}", corpId, JSONObject.toJSONString(wxMessage), ex);
            }
            log.info("通讯录回调：添加企业ID{}的部门信息结束", corpId);
        }
        if (EventConstants.CHANGE_CONTACT_UPDATE_PARTY.equals(wxMessage.getChangeType())) {
            log.info("通讯录回调：修改企业ID{}的部门", corpId);
            try {
                // 修改部门
                log.info("【服务商应用】通讯录回调：修改企业Id{}的部门信息开始，接收参数{}", corpId, JSONObject.toJSONString(wxMessage));
                TbWxDepartment tbWxDepartment = new TbWxDepartment();
                tbWxDepartment.setId(wxMessage.getId() + "");
                tbWxDepartment.setCorpId(corpId);
                if (StringUtils.isNotEmpty(wxMessage.getName())) {
                    tbWxDepartment.setName(wxMessage.getName());
                }
                if (wxMessage.getParentId() != null) {
                    tbWxDepartment.setParentId(wxMessage.getParentId());
                }

                QueryWrapper<TbWxDepartment> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("corp_id", corpId);
                queryWrapper.eq("id", wxMessage.getId());
                // tbWxDepartmentService.update(tbWxDepartment, queryWrapper);
                log.info("【服务商应用】通讯录回调：修改企业Id{}的部门信息结束", corpId);
            } catch (Exception ex) {
                log.error("【服务商应用】通讯录回调：修改企业ID{}的部门信息异常，接收参数为：{}；异常信息为：{}", corpId, JSONObject.toJSONString(wxMessage), ex);
            }
            log.info("【服务商应用】通讯录回调：修改企业ID{}的部门结束", corpId);
        }
        if (EventConstants.CHANGE_CONTACT_DELETE_PARTY.equals(wxMessage.getChangeType())) {
            log.info("【服务商应用】通讯录回调：删除企业ID{}的部门", corpId);
            try {
                log.info("【服务商应用】通讯录回调：删除企业Id{}的部门信息开始，接收参数{}", corpId, JSONObject.toJSONString(wxMessage));
                // 删除部门
                TbWxDepartment tbWxDepartment = new TbWxDepartment();
                tbWxDepartment.setCorpId(corpId);
                tbWxDepartment.setId(wxMessage.getId());
                tbWxDepartment.setDelFlag("2");

                QueryWrapper<TbWxDepartment> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("corp_id", corpId);
                queryWrapper.eq("id", wxMessage.getId());
                // tbWxDepartmentService.update(tbWxDepartment, queryWrapper);
                log.info("通讯录回调：删除企业Id{}的部门信息结束", corpId);
            } catch (Exception ex) {
                log.error("通讯录回调：删除企业ID{}的部门信息异常，接收参数为：{}；异常信息为：{}", corpId, JSONObject.toJSONString(wxMessage), ex);
            }
            log.info("通讯录回调：删除企业ID{}的部门结束", corpId);
        }
        log.info("企业微信通讯录回调事件处理结束");
    }

    @Override
    public void setCorpAdminByUserId(TbWxUser tbWxUser) {
        baseMapper.setCorpAdminByUserId(tbWxUser);
    }

    @Override
    public TbWxUser checkUserIsCorpAdmin(String userId, String corpId) {
        return baseMapper.checkUserIsCorpAdmin(userId, corpId);
    }

    @Override
    public List<TbWxUser> querySysUserExist(String corpId) {
        return baseMapper.querySysUserExist(corpId);
    }

    @Override
    public void insertSysUser(SysUser sysUser) {
        baseMapper.insertSysUser(sysUser);
    }

    @Override
    public List<MemberVo> queryMemberVos(List<MemberVo> memberVos, Long corpConfigId) {
        TbWxCorpConfig tbWxCorpConfig = corpConfigService.selectTbWxCorpConfigById(corpConfigId);
        if (CollectionUtil.isNotEmpty(memberVos) && tbWxCorpConfig != null) {
            String corpId = tbWxCorpConfig.getCorpId();
            for (MemberVo memberVo : memberVos) {
                // baseMapper.get
            }
            return memberVos;
        }
        memberVos = Lists.newArrayList();
        return memberVos;
    }

    @Override
    public TbWxUser selectTbWxUserByCorpIdAndOpenUserId(String corpId, String openUserId) {
        return baseMapper.selectTbWxUserByCorpIdAndOpenUserId(corpId, openUserId);
    }

    @Override
    public void setSideAbleByUserId(TbWxUser user) {
        baseMapper.setSideAbleByUserId(user);
    }

    @Override
    public WorkbenchLoginInfo selectWorkLoginInfo(MobileUser mobileUser) {
        return baseMapper.selectWorkLoginInfo(mobileUser);
    }

    @Override
    public List<TbWxUser> selectAdmin4Corp(FeedbackContentDTO contentDTO) {
        return baseMapper.selectAdmin4Corp(contentDTO);
    }

    @Override
    public List<WechatUser> selectNoneDeptUserByCorpId(String corpId) {
        return baseMapper.selectNoneDeptUserByCorpId(corpId);
    }

    @Override
    public List<String> getTbWxUserNames(List<String> userList, String corpId) {
        return baseMapper.getTbWxUserNames(userList, corpId);
    }

    @Override
    public List<TbWxUser> selectTbWxUserListByDepartmentList(List<String> departmentList, String corpId) {
        // 需要遍历所有子部门
        SpringUtils.getBean(ITbWxDepartmentService.class).getSonDepartmentByIds(departmentList, null, corpId);
        return baseMapper.selectTbWxUserListByDepartmentList(departmentList, corpId);
    }

    @Override
    public void synchronizationWxUserByUserId(String userId, String corpId,WxCpServiceImpl wxCpService) {
        try {
            WxCpUser wxCpUser = wxCpService.getUserService().getById(userId);
            if (ObjectUtil.isNotNull(wxCpUser)) {
                List<WxCpUser> wxUserList = Lists.newArrayList();
                wxUserList.add(wxCpUser);
                // 获取默认用户头像
                String defaultUserAvatar = redisCache.getCacheObject(DefaultConstants.DEFAULT_USER_AVATAR);
                List<TbWxUser> tbWxUserList = ConvertUtils.wxUserConvert(corpId, wxUserList, defaultUserAvatar);
                if (CollectionUtil.isNotEmpty(tbWxUserList)) {
                    for (TbWxUser tbWxUser : tbWxUserList) {
                        log.info("【同步通讯录】开始同步成员：{}", tbWxUser);
                        // 更新信息重新
                        LambdaUpdateWrapper<TbWxUser> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(TbWxUser::getCorpId, corpId);
                        updateWrapper.eq(TbWxUser::getUserid, userId);
                        // 关注事件肯定是激活
                        tbWxUser.setDelFlag("1");
                        saveOrUpdate(tbWxUser,updateWrapper);
                    }
                }
            }
        } catch (WxErrorException e) {
            log.error("【同步通讯录】同步成员失败：{}", e.getMessage());
        }

    }

    @Override
    public CorpRealTimeDataVO getDailyTotalUserCountGraph(QueryRadarStatisticsRankDTO dto) {
        CorpRealTimeDataVO vo = new CorpRealTimeDataVO();
        Date startDate = DateUtil.parseDate(dto.getBeginTime());
        Date endDate = DateUtil.parseDate(dto.getEndTime());
        vo.setStartTime(startDate);
        vo.setEndTime(endDate);

        List<ContactStatisticsDailyVO> originalData = baseMapper.getDailyTotalUserCountGraph(dto);
        vo.setData(DataUtil.fillAccumulatedData(originalData, startDate, endDate));

        return vo;
    }

    @Override
    public List<TbWxUser> selectAllTbWxUserList(String corpId){
        TbWxUser tbWxUser = new TbWxUser();
        tbWxUser.setCorpId(corpId);
        return baseMapper.getTbWxUserList(tbWxUser);
    }

    @Override
    public TbWxUserCardVO selectWxUserCardById(String userId) {
        TbWxUser tbWxUser = baseMapper.selectOne(new LambdaQueryWrapper<>(TbWxUser.class).eq(TbWxUser::getUserid, userId));
        if (ObjectUtil.isNull(tbWxUser)) {
            return null;
        }
        TbWxUserCardVO wxUserCardVO = new TbWxUserCardVO();
        BeanUtils.copyProperties(tbWxUser, wxUserCardVO);
        wxUserCardVO.setUserId(tbWxUser.getUserid());
        if (StrUtil.isNotBlank(tbWxUser.getWelcomeAttachment())) {
            wxUserCardVO.setAttachments(JSON.parseArray(tbWxUser.getWelcomeAttachment(), WelcomeAttachmentVo.class));
        }
        wxUserCardVO.setWelContent(tbWxUser.getWelcomeContent());
        return wxUserCardVO;
    }
}
