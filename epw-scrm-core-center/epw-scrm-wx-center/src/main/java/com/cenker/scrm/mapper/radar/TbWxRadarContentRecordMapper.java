package com.cenker.scrm.mapper.radar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.pojo.dto.condition.SopTriggerDetailDTO;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerTrajectory;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent;
import com.cenker.scrm.pojo.entity.wechat.TbWxRadarContentRecord;
import com.cenker.scrm.pojo.vo.radar.CorpInteractVO;
import com.cenker.scrm.pojo.vo.radar.RadarCustomerDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface TbWxRadarContentRecordMapper extends BaseMapper<TbWxRadarContentRecord> {
    TbWxRadarContentRecord getReadRecordByParamId(Map<String, Object> params);

    Date getEndDateByData(TbWxRadarContent content);

    List<RadarCustomerDetailVO> getRadarReadRecordDetail(Map<String, Object> params);

    List<RadarCustomerDetailVO> getRadarReadRecordDetail4Chat(Map<String, Object> params);

    /**
     * 统计转发次数
     */
    int sumForWordCount(Map<String, Object> params);

    CorpInteractVO countCorpInteractByExtUserId(CustomerChurnDTO dto);

    /**
     * 修数据
     */
    List<TbWxCustomerTrajectory> testRepairLinkVoAddType();

    /**
     * 修复数据2022-06-23
     * @param corpId 企业id明文
     * @param userId 员工id
     * @param openUserId 加密员工id
     */
    void updateStaffId(@Param("corpId") String corpId, @Param("userId") String userId, @Param("openUserId") String openUserId);

    /**
     * 筛选浏览指定智能物料次数 大于等于、小于
     * @param sopTriggerDetailDTO triggerRelationType 1 等于 2 不等于 3 包含 4 不包含 5 大于 6 小于 7 大于等于 8 小于等于
     * @return
     */
    List<ConditionSopCustomerDTO> allSelectViewCondition4Sop(SopTriggerDetailDTO sopTriggerDetailDTO);

    /**
     * 筛选浏览或转发指定物料
     * @param sopTriggerDetailDTO
     * @return
     */
    List<ConditionSopCustomerDTO> assignSelectViewAndForward4Sop(SopTriggerDetailDTO sopTriggerDetailDTO);
}
