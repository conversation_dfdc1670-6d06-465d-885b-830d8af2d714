package com.cenker.scrm.controller.workbench;

import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.MobileUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerGroup;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerTrajectory;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.external.CustomerTrajectoryVO;
import com.cenker.scrm.pojo.vo.group.GroupCustomerUserTotalVo;
import com.cenker.scrm.pojo.vo.workbench.WorkbenchLoginInfo;
import com.cenker.scrm.service.contact.ITbWxUserService;
import com.cenker.scrm.service.external.ITbWxCustomerTrajectoryService;
import com.cenker.scrm.service.external.TbWxExtCustomerService;
import com.cenker.scrm.service.group.ITbWxCustomerGroupMemberService;
import com.cenker.scrm.service.group.ITbWxCustomerGroupService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2021/11/2
 * @Description 工作台数据
 */
@RequestMapping("/work/homeData")
@RestController
@AllArgsConstructor
public class HomeDataController extends BaseController {

    private TokenParseUtil tokenService;
    private TbWxExtCustomerService customerService;
    private ITbWxCustomerGroupMemberService groupMemberService;
    private ITbWxUserService userService;
    private ITbWxCustomerTrajectoryService trajectoryService;
    private ITbWxCustomerGroupService tbWxCustomerGroupService;

    @RequestMapping("/homeData")
    public AjaxResult getWordHomeData(@RequestBody MobileUser mobileUser) {
        Map<String, Integer> result = Maps.newHashMap();
        String today = DateUtils.getDate();
        GroupCustomerUserTotalVo groupCustomerUserTotalVo = groupMemberService.countGroupMemberByDate(mobileUser.getUserId(), today);

        /**
         * 2022-06-09 规则
         * 客户总数：当前员工添加的累计客户总数，不含流失客户。
         * 今日新增：当日添加当前员工的客户数量，含主动添加和被动添加。客户当天内重复添加只计1次。
         * 今日流失：当日删除当前员工的客户数量。客户当天内重复删除只计1次。
         *
         * 管理客户群：当前员工创建的客户群数量（员工为群主）。
         * 今日进群：当前员工创建的客户群当日进群人数，不含本企业员工。客户当天内重复进群计多次。
         * 今日退群：当前员工创建的客户群当日退群人数，不含本企业员工。客户当天内重复退群计多次。
         */
        Integer groupTotal = tbWxCustomerGroupService.lambdaQuery()
                .eq(TbWxCustomerGroup::getCorpId, mobileUser.getCorpId())
                .eq(TbWxCustomerGroup::getOwner, mobileUser.getUserId())
                .eq(TbWxCustomerGroup::getStatus, CommonConstants.STATUS_NORMAL)
                .count();

        result.put("customerTotal", customerService.countTotalCustomer(mobileUser));
        result.put("todayUserAddTotal", customerService.countAddOrDecreaseByDay(today, StatusConstants.CUSTOMER_NORMAL_STATUS, mobileUser));
        result.put("todayUserDecreaseTotal", customerService.countAddOrDecreaseByDay(today, StatusConstants.CUSTOMER_DEL_STATUS, mobileUser));
        result.put("groupTotal", groupTotal);
        result.put("todayGroupAddTotal", groupCustomerUserTotalVo.getTodayGroupAddTotal());
        result.put("todayGroupDecreaseTotal", groupCustomerUserTotalVo.getTodayGroupDecreaseTotal());

        return AjaxResult.success(result);
    }

    @RequestMapping("/getUserInfo")
    public AjaxResult getUserInfo(@RequestBody MobileUser mobileUser) {
        WorkbenchLoginInfo workbenchLoginInfo = Optional.ofNullable(userService.selectWorkLoginInfo(mobileUser)).orElse(new WorkbenchLoginInfo());
        workbenchLoginInfo.setUserId(mobileUser.getUserId());
        workbenchLoginInfo.setCorpId(mobileUser.getCorpId());
        if (workbenchLoginInfo.getAdministrator() == null) {
            workbenchLoginInfo.setAdministrator(false);
        }
        workbenchLoginInfo.setAvatar(workbenchLoginInfo.getAvatar());
        // 如果获取不到信息...
        return AjaxResult.success(workbenchLoginInfo);
    }

    /**
     * 待办动态
     *
     * @param status 0 未完成 1 已完成 否则就是全部
     * @return
     */
    @RequestMapping("/findTrajectory")
    public TableDataInfo findTrajectory(HttpServletRequest request, String status) {
        startPage();
        MobileUser mobileUser = tokenService.getLoginUserH5(request).getMobileUser();

        Map<String, Object> params = Maps.newHashMap();
        params.put("status", status);
        params.put("corpId", mobileUser.getCorpId());
        params.put("userId", mobileUser.getUserId());
        List<CustomerTrajectoryVO> list = trajectoryService.selectTrajectoryAndCustomerInfo(params);
/*        if (CollectionUtils.isNotEmpty(list) && mobileUser.getLoginType() == TypeConstants.LOGIN_FROM_WORKBENCH) {
            for (CustomerTrajectoryVO customerTrajectoryVo : list) {
                // 转换为第三方应用所需外部客户联系id
                WxCpServiceImpl wxCpService = appMultiCorpService.getWxCpServiceByCorpId(mobileUser.getCorpId());
                String accessToken = null;
                try {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("external_userid", customerTrajectoryVo.getExternalUserId());
                    accessToken = wxCpService.getAccessToken();
                    HttpRequest post = HttpUtil.createPost("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/to_service_external_userid?access_token=" + accessToken);
                    post.body(JSON.toJSONString(map));
                    HttpResponse execute = post.execute();
                    if (execute.isOk()) {
                        Map<String, Object> result = (Map<String, Object>) JSONObject.parse(execute.body());
                        customerTrajectoryVo.setExternalUserId((String) result.get("external_userid"));
                    }
                } catch (WxErrorException e) {
                    e.printStackTrace();
                }
            }
        }*/
        return getDataTable(list);
    }

    /**
     * 添加或编辑待办
     */
    @RequestMapping(value = "/addOrEditWaitHandle")
    public AjaxResult addOrEditWaitHandle(@RequestBody TbWxCustomerTrajectory trajectory, HttpServletRequest request) {
        MobileUser mobileUser = tokenService.getLoginUserH5(request).getMobileUser();
        if (StringUtils.isEmpty(trajectory.getId())) {
            // 创建时间
            trajectory.setCreateTime(DateUtils.getNowDate());
            trajectory.setStatus(StatusConstants.WAIT_TO_DO_DYNAMIC_NOT_FINISH);
        }
        trajectory.setCorpId(mobileUser.getCorpId());
        trajectory.setUserId(mobileUser.getUserId());
        trajectory.setTrajectoryType(TypeConstants.WAIT_TO_DO_DYNAMIC);
        trajectoryService.saveOrUpdate(trajectory);
        return AjaxResult.success();
    }

}
