package com.cenker.scrm.controller.workbench;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.client.system.SysUserFeign;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.condition.UserConditionDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxContact;
import com.cenker.scrm.pojo.entity.wechat.TbWxContactRel;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTagGroup;
import com.cenker.scrm.pojo.vo.UserVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.contact.ContactVO;
import com.cenker.scrm.pojo.vo.contact.WorkContactVO;
import com.cenker.scrm.pojo.vo.external.CustomerFollowUserVO;
import com.cenker.scrm.service.contact.ITbWxContactRelService;
import com.cenker.scrm.service.contact.ITbWxContactService;
import com.cenker.scrm.service.tag.ITbWxCorpTagGroupService;
import com.cenker.scrm.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/3
 * @Description 工作台渠道活码
 */
@RequestMapping("/work/contact")
@RestController
public class WorkContactController extends BaseController {
    @Autowired
    private ITbWxContactService tbWxContactService;
    @Autowired
    private ITbWxCorpTagGroupService tagGroupService;
    @Autowired
    private ITbWxContactRelService contactRelService;
    @Autowired
    private SysUserFeign sysUserFeign;

    @RequestMapping("/getContactList")
    public TableDataInfo list(@RequestBody WorkContactVO workContactVo) {
        // 查询是否还有后台账号
        String sysUserId = sysUserFeign.selectSysUserByCorpUserId(workContactVo.getUserId(), workContactVo.getCorpId());
        workContactVo.setSysUserId(StringUtils.isNotEmpty(sysUserId) ? sysUserId : "");

        startPage();
        List<WorkContactVO> workContactVoList = tbWxContactService.selectTbWxContactListByWork(workContactVo);
        return getDataTable(workContactVoList);
    }

    /**
     * 添加渠道活码 新增工卡模式
     */
    @RequestMapping("/addContact")
    public AjaxResult add(@RequestBody ContactVO contactVo) {
        contactVo.setId(null);
        TbWxContact contact = tbWxContactService.add(contactVo);
        if (contact != null) {
            Integer qrStyle = contact.getQrStyle();
            if (qrStyle == TypeConstants.CONTACT_IMG_CARD) {
                return new AjaxResult(200, "操作成功", contact.getQrStyleCode());
            }
            return new AjaxResult(200, "操作成功", contact.getQrCode());
        }
        return toAjax(1);
    }

    /**
     * 活码添加的客户信息
     */
    @RequestMapping("/getAddCustomerInfo")
    public TableDataInfo getAddCustomerInfo(@RequestBody TbWxContact tbWxContact) {
        startPage();
        List<CustomerFollowUserVO> list = tbWxContactService.getAddCustomerInfo(tbWxContact);
        return getDataTable(list);
    }

    /**
     * 编辑渠道活码
     */
    @RequestMapping("/update")
    public AjaxResult updateInfo(@RequestBody ContactVO contactVo) {
        AjaxResult result;
        TbWxContact tbWxContact = tbWxContactService.updateInfo(contactVo);
        // 原先就有工卡模式
        if (tbWxContact.getQrStyle() == TypeConstants.CONTACT_IMG_CARD) {
            if (StringUtils.isNotEmpty(tbWxContact.getQrStyleCode())) {
                result = new AjaxResult(200, "操作成功", tbWxContact.getQrStyleCode());
            } else {
                String userId = contactVo.getUserConditionList().stream().map(UserConditionDTO::getUserId).collect(Collectors.toList()).get(0);
                tbWxContactService.generateQrStyle(userId, tbWxContact);
                // 生成
                tbWxContactService.updateById(tbWxContact);
                result = new AjaxResult(200, "操作成功", tbWxContact.getQrStyleCode());
            }
        } else {
            result = new AjaxResult(200, "操作成功", tbWxContact.getQrCode());
        }
        return result;
    }

    /**
     * 删除员工活码
     */
    @RequestMapping("/remove")
    public AjaxResult remove(@RequestBody ContactVO contactVo) {
        String id = contactVo.getId();
        // 删除员工关联
        contactRelService.remove(new LambdaQueryWrapper<TbWxContactRel>()
                .eq(TbWxContactRel::getContactId, id)
        );
        TbWxContact contact = tbWxContactService.getOne(new LambdaQueryWrapper<TbWxContact>()
                .select(TbWxContact::getConfigId)
                .eq(TbWxContact::getId, id)
        );
        if (contact != null) {
            contact.setId(id);
            contact.setDelFlag(StatusConstants.DEL_FLAG_TRUE);
            tbWxContactService.updateById(contact);
            // 企微端删除
            tbWxContactService.deleteTbWxContactByConfigIds(contact.getConfigId(), contactVo.getCorpId());
        }
        return AjaxResult.success();
    }

    @RequestMapping("/getInfo")
    public AjaxResult getInfo(@RequestBody ContactVO contactVo) {
        contactVo = tbWxContactService.getInfo(contactVo);
        if (contactVo == null) {
            return AjaxResult.error("数据不存在");
        }
        return AjaxResult.success(contactVo);
    }

    /**
     * 获取当前系统所有可用标签
     */
    @RequestMapping("/findAllTags")
    public AjaxResult findAllTags(@RequestParam("corpId") String corpId) {
        TbWxCorpTagGroup tbWxCorpTagGroup = new TbWxCorpTagGroup();
        tbWxCorpTagGroup.setCorpId(corpId);
        tbWxCorpTagGroup.setStatus(StatusConstants.DEL_FLAG_FALSE);
        return AjaxResult.success(tagGroupService.selectTbWxCorpTagGroupList(tbWxCorpTagGroup));
    }
}
