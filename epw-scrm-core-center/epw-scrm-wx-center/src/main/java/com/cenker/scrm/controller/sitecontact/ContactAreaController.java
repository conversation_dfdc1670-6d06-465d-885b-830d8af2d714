package com.cenker.scrm.controller.sitecontact;

import cn.hutool.core.util.ObjectUtil;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.ContactArea;
import com.cenker.scrm.pojo.exception.BaseException;
import com.cenker.scrm.pojo.request.ContactAreaRequest;
import com.cenker.scrm.pojo.request.SiteRequest;
import com.cenker.scrm.pojo.request.StoreImportRequest;
import com.cenker.scrm.pojo.request.data.StatisticQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.contact.ContactCustomerStatisticsVO;
import com.cenker.scrm.pojo.vo.contact.ContactStatisticsVO;
import com.cenker.scrm.pojo.vo.sitecontact.*;
import com.cenker.scrm.service.sitecontact.IContactAreaService;
import com.cenker.scrm.service.sitecontact.IContactSiteService;
import com.cenker.scrm.service.sitecontact.ITbWxDeliveryContactService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/20
 * @Description 城市区域
 */
@Slf4j
@RestController
@RequestMapping("/contact/area")
@RequiredArgsConstructor
public class ContactAreaController extends BaseController {

    private final IContactAreaService contactAreaService;
    private final TokenParseUtil tokenService;
    private final ITbWxDeliveryContactService tbWxDeliveryContactService;
    private final IContactSiteService contactSiteService;

    @RequestMapping("/treeAllAreaSelect")
    public AjaxResult treeAllAreaSelect(@RequestBody ContactAreaRequest contactAreaRequest) {
        List<ContactArea> list = contactAreaService.treeAllAreaSelect(contactAreaRequest);
        return AjaxResult.success(contactAreaService.buildAreaTreeSelect(list));
    }

    @RequestMapping("/addSite")
    public AjaxResult add(@RequestBody SiteRequest siteRequest) {
        contactAreaService.addSite(siteRequest);
        return AjaxResult.success();
    }

    /**
     * 站点列表
     * @param siteRequest 参数
     * @return 站点列表
     */
    @RequestMapping("/siteList")
    public TableDataInfo siteList(@RequestBody SiteRequest siteRequest) {
        startPage();
        List<ContactSiteVO> list = contactAreaService.selectSiteList(siteRequest);
        return getDataTable(list);
    }

    @RequestMapping("/allSiteNameList")
    public TableDataInfo allSiteNameList(@RequestBody SiteRequest siteRequest) {
        List<String> list = contactAreaService.selectAllSiteNameList(siteRequest);
        return getDataTable(list);
    }

    /**
     * 获取站点活码详细信息(回显)
     */
    @RequestMapping("/getInfo")
    public AjaxResult getInfo(@RequestBody SiteRequest siteRequest) {
        return AjaxResult.success(contactSiteService.getInfo(siteRequest));
    }

    /**
     * 数据统计
     */
    @RequestMapping("/getDataStatistics")
    public AjaxResult getDataStatistics(@RequestBody SiteRequest siteRequest) {
        return AjaxResult.success(contactSiteService.getDataStatistics(siteRequest));
    }

    /**
     * 活码添加的客户信息
     */
    @RequestMapping("/getAddCustomerInfo")
    public TableDataInfo getAddCustomerInfo(@RequestBody SiteRequest siteRequest) {
        startPage();
        List<ContactCustomerStatisticsVO> list = contactSiteService.getAddCustomerInfo4Web(siteRequest);
        return getDataTable(list);
    }

    /**
     * 获取活码统计数据线性图
     */
    @RequestMapping("/getBehaviorData")
    public AjaxResult getScanBehaviorData(@RequestBody StatisticQuery statisticQuery) {
        Date startTime = null;
        Date endTime = DateUtils.getNowDate();
        // 查询时间范围
        if (statisticQuery.getSeven()) {
            startTime = DateUtils.addDays(endTime, -7);
        } else if (statisticQuery.getThirty()) {
            startTime = DateUtils.addDays(endTime, -30);
        } else {
            if (StringUtils.isNotBlank(statisticQuery.getBeginTime()) ^ StringUtils.isNotBlank(statisticQuery.getEndTime())) {
                throw new BaseException("参数错误");
            }
            try {
                startTime = DateUtils.parseDate(statisticQuery.getBeginTime() + " 00:00:00", DateUtils.YYYY_MM_DD_HH_MM_SS);
                endTime = DateUtils.parseDate(statisticQuery.getEndTime() + " 23:59:59", DateUtils.YYYY_MM_DD_HH_MM_SS);
            } catch (ParseException e) {
                log.error("获取时间格式错误");
                log.error("错误信息:{}",e);
            }
        }
        ContactStatisticsVO contactStatisticsVo = contactSiteService.getBehaviorData(startTime,endTime,statisticQuery);
        return AjaxResult.success(contactStatisticsVo);
    }

    /**
     * 删除站点活码
     */
    @RequestMapping("/remove")
    public AjaxResult remove(@RequestBody SiteRequest siteRequest) {
        contactAreaService.removeSite(siteRequest);
        return AjaxResult.success();
    }

    /**
     * 更新站点活码
     */
    @RequestMapping("/updateInfo")
    public AjaxResult update(@RequestBody SiteRequest siteRequest) {
        contactAreaService.updateInfo(siteRequest);
        return AjaxResult.success();
    }

    /**
     * 新增门店
     * @param contactAreaRequest
     * @return
     */
    @RequestMapping("/addStore")
    public AjaxResult addStore(@RequestBody ContactAreaRequest contactAreaRequest){
        contactAreaService.addStore(contactAreaRequest);
        return AjaxResult.success();
    }

    /**
     * 门店列表
     * @param contactAreaRequest
     * @return
     */
    @RequestMapping("/storeList")
    public TableDataInfo storeList(@RequestBody ContactAreaRequest contactAreaRequest){
        startPage();
        List<ContactStoreListVO> list = contactAreaService.storeList(contactAreaRequest);
        return getDataTable(list);
    }

    @RequestMapping("/removeStore")
    public AjaxResult removeStore(@RequestBody ContactAreaRequest contactAreaRequest){
        contactAreaService.removeStore(contactAreaRequest);
        return AjaxResult.success();
    }

    @RequestMapping("/updateStore")
    public AjaxResult updateStore(@RequestBody ContactAreaRequest contactAreaRequest){
        ContactArea contactArea = contactAreaService.updateStoreStatus(contactAreaRequest);
        if (ObjectUtil.isNotNull(contactArea)) {
            // 停用门店 删除底下配送员的所有活码
            tbWxDeliveryContactService.removeBySignId(contactArea.getSignId(), TypeConstants.SIGN_STORE);
        }
        return AjaxResult.success();
    }

    @RequestMapping("/getStoreById")
    public AjaxResult getStoreById(@RequestBody ContactAreaRequest contactAreaRequest){
        ContactStoreInfoVO contactAreaVO = contactAreaService.getStoreById(contactAreaRequest);
        return AjaxResult.success(contactAreaVO);
    }

    @RequestMapping("/storeImport")
    public AjaxResult storeImport(@RequestBody List<StoreImportRequest> importExcel){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Long loginUserId = Long.valueOf(loginUser.getUser().getUserId());
        String msg = contactAreaService.storeImport(importExcel,loginUserId,loginUser.getUser().isAdmin(),null);
        return AjaxResult.success(msg);
    }

    /**
     * 门店搜索-指定格式（配送员门店列表）：广东省-深圳市-零一部
     */
    @RequestMapping("/searchStore")
    public TableDataInfo selectStoreByTree(@RequestBody ContactAreaRequest contactAreaRequest){
        startPage();
        List<ContactStoreVO> list = contactAreaService.selectStoreByTree(contactAreaRequest);
        return getDataTable(list);
    }


    /**
     * 站点搜索-指定格式（角色-可见范围）：广东省-深圳市-零一站
     */
    @RequestMapping("/searchSite")
    public TableDataInfo selectSiteByTree(@RequestBody SiteRequest siteRequest){
        startPage();
        List<ContactSiteTreeVO> list = contactAreaService.selectSiteByTree(siteRequest);
        return getDataTable(list);
    }

    @RequestMapping("/allSiteList")
    public TableDataInfo allSiteList(@RequestBody SiteRequest siteRequest){
        startPage();
        List<ContactSiteTreeVO> list = contactAreaService.selectAllSiteList(siteRequest);
        return getDataTable(list);
    }

    /**
     * 获取全国省市区
     */
    @RequestMapping("/treeSelectAll")
    public AjaxResult treeSelectAll() {
        List<ContactArea> list = contactAreaService.selectAllAreaList();
        return AjaxResult.success(contactAreaService.buildAreaTreeSelect(list));
    }

    //
  /*  @RequestMapping("/treeSelect")
    public AjaxResult treeSelect(@RequestBody ContactAreaRequest contactAreaRequest) {
        List<ContactArea> list = contactAreaService.selectAllAreaList(contactAreaRequest);
        return AjaxResult.success(contactAreaService.buildAreaTreeSelect(list));
    }*/

    @RequestMapping("/getMediaIdTask")
    public void getMediaIdTask(@RequestBody TbWxCorpConfig tbWxCorpConfig){
        contactSiteService.getMediaIdTask();
    }

}
