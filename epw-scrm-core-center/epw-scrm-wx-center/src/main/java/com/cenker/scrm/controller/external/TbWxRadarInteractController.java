package com.cenker.scrm.controller.external;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.cenker.scrm.base.RemoteResult;
import com.cenker.scrm.config.RadarConfig;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.*;
import com.cenker.scrm.enums.BatchType;
import com.cenker.scrm.enums.OperateType;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.PageDomain;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.base.TableSupport;
import com.cenker.scrm.model.login.H5LoginUser;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.dto.login.CurrentUserDTO;
import com.cenker.scrm.pojo.dto.radar.QueryRadarStatisticsRankDTO;
import com.cenker.scrm.pojo.dto.radar.RadarBatchOperateDto;
import com.cenker.scrm.pojo.dto.radar.RadarStatisticsDTO;
import com.cenker.scrm.pojo.entity.subscr.BuSectionRadar;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.*;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.radar.*;
import com.cenker.scrm.service.ISysConfigService;
import com.cenker.scrm.service.contact.ITbWxContactService;
import com.cenker.scrm.service.external.ITbWxExtCustomerWorkService;
import com.cenker.scrm.service.external.ITbWxExtFollowUserWorkService;
import com.cenker.scrm.service.radar.*;
import com.cenker.scrm.service.subscr.IBuSectionRadarService;
import com.cenker.scrm.service.tag.ITbWxCorpTagService;
import com.cenker.scrm.util.*;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.cenker.scrm.util.DateUtils.YYYY_MM_DD;

/**
 * <AUTHOR>
 * @Date 2021/11/30
 * @Description 智能物料
 */
@RestController
@RequestMapping("/interact/radar")
@Slf4j
@AllArgsConstructor
public class TbWxRadarInteractController extends BaseController {

    private final ITbWxRadarInteractService radarService;
    private final ITbWxRadarContentService contentService;
    private final ITbWxRadarTagRuleService tagRuleService;
    private final TokenParseUtil tokenService;
    private final ITbWxCorpTagService corpTagService;
    private final ITbWxRadarContactService radarContactService;
    private final ITbWxRadarContentRecordService contentRecordService;
    private final ITbWxContactService tbWxContactService;
    private final RedisCache redisCache;
    private final ITbWxExtFollowUserWorkService tbWxExtFollowUserService;
    private final ITbWxExtCustomerWorkService tbWxExtCustomerWorkService;
    private final ISysConfigService sysConfigService;
    private final IBuSectionRadarService buSectionRadarService;



    /**
     * 新建智能物料
     */
    @RequestMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody InteractRadarVo radarVo) {
        log.info("【新建智能物料】添加事件接收json:{}", JSONUtil.toJsonStr(radarVo));
        // 应用层校验..包括corpId由应用层获取
        // 添加智能物料
        TbWxRadarInteract tbWxRadarInteract = new TbWxRadarInteract();
        BeanUtils.copyProperties(radarVo, tbWxRadarInteract);
        String id = SnowflakeIdUtil.getSnowIdStr();
        tbWxRadarInteract.setId(id);
        radarService.save(tbWxRadarInteract);
        log.info("【新建智能物料】添加智能物料成功");
        // 添加图文
        TbWxRadarContent tbWxRadarContent = radarVo.getTbWxRadarContent();
        tbWxRadarContent.setRadarId(id);
        tbWxRadarContent.setCreateBy(tbWxRadarInteract.getCreateBy());
        tbWxRadarContent.setCreateTime(tbWxRadarInteract.getCreateTime());
        tbWxRadarContent.setType(tbWxRadarInteract.getType());
        if (tbWxRadarInteract.getType() == TypeConstants.RADAR_TYPE_PDF) {
            // 由于pdf转换图片需要时间，如果转换未完成，暂时控制无法访问页面
            tbWxRadarContent.setShowStatus(false);
        }

        contentService.addContent(tbWxRadarContent);
        log.info("【新建智能物料】添加图文成功");
        // pdf拆分图片
        if (tbWxRadarInteract.getType() == TypeConstants.RADAR_TYPE_PDF) {
            contentService.pdf2Image(tbWxRadarContent);
        }
        // 如果存在客户标签规则
        if (StatusConstants.RADAR_CUSTOMER_TAG_TRUE.equals(tbWxRadarInteract.getCustomerTag())) {
            List<TbWxRadarTagRule> tbWxRadarTagRuleList = radarVo.getTbWxRadarTagRuleList();
            if (CollectionUtils.isEmpty(tbWxRadarTagRuleList)) {
                throw new CustomException("非法请求");
            }
            tagRuleService.addTagRule(tbWxRadarTagRuleList, id);
            log.info("【新建智能物料】添加客户标签规则成功");
        }
        return AjaxResult.success();
    }

    /**
     * 智能物料列表 2023-07-04 新增pdf切图 在切图中禁用侧边栏及选择智能物料的展示 及后台列表禁用复制链接
     */
    @RequestMapping("/list")
    public TableDataInfo list(@RequestBody InteractRadarVo radarVo) {
        log.info("【智能物料列表】入参:{}", JSONUtil.toJsonStr(radarVo));
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, radarVo);

        radarVo.setPage(RadarConfig.getContentPage());
        startPage();
        List<InteractRadarVo> radarList = radarService.getRadarList(radarVo);
        log.info("【智能物料列表】查询结果数量:{}", radarList.size());
        return getDataTable(radarList);
    }


    /**
     * 获取智能物料统计排行榜
     */
    @RequestMapping("/getRadarStatisticsRank")
    public TableDataInfo getRadarStatisticsRank(QueryRadarStatisticsRankDTO dto) {
        startPage();
        if(Objects.equals(dto.getToday(),true)){
            dto.setBeginTime(DateUtils.getDate());
            dto.setEndTime(DateUtils.getDate());
        }
        if (Objects.equals(dto.getSeven(),true)){
            // 最近七天,年月日字符串
            dto.setBeginTime(DateUtils.parseDateToStr(YYYY_MM_DD,DateUtils.addDays(DateUtils.getNowDate(), -7)));
            dto.setEndTime(DateUtils.getDate());
        }
        if (Objects.equals(dto.getThirty(),true)) {
            // 最近30天,年月日字符串
            dto.setBeginTime(DateUtils.parseDateToStr(YYYY_MM_DD,DateUtils.addDays(DateUtils.getNowDate(), -30)));
            dto.setEndTime(DateUtils.getDate());
        }
        return getDataTable(radarService.getRadarStatisticsRank(getCurrentUser(),dto));
    }
    /**
     * 获取当前用户
     */
    protected CurrentUserDTO getCurrentUser() {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        return new CurrentUserDTO(user.getCorpId(),user.getUserId());
    }
    /**
     * 删除智能物料
     */
    @RequestMapping("/removeRadar")
    public AjaxResult remove(@RequestBody InteractRadarVo radarVo) {
        String userId = radarVo.getUserId();
        String corpId = radarVo.getCorpId();
        String id = radarVo.getId();
        // 查询智能物料状态
        TbWxRadarInteract radar = radarService.getOne(new LambdaQueryWrapper<TbWxRadarInteract>()
                .eq(TbWxRadarInteract::getId, id)
                .eq(TbWxRadarInteract::getDelFlag, StatusConstants.DEL_FLAG_FALSE)
                .eq(TbWxRadarInteract::getCorpId, corpId));
        if (ObjectUtil.isNull(radar)) {
            return AjaxResult.error("该数据已被删除，请刷新数据重试");
        }

        LogUtil.logOperDesc(radarVo.getTitle());

        // 逻辑删除
        radar.setDelFlag(StatusConstants.DEL_FLAG_TRUE);
        radar.setUpdateBy(userId);
        radarService.update(radar, new LambdaQueryWrapper<TbWxRadarInteract>().eq(TbWxRadarInteract::getId, id));
        // 查询文章状态
        TbWxRadarContent content = contentService.getOne(new LambdaQueryWrapper<TbWxRadarContent>().eq(TbWxRadarContent::getRadarId, radar.getId()));
        if (ObjectUtil.isNull(content)) {
            log.error("【智能物料】内容数据被删除");
            return AjaxResult.success();
        }
        // 删除智能物料缓存
        redisCache.expire(CacheKeyConstants.RADAR_CONTENT_H5 + content.getId(), 0);

        // 逻辑删除
        content.setUpdateBy(userId);
        content.setDelFlag(StatusConstants.DEL_FLAG_TRUE);
        // 同时删除文章
        contentService.update(content, new LambdaQueryWrapper<TbWxRadarContent>().eq(TbWxRadarContent::getRadarId, id));

        List<TbWxRadarContact> list = radarContactService.list(new LambdaQueryWrapper<TbWxRadarContact>()
                .eq(TbWxRadarContact::getContentId, content.getId())
                .eq(TbWxRadarContact::getDelFlag, StatusConstants.DEL_FLAG_FALSE));
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> contactIds = list.stream().map(TbWxRadarContact::getContactId).distinct().collect(Collectors.toList());
            for (TbWxRadarContact tbWxRadarContact : list) {
                tbWxRadarContact.setDelFlag(StatusConstants.DEL_FLAG_TRUE_INT);
                tbWxRadarContact.setUpdateBy(userId);
            }
            radarContactService.updateBatchById(list);
            tbWxContactService.deleteTbWxContactByIds(contactIds, corpId);
        }

        // 删除关联的栏目发布记录
        buSectionRadarService.lambdaUpdate().set(BuSectionRadar::getDelFlag, StatusConstants.DEL_FLAG_TRUE)
                .set(BuSectionRadar::getEndTime, new Date())
                .eq(BuSectionRadar::getRadarId, radar.getId())
                .eq(BuSectionRadar::getDelFlag, StatusConstants.DEL_FLAG_FALSE).update();
        return AjaxResult.success();
    }

    @RequestMapping("/{radarId}")
    public AjaxResult getById(@PathVariable("radarId") String radarId) {
        log.info("【获取智能物料详情】入参:{}", radarId);
        TbWxRadarInteract radar = radarService.getOneById(radarId);
        if (ObjectUtil.isNull(radar)) {
            log.warn("【获取智能物料详情】数据不存在:{}", radarId);
            throw new CustomException("数据已被删除或不存在");
        }
        InteractRadarVo interactRadarVo = new InteractRadarVo();
        BeanUtils.copyProperties(radar, interactRadarVo);
        // 查询图文类型
        TbWxRadarContent content = contentService.getOne(new LambdaQueryWrapper<TbWxRadarContent>().eq(TbWxRadarContent::getRadarId, radarId));
        log.info("【获取智能物料详情】获取图文内容完成，contentId:{}", content.getId());
        // 链接
        content.setUrl(RadarConfig.getContentPage() + "?id=" + content.getId());
        interactRadarVo.setTbWxRadarContent(content);
        // 查询客户标签
        if (StatusConstants.RADAR_CUSTOMER_TAG_TRUE.equals(interactRadarVo.getCustomerTag())) {
            List<TbWxRadarTagRule> list = tagRuleService.list(new LambdaQueryWrapper<TbWxRadarTagRule>().eq(TbWxRadarTagRule::getRadarId, radarId));
            if (CollectionUtils.isNotEmpty(list)) {
                for (TbWxRadarTagRule tbWxRadarTagRule : list) {
                    String[] tagIds = tbWxRadarTagRule.getTagId().split(",");
                    // 查询标签名
                    List<String> tagNames = corpTagService.selectTbWxCorpTagNameList(radar.getCorpId(), tagIds);
                    tbWxRadarTagRule.setTagName(StringUtils.join(tagNames, ","));
                    tbWxRadarTagRule.setTagNameList(tagNames);
                }
                interactRadarVo.setTbWxRadarTagRuleList(list);
            }
            log.info("【获取智能物料详情】获取客户标签完成");
        }
        log.info("【获取智能物料详情】获取智能物料详情完成");
        return AjaxResult.success(interactRadarVo);
    }

    @Transactional
    @RequestMapping("/edit")
    public AjaxResult edit(@RequestBody InteractRadarVo radarVo) {
        log.info("【编辑智能物料】入参:{}", JSONUtil.toJsonStr(radarVo));
        // 查询智能物料状态
        TbWxRadarInteract radar = radarService.getOne(new LambdaQueryWrapper<TbWxRadarInteract>()
                .eq(TbWxRadarInteract::getId, radarVo.getId())
                .eq(TbWxRadarInteract::getDelFlag, StatusConstants.DEL_FLAG_FALSE));
        if (ObjectUtil.isNull(radar)) {
            log.warn("【编辑智能物料】数据不存在:{}", radarVo.getId());
            throw new CustomException("数据已被删除或不存在");
        }
        BeanUtils.copyProperties(radarVo, radar);
        // 更新智能物料
        radarService.updateById(radar);
        log.info("【编辑智能物料】更新智能物料主表成功");
        TbWxRadarContent content = contentService.getOne(new LambdaQueryWrapper<TbWxRadarContent>()
                .eq(TbWxRadarContent::getRadarId, radar.getId())
                .eq(TbWxRadarContent::getDelFlag, StatusConstants.DEL_FLAG_FALSE));
        if (Objects.isNull(content)) {
            log.warn("【编辑智能物料】图文数据已被删除或不存在，radarId:{}", radar.getId());
            throw new CustomException("数据已被删除或不存在");
        }
        // 更新图文
        TbWxRadarContent tbWxRadarContent = radarVo.getTbWxRadarContent();
        tbWxRadarContent.setUpdateBy(radar.getUpdateBy());
        tbWxRadarContent.setUpdateTime(new Date());
        // 容错设置默认图
        if (StringUtils.isEmpty(tbWxRadarContent.getCover())) {
            tbWxRadarContent.setCover(redisCache.getCacheObject(DefaultConstants.RADAR_CONTENT_COVER));
        }
        tbWxRadarContent.setId(content.getId());
        contentService.updateById(tbWxRadarContent);
        log.info("【编辑智能物料】更新图文成功");
        // pdf拆分图片
        if (radar.getType() == TypeConstants.RADAR_TYPE_PDF) {
            // 由于pdf转换图片需要时间，如果转换未完成，暂时控制无法访问页面
            tbWxRadarContent.setShowStatus(false);
            contentService.pdf2Image(tbWxRadarContent);
        }
        // 删除客户标签->重新添加或关闭
        List<TbWxRadarTagRule> ruleList = tagRuleService.list(new LambdaQueryWrapper<TbWxRadarTagRule>().eq(TbWxRadarTagRule::getRadarId, radar.getId()));
        if (CollectionUtils.isNotEmpty(ruleList)) {
            tagRuleService.removeByIds(ruleList.stream().map(TbWxRadarTagRule::getId).collect(Collectors.toList()));
            log.info("【编辑智能物料】删除客户标签规则成功");
        }
        if (StatusConstants.RADAR_CUSTOMER_TAG_TRUE.equals(radarVo.getCustomerTag())) {
            List<TbWxRadarTagRule> tbWxRadarTagRuleList = radarVo.getTbWxRadarTagRuleList();
            if (CollectionUtils.isEmpty(tbWxRadarTagRuleList)) {
                throw new CustomException("非法请求");
            }
            tagRuleService.addTagRule(tbWxRadarTagRuleList, radar.getId());
            log.info("【编辑智能物料】添加客户标签规则成功");
        }
        // 清除页面缓存

        redisCache.deleteObject(CacheKeyConstants.RADAR_CONTENT_H5 + content.getId());
        log.info("【编辑智能物料】清除页面缓存成功");
        log.info("【编辑智能物料】编辑智能物料成功");
        return AjaxResult.success();
    }

    /**
     * h5获取文章详情
     *
     * @param id      文章id
     * @return 文章内容对象
     */
    @RequestMapping("/getRadarContent")
    public AjaxResult getRadarContent(String id, HttpServletRequest request) {
        RadarContentVO result = redisCache.getCacheObject(CacheKeyConstants.RADAR_CONTENT_H5 + id);
        if (result == null) {
            // 获取文章信息
            result = contentService.getRadarContentH5(id);
            if (result == null) {
                return AjaxResult.success(new AjaxResult(HttpStatus.NOT_FOUND, "无法找到内容"));
            }
            redisCache.setCacheObject(CacheKeyConstants.RADAR_CONTENT_H5 + id, result, 7200, TimeUnit.SECONDS);
        }
        try {
            MpWxUser mpWxUser = tokenService.getLoginUserH5Wx(request).getMpWxUser();
            result.setForwardUser(mpWxUser.getId());
        } catch (Exception e) {
            log.info("用户未登录");
        }
        // 阅读量
        int realReadNum = contentRecordService.count(new LambdaQueryWrapper<TbWxRadarContentRecord>()
                .eq(TbWxRadarContentRecord::getContentId, result.getId()));
        result.setBaseReadNum(realReadNum + result.getBaseReadNum());
        return AjaxResult.success(result);
    }

    /**
     * h5获取员工二维码
     */
    @RequestMapping("/getRadarContactInfo")
    public Result<RadarContactVo> getRadarContactInfo(String id, String staffId) {
        log.info("【获取成员名片】物料内容id：{}，员工id：{}", id, staffId);
        // 初始化结果
        RadarContactVo result = new RadarContactVo();
        result.setContactStatus(StatusConstants.CLOSE_FLAG);
        result.setAddedEnterpriseWeChat(CommonConstants.NUM_0);

        // 设置客户认证链接
        String authUrl = sysConfigService.selectConfigByKey(ConfigConstant.CUSTOMER_AUTH_LINK_KEY);
        result.setAuthLink(authUrl);
        log.info("【获取成员名片】客户认证链接：{}", authUrl);
        // 查询智能物料
        TbWxRadarInteract radar = radarService.selectRadarByContentId(id);
        log.info("【获取成员名片】物料信息：{}", JSONUtil.toJsonStr(radar));
        // 查询好友数量，并设置认证状态
        Integer friends = determineRelationships(null, result);
        result.setFriends(friends);
        log.info("【获取成员名片】客户认证状态：{}, 好友数量：{}", result.getIsAuth(), friends);
        if (Objects.isNull(radar)
                || !StatusConstants.OPEN_FLAG.equals(radar.getContactStatus())) {
            log.warn("【获取成员名片】成员名片未开启或员工id为空！物料id：{}，员工id：{}", id, staffId);
            return Result.success("操作成功", result);
        }

        result.setContactStatus(StatusConstants.OPEN_FLAG);

        TbWxRadarContact radarContact = radarContactService.getRadarContactByStaffIdAndCorpId(radar.getCorpId(), staffId, id);
        if (radarContact == null || StrUtil.isBlank(radarContact.getContactCode())) {
            log.warn("【获取成员名片】未获得成员活码！物料id：{}，员工id：{}", id, staffId);
            // 放置默认的成员活码
            String defaultContactCode = sysConfigService.selectConfigByKey(ConfigConstant.DEFAULT_STAFF_RADAR_INTERACT);
            log.info("【获取成员名片】默认成员名片信息：{}", defaultContactCode);
            result.setContactCode(defaultContactCode);
        } else {
            log.info("【获取成员名片】成员名片信息：{}", JSONUtil.toJsonStr(radarContact));
            result.setContactCode(radarContact.getContactCode());
            result.setStaffImg(radarContact.getStaffImg());
            result.setStaffName(radarContact.getStaffName());

            Integer relationships = determineRelationships(staffId, result);
            result.setAddedEnterpriseWeChat(relationships > 0? CommonConstants.NUM_1 : CommonConstants.NUM_0);

            log.info("【获取成员名片】成员【员工ID：{}】的名片信息：{}", staffId, JSONUtil.toJsonStr(result));
        }
        return Result.success("操作成功", result);
    }

    /**
     * 判断当前登录的用户是否员工的好友
     *
     * @param staffId
     * @param result
     * @return
     */
    private Integer determineRelationships(String staffId, RadarContactVo result) {
        H5LoginUser h5LoginUser = tokenService.getLoginUserH5Wx(ServletUtils.getRequest());
        if (ObjectUtil.isNull(h5LoginUser) || Objects.isNull(h5LoginUser.getMpWxUser()) || StrUtil.isBlank(h5LoginUser.getMpWxUser().getUnionId())) {
            return CommonConstants.NUM_0;
        }

        String unionId = h5LoginUser.getMpWxUser().getUnionId();

        TbWxExtCustomer customer = tbWxExtCustomerWorkService.lambdaQuery()
                .eq(TbWxExtCustomer::getUnionId, unionId)
                .eq(TbWxExtCustomer::getStatus, CommonConstants.STATUS_NORMAL)
                .last("limit 1").one();
        if (ObjectUtil.isNull(customer)) {
            return CommonConstants.NUM_0;
        }
        if (result != null) {
            result.setIsAuth(customer.getIsAuth());
        }
        // 可能存在离职员工、删除员工的数据，需要判断员工状态
//        LambdaQueryChainWrapper<TbWxExtFollowUser> wrapper = tbWxExtFollowUserService.lambdaQuery()
//                .eq(TbWxExtFollowUser::getExternalUserId, customer.getExternalUserId())
//                .eq(TbWxExtFollowUser::getStatus, CommonConstants.STATUS_NORMAL);
//        if (StringUtils.isNotEmpty(staffId)) {
//            wrapper.eq(TbWxExtFollowUser::getUserId, staffId);
//        }
//        Integer count = wrapper.count();
        Integer count = tbWxExtFollowUserService.getFrients(customer.getExternalUserId(), staffId, CommonConstants.STATUS_NORMAL);
        return count;
    }

    /**
     * h5添加统计数据
     */
    @RequestMapping("/saveReadRecord")
    public AjaxResult saveReadRecord(@RequestBody TbWxRadarContentRecord record, HttpServletRequest request) {
        H5LoginUser h5LoginUser = tokenService.getLoginUserH5Wx(request);
        log.info("保存智能物料访问数据，登录信息：{}", JSONUtil.toJsonStr(h5LoginUser));
        MpWxUser mpWxUser = h5LoginUser.getMpWxUser();
        log.info("保存智能物料访问数据，微信用户信息：{}", JSONUtil.toJsonStr(mpWxUser));
        if(null != mpWxUser){
            // 客户id
            record.setCustomerId(mpWxUser.getId());
            record.setCreateTime(DateUtils.getNowDate());
            if (!StatusConstants.OPEN_FLAG.equals(record.getForwardTo())) {
                // 如果有转发者
                if (StringUtils.isNotEmpty(record.getForwardUser())) {
                    record.setForwardUser(radarService.getForwardUserById(record.getForwardUser()));
                }
            }
            // 转发时前端会调此接口 返回id 结束时会携带该id进来修改
            contentRecordService.saveOrUpdate(record);

            // 仅当员工生成的链接才发送行为通知
//            if (StringUtils.isNotBlank(record.getStaffId())) {
            radarService.sendMessageByReadRecord(record, mpWxUser);
//            }

            return AjaxResult.success("操作成功", record.getId());
        }
        return AjaxResult.success();
    }

    /**
     * 管理后台智能物料统计数据
     */
    @RequestMapping("/getRadarStatistics/{radarId}")
    public AjaxResult getRadarStatistics(@PathVariable("radarId") String radarId, RadarStatisticsDTO radarStatisticsDTO) {
        RadarStatisticsVO result = null;
        Date startTime;
        Date endTime = DateUtils.getNowDate();
        if (radarStatisticsDTO.getDefaultRule()) {
            result = radarService.getRadarStatistics(radarId, null, endTime);
        } else if (radarStatisticsDTO.getFifteen()) {
            // 最近十五天
            startTime = DateUtils.addDays(endTime, -15);
            result = radarService.getRadarStatistics(radarId, startTime, endTime);
        }
        return AjaxResult.success(result);
    }

    /**
     * 智能物料图文链接客户数据
     */
    @RequestMapping("/getRadarReadRecordStatistics/{radarId}")
    public TableDataInfo getRadarReadRecordStatistics(@PathVariable("radarId") String radarId, RadarStatisticsDTO radarStatisticsDTO) {
        TbWxRadarInteract radar = radarService.getOne(new LambdaQueryWrapper<TbWxRadarInteract>()
                .eq(TbWxRadarInteract::getId, radarId)
                .eq(TbWxRadarInteract::getDelFlag, StatusConstants.DEL_FLAG_FALSE));
        if (radar == null) {
            return getDataTable(new ArrayList<>());
        }
        TbWxRadarContent content = contentService.getOne(new LambdaQueryWrapper<TbWxRadarContent>()
                .eq(TbWxRadarContent::getRadarId, radarId));
        if (radarStatisticsDTO == null) {
            radarStatisticsDTO = new RadarStatisticsDTO();
        }
        radarStatisticsDTO.setCorpId(radar.getCorpId());
        radarStatisticsDTO.setContentId(content.getId());
        startPage();
        List<RadarCustomerStatisticsVO> list = radarService.getRadarReadRecordStatistics(radarStatisticsDTO);
        return getDataTable(list);
    }


    /**
     * 客户点击记录详情
     *
     * @param customerId 客户id
     */
    @RequestMapping("/getRadarReadRecordDetail/{customerId}")
    public TableDataInfo getRadarReadRecordDetail(@PathVariable("customerId") String customerId, @RequestParam String contentId) {
        startPage();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        return getDataTable(contentRecordService.getRadarReadRecordDetail(customerId, contentId, pageNum, pageSize));
    }

    /**
     * 聊天素材/工作台智能物料列表
     */
    @RequestMapping("/chatList")
    public TableDataInfo chatList(@RequestBody InteractRadarVo radarVo) {
        radarVo.setPage(RadarConfig.getContentPage());
        startPage();
        return getDataTable(radarService.getRadarChatList(radarVo));
    }


    /**
     * 聊天素材智能物料统计数据
     */
    @RequestMapping("/getRadarChatStatistics/{radarId}")
    public AjaxResult getRadarStatistics(@PathVariable("radarId") String radarId) {
        return AjaxResult.success(radarService.getRadarChatStatistics(radarId));
    }

    /**
     * 聊天素材智能物料链接数据
     */
    @RequestMapping("/getRadarReadRecordStatistics4Chat/{radarId}")
    public TableDataInfo getRadarReadRecordStatistics4Chat(@PathVariable("radarId") String radarId) {
        startPage();
        List<RadarCustomerStatisticsVO> list = Lists.newArrayList();
        TbWxRadarInteract radar = radarService.getOne(new LambdaQueryWrapper<TbWxRadarInteract>()
                .eq(TbWxRadarInteract::getId, radarId)
                .eq(TbWxRadarInteract::getDelFlag, StatusConstants.DEL_FLAG_FALSE));
        if (radar == null) {
            return getDataTable(list);
        }
        TbWxRadarContent content = contentService.getOne(new LambdaQueryWrapper<TbWxRadarContent>()
                .eq(TbWxRadarContent::getRadarId, radarId));
        RadarStatisticsDTO radarStatisticsDTO = new RadarStatisticsDTO();
        radarStatisticsDTO.setCorpId(radar.getCorpId());
        radarStatisticsDTO.setContentId(content.getId());
        list = radarService.getRadarReadRecordStatistics(radarStatisticsDTO);
        return getDataTable(list);
    }

    /**
     * 聊天素材智能物料链接点击记录详情
     */
    @RequestMapping("/getRadarReadRecordDetail4Chat/{customerId}")
    public TableDataInfo getRadarReadRecordDetail4Chat(@PathVariable("customerId") String customerId, @RequestParam String contentId) {
        startPage();
        return getDataTable(contentRecordService.getRadarReadRecordDetail4Chat(customerId, contentId));
    }

    /**
     * 提取公众号文章
     */
    @RequestMapping("/extract/radarContent")
    public AjaxResult extractRadarContent(@RequestBody TbWxRadarContent content) {
        log.info("【智能物料】提取公众号文章链接：【{}】", content.getUrl());
        radarService.extractRadarContent(content, true);
        return AjaxResult.success(content);
    }

    /**
     * 提取链接标题、描述、封面数据
     */
    @RequestMapping("/extract/linkData")
    public AjaxResult extractLinkData(@RequestBody TbWxRadarContent content) {
        log.info("【智能物料】提取链接：【{}】", content.getUrl());
        radarService.linkData(content);
        return AjaxResult.success(content);
    }

    /**
     * 获取所有企业物料 给其他如渠道活码功能使用
     */
    @RequestMapping("/getRadarSource")
    public TableDataInfo getRadarSource(@RequestBody InteractRadarVo radarVo) {
        // 目前获取所有企业物料
        startPage();
        radarVo.setPage(RadarConfig.getContentPage());
        List<InteractRadarVo> list = radarService.getRadarSource(radarVo);
        return getDataTable(list);
    }

    @RequestMapping("/batchOperate")
    public AjaxResult batchOperate(@RequestBody RadarBatchOperateDto batchOperateDto) {
        List<String> radarIdList;

        if (BatchType.ALL.name().equals(batchOperateDto.getBatchType())) {
            InteractRadarVo radarVo = new InteractRadarVo();
            radarVo.setContentTitle(batchOperateDto.getContentTitle());
            radarVo.setTitle(batchOperateDto.getTitle());
            radarVo.setCreateBy(batchOperateDto.getCreateBy());
            radarVo.setCategoryScope(batchOperateDto.getCategoryScope());
            radarVo.setCategoryId(batchOperateDto.getCategoryId());
            radarVo.setType(batchOperateDto.getType());
            radarVo.setScope(1);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            filterCondition(loginUser, radarVo);
            List<InteractRadarVo> radarList = radarService.getRadarList(radarVo);
            radarIdList = radarList.stream().map(InteractRadarVo::getId).collect(Collectors.toList());
        } else {
            radarIdList = StrUtil.splitTrim(batchOperateDto.getIds(), StrUtil.COMMA);
        }

        if (CollectionUtils.isEmpty(radarIdList)) {
            return AjaxResult.success();
        }

        if (OperateType.SHOW.name().equals(batchOperateDto.getOperateType())) {
            radarService.lambdaUpdate()
                    .set(TbWxRadarInteract::getShowStatus, CommonConstants.YES)
                    .set(TbWxRadarInteract::getUpdateBy, batchOperateDto.getLoginUserId())
                    .set(TbWxRadarInteract::getUpdateTime, new Date())
                    .in(TbWxRadarInteract::getId, radarIdList)
                    .update();
        } else if (OperateType.HIDE.name().equals(batchOperateDto.getOperateType())) {
            radarService.lambdaUpdate()
                    .set(TbWxRadarInteract::getShowStatus, CommonConstants.NO)
                    .set(TbWxRadarInteract::getUpdateBy, batchOperateDto.getLoginUserId())
                    .set(TbWxRadarInteract::getUpdateTime, new Date())
                    .in(TbWxRadarInteract::getId, radarIdList)
                    .update();
        } else if (OperateType.REMOVE.name().equals(batchOperateDto.getOperateType())) {
            radarService.lambdaUpdate()
                    .set(TbWxRadarInteract::getDelFlag, CommonConstants.DELETED)
                    .set(TbWxRadarInteract::getUpdateBy, batchOperateDto.getLoginUserId())
                    .set(TbWxRadarInteract::getUpdateTime, new Date())
                    .in(TbWxRadarInteract::getId, radarIdList)
                    .update();
            contentService.lambdaUpdate()
                    .set(TbWxRadarContent::getDelFlag, CommonConstants.DELETED)
                    .set(TbWxRadarContent::getUpdateBy, batchOperateDto.getLoginUserId())
                    .set(TbWxRadarContent::getUpdateTime, new Date())
                    .in(TbWxRadarContent::getRadarId, radarIdList)
                    .update();
            // 批量清除缓存
            List<String> redisKeys = radarIdList.stream().map(id -> CacheKeyConstants.RADAR_CONTENT_H5 + id).collect(Collectors.toList());
            redisCache.deleteObject(redisKeys);
        }

        return AjaxResult.success();
    }

    @RequestMapping("/updateNamingRule")
    public RemoteResult<Boolean> updateNamingRule(@RequestBody Map<String, String> params) {
        String namingRule = params.get("namingRule");
        if (StrUtil.isNotBlank(namingRule)) {
            radarService.lambdaUpdate()
                    .set(TbWxRadarInteract::getNamingRule, namingRule)
                    .set(TbWxRadarInteract::getUpdateTime, new Date())
                    .eq(TbWxRadarInteract::getDelFlag, StatusConstants.DEL_FLAG_FALSE)
                    .update();
        }

        return RemoteResult.success();
    }

    @GetMapping("/countRadarByCategoryId")
    public RemoteResult<Integer> countRadarByCategoryId(@RequestParam("categoryId") String categoryId) {
        Integer count = radarService.lambdaQuery()
                .eq(TbWxRadarInteract::getCategoryId, categoryId)
                .eq(TbWxRadarInteract::getDelFlag, StatusConstants.DEL_FLAG_FALSE).count();
        return RemoteResult.data(count);
    }
}
