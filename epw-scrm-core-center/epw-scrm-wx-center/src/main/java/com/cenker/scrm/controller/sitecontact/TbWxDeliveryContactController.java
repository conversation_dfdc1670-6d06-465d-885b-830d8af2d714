package com.cenker.scrm.controller.sitecontact;

import com.cenker.scrm.pojo.request.DeliveryUserContactRequest;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.sitecontact.ContactConfigVO;
import com.cenker.scrm.pojo.vo.sitecontact.DeliveryContactVO;
import com.cenker.scrm.pojo.vo.sitecontact.WorkbenchContactVO;
import com.cenker.scrm.service.sitecontact.IContactSiteService;
import com.cenker.scrm.service.sitecontact.ITbWxContactConfigService;
import com.cenker.scrm.service.sitecontact.ITbWxDeliveryContactService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/10/27
 * @Description 配送员活码
 */
@RestController
@RequestMapping("/delivery/contact")
@RequiredArgsConstructor
public class TbWxDeliveryContactController {

    private final ITbWxContactConfigService tbWxContactConfigService;
    private final ITbWxDeliveryContactService tbWxDeliveryContactService;
    private final IContactSiteService contactSiteService;

    /**
     * 获取加我企微信息
     */
    @RequestMapping("/getContactIndex")
    public AjaxResult getContactIndex(@RequestBody DeliveryUserContactRequest deliveryUserContactRequest) {
        WorkbenchContactVO workbenchContactVO = new WorkbenchContactVO();
        // 获取背景图
        ContactConfigVO contactConfig = tbWxContactConfigService.getContactConfig(deliveryUserContactRequest);
        // 获取活码
        if (deliveryUserContactRequest.getGetCode()) {
            deliveryUserContactRequest.setRefreshRate(contactConfig.getRefreshRate());
            DeliveryContactVO deliveryContactVO = contactSiteService.getEffectiveCode(deliveryUserContactRequest);
            workbenchContactVO.setCodeId(deliveryContactVO.getCodeId());
            workbenchContactVO.setQrCode(deliveryContactVO.getQrCode());
        }
        workbenchContactVO.setBackImg(contactConfig.getBackImg());
        return AjaxResult.success(workbenchContactVO);
    }

    /**
     * 轮询查看二维码的失效情况
     */
    @RequestMapping("/expireStatus")
    public AjaxResult expireStatus(@RequestBody DeliveryUserContactRequest deliveryUserContactRequest) {
        DeliveryContactVO deliveryContactVO = tbWxDeliveryContactService.getExpireStatus(deliveryUserContactRequest);
        return AjaxResult.success(deliveryContactVO);
    }

    /**
     * 获取最新有效活码（相当于手动刷新）
     */
    @RequestMapping("/refreshContact")
    public AjaxResult refreshContact(@RequestBody DeliveryUserContactRequest deliveryUserContactRequest) {
        // 获取背景图
        DeliveryContactVO deliveryContactVO = contactSiteService.refreshContactCode(deliveryUserContactRequest);
        WorkbenchContactVO workbenchContactVO = new WorkbenchContactVO();
        workbenchContactVO.setCodeId(deliveryContactVO.getCodeId());
        workbenchContactVO.setQrCode(deliveryContactVO.getQrCode());
        return AjaxResult.success(workbenchContactVO);
    }

}
