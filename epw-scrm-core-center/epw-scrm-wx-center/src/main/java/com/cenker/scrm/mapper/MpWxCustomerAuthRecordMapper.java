package com.cenker.scrm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cenker.scrm.entity.MpWxCustomerAuthRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MpWxCustomerAuthRecordMapper extends BaseMapper<MpWxCustomerAuthRecord> {
    List<MpWxCustomerAuthRecord> selectAuthRecoreds(@Param("lastDate") String lastDate, @Param("now") String now);
}