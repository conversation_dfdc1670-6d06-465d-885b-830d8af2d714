package com.cenker.scrm.controller.contentcache;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.constants.CommonConstants;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.pojo.dto.cachecontent.CacheContentDTO;
import com.cenker.scrm.pojo.entity.cachecontent.TbWxCacheContent;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.service.cachecontent.ITbWxCacheContentService;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 内容缓存接口
 * @date 2023/5/22 18:26
 */
@RestController
@RequestMapping("/tp/cachecontent")
@RequiredArgsConstructor
public class TbWxCacheContentController extends BaseController {
    private final TokenParseUtil tokenService;


    private final ITbWxCacheContentService cacheContentService;


    @PostMapping("/add")
    public AjaxResult add(@RequestBody CacheContentDTO cacheContentDTO) {
        SysUser sysUser = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        boolean result = cacheContentService.addCacheContent(sysUser, cacheContentDTO);
        if (result) {
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    @GetMapping("/list/{type}")
    public AjaxResult list(@PathVariable("type") String type) {
        startPage();
        String corpId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpId();
        LambdaQueryWrapper<TbWxCacheContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbWxCacheContent::getCorpId, corpId).eq(TbWxCacheContent::getType, type);
        queryWrapper.orderByDesc(TbWxCacheContent::getCreateTime);
        List<TbWxCacheContent> list = cacheContentService.list(queryWrapper);
        return AjaxResult.success(list);
    }

    @GetMapping("/deleteById/{id}")
    public AjaxResult deleteById(@PathVariable("id") String id) {
        boolean result = cacheContentService.removeById(id);
        if (result) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.error("删除失败");
    }

    @PostMapping("/updateByCondition/{id}")
    public AjaxResult updateByCondition(@PathVariable("id") String id, @RequestBody CacheContentDTO cacheContentDTO) {
        boolean result = false;
        if (CommonConstants.LINK.equals(cacheContentDTO.getType())) {
            result = cacheContentService.updateLink(id, cacheContentDTO.getValue());
        } else if (CommonConstants.MINIPROGRAM.equals(cacheContentDTO.getType())) {
            result = cacheContentService.updateMiniprogram(id, cacheContentDTO.getValue());
        } else {
            throw new CustomException(ErrCodeEnum.CONTENT_TYPE_ERROR_OR_NOT_SUPPORT_TYPE.getMessage());
        }
        if (result) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

}
