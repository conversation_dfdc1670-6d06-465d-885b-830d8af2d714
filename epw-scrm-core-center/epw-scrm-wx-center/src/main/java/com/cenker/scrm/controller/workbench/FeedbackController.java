package com.cenker.scrm.controller.workbench;

import cn.hutool.core.collection.CollectionUtil;
import com.cenker.scrm.biz.manager.WxMqSendMessageManager;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.pojo.dto.kf.FeedbackContentDTO;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.service.contact.ITbWxUserService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/7/6
 * @Description
 */
@Slf4j
@RequestMapping("/work/feedback")
@RestController
public class FeedbackController extends BaseController {
    @Autowired
    private WxMqSendMessageManager mqSendMessageManager;
    @Autowired
    private ITbWxUserService tbWxUserService;

    @PostMapping("/submit")
    public AjaxResult add(@RequestBody FeedbackContentDTO contentDTO) {
        // 查询企业管理员
        List<TbWxUser> list = tbWxUserService.selectAdmin4Corp(contentDTO);
        if (CollectionUtil.isNotEmpty(list)) {
            WxCpMessage wxCpMessage = new WxCpMessage();
            // 开启id转译
            wxCpMessage.setEnableIdTrans(true);
            wxCpMessage.setMsgType("text");
            wxCpMessage.setToUser(list.stream().map(TbWxUser::getUserid).distinct().collect(Collectors.joining("|")));
            wxCpMessage.setContent("【收到一条工作台需求】\n" +
                    "$userName=" + contentDTO.getUserId() + "$在成客SCRM个人工作台提交了一条需求申请\n\n" +
                    "提交板块：" + contentDTO.getSubmitFromName() + "\n" +
                    "内容：" + contentDTO.getContent());
            // applicationEventPublisher.publishEvent(new SendAgentMessageEvent(wxCpMessage, contentDTO.getCorpId()));
            mqSendMessageManager.sendAgentMessage(wxCpMessage);
        }
        return AjaxResult.success();
    }
}
