package com.cenker.scrm.controller.sop;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cenker.scrm.annotation.RedisLockAspect;
import com.cenker.scrm.biz.sop.JourneySopBiz;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.ApprovalStatusEnum;
import com.cenker.scrm.enums.ApprovalTypeEnum;
import com.cenker.scrm.enums.DateStyle;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.entity.wechat.sop.SopInfo;
import com.cenker.scrm.pojo.exception.CustomException;
import com.cenker.scrm.pojo.request.sop.ConditionSopQueryRequest;
import com.cenker.scrm.pojo.request.sop.JourneySopQueryRequest;
import com.cenker.scrm.pojo.request.sop.JourneySopRequest;
import com.cenker.scrm.pojo.vo.approval.ApprovalVO;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.message.MassMessageContentVO;
import com.cenker.scrm.pojo.vo.message.MassMessageSenderListVO;
import com.cenker.scrm.pojo.vo.sop.*;
import com.cenker.scrm.service.message.ITbWxMassMessageInfoService;
import com.cenker.scrm.service.sop.ISopContentInfoService;
import com.cenker.scrm.service.sop.ISopInfoService;
import com.cenker.scrm.util.*;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/25
 * @Description 旅程sop 控制层
 */
@RestController
@RequestMapping("/sop/journey")
@RequiredArgsConstructor
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class JourneySopController extends BaseController {

    private final JourneySopBiz journeySopBiz;
    private final ISopInfoService sopInfoService;
    private final ISopContentInfoService sopContentInfoService;
    private final ITbWxMassMessageInfoService tbWxMassMessageInfoService;
    private final TokenParseUtil tokenService;

    @RequestMapping("/addJourneySop")
    public AjaxResult add(@RequestBody JourneySopRequest request) {
        log.info("【新增旅程sop】添加事件接收json:{}", JSON.toJSONString(request));
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, request);
        // 校验是否启用了审批流程
        // 获取用户所在部门是否启用审批
        TbWxCorpConfig config = journeySopBiz.getApprovalService().getTbWxCorpConfig(loginUser.getUser().getCorpId());
        boolean isApproval = getEnableApproval(loginUser.getDeptId()+"", config);
        request.setEnableApproval(isApproval);
        log.info("【新增旅程sop】创建旅程SOP：设置是否需要审批：{}", isApproval);
        request.setSopType(TypeConstants.SOP_TYPE_OF_JOURNEY);
        // 旅程信息保存
        journeySopBiz.saveJourneyInfo(request);
        log.info("【新增旅程sop】保存旅程信息");
        // 基础信息存储
        journeySopBiz.saveSopInfo(request);
        log.info("【新增旅程sop】保存 sopInfo");
        // 条件信息存储
        journeySopBiz.saveSopConditionInfo(request);
        log.info("【新增旅程sop】保存 sopConditionInfo");
        // 内容序列存储
        journeySopBiz.saveSopContentInfo(request, false);
        log.info("【新增旅程sop】保存 sopContentInfo");
        // 发送消息
        journeySopBiz.getApprovalService().sendPendingApprovalMsg(request.getSopName(), String.valueOf(request.getSopId()),
                ApprovalTypeEnum.JOURNEYSOP, request.isEnableApproval(), request.getAlive(), request.getCreateBy()+"");
        log.info("【新增旅程sop】发送消息");
        return AjaxResult.success();
    }

    @RequestMapping("/listJourneySop")
    public TableDataInfo<JourneySopListVO> list(@RequestBody JourneySopRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, request);
        request.setSopType(TypeConstants.SOP_TYPE_OF_JOURNEY);

        startPage();
        log.info("【查询旅程sop列表】入参：{}", JSON.toJSONString(request));
        List<JourneySopListVO> list = journeySopBiz.listJourneySop(request);
        log.info("【查询旅程sop列表】出参：{}", JSON.toJSONString(list));
        journeySopBiz.dealData(list, loginUser, ApprovalTypeEnum.JOURNEYSOP);
        log.info("【查询旅程sop列表】处理能否审批");
        return getDataTable(list);
    }

    @RequestMapping("/detailJourneySop")
    public Result<JourneySopDetailVO> detail(@RequestBody JourneySopRequest request) {
        log.info("【旅程sop】查询详情入参：{}", JSON.toJSONString(request));
        JourneySopDetailVO journeySopDetailVO = journeySopBiz.detailJourneySop(request);
        journeySopBiz.dealData(journeySopDetailVO, tokenService.getLoginUser(ServletUtils.getRequest()), ApprovalTypeEnum.JOURNEYSOP);
        log.info("【旅程sop】查询详情出参：{}", JSON.toJSONString(journeySopDetailVO));
        return Result.success("操作成功", journeySopDetailVO);
    }

    @RequestMapping("/getDataStatistics")
    public AjaxResult getDataStatistics(@RequestBody JourneySopQueryRequest request) {
        JourneySopDataStatisticsVO journeySopDataStatistics = sopInfoService.getJourneySopDataStatistics(request);
        return AjaxResult.success(journeySopDataStatistics);
    }

    @RequestMapping("/updateJourneySop")
    @RedisLockAspect(key = CacheKeyConstants.SOP_JOURNEY_UPDATE, value = "#sopId", waitTime = 20)
    public AjaxResult update(@RequestBody JourneySopRequest request) {
        log.info("【修改旅程sop】编辑事件接收json:{}", JSON.toJSONString(request));
        request.setSopType(TypeConstants.SOP_TYPE_OF_JOURNEY);
        // 校验sop状态 如未停用不可编辑
        journeySopBiz.validSopAliveStatus(request);
        log.info("【修改旅程sop】校验 sop 状态");
        // 基础信息保存
        journeySopBiz.saveSopInfo(request);
        log.info("【修改旅程sop】保存 sopInfo");
        // 旅程信息保存
        journeySopBiz.saveJourneyInfo(request);
        log.info("【修改旅程sop】保存JourneyInfo");
        // 条件信息保存
        journeySopBiz.saveSopConditionInfo(request);
        log.info("【修改旅程sop】保存 sopConditionInfo");
        // 内容序列存储
        journeySopBiz.saveSopContentInfo(request, true);
        log.info("【修改旅程sop】保存 sopContentInfo");
        // 发送消息
        journeySopBiz.getApprovalService().sendPendingApprovalMsg(request.getSopName(), String.valueOf(request.getSopId()),
                ApprovalTypeEnum.JOURNEYSOP, request.isEnableApproval(), request.getAlive(), request.getCreateBy()+"");
        log.info("【修改旅程sop】发送消息");
        return AjaxResult.success();
    }

    @RequestMapping("/changeJourneySopStatus")
    @RedisLockAspect(key = CacheKeyConstants.SOP_JOURNEY_UPDATE, value = "#request.sopId", waitTime = 20)
    public AjaxResult changeJourneySopStatus(@RequestBody JourneySopRequest request) {
        JourneySopDetailVO journeySopDetailVO = journeySopBiz.detailJourneySop(request);
        if (TypeConstants.SOP_TYPE_OF_JOURNEY != journeySopDetailVO.getSopType()) {
            throw new CustomException(ErrCodeEnum.SOP_TYPE_ERROR);
        }

        if (request.isEnableSop()) {
            request.setAlive(ApprovalStatusEnum.EXECUTING.getStatus());
        } else {
            request.setAlive(ApprovalStatusEnum.EXEC_INTERRUPTED.getStatus());
        }
        String statusDesc = ApprovalStatusEnum.isRunning(request.getAlive())? "启用" : "停用";
        LogUtil.logOperDesc(StrUtil.format("{}【{}】", statusDesc, journeySopDetailVO.getSopName()));

        if (ApprovalStatusEnum.isRunning(request.getAlive())) {
            // 启用
            // 启用sop内容序列
            if (journeySopDetailVO.isEnableApproval()) {
                if (!ApprovalStatusEnum.isStoped(journeySopDetailVO.getAlive())) {
                    throw new CustomException(ErrCodeEnum.SOP_STATUS_START_ERROR);
                }
            } else {
                if (ApprovalStatusEnum.isRunning(journeySopDetailVO.getAlive())) {
                    throw new CustomException(ErrCodeEnum.SOP_REPEAT_START_ERROR);
                }
            }
            log.info("【旅程sop】启动sop定时任务：{}", request.getSopId());
            journeySopBiz.createOrStartSop(journeySopDetailVO, request);
            return AjaxResult.success();
        }
        if (!ApprovalStatusEnum.isRunning(journeySopDetailVO.getAlive())) {
            throw new CustomException(ErrCodeEnum.SOP_REPEAT_STOP_ERROR);
        }
        // 停用
        if (journeySopDetailVO.isEnableApproval()) {
            if (!ApprovalStatusEnum.isRunning(journeySopDetailVO.getAlive())) {
                throw new CustomException(ErrCodeEnum.SOP_STATUS_STOP_ERROR);
            }
        } else {
            if (ApprovalStatusEnum.isStoped(journeySopDetailVO.getAlive())) {
                throw new CustomException(ErrCodeEnum.SOP_REPEAT_STOP_ERROR);
            }
        }

        log.info("【旅程sop】停用sop定时任务：{}", request.getSopId());
        // 停用sop内容序列
        journeySopBiz.stopContentTask(journeySopDetailVO);
        // 停用sop定时扫描
        journeySopBiz.stopSopScanTask(journeySopDetailVO);
        // 修改sopInfo的状态
        journeySopBiz.changeSopStatus(request);
        return AjaxResult.success();
    }

    /**
     * 以下列表数据接口直接使用旅程sop的查询做阶段id兼容即可
     *
     * @param request
     * @return
     */
    @RequestMapping("/sopTaskList")
    public TableDataInfo sopTaskList(@RequestBody ConditionSopQueryRequest request) {
        startPage();
        List<ConditionSopTaskDataVO> list = sopContentInfoService.sopTaskList(request);
        return getDataTable(list);
    }

    @RequestMapping("/taskExecuteList")
    public TableDataInfo taskExecuteList(@RequestBody ConditionSopQueryRequest request) {
        startPage();
        List<ConditionSopDataVO> list = sopContentInfoService.taskExecuteList(request);
        return getDataTable(list);
    }

    @RequestMapping("/taskReachMessageList")
    public TableDataInfo taskReachMessageList(@RequestBody ConditionSopQueryRequest request) {
        startPage();
        List<ConditionSopDataVO> list = sopContentInfoService.taskReachMessageList(request);
        return getDataTable(list);
    }

    @RequestMapping("/taskReachCustomerList")
    public TableDataInfo taskReachCustomerList(@RequestBody ConditionSopQueryRequest request) {
        startPage();
        List<ConditionSopDataVO> list = sopContentInfoService.taskReachCustomerList(request);
        return getDataTable(list);
    }

    @RequestMapping("/churnCustomerList")
    public TableDataInfo churnCustomerList(@RequestBody ConditionSopQueryRequest request) {
        startPage();
        List<ConditionSopDataVO> list = sopContentInfoService.churnCustomerList(request);
        return getDataTable(list);
    }

    @RequestMapping("/removeJourneySop")
    public AjaxResult remove(@RequestBody JourneySopRequest request) {
        request.setSopType(TypeConstants.SOP_TYPE_OF_JOURNEY);
        // 校验sop状态 如未停用不可删除
        SopInfo sopInfo = journeySopBiz.validSopAliveStatus(request);
        request.setJourneyId(sopInfo.getJourneyId());
        // 删除旅程
        journeySopBiz.removeSopJourney(request);
        // 删除内容序列
        journeySopBiz.removeSopContent(request);
        // 删除条件信息
        journeySopBiz.removeSopCondition(request);
        // 删除sop信息
        journeySopBiz.removeSopInfo(request);
        // 删除定时任务?
        return AjaxResult.success();
    }

    @RequestMapping("/remindToSend")
    public AjaxResult remindToSend(@RequestBody ConditionSopQueryRequest request) {
        MassMessageSenderListVO massMessageSenderListVO = new MassMessageSenderListVO();
        if (StringUtils.isNotEmpty(request.getUserId())) {
            MassMessageContentVO massMessageContentVO = tbWxMassMessageInfoService.getMassMessageInfoByRecordId(request.getMsgId());
            if (ObjectUtil.isNull(massMessageContentVO)) {
                log.error("【旅程sop】提醒成员失败，未找到对应消息信息，请求原文：{}", request);
                return AjaxResult.success();
            }
            String stopTime = ObjectUtil.isNotNull(massMessageContentVO.getStopTime()) ? DateUtils.getDate(massMessageContentVO.getStopTime(), DateStyle.YYYY_MM_DD_HH_MM) : "无";
            String content = "【旅程sop 群发任务提醒】\n你有一条群发消息还未发送\n任务触发时间："
                    + DateUtils.getDate(massMessageContentVO.getCreateTime(), DateStyle.YYYY_MM_DD_HH_MM) +
                    "\n任务截止时间：" + stopTime + "\n发送地址：工作台>客户联系与管理>群发助手中发送";
            massMessageSenderListVO = new MassMessageSenderListVO();
            massMessageSenderListVO.setUserId(request.getUserId());
            List<MassMessageSenderListVO> list = Lists.newArrayList(massMessageSenderListVO);
            tbWxMassMessageInfoService.remindUser2SendMessage(CorpInfoProperties.getCorpId(), list, content);
            return AjaxResult.success();
        }
        // 提醒全部要找出sop下所有未完成的
        request.setExecuteStatus(TypeConstants.SOP_TASK_EXECUTE_STATUS_UNFINISHED);
        List<ConditionSopDataVO> list = sopContentInfoService.taskExecuteList(request);
        if (CollectionUtils.isNotEmpty(list)) {
            // 这里应该拆分任务来循环 因为临时修改发送的文案 后续优化
            for (ConditionSopDataVO conditionSopDataVO : list) {
                MassMessageContentVO massMessageContentVO = tbWxMassMessageInfoService.getMassMessageInfoByRecordId(conditionSopDataVO.getMsgId());
                if (ObjectUtil.isNull(massMessageContentVO)) {
                    log.error("【旅程sop】提醒成员失败，未找到对应消息信息，请求原文：{}", request);
                    continue;
                }
                String executeTime = DateUtils.getDate(massMessageContentVO.getCreateTime(), DateStyle.YYYY_MM_DD_HH_MM);
                String stopTime = ObjectUtil.isNotNull(massMessageContentVO.getStopTime()) ? DateUtils.getDate(massMessageContentVO.getStopTime(), DateStyle.YYYY_MM_DD_HH_MM) : "无";
                String content = "【旅程sop 群发任务提醒】\n你有一条群发消息还未发送\n任务触发时间：" + executeTime + "\n任务截止时间：" + stopTime + "\n发送地址：工作台>客户联系与管理>群发助手中发送";
                massMessageSenderListVO.setUserId(conditionSopDataVO.getUserId());
                tbWxMassMessageInfoService.remindUser2SendMessage(CorpInfoProperties.getCorpId(), Lists.newArrayList(massMessageSenderListVO), content);
            }
        }
        return AjaxResult.success();
    }

    @RequestMapping("/synchronizeData")
    public AjaxResult synchronizeData(@RequestBody ConditionSopQueryRequest request){
        request.setSopType(TypeConstants.SOP_TYPE_OF_JOURNEY);
        // 当前同步不区分阶段 全部同步
        journeySopBiz.synchronizeData(request);
        return AjaxResult.success();
    }
    @RequestMapping("/approve")
    Result approve(@RequestBody ApprovalVO approvalVO) {
        log.info("【审核sop】审核旅程sop开始");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return journeySopBiz.approve(approvalVO, loginUser);
    }
    @RequestMapping("/revoked")
    Result revoked(@RequestBody ApprovalVO approvalVO) {
        log.info("【撤回sop】撤回旅程sop开始");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return journeySopBiz.revoked(approvalVO, loginUser);
    }
}
