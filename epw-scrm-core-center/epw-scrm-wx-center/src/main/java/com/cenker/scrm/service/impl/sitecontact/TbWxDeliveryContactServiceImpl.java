package com.cenker.scrm.service.impl.sitecontact;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.annotation.RedisLockAspect;
import com.cenker.scrm.client.work.feign.WxCpFeign;
import com.cenker.scrm.config.CorpInfoProperties;
import com.cenker.scrm.config.RedisCache;
import com.cenker.scrm.constants.CacheKeyConstants;
import com.cenker.scrm.constants.DelayQueueConstant;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.mapper.sitecontact.TbWxDeliveryContactMapper;
import com.cenker.scrm.pojo.entity.wechat.TbWxContactRel;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.ContactDeliveryUser;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.ContactSite;
import com.cenker.scrm.pojo.entity.wechat.sitecontact.TbWxDeliveryContact;
import com.cenker.scrm.pojo.request.DeliveryUserContactRequest;
import com.cenker.scrm.pojo.vo.sitecontact.DeliveryContactVO;
import com.cenker.scrm.service.contact.ITbWxContactRelService;
import com.cenker.scrm.service.sitecontact.IContactDeliveryUserService;
import com.cenker.scrm.service.sitecontact.ITbWxDeliveryContactService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.DelayQueueTaskUtils;
import com.cenker.scrm.util.StringUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.external.WxCpContactWayInfo;
import me.chanjar.weixin.cp.bean.external.WxCpContactWayResult;
import org.redisson.api.RBlockingDeque;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/10/26
 * @Description
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TbWxDeliveryContactServiceImpl extends ServiceImpl<TbWxDeliveryContactMapper, TbWxDeliveryContact> implements ITbWxDeliveryContactService {

    private final WxCpFeign wxCpFeign;
    private final ITbWxContactRelService tbWxContactRelService;
    private final IContactDeliveryUserService contactDeliveryUserService;
    private final RedisCache redisCache;
    private final RedissonClient redissonClient;

    @Override
    @RedisLockAspect(key = "generateDeliveryContact", value = "#contact.signId")
    public String generateDeliveryContact(ContactSite contact) {
        WxCpContactWayInfo.ContactWay contactWay = new WxCpContactWayInfo.ContactWay();
        contactWay.setScene(WxCpContactWayInfo.SCENE.QRCODE);
        contactWay.setType(WxCpContactWayInfo.TYPE.MULTI);
        // 是否跳过验证
        contactWay.setSkipVerify(TypeConstants.SKIP_VERIFY_TRUE == contact.getSkipVerify());
        // 城市活码+门店+配送员 唯一标识
        contactWay.setState(contact.getSignId());
        // userId列表
        List<TbWxContactRel> list = tbWxContactRelService.list(new LambdaQueryWrapper<TbWxContactRel>().eq(TbWxContactRel::getContactId, contact.getId()));
        contactWay.setUsers(list.stream().map(TbWxContactRel::getUserId).collect(Collectors.toList()));
        WxCpContactWayInfo wxCpContactWayInfo = new WxCpContactWayInfo();
        wxCpContactWayInfo.setContactWay(contactWay);
        WxCpContactWayResult wxCpContactWayResult;
        try {
            wxCpContactWayResult = wxCpFeign.addContactWay(wxCpContactWayInfo,CorpInfoProperties.getCorpId());
            contact.setQrCode(wxCpContactWayResult.getQrCode());
            contact.setConfigId(wxCpContactWayResult.getConfigId());
            return wxCpContactWayResult.getQrCode();
        } catch (Exception e) {
            log.error("【生成配送员活码】生成失败，{}", e.getMessage());
        }
        return null;
    }

    @Override
    @Async
    public void saveTbWxDeliveryUserContact(ContactSite contact, Long deliveryUserId) {
        log.info("【配送员活码】记录数据库");
        TbWxDeliveryContact tbWxDeliveryContact = new TbWxDeliveryContact();
        tbWxDeliveryContact.setId(Long.valueOf(contact.getDeliveryContactId()));
        tbWxDeliveryContact.setState(contact.getSignId());
        tbWxDeliveryContact.setType(contact.getType());
        tbWxDeliveryContact.setContactId(Long.valueOf(contact.getId()));
        tbWxDeliveryContact.setStoreId(contact.getStoreId());
        tbWxDeliveryContact.setDeliveryUserId(deliveryUserId);
        tbWxDeliveryContact.setConfigId(contact.getConfigId());
        tbWxDeliveryContact.setQrCode(contact.getQrCode());
        tbWxDeliveryContact.setGenerateTime(System.currentTimeMillis() / 1000);
        tbWxDeliveryContact.setCreateTime(DateUtils.getNowDate());
        save(tbWxDeliveryContact);
    }

    @Override
    @Async
    public void handleDeliveryUserContact(ContactSite contact, Long deliveryUserId) {
        Integer refreshRate = contact.getRefreshRate() * 2;
     /*   List<TbWxDeliveryContact> list = list(new LambdaQueryWrapper<TbWxDeliveryContact>()
                .eq(TbWxDeliveryContact::getDeliveryUserId, deliveryUserId));
        if (CollectionUtil.isNotEmpty(list)) {
            long now = System.currentTimeMillis() / 1000;
            List<TbWxDeliveryContact> expireList = list.stream().filter(c -> (c.getGenerateTime() + refreshRate) <= now).collect(Collectors.toList());
            // 删除超过刷新时间两倍以上的活码
            deleteDeliveryContactList(contact.getCorpId(), expireList);
        }*/
        String deliveryContactId = contact.getDeliveryContactId();
        DelayQueueTaskUtils.getInstance().pullTask(deliveryContactId, refreshRate);
    }

    /**
     * 删除活码
     */
    private void deleteDeliveryContactList(String corpId, List<TbWxDeliveryContact> expireList) {
        if (CollectionUtil.isNotEmpty(expireList)) {
            for (TbWxDeliveryContact tbWxDeliveryContact : expireList) {
                boolean remove = removeById(tbWxDeliveryContact.getId());
                if (remove) {
                    try {
                        wxCpFeign.deleteContactWay(tbWxDeliveryContact.getConfigId(),corpId);
                    } catch (Exception e) {
                        log.error("【配送员活码】删除企微活码失败，{}", e.getMessage());
                    }
                }
            }
        }
    }

    @Override
    public DeliveryContactVO getExpireStatus(DeliveryUserContactRequest deliveryUserContactRequest) {
        String qrCode = redisCache.getCacheObject(CacheKeyConstants.DELIVERY_CONTACT_CACHE_KEY + deliveryUserContactRequest.getDeliveryUserId() + ":" + deliveryUserContactRequest.getCodeId());
        DeliveryContactVO deliveryContactVO = new DeliveryContactVO();
        deliveryContactVO.setExpire(StringUtils.isBlank(qrCode));
        deliveryContactVO.setCodeId(deliveryUserContactRequest.getCodeId());
        deliveryContactVO.setDeliveryUserId(deliveryUserContactRequest.getDeliveryUserId());
        return deliveryContactVO;
    }

    @Override
    @Async
    public void removeBySignId(String signId, int contactProvince) {
        List<ContactDeliveryUser> deliveryUserList = contactDeliveryUserService.selectDeliveryUserListBySignId(signId, contactProvince);
        if (CollectionUtil.isNotEmpty(deliveryUserList)) {
            for (ContactDeliveryUser contactDeliveryUser : deliveryUserList) {
                redisCache.deleteObject(redisCache.keys(CacheKeyConstants.DELIVERY_CONTACT_CACHE_KEY + contactDeliveryUser.getId() + "*"));
                List<TbWxDeliveryContact> list = list(new LambdaQueryWrapper<TbWxDeliveryContact>().eq(TbWxDeliveryContact::getDeliveryUserId, contactDeliveryUser.getId()));
                deleteDeliveryContactList(CorpInfoProperties.getCorpId(), list);
            }
        }
    }

    /**
     * 处理redis队列
     */
    @Scheduled(fixedRate = 6000)
    @Async
    public void init() {
        RBlockingDeque<String> blockingDeque = redissonClient.getBlockingDeque(DelayQueueConstant.DELIVERY_QUEUE);
        /**
         * peek：获取队列的head对象，但不是从队列中移除。如果队列空，返回空
         * poll ：​获取并移出队列head对象，如果head没有超时，返回空
         * take ：​获取并移出队列head对象，如果没有超时head对象，会wait当前线程直到有对象满足超时条件
         */
        String deliveryContactId = blockingDeque.poll();
        if (StringUtils.isNoneBlank(deliveryContactId)) {
            log.info("拿到了延时队列的：{}", deliveryContactId);
            // 删除活码
            TbWxDeliveryContact expireCode = getById(deliveryContactId);
            if (ObjectUtil.isNotNull(expireCode)) {
                deleteDeliveryContactList(CorpInfoProperties.getCorpId(), Lists.newArrayList(expireCode));
            }
        }
    }
}
