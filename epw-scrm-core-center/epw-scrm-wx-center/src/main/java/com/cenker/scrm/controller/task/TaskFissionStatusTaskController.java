package com.cenker.scrm.controller.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cenker.scrm.constants.FissionConstants;
import com.cenker.scrm.pojo.entity.wechat.TbWxContact;
import com.cenker.scrm.pojo.entity.wechat.TbWxFission;
import com.cenker.scrm.pojo.entity.wechat.TbWxTaskFissionRecord;
import com.cenker.scrm.service.contact.ITbWxContactService;
import com.cenker.scrm.service.fission.ITbWxFissionService;
import com.cenker.scrm.service.fission.ITbWxTaskFissionRecordService;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.cenker.scrm.constants.StatusConstants.FISSION_ACTIVITY_END;
import static com.cenker.scrm.constants.StatusConstants.FISSION_ACTIVITY_NORMAL;

/**
 * <AUTHOR>
 * @Date 2021/11/24
 * @Description 裂变相关
 */
@RequestMapping("/wx/task/taskFissionStatus")
@RestController
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskFissionStatusTaskController {
    @Autowired
    private ITbWxFissionService fissionService;
    @Autowired
    private ITbWxTaskFissionRecordService recordService;
    @Autowired
    private ITbWxContactService contactService;

    @RequestMapping("/taskFissionExpiredStatusHandle")
    public void taskFissionExpiredStatusHandle() {
        // 查询所有过期任务裂变
        List<TbWxFission> fissionList = fissionService.list(new LambdaQueryWrapper<TbWxFission>()
                .lt(TbWxFission::getEndTime, DateUtils.getNow())
                .eq(TbWxFission::getFissStatus, FISSION_ACTIVITY_NORMAL)
                .eq(TbWxFission::getFissionType, FissionConstants.USER_FISSION));
        if (CollectionUtils.isNotEmpty(fissionList)) {
            log.info("【定时任务】查询到的过期裂变数量：{}", fissionList.size());
            Set<String> corpIdList = fissionList.stream().map(TbWxFission::getCorpId).collect(Collectors.toSet());
            log.info("【定时任务】查询到执行企业数量：{}", corpIdList.size());
            for (TbWxFission tbWxFission : fissionList) {
                // 找到对应的裂变记录
                List<TbWxTaskFissionRecord> recordList = recordService.list(new LambdaQueryWrapper<TbWxTaskFissionRecord>()
                        .eq(TbWxTaskFissionRecord::getTaskFissionId, tbWxFission.getId()));
                if (CollectionUtils.isNotEmpty(recordList)) {
                    log.info("【定时任务】查询到当前裂变任务:{},任务id为:{},裂变记录(活码生成总数)为：{}", tbWxFission.getTaskName(), tbWxFission.getId(), recordList.size());
                    // 根据活码地址搜索活码对象
                    for (TbWxTaskFissionRecord record : recordList) {
                        List<TbWxContact> contactList = contactService.list(new LambdaQueryWrapper<TbWxContact>().eq(TbWxContact::getQrCode, record.getQrCode()));
                        // 一般一条记录只会有一条活码 防止报错
                        if (CollectionUtils.isNotEmpty(contactList)) {
                            log.info("【定时任务】开始删除活码");
                            String configIds = StringUtils.join(contactList.stream().map(TbWxContact::getConfigId).distinct().toArray(), ",");
                            contactService.deleteTbWxContactByConfigIds(configIds, tbWxFission.getCorpId());
                        }
                    }
                }
                // 设置为结束
                tbWxFission.setFissStatus(FISSION_ACTIVITY_END);
            }
            fissionService.updateBatchById(fissionList);
            return;
        }
        log.info("【定时任务】当前无可执行的过期裂变");
    }

    public static void main(String[] args) {
        List<TbWxContact> contactList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            TbWxContact tbWxContact = new TbWxContact();
            tbWxContact.setConfigId(i+"");
            contactList.add(tbWxContact);
        }
        Set<String> configIdList = contactList.stream().map(TbWxContact::getConfigId).collect(Collectors.toSet());
        String join = StringUtils.join(configIdList.toArray(), ",");
        System.out.println(join);
    }
}
