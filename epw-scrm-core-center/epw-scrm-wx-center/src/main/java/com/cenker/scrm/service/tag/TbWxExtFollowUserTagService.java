package com.cenker.scrm.service.tag;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cenker.scrm.pojo.dto.tag.WeMakeCustomerTag;
import com.cenker.scrm.pojo.entity.TbWxExtFollowUserTag;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpTag;


import java.util.List;

/**
 * <AUTHOR>
 */
public interface TbWxExtFollowUserTagService extends IService<TbWxExtFollowUserTag> {
    /**
     * 修改所有添加员工标签
     * @param addTag 添加标签
     * @param weMakeCustomerTag 企业、员工、外部客户id
     */
    void syncAllStaffTagByExternalUserId(List<TbWxCorpTag> addTag, WeMakeCustomerTag weMakeCustomerTag);
    /**
     * 逻辑需要和com.cenker.scrm.service.tag.ITbWxExtFollowUserTagWorkService的同名方法保持一致 start
     */
    boolean saveBatchWithLog(List<TbWxExtFollowUserTag> entityList);

    void removeByTagIdsWithLog(List<String> tagIds, String externalUserId);

    void removeByIdsWithLog(List<String> userTagIds);

    void removeByWrapperWithLog(LambdaQueryWrapper<TbWxExtFollowUserTag> warpper);

    void removeUserTagWithLog(List<TbWxExtFollowUserTag> removeTags);
    /**
     * 逻辑需要和com.cenker.scrm.service.tag.ITbWxExtFollowUserTagWorkService的同名方法保持一致 end
     */
}
