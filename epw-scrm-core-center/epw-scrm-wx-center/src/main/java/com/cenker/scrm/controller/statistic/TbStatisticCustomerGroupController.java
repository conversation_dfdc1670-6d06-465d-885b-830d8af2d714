package com.cenker.scrm.controller.statistic;

import com.cenker.scrm.enums.StatisticTypeEnum;
import com.cenker.scrm.event.StatisticUpdateEvent;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticCustomerGroup;
import com.cenker.scrm.pojo.request.statistic.StatisticCustomerGroupListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.statistic.StatisticCustomerGroupSummaryVo;
import com.cenker.scrm.pojo.vo.statistic.StatisticGraphVo;
import com.cenker.scrm.service.statistic.ITbStatisticCustomerGroupService;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据统计-客户群数据 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/statistic/customerGroup")
public class TbStatisticCustomerGroupController extends BaseController {

    private final ITbStatisticCustomerGroupService tbStatisticCustomerGroupService;
    private final TokenParseUtil tokenService;
    private final ApplicationEventPublisher applicationEventPublisher;


    /**
     * 统计
     */
    @GetMapping("/summary")
    public AjaxResult summary(@RequestBody StatisticSummaryQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        StatisticCustomerGroupSummaryVo vo = tbStatisticCustomerGroupService.summary(query);
        return AjaxResult.success(vo);
    }

    /**
     * 图表
     */
    @GetMapping("/graph")
    public AjaxResult graph(@RequestBody StatisticGraphQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        List<StatisticGraphVo> lstGraph = tbStatisticCustomerGroupService.graph(query);
        return AjaxResult.success(lstGraph);
    }

    /**
     * 明细
     */
    @GetMapping("/list")
    public TableDataInfo list(@RequestBody StatisticCustomerGroupListQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        this.startPage();
        List<TbStatisticCustomerGroup> lstCustomerGroup = tbStatisticCustomerGroupService.list(query);
        return getDataTable(lstCustomerGroup);
    }

    /**
     * 导出
     */
    @GetMapping("/export")
    public List<TbStatisticCustomerGroup> export(@RequestBody StatisticCustomerGroupListQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);

        List<TbStatisticCustomerGroup> lstCustomerGroup = tbStatisticCustomerGroupService.list(query);
        return lstCustomerGroup;
    }

    /**
     * 更新数据
     */
    @RequestMapping("/synData")
    public Result synData(@RequestBody StatisticSummaryQuery query) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        StatisticUpdateEvent event = new StatisticUpdateEvent(StatisticTypeEnum.CUSTOMER_GROUP, query.getBeginTime(), query.getEndTime(), ITbStatisticCustomerGroupService.class, loginUser.getUserId());
        applicationEventPublisher.publishEvent(event);
        return Result.success("更新操作成功，数据同步中");
    }
}
