package com.cenker.scrm.controller.sitecontact;


import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.request.DeliveryUserContactDataRequest;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.sitecontact.*;
import com.cenker.scrm.service.sitecontact.ITbWxDeliveryContactRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/2
 * @Description 数据统计
 */
@RestController
@RequestMapping("/delivery/data")
@RequiredArgsConstructor
public class TbWxDeliveryContactRecordController extends BaseController {

    private final ITbWxDeliveryContactRecordService tbWxDeliveryContactRecordService;

    /**
     * 数据概览(配送员端)
     */
    @RequestMapping("/getDataStatistics")
    public AjaxResult getDataStatistics(@RequestBody DeliveryUserContactDataRequest deliveryUserContactDataRequest) {
        AcquisitionDataStatisticsVO acquisitionDataStatisticsVO = tbWxDeliveryContactRecordService.getDataStatistics(deliveryUserContactDataRequest);
        return AjaxResult.success(acquisitionDataStatisticsVO);
    }

    /**
     * 配送员排行榜(配送员端)
     */
    @RequestMapping("/getDeliveryRankData")
    public AjaxResult getDeliveryRankData(@RequestBody DeliveryUserContactDataRequest deliveryUserContactDataRequest) {
    /*    PageDomain pageDomain = TableSupport.buildPageRequest();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        PageHelper.startPage(1, 50, orderBy);*/
        ContactDeliveryRankDataVO contactDeliveryRankDataVO = tbWxDeliveryContactRecordService.getDeliveryRankData(deliveryUserContactDataRequest);
        return AjaxResult.success(contactDeliveryRankDataVO);
    }

    /**
     * 配送员排行榜(后台)
     */
    @RequestMapping("/getDeliveryRankData4Admin")
    public AjaxResult getDeliveryRankData4Admin(@RequestBody DeliveryUserContactDataRequest deliveryUserContactDataRequest) {
        ContactDeliveryRankDataVO contactDeliveryRankDataVO = tbWxDeliveryContactRecordService.getDeliveryRankData4Admin(deliveryUserContactDataRequest);
        return AjaxResult.success(contactDeliveryRankDataVO);
    }

    /**
     * 站点排行榜
     */
    @RequestMapping("/getCityRankData")
    public AjaxResult getCityRankData(@RequestBody DeliveryUserContactDataRequest deliveryUserContactDataRequest) {
        ContactCityRankDataVO contactCityRankDataVO = tbWxDeliveryContactRecordService.getCityRankData(deliveryUserContactDataRequest);
        return AjaxResult.success(contactCityRankDataVO);
    }

    /**
     * 清除缓存
     */
    @RequestMapping("/cleanDataCache")
    public AjaxResult cleanDataCache(@RequestBody DeliveryUserContactDataRequest deliveryUserContactDataRequest) {
        tbWxDeliveryContactRecordService.cleanDataCache(deliveryUserContactDataRequest);
        return AjaxResult.success();
    }

    /**
     * 每日拉新数据（首页曲线图）
     */
    @RequestMapping("/getAcquisitionDailyData")
    public AjaxResult getAcquisitionDailyData(@RequestBody DeliveryUserContactDataRequest deliveryUserContactDataRequest) {
        AcquisitionDataVO acquisitionDataVO = tbWxDeliveryContactRecordService.getAcquisitionDailyData(deliveryUserContactDataRequest);
        return AjaxResult.success(acquisitionDataVO);
    }

    /**
     * 配送员拉新数据查询(数据统计)
     */
    @RequestMapping("/getDeliveryAcquisitionData")
    public TableDataInfo getDeliveryAcquisitionData(@RequestBody DeliveryUserContactDataRequest deliveryUserContactDataRequest) {
        startPage();
        List<ContactDeliveryAcquisitionDataVO> list = tbWxDeliveryContactRecordService.getDeliveryAcquisitionData(deliveryUserContactDataRequest);
        return getDataTable(list);
    }

    /**
     * 配送员导出数据查询
     */
    @RequestMapping("/exportDeliveryAcquisitionData")
    public List<ContactDeliveryAcquisitionDataVO> exportDeliveryAcquisitionData(@RequestBody DeliveryUserContactDataRequest deliveryUserContactDataRequest) {
        return tbWxDeliveryContactRecordService.getDeliveryAcquisitionData(deliveryUserContactDataRequest);
    }

}
