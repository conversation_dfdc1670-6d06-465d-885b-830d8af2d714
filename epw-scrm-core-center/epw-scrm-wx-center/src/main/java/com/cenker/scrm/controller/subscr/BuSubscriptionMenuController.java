package com.cenker.scrm.controller.subscr;

import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.model.login.LoginUser;
import com.cenker.scrm.pojo.entity.subscr.BuSubscriptionMenu;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionMenuDetail;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionMenuVO;
import com.cenker.scrm.pojo.vo.subscr.BuSubscriptionQuery;
import com.cenker.scrm.service.subscr.IBuSubscriptionMenuService;
import com.cenker.scrm.util.ServletUtils;
import com.cenker.scrm.util.TokenParseUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订阅菜单控制器
 * 提供对订阅菜单的RESTful接口
 */
@RestController
@RequestMapping("/bu/subscription/menu")
@RequiredArgsConstructor
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class BuSubscriptionMenuController extends BaseController {

    private final IBuSubscriptionMenuService buSubscriptionMenuService;
    private final TokenParseUtil tokenService;

    /**
     * 获取订阅菜单列表
     * @param query 查询条件
     * @return 订阅菜单列表
     */
    @GetMapping("/list")
    public TableDataInfo<BuSubscriptionMenuVO> list(@SpringQueryMap BuSubscriptionQuery query) {
        log.info("【查询订阅菜单列表】开始查询，参数：{}", query);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, query);
        startPage();
        List<BuSubscriptionMenuVO> menuList = buSubscriptionMenuService.getMenuList(query);
        log.info("【查询订阅菜单列表】查询完成，结果数量：{}", menuList.size());
        return getDataTable(menuList);
    }

    /**
     * 新增订阅菜单
     * @param detailVO 订阅菜单详情
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<Void> add(@RequestBody @Validated(InsertGroup.class) BuSubscriptionMenuDetail detailVO) {
        log.info("【新增订阅菜单】开始新增，参数：{}", detailVO);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, detailVO);
        detailVO.setCreateBy(loginUser.getUser().getUserId());
        // 验证名称是否重复
        if (!buSubscriptionMenuService.checkMenuNameUnique(detailVO)) {
            log.error("【新增订阅菜单】订阅菜单名称已存在，菜单名称：{}", detailVO.getMenuName());
            return Result.error(500, "订阅菜单名称已存在，请重新输入！");
        }
        // 验证子菜单关联的栏目是否存在重复
        Result validateSection = buSubscriptionMenuService.checkMenuColumnUnique(detailVO);
        if (validateSection != null) {
            log.error("【新增订阅菜单】子菜单关联的栏目存在重复");
            return validateSection;
        }
        boolean result = buSubscriptionMenuService.addMenu(detailVO);
        if (result) {
            log.info("【新增订阅菜单】新增成功，菜单名称：{}", detailVO.getMenuName());
            return Result.success();
        } else {
            log.error("【新增订阅菜单】新增失败，菜单名称：{}", detailVO.getMenuName());
            return Result.error(500, "新增订阅菜单失败！");
        }
    }

    /**
     * 删除订阅菜单
     * @param id 菜单ID
     * @return 操作结果
     */
    @DeleteMapping("/remove")
    public Result<Void> remove(@RequestParam("id") String id) {
        log.info("【删除订阅菜单】开始删除，id：{}", id);
        BuSubscriptionMenu buSubscriptionMenu = buSubscriptionMenuService.getBaseMapper().selectById(id);
        if (buSubscriptionMenu == null) {
            log.warn("【删除订阅菜单】菜单不存在或已删除，id：{}", id);
            return Result.error(500, "菜单不存在或已删除");
        }
        if (buSubscriptionMenu.getReleaseStatus() == 1) {
            log.warn("【删除订阅菜单】已正式对客的菜单不能删除，id：{}", id);
            return Result.error(500, "已正式对客的菜单不能删除");
        }
        boolean result = buSubscriptionMenuService.removeMenu(id);
        if (result) {
            log.info("【删除订阅菜单】删除成功，id：{}", id);
            return Result.success();
        } else {
            log.warn("【删除订阅菜单】删除失败，id：{}", id);
            return Result.error(500, "删除订阅菜单失败");
        }
    }

    /**
     * 获取订阅菜单详情
     * @param id 菜单ID
     * @return 订阅菜单详情
     */
    @GetMapping("/detail")
    public Result<BuSubscriptionMenuDetail> detail(@RequestParam("id") String id) {
        log.info("【查询订阅菜单详情】开始查询，id：{}", id);
        BuSubscriptionMenuDetail detail = buSubscriptionMenuService.getMenuDetail(id);
        if (detail != null) {
            log.info("【查询订阅菜单详情】查询成功，id：{}", id);
            return Result.success("操作成功", detail);
        } else {
            log.warn("【查询订阅菜单详情】菜单不存在或已删除，id：{}", id);
            return Result.error(500, "菜单不存在或已删除");
        }
    }

    /**
     * 更新订阅菜单
     * @param detailVO 订阅菜单详情
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<Void> update(@RequestBody @Validated(UpdateGroup.class) BuSubscriptionMenuDetail detailVO) {
        log.info("【修改订阅菜单】开始修改，参数：{}", detailVO);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        filterCondition(loginUser, detailVO);
        detailVO.setUpdateBy(loginUser.getUser().getUserId());
        // 验证名称是否重复
        if (!buSubscriptionMenuService.checkMenuNameUnique(detailVO)) {
            log.warn("【修改订阅菜单】订阅菜单名称已存在，菜单名称：{}", detailVO.getMenuName());
            return Result.error(500, "订阅菜单名称已存在，请重新输入！");
        }
        // 验证子菜单关联的栏目是否存在重复
        Result validateSection = buSubscriptionMenuService.checkMenuColumnUnique(detailVO);
        if (validateSection != null) {
            log.warn("【修改订阅菜单】子菜单关联的栏目存在重复");
            return validateSection;
        }
        boolean result = buSubscriptionMenuService.updateMenu(detailVO);
        if (result) {
            log.info("【修改订阅菜单】修改成功，id：{}", detailVO.getId());
            return Result.success();
        } else {
            log.warn("【修改订阅菜单】修改失败，id：{}", detailVO.getId());
            return Result.error(500, "更新订阅菜单失败");
        }
    }

    /**
     * 修改订阅菜单状态
     * @return 操作结果
     */
    @PostMapping("/changeStatus")
    public Result<Void> changeStatus(@RequestBody BuSubscriptionMenuDetail detail) {
        log.info("【修改订阅菜单状态】开始修改，参数：{}", detail);
        boolean result = buSubscriptionMenuService.changeMenuStatus(detail);
        if (result) {
            log.info("【修改订阅菜单状态】修改成功，id：{}", detail.getId());
            return Result.success();
        } else {
            log.warn("【修改订阅菜单状态】修改失败，id：{}", detail.getId());
            return Result.error(500, "修改订阅菜单状态失败");
        }
    }

    /**
     * 验证子菜单/栏目所在订阅菜单是否正式对客
     * @param id
     * @param type subMenu 子菜单 section 栏目
     * @return
     */
    @GetMapping("/validateIsPublish")
    public Result validateIsPublish(@RequestParam("id") String id, @RequestParam("type") String type) {
        log.info("【订阅菜单是否正式对客】开始验证，id：{}，type：{}", id, type);
        Result result = buSubscriptionMenuService.validateIsPublish(id, type);
        log.info("【验证订阅菜单是否正式对客】验证完成，结果：{}", result.isSuccess() ? "成功" : "失败");
        return result;
    }
}