* {
  margin: 0;
  padding: 0;
}

body {
  min-width: 320px;
  max-width: 750px;
  width: 10rem;
  margin: 0 auto;
  line-height: 1.5;
  font-family: Arial, Helvetica;
  background: #fff;
}

@media screen and (min-width: 750px) {
  html {
    font-size: 75px !important;
  }
}

.container {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
}

.phone-img {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  margin: 0 auto;
}

.phone-top-icon {
  height: 62px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  background: #ededed;
}

.phone-content {
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.code-avatar-box {
  width: 8.9333rem;
  height: 13.32rem;
  background: #fff;
  box-shadow: 0px 0.16rem 0.4rem 0px rgba(96, 96, 96, 0.1);
  border-radius: 0.2667rem;
  /* padding: 1.0667rem 0.7467rem; */
  padding: 0.8rem 0.5333rem;
  box-sizing: border-box;
}

.code-avatar-box2 {
  width: 8.9333rem;
  height: 13.32rem;
  background: #fff;
  box-shadow: 0px 0.16rem 0.4rem 0px rgba(96, 96, 96, 0.1);
  border-radius: 0.2667rem;
  padding: 1.76rem 1.6667rem;
  box-sizing: border-box;
}

.content-username {
  font-size: 0.5333rem;
  font-weight: 400;
  color: #27292c;
}

.content-username-p {
  line-height: 0.8533rem;
}

.content-username-p:first-child {
  padding-bottom: 0.0667rem;
}

.content-username2 {
  margin-top: 0.9867rem;
  font-size: 0.5333rem;
  font-weight: 400;
  color: #27292c;
  line-height: 0.8533rem;
  text-align: center;
}

.content-username2-title {
  font-size: 0.64rem;
  font-weight: 400;
  color: #27292c;
  line-height: 0.8533rem;
  margin-bottom: 0.32rem;
}

.content-username2-txt {
  font-size: 0.3733rem;
  font-weight: 400;
  color: #999da5;
  line-height: 0.64rem;
}

.content-qr {
  width: 5.6933rem;
  height: 5.6933rem;
  margin-top: 1.08rem;
  margin-left: 0.88rem;
}

.content-qr2 {
  width: 5.6rem;
  height: 5.6rem;
}

.qr-text {
  margin-top: 0.6667rem;
  text-align: center;
  font-size: 0.4267rem;
  font-weight: 400;
  color: #999da5;
  line-height: 0.4rem;
}

.content-bottom-icon {
  width: 3.2rem;
  height: 0.64rem;
  margin-top: 1.36rem;
}

.content-bottom-icon > img {
  width: 100%;
  height: 100%;
}

.img-box {
  width: 100%;
  height: 100%;
}
