<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.fission.TbWxTaskFissionRecordMapper">
    <select id="statisticRecords" resultType="TbWxTaskFissionRecord">
        select id,create_time
        from tb_wx_task_fission_record
        where task_fission_id = #{taskFissionId}
          and date_format(create_time, '%Y-%m-%d') >= #{startTime}
          and date_format(create_time, '%Y-%m-%d') &lt;= #{endTime}
        order by create_time
    </select>

    <select id="countCompleteCnt" resultType="java.lang.Integer">
        select ifnull(sum(fission_num >= fiss_num), 0)
        from (
                 select
                /*        (select count(1)
                         from tb_wx_ext_follow_user fu
                         where fu.external_user_id in
                               (select cr.customer_id
                                from tb_wx_fission_complete_record cr
                                where r.id = cr.fission_record_id
                                group by cr.customer_id)
                           and fu.user_id = f.fission_target_id
                           and fu.`status` = 0
                        ) retention,*/
                        (select count(distinct customer_id) from tb_wx_fission_complete_record
                        where r.id = fission_record_id)fission_num,
                        f.fiss_num
                 from tb_wx_task_fission_record r
                          left join tb_wx_fission f on r.task_fission_id = f.id
                 where r.task_fission_id = #{taskFissionId}
             ) t
    </select>
</mapper>