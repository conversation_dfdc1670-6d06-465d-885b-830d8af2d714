<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.subscr.BuSectionStatisticsMapper">
    <!-- 自定义SQL查询 -->

    <insert id="saveStatisticDateByDay">
        INSERT INTO `bu_section_statistics` (`section_id`, `statistic_date`, `subscription_count`, `subscription_count_total`, `subscription_ratio`, `new_subscription_count`, `cancel_subscription_count`, `create_time`)
        SELECT `section_id`, `statistic_date`, `subscription_count`, `subscription_count_total`, `subscription_ratio`, `new_subscription_count`, `cancel_subscription_count`, `create_time`
        FROM (
            SELECT
            bss.id as `section_id`,
            str_to_date(#{statDate}, '%Y-%m-%d') as statistic_date,
            COUNT(
            distinct
            case when bs.del_flag = 0 and twec.status = '0' then bs.external_user_id
            when bs.del_flag = 1 and date(bs.cancel_subscr_time) > str_to_date(#{statDate}, '%Y-%m-%d') and twec.status = '0' then bs.external_user_id
            else null end
            )  AS `subscription_count`,
            min(totalTbl.subscription_count_total) as subscription_count_total,
            IFNULL(
            COUNT(
            distinct
            case when bs.del_flag = 0 and twec.status = '0' then bs.external_user_id
            when bs.del_flag = 1 and bs.cancel_subscr_time > str_to_date(#{statDate}, '%Y-%m-%d') and twec.status = '0' then bs.external_user_id
            else null end
            ) * 100 / NULLIF(totalTbl.`subscription_count_total`, 0), 0) AS `subscription_ratio`,
            COUNT(CASE WHEN date(bs.subscr_time) = str_to_date(#{statDate}, '%Y-%m-%d') and twec.status = '0' THEN 1 ELSE NULL END) AS `new_subscription_count`,
            COUNT(CASE WHEN date(bs.cancel_subscr_time) = str_to_date(#{statDate}, '%Y-%m-%d') and twec.status = '0' THEN 1 ELSE NULL END) AS `cancel_subscription_count`,
            NOW() AS `create_time`
            FROM bu_subscription_section bss
            left join `bu_subscription` bs on bss.id = bs.section_id and date(bs.subscr_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
            left join tb_wx_ext_customer twec on twec.external_user_id = bs.external_user_id and twec.status = '0',
            (
                select count(totalTbl.external_user_id) as `subscription_count_total` from bu_subscription totalTbl,tb_wx_ext_customer twec2,bu_subscription_section bss1
                where date(totalTbl.subscr_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
                and (
                    totalTbl.del_flag = 0
                    or
                    (totalTbl.del_flag = 1 and totalTbl.cancel_subscr_time > str_to_date(#{statDate}, '%Y-%m-%d'))
                )
                and twec2.external_user_id = totalTbl.external_user_id and twec2.status = '0'
                and totalTbl.section_id = bss1.id and bss1.del_flag = 0
                and date(bss1.create_time)  &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
                AND (bss1.del_flag = 0 or (bss1.del_flag = 1 and bss1.update_time >= str_to_date(#{statDate}, '%Y-%m-%d')))
            ) totalTbl
        WHERE date(bss.create_time)  &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        AND (bss.del_flag = 0 or (bss.del_flag = 1 and bss.update_time >= str_to_date(#{statDate}, '%Y-%m-%d')))
        GROUP BY bss.id
        ) AS `t`
    </insert>
    <select id="countStatisticData" resultType="com.cenker.scrm.pojo.vo.subscr.BuSectionStatisticsVO">
        SELECT
        bss.id as `section_id`,
        str_to_date(#{statDate}, '%Y-%m-%d') as statistic_date,
        COUNT(
        distinct
        case when bs.del_flag = 0 and twec.status = '0' then bs.external_user_id
        when bs.del_flag = 1 and date(bs.cancel_subscr_time) > str_to_date(#{statDate}, '%Y-%m-%d') and twec.status = '0' then bs.external_user_id
        else null end
        )  AS `subscription_count`,
        min(totalTbl.subscription_count_total) as subscription_count_total,
        IFNULL(
        COUNT(
        distinct
        case when bs.del_flag = 0 and twec.status = '0' then bs.external_user_id
        when bs.del_flag = 1 and bs.cancel_subscr_time > str_to_date(#{statDate}, '%Y-%m-%d') and twec.status = '0' then bs.external_user_id
        else null end
        ) * 100 / NULLIF(totalTbl.`subscription_count_total`, 0), 0) AS `subscription_ratio`,
        COUNT(CASE WHEN date(bs.subscr_time) = str_to_date(#{statDate}, '%Y-%m-%d') and twec.status = '0' THEN 1 ELSE NULL END) AS `new_subscription_count`,
        COUNT(CASE WHEN date(bs.cancel_subscr_time) = str_to_date(#{statDate}, '%Y-%m-%d') and twec.status = '0' THEN 1 ELSE NULL END) AS `cancel_subscription_count`,
        NOW() AS `create_time`
        FROM bu_subscription_section bss
        left join `bu_subscription` bs on bss.id = bs.section_id and date(bs.subscr_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        left join tb_wx_ext_customer twec on twec.external_user_id = bs.external_user_id and twec.status = '0',
        (
        select count(totalTbl.external_user_id) as `subscription_count_total` from bu_subscription totalTbl,tb_wx_ext_customer twec2,bu_subscription_section bss1
        where date(totalTbl.subscr_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        and (
        totalTbl.del_flag = 0
        or
        (totalTbl.del_flag = 1 and totalTbl.cancel_subscr_time > str_to_date(#{statDate}, '%Y-%m-%d'))
        )
        and twec2.external_user_id = totalTbl.external_user_id and twec2.status = '0'
        and totalTbl.section_id = bss1.id and bss1.del_flag = 0
        and date(bss1.create_time)  &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        AND (bss1.del_flag = 0 or (bss1.del_flag = 1 and bss1.update_time >= str_to_date(#{statDate}, '%Y-%m-%d')))
        ) totalTbl
        WHERE date(bss.create_time)  &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        AND (bss.del_flag = 0 or (bss.del_flag = 1 and bss.update_time >= str_to_date(#{statDate}, '%Y-%m-%d')))
        and bss.id = #{sectionId}
    </select>
</mapper>