<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.subscr.BuSubscriptionSectionMapper">
    <!-- 自定义SQL查询 -->

    <select id="selectReleaseMenu" resultType="java.lang.String">
<!-- bu_subscription_menu \ bu_subscription_menu_sub \ bu_subscription_section 关联 3 个表，查询 id 所在的 section 关联的 bu_subscription_menu_sub 对应的bu_subscription_menu是否为已发布状态-->
        SELECT m.id
        FROM bu_subscription_menu m
        JOIN bu_subscription_menu_sub ms ON m.id = ms.menu_id
        JOIN bu_subscription_section s ON ms.section_id = s.id
        WHERE s.id = #{id} AND m.release_status = 1
    </select>
    <select id="getSectionList" resultType="com.cenker.scrm.pojo.vo.subscr.BuSubscriptionSectionVO">
<!-- 查询所有 bu_subscription_section 及其对应的 统计数据bu_section_statistics，以及创建人名称 sys_user -->
        SELECT
        s.id,
        s.section_name,
        s.section_intro,
        s.del_flag,
        s.create_by,
        u.nick_name create_by_name,
        s.create_time,
        ss.subscription_count,
        ss.subscription_ratio
        <if test="radarId != null and radarId != ''">
            , (case when br.id is not null then 1 else 0 end) as selected
        </if>
        FROM bu_subscription_section s
        LEFT JOIN (select max(statistic_date) as statistic_date, section_id from bu_section_statistics group by section_id) maxTbl ON s.id = maxTbl.section_id
        LEFT JOIN bu_section_statistics ss ON maxTbl.section_id = ss.section_id and ss.statistic_date = maxTbl.statistic_date
        LEFT JOIN sys_user u ON s.create_by = u.user_id
        <if test="radarId != null and radarId != ''">
            LEFT JOIN bu_section_radar br on br.section_id = s.id and br.radar_id = #{radarId} and br.del_flag = 0
        </if>
        <where>
            s.del_flag = 0
            <if test="name != null and name != ''">
                AND s.section_name LIKE CONCAT('%', #{name}, '%')
            </if>
            <!-- 权限控制 -->
<!--            如果 filterData 为 true，则进行权限控制，否则不进行权限控制 -->
            <if test="filterData == true">
                <if test="dataScope == null or dataScope != '1'.toString()">
                    <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                        and s.dept_id in
                        <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                    </if>
                    <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                        and s.create_by = #{userId}
                    </if>
                </if>
            </if>
        </where>
        ORDER BY s.create_time DESC
    </select>
    <select id="hasNewSection" resultType="java.lang.Integer">
        select count(1) from bu_subscription_menu_sub
        where menu_id = #{newId} and section_id not in (select section_id from bu_subscription_menu_sub where menu_id = #{oldId})
    </select>
</mapper>