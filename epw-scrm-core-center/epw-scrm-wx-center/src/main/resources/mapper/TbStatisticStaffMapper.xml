<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cenker.scrm.mapper.statistic.TbStatisticStaffMapper">

    <insert id="saveStatisticDateByDay">
        INSERT INTO `tb_statistic_staff`(
        `statistic_date`, `userid`, `user_name`, `customer_total_num`,
        `customer_add_num`, `customer_del_num`, `staff_group_manage_num`,
        `staff_group_join_num`, `customer_chat_num`, `alone_chat_num`,
        `staff_group_chat_num`, `total_conversation_num`, `reply_timely_num`,
        `total_conversation_duration`, `create_time`, `dept_id`
        )
        SELECT
        str_to_date(#{statDate}, '%Y-%m-%d') AS statistic_date,
        u.userid,
        u.user_name,
        COALESCE(cs.customer_total_num, 0),
        COALESCE(cs.customer_add_num, 0),
        COALESCE(cs.customer_del_num, 0),
        COALESCE(gm.staff_group_manage_num, 0),
        COALESCE(gj.staff_group_join_num, 0),
        COALESCE(cht.customer_chat_num, 0),
        COALESCE(cht.alone_chat_num, 0),
        COALESCE(cht.staff_group_chat_num, 0),
        COALESCE(cv.total_conversation_num, 0),
        COALESCE(cv.reply_timely_num, 0),
        COALESCE(cv.total_conversation_duration, 0),
        now() AS create_time,
        u.dept_id
        FROM
        (
        SELECT
        userid,
        `name` AS user_name,
        main_department AS dept_id
        FROM
        tb_wx_user
        WHERE
        date(create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        and (
        ( `status` = 1 AND del_flag = 1)
        or
        (
        del_flag != 1
        and (
        date(update_time) &gt;= str_to_date(#{statDate}, '%Y-%m-%d') or
        date(dimission_time) &gt;= str_to_date(#{statDate}, '%Y-%m-%d')
        )
        )
        )
        ) AS u
        LEFT JOIN (
        SELECT
        user_id,
        count(DISTINCT
        CASE
        WHEN `status` = 0 or date(del_time) &gt;= str_to_date(#{statDate}, '%Y-%m-%d')
        THEN external_user_id
        END) AS customer_total_num,
        count(DISTINCT CASE WHEN (`status` = 0 or date(del_time) &gt; str_to_date(#{statDate}, '%Y-%m-%d')) AND date(create_time) = str_to_date(#{statDate}, '%Y-%m-%d') THEN external_user_id END) AS customer_add_num,
        count(CASE WHEN `status` = 2 AND date(del_time) = str_to_date(#{statDate}, '%Y-%m-%d') THEN id END) AS customer_del_num
        FROM
        tb_wx_ext_follow_user
        where date(create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        GROUP BY
        user_id
        ) AS cs ON cs.user_id = u.userid
        LEFT JOIN (
        SELECT
        from_id,
        count(DISTINCT CASE WHEN chat_type = 1 THEN consume_id END) AS customer_chat_num,
        count(DISTINCT CASE WHEN chat_type = 1 THEN msg_id END) AS alone_chat_num,
        count(DISTINCT CASE WHEN chat_type = 2 THEN msg_id END) AS staff_group_chat_num
        FROM
        wk_chat_archive_info
        WHERE
        msg_day = str_to_date(#{statDate}, '%Y-%m-%d')
        GROUP BY
        from_id
        ) AS cht ON cht.from_id = u.userid
        LEFT JOIN (
        SELECT
        staff_id,
        count(DISTINCT origin_msg_id) AS total_conversation_num,
        count(DISTINCT CASE WHEN timeout = 0 THEN origin_msg_id END) AS reply_timely_num,
        IFNULL(sum(duration), 0) AS total_conversation_duration
        FROM
        wk_chat_conversation
        WHERE
        statistic_date = str_to_date(#{statDate}, '%Y-%m-%d')
        GROUP BY
        staff_id
        ) AS cv ON cv.staff_id = u.userid
        LEFT JOIN (
        SELECT
        `owner` AS userid,
        count(chat_id) AS staff_group_manage_num
        FROM
        tb_wx_customer_group
        WHERE
        date(create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        and (dismiss_date is null or date(dismiss_date) &gt;= str_to_date(#{statDate}, '%Y-%m-%d'))
        GROUP BY
        `owner`
        ) AS gm ON gm.userid = u.userid
        LEFT JOIN  (
        SELECT
        user_id AS userid,
        count(distinct group_id) AS staff_group_join_num
        FROM
        tb_wx_customer_group_member
        WHERE
        date(join_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        and (departure_time is null or date(departure_time) &gt;= str_to_date(#{statDate}, '%Y-%m-%d'))
        GROUP BY
        user_id
        ) AS gj ON gj.userid = u.userid;
    </insert>

    <select id="summary" resultType="com.cenker.scrm.pojo.vo.statistic.StatisticStaffSummaryVo">
      SELECT
          COUNT( DISTINCT userid ) AS staffTotal,
          IFNULL( SUM( customer_del_num ), 0 ) AS delTotal,
          IFNULL( SUM( alone_chat_num ), 0 ) AS aloneChatTotal,
          IFNULL( ROUND( SUM( reply_timely_num ) / SUM( total_conversation_num ) * 100, 2 ), 0 ) AS chatTimelyRate,
          IFNULL( ROUND( SUM( total_conversation_duration ) / SUM( total_conversation_num )), 0 ) AS averageReplyTime,
          IFNULL( SUM( staff_group_join_num ), 0 ) AS staffGroupJoinTotal,
          IFNULL( SUM( staff_group_chat_num ), 0 ) AS staffGroupChatTotal
      FROM
          tb_statistic_staff
      WHERE
          statistic_date BETWEEN #{beginTime} AND #{endTime}
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and dept_id in
                <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and userid = #{wxUserId} and dept_id = #{deptId}
            </if>
        </if>
    </select>

  <select id="graph" resultType="com.cenker.scrm.pojo.vo.statistic.StatisticStaffSummaryVo">
        SELECT
            statistic_date AS statisticDate,
            COUNT( DISTINCT userid ) AS staffTotal,
            IFNULL( SUM( customer_del_num ), 0 ) AS delTotal,
            IFNULL( SUM( alone_chat_num ), 0 ) AS aloneChatTotal,
            IFNULL( ROUND( SUM( reply_timely_num ) / SUM( total_conversation_num ), 2 ), 0 ) AS chatTimelyRate,
            IFNULL( ROUND( SUM( total_conversation_duration ) / SUM( total_conversation_num )), 0 ) AS averageReplyTime,
            IFNULL( SUM( staff_group_join_num ), 0 ) AS staffGroupJoinTotal,
            IFNULL( SUM( staff_group_chat_num ), 0 ) AS staffGroupChatTotal
        FROM tb_statistic_staff
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
      <!-- 权限控制 -->
      <if test="dataScope == null or dataScope != '1'.toString()">
          <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
              and dept_id in
              <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
          <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
              and userid = #{wxUserId} and dept_id = #{deptId}
          </if>
      </if>
            GROUP BY statistic_date
		    ORDER BY statistic_date
    </select>
</mapper>
