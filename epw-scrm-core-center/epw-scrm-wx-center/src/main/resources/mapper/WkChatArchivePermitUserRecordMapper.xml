<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.chatarchive.WkChatArchivePermitUserRecordMapper">

    <insert id="batchInsertPermitUser">
        insert into wk_chat_archive_permit_user_record (`id`, `corp_id`, `user_id`, `status`, `start_time`, `create_time`) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},#{item.corpId},#{item.userId},#{item.status},#{item.startTime},#{item.createTime})
        </foreach>
    </insert>

    <update id="batchClosePermitUser">
        update wk_chat_archive_permit_user_record set `status`=0,end_time=now(),update_time=now() where user_id in
        <foreach item="id" collection="userIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchOpenPermitUser">
        update wk_chat_archive_permit_user_record set `status`=1, start_time=now(), update_time=now() where user_id in
        <foreach item="id" collection="userIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectPermitUserList" parameterType="String" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchivePermitUserVo">
        SELECT
            pur.status chatStatus,
            pur.start_time startTime,
            pur.user_id userId,
            wu.name,
            wu.avatar,
            wu.status userStatus,
	        wu.main_department mainDepartment,
	        wu.gender gender,
	        wu.del_flag delFlag
        FROM
            wk_chat_archive_permit_user_record pur
        LEFT JOIN tb_wx_user wu on wu.userid = pur.user_id
        WHERE
            pur.corp_id = #{corpId} and  wu.userid is not null
            <if test="departId != null and departId != ''">
                and wu.main_department = #{departId}
            </if>
    </select>
    <select id="selectPermitUserListByNullDepart" parameterType="String" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchivePermitUserVo">
        SELECT
            pur.status chatStatus,
            pur.start_time startTime,
            pur.user_id userId,
            wu.name,
            wu.avatar,
            wu.status userStatus,
	        wu.main_department mainDepartment,
	        wu.gender gender,
	        wu.del_flag delFlag
        FROM
            wk_chat_archive_permit_user_record pur
        LEFT JOIN tb_wx_user wu on wu.userid = pur.user_id
        WHERE
            pur.corp_id = #{corpId}
            and wu.userid is not null
            and wu.main_department is null
    </select>

    <select id="selectPermitUserListByFindNotDepart" parameterType="String" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchivePermitUserVo">
        SELECT
            DISTINCT
            pur.status chatStatus,
            pur.start_time startTime,
            pur.user_id userId,
            wu.name,
            wu.avatar,
            wu.status userStatus,
            wu.main_department mainDepartment,
            wu.gender gender,
            wu.del_flag delFlag
        FROM
            wk_chat_archive_permit_user_record pur
                LEFT JOIN tb_wx_user wu ON pur.user_id = wu.userid
                LEFT JOIN tb_wx_department dep ON wu.main_department != dep.id
        WHERE
            pur.corp_id = #{corpId}
          and wu.main_department IS NOT NULL
          AND dep.id IS NOT NULL
    </select>
</mapper>