<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.subscr.BuSubscriptionMapper">
    <!-- 自定义SQL查询 -->
    <select id="selectSubscriptionListByExternalUserId"
            resultType="com.cenker.scrm.pojo.vo.subscr.BuSubscriptionVO">
        SELECT
        s.id,
        s.external_user_id,
        (case when release_menu.sub_menu_id is not null then release_menu.sub_menu_id
        else s.sub_menu_id end) as sub_menu_id,
        (case when release_menu.first_sub_menu_id is not null then release_menu.first_sub_menu_id
        else s.first_sub_menu_id end) as first_sub_menu_id,
        (case when release_menu.first_sub_menu_id is not null then release_menu.first_sub_menu_name
        else mss.sub_menu_name end) as first_sub_menu_name,
        s.section_id,
        bss.section_name
        FROM
        bu_subscription s
        left join bu_subscription_menu_sub mss ON mss.id = s.first_sub_menu_id
        left join bu_subscription_section bss ON bss.id = s.section_id
        left join (
            select
            bssm.section_id,
            bssm.id as sub_menu_id,
            (case when bssmss.id is not null then bssmss.id
            when bssms.id is not null then bssms.id
            else bssm.id end) as first_sub_menu_id,
            (case when bssmss.id is not null then bssmss.sub_menu_name
            when bssms.id is not null then bssms.sub_menu_name
            else bssm.sub_menu_name end) as first_sub_menu_name
            from bu_subscription_menu bsm, bu_subscription_menu_sub bssm
            left join bu_subscription_menu_sub bssms on bssm.parent_id = bssms.id
            left join bu_subscription_menu_sub bssmss on bssms.parent_id = bssmss.id
            where bsm.id = bssm.menu_id and bsm.release_status = 1 and bssm.section_id is not null
        ) release_menu on release_menu.section_id = s.section_id
        WHERE
        s.external_user_id = #{externalUserId}
        AND s.del_flag = 0 and bss.del_flag = 0
        ORDER BY
        s.subscr_time DESC
    </select>

    <resultMap id="BuSubscriptionVOMap" type="com.cenker.scrm.pojo.vo.subscr.BuSubscriptionVO">
        <id property="id" column="id"/>
        <result property="externalUserId" column="external_user_id"/>
        <result property="firstSubMenuId" column="first_sub_menu_id"/>
        <result property="firstSubMenuName" column="first_sub_menu_name"/>
        <result property="sectionId" column="section_id"/>
        <result property="sectionName" column="section_name"/>
        <result property="radarId" column="radar_id"/>
        <result property="radarType" column="radar_type"/>
        <result property="radarSummary" column="radar_summary"/>
        <result property="radarUploadTime" column="radar_upload_time"/>
        <association property="tbWxRadarContent" javaType="com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent">
            <result column="content_id" property="id"/>
            <result column="digest" property="digest"/>
            <result column="title" property="title"/>
            <result column="cover" property="cover"/>
            <result column="show_status" property="showStatus"/>
        </association>
    </resultMap>

    <select id="selectSubscrSectionList" resultMap="BuSubscriptionVOMap">
        SELECT
        concat(s.id, br.id) as id,
        s.external_user_id,
        s.first_sub_menu_id,
        mss.sub_menu_name as first_sub_menu_name,
        s.section_id,
        bss.section_name,
        br.radar_id,
        i.type as radar_type,
        br.upload_time as radar_upload_time,
        br.summary as radar_summary,
        c.show_status,
        c.title,
        c.digest,
        c.cover,
        c.id as content_id
        FROM
        bu_subscription_section bss,
        bu_subscription s
        left join bu_section_radar br ON br.section_id = s.section_id and br.del_flag = 0
        left join bu_subscription_menu_sub mss ON mss.id = s.first_sub_menu_id
        LEFT JOIN tb_wx_radar_interact i ON br.radar_id = i.id
        LEFT JOIN tb_wx_radar_content c ON br.radar_id = c.radar_id
        WHERE bss.id = s.section_id and bss.del_flag = 0 and
        s.external_user_id = #{externalUserId}
        <choose>
            <when test="type != null and type == 'today'">
                AND s.del_flag = 0
                AND date(br.upload_time) = CURDATE()
            </when>
            <when test="type != null and type == 'week'">
                AND s.del_flag = 0
                AND date(br.upload_time) > CURDATE() - INTERVAL (WEEKDAY(CURDATE()) + 1) DAY AND br.upload_time &lt; CURDATE()
                AND not exists (
                    select * from bu_section_radar br1
                    where br1.section_id = s.section_id and br1.del_flag = 0 AND date(br1.upload_time) = CURDATE()
                )
            </when>
            <when test="type != null and type == 'history'">
                and br.upload_time is not null
                AND not exists (
                    select * from bu_section_radar br1, bu_subscription bs1
                    where br1.section_id = s.section_id and br1.del_flag = 0
                    AND date(br1.upload_time) > CURDATE() - INTERVAL (WEEKDAY(CURDATE()) + 1) DAY AND date(br1.upload_time) &lt;= CURDATE()
                    and br1.section_id = bs1.section_id and bs1.del_flag = 0
                )
            </when>
        </choose>
        ORDER BY
        s.subscr_time DESC
    </select>
    <select id="selectSubscrSectionByIds"
            resultType="com.cenker.scrm.pojo.vo.subscr.BuSubscriptionVO">
        select
            bssm.section_id,
            bss.section_name,
            bssm.id as sub_menu_id,
            (case when bssmss.id is not null then bssmss.id
            when bssms.id is not null then bssms.id
            else bssm.id end) as first_sub_menu_id,
            (case when bssmss.id is not null then bssmss.sub_menu_name
            when bssms.id is not null then bssms.sub_menu_name
            else bssm.sub_menu_name end) as first_sub_menu_name
        from bu_subscription_section bss,
            bu_subscription_menu bsm, bu_subscription_menu_sub bssm
            left join bu_subscription_menu_sub bssms on bssm.parent_id = bssms.id
            left join bu_subscription_menu_sub bssmss on bssms.parent_id = bssmss.id
        where bss.id = bssm.section_id and bsm.id = bssm.menu_id and bsm.release_status = 1
        and bssm.section_id in
        <foreach collection="sectionIds" item="sectionId" open="(" separator="," close=")">
            #{sectionId}
        </foreach>

    </select>
    <select id="selectNotIn" resultType="java.lang.String">
        select tb.external_user_id
        from tb_wx_ext_customer tb
        where tb.external_user_id not in (
            select external_user_id from bu_subscription
        ${ew.customSqlSegment}
        )
    </select>
</mapper>