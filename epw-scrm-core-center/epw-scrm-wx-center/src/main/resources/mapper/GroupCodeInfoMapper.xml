<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.group.GroupCodeInfoMapper">

    <select id="listGroupCode" resultType="com.cenker.scrm.pojo.vo.group.GroupCodeListVO">
         select
                t.*
         from (
               select
               info.id codeId,
               info.code_name,
               info.code_type,
               (select nick_name from sys_user s where s.user_id = info.create_by) create_user_name,
               info.create_time,
               info.group_code_url,
               info.use_status,
               (select count(1) from ck_click_record where scene_id = info.id and scene = 1) scanCodeCnt,
               ifnull((select count(1) from wk_wx_customer_group_member_log m
               where m.state = info.state
               and m.change_type = 1 and m.type =2 and date_format(m.change_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
               ),0) todayAddGroupCnt,
               ifnull((select count(1) from wk_wx_customer_group_member_log m
               where m.state = info.state
               and m.change_type = 1 and m.type =2
               ),0) totalAddGroupCnt
               from ck_group_code_info info
               where info.is_deleted = 0
               <if test="codeName != null and codeName != ''">
                   and info.code_name like concat('%',#{codeName},'%')
               </if>
               <if test="codeType != null">
                   and info.code_type = #{codeType}
               </if>
                <!-- 权限控制 -->
                <if test="dataScope == null or dataScope != '1'.toString()">
                    <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                        and info.dept_id in
                        <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                    </if>
                    <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                        and info.create_by = #{userId}
                    </if>
                </if>
               <if test="useStatus != null">
                   and info.use_status = #{useStatus}
               </if>
        )t
        <where>
            <if test="createUserName != null and createUserName != ''">
                and t.create_user_name like concat('%',#{createUserName},'%')
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="getGroupListByCodeId" resultType="com.cenker.scrm.pojo.dto.group.GroupCodeConfigDTO">
        select
            config.chat_id,
            g.owner chatOwnerUserId,
            if(g.group_name = '','群聊',g.group_name)  chatGroupName,
            (select name from tb_wx_user where userid = g.owner limit 1) chatOwnerName,
            (select count(gm.user_id) from tb_wx_customer_group_member gm where gm.group_id = g.chat_id and gm.`status` = 0) chatMemberCnt
        from ck_group_code_config config
        join tb_wx_customer_group g on g.chat_id = config.chat_id
        where config.group_code_id  = #{codeId}
        and config.is_deleted = 0
    </select>


    <resultMap id="detailGroupCodeMap" type="com.cenker.scrm.pojo.vo.group.GroupCodeDetailVO">
        <id column="id" property="codeId"/>
        <result column="is_auto_create_room" property="autoCreateRoom"/>
        <result column="state" property="codeState"/>
        <result column="use_status" property="useStatus"/>
        <collection property="groupList" column="{codeId=id}" select="getGroupListByCodeId"/>
    </resultMap>

    <select id="detailGroupCode" resultMap="detailGroupCodeMap">
        select
               info.id,
               info.code_type,
               info.code_name,
               info.is_auto_create_room,
               info.room_base_id,
               info.state,
               ifnull(info.room_base_name,'')room_base_name,
               info.spare_code_id,
               info.spare_code_url,
               info.create_time,
               info.group_code_url,
               (select nick_name from sys_user s where s.user_id = info.create_by) create_user_name,
               info.tag,
               info.use_status
        from ck_group_code_info info
        where info.id = #{codeId}
        and info.is_deleted = 0
    </select>

    <select id="getDataStatistics" resultType="com.cenker.scrm.pojo.vo.group.GroupCodeDataStatisticsVO">
        select
            info.id codeId,
            info.code_type,
            info.code_name,
            info.create_time,
            (select count(1) from ck_click_record where scene_id = info.id and scene = 1
             <if test="queryBeginTime != null">
                  and click_date >= #{queryBeginTime}
             </if>
             <if test="queryEndTime != null">
                 and click_date &lt;= #{queryEndTime}
             </if>
              ) scanCodeCnt,
            ifnull((select count(1) from wk_wx_customer_group_member_log m
                    where m.state = info.state
                      and m.change_type = 1 and m.type = 2
                      <if test="queryBeginTime != null">
                          and m.change_time >= #{queryBeginTime}
                      </if>
                      <if test="queryEndTime != null">
                          and m.change_time &lt;= #{queryEndTime}
                      </if>
                   ),0) totalAddGroupCnt,
            ifnull((select count(1) from wk_wx_customer_group_member_log m
                    where m.state = info.state
                      and m.change_type = 2 and m.type = 2
                      <if test="queryBeginTime != null">
                          and m.change_time >= #{queryBeginTime}
                      </if>
                      <if test="queryEndTime != null">
                          and m.change_time &lt;= #{queryEndTime}
                      </if>
                   ),0) totalQuitGroupCnt
        from ck_group_code_info info
        where info.id = #{codeId}
        and info.is_deleted = 0
    </select>

    <select id="getDataStatisticsDetailByGroup"
            resultType="com.cenker.scrm.pojo.vo.group.GroupCodeDataStatisticsDetailGroupVO">
        select
            t.*,
            t.totalAddGroupCnt - t.totalQuitGroupCnt totalNetAddGroupCnt
        from (
                 select
                     g.chat_id,
                     if(g.group_name = '','群聊',g.group_name) chatGroupName,
                     g.`owner` chatOwnerUserId,
                     (select name from tb_wx_user where userid = g.`owner` limit 1) chatOwnerName,
                     (select count(gm.user_id) from tb_wx_customer_group_member gm where gm.group_id = g.chat_id and gm.`status` = 0) chatMemberCnt,
                     ifnull((select count(1) from wk_wx_customer_group_member_log m
                             where m.state = (select work_group_info.state from wk_wx_group_chat_info work_group_info where work_group_info.group_code_id = code_info.id limit 1)
                               and m.change_type = 1 and m.type = 2 and m.group_id = g.chat_id
                            ),0) totalAddGroupCnt,
                     ifnull((select count(1) from wk_wx_customer_group_member_log m
                             where m.state = (select work_group_info.state from wk_wx_group_chat_info work_group_info where work_group_info.group_code_id = code_info.id limit 1)
                               and m.change_type = 2 and m.type = 2 and m.group_id = g.chat_id
                            ),0) totalQuitGroupCnt
                 from ck_group_code_info code_info
                          join ck_group_code_config code_config on code_info.id = code_config.group_code_id
                          join tb_wx_customer_group g on g.chat_id = code_config.chat_id
                 where code_info.id = #{codeId}
                   and code_info.is_deleted = 0
                   and code_config.is_deleted = 0
                   and g.`status` = 0
                   <if test="chatGroupName != null and chatGroupName != ''">
                       and g.group_name like concat('%',#{chatGroupName},'%')
                   </if>
                 order by g.create_time desc
             )t
            <where>
                <if test="chatOwnerName != null and chatOwnerName != ''">
                    and t.chatOwnerName like concat('%',#{chatOwnerName},'%')
                </if>
            </where>
    </select>

    <select id="getDataStatisticsDetailByDate"
            resultType="com.cenker.scrm.pojo.vo.group.GroupCodeDataStatisticsDetailDateVO">
        select
            date_format(log.change_day,'%Y-%m-%d') statisticsDate,
            count(log.user_id) totalAddGroupCnt
        from wk_wx_customer_group_member_log log
        where log.type = 2
          and log.change_type = #{changeType}
          and log.state = (select state from ck_group_code_info where id = #{codeId} and is_deleted = 0)
          and log.change_day >= #{queryBeginTime}
          and log.change_day &lt;= #{queryEndTime}
          group by log.change_day
    </select>

    <select id="getDataStatisticsDetailByHour"
            resultType="com.cenker.scrm.pojo.vo.group.GroupCodeDataStatisticsDetailDateVO">
        select
            if(date_format(log.change_time,'%H') > 9,date_format(log.change_time,'%H') ,substr(date_format(log.change_time,'%H'),2,1)) statisticsDate,
            count(log.user_id) totalAddGroupCnt
        from wk_wx_customer_group_member_log log
        where log.type = 2
          and log.change_type = #{changeType}
          and log.state = (select state from ck_group_code_info where id = #{codeId} and is_deleted = 0)
          and log.change_day >= #{queryBeginTime}
          and log.change_day &lt;= #{queryEndTime}
        group by date_format(log.change_time,'%H')
    </select>

    <select id="getDataStatisticsDetail4ScanByHour"
            resultType="com.cenker.scrm.pojo.vo.group.GroupCodeDataStatisticsDetailDateVO">
        select
             if(date_format(click_date,'%H') > 9,date_format(click_date,'%H') ,substr(date_format(click_date,'%H'),2,1))statisticsDate,
             count(1)scanCodeCnt
        from ck_click_record where scene_id = #{codeId} and scene = 1
        and click_date >= #{queryBeginTime}
        and click_date &lt;= #{queryEndTime}
        group by date_format(click_date,'%H')
    </select>

    <select id="getDataStatisticsDetail4ScanByDate" resultType="com.cenker.scrm.pojo.vo.group.GroupCodeDataStatisticsDetailDateVO">
        select
            date_format(click_date,'%Y-%m-%d') statisticsDate,
            count(1)scanCodeCnt
        from ck_click_record
        where scene_id = #{codeId}
        and scene = 1
        and click_date >= #{queryBeginTime}
        and click_date &lt;= #{queryEndTime}
        group by date_format(click_date,'%Y-%m-%d')
    </select>
</mapper>