<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cenker.scrm.mapper.statistic.TbStatisticReplyTimeoutMapper">

    <insert id="saveStatisticDateByDay">
        INSERT INTO `tb_statistic_reply_timeout`(`statistic_date`, `userid`, `user_name`, `dept_id`, `dept_name`, `timeout_times`, `create_time`)
        SELECT
            str_to_date(#{statDate}, '%Y-%m-%d') as statistic_date,
            u.userid,
            u.NAME AS user_name,
            u.main_department AS dept_id,
            d.NAME AS dept_name,
            COUNT( DISTINCT c.origin_msg_id ) AS timeout_times,
            NOW() AS create_time
        FROM
            wk_chat_conversation c
            JOIN tb_wx_user u ON u.userid = c.staff_id
            LEFT JOIN tb_wx_department d ON d.id = u.main_department
        WHERE
            c.timeout = 1
            AND c.statistic_date = str_to_date(#{statDate}, '%Y-%m-%d')
        GROUP BY
            c.staff_id
    </insert>

    <select id="summary" resultType="com.cenker.scrm.pojo.vo.statistic.StatisticReplyTimeoutSummaryVo">
        SELECT
            COUNT(DISTINCT userid) AS timeoutNum,
            IFNULL(SUM(timeout_times), 0) AS timeoutTimes
        FROM
            tb_statistic_reply_timeout
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and dept_id in
                <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and userid = #{wxUserId} and dept_id = #{deptId}
            </if>
        </if>
    </select>

  <select id="graph" resultType="com.cenker.scrm.pojo.vo.statistic.StatisticReplyTimeoutSummaryVo">
        SELECT
                statistic_date AS statisticDate,
                COUNT(DISTINCT userid) AS timeoutNum,
            IFNULL(SUM(timeout_times), 0) AS timeoutTimes
        FROM
                tb_statistic_reply_timeout
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
          <!-- 权限控制 -->
          <if test="dataScope == null or dataScope != '1'.toString()">
              <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                  and dept_id in
                  <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                      #{item}
                  </foreach>
              </if>
              <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                  and userid = #{wxUserId} and dept_id = #{deptId}
              </if>
          </if>
            GROUP BY statistic_date
		    ORDER BY statistic_date
    </select>
</mapper>
