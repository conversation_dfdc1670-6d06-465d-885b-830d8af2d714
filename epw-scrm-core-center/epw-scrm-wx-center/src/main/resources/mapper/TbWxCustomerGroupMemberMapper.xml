<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.group.TbWxCustomerGroupMemberMapper">

    <resultMap type="TbWxCustomerGroupMember" id="TbWxCustomerGroupMemberResult">
        <result property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="type" column="type"/>
        <result property="userId" column="user_id"/>
        <result property="joinTime" column="join_time"/>
        <result property="joinScene" column="join_scene"/>
        <result property="unionId" column="union_id"/>
        <result property="corpId" column="corp_id"/>
        <result property="status" column="status"/>
        <result property="departureTime" column="departure_time"/>
    </resultMap>

    <sql id="selectTbWxCustomerGroupMemberVo">
        select id, group_id, type, user_id, join_time, join_scene, union_id, corp_id, status, departure_time from tb_wx_customer_group_member
    </sql>

    <select id="selectTbWxCustomerGroupMemberList" parameterType="TbWxCustomerGroupMember"
            resultMap="TbWxCustomerGroupMemberResult">
        <include refid="selectTbWxCustomerGroupMemberVo"/>
        <where>
            <if test="groupId != null  and groupId != ''">
                and group_id = #{groupId}
            </if>
            <if test="type != null  and type != ''">
                and type = #{type}
            </if>
            <if test="userId != null  and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="joinTime != null ">
                and join_time = #{joinTime}
            </if>
            <if test="joinScene != null  and joinScene != ''">
                and join_scene = #{joinScene}
            </if>
            <if test="unionId != null  and unionId != ''">
                and union_id = #{unionId}
            </if>
            <if test="corpId != null  and corpId != ''">
                and corp_id = #{corpId}
            </if>
            <if test="status != null ">
                and status = #{status}
            </if>
            <if test="departureTime != null ">
                and departure_time = #{departureTime}
            </if>
        </where>
    </select>

    <select id="selectTbWxCustomerGroupMemberById" parameterType="Long"
            resultMap="TbWxCustomerGroupMemberResult">
        <include refid="selectTbWxCustomerGroupMemberVo"/>
        where id = #{id}
    </select>

    <insert id="insertTbWxCustomerGroupMember" parameterType="TbWxCustomerGroupMember" useGeneratedKeys="true"
            keyProperty="id">
        insert into tb_wx_customer_group_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupId != null">group_id,
            </if>
            <if test="type != null">type,
            </if>
            <if test="userId != null">user_id,
            </if>
            <if test="joinTime != null">join_time,
            </if>
            <if test="joinScene != null">join_scene,
            </if>
            <if test="unionId != null">union_id,
            </if>
            <if test="corpId != null">corp_id,
            </if>
            <if test="status != null">status,
            </if>
            <if test="departureTime != null">departure_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupId != null">#{groupId},
            </if>
            <if test="type != null">#{type},
            </if>
            <if test="userId != null">#{userId},
            </if>
            <if test="joinTime != null">#{joinTime},
            </if>
            <if test="joinScene != null">#{joinScene},
            </if>
            <if test="unionId != null">#{unionId},
            </if>
            <if test="corpId != null">#{corpId},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="departureTime != null">#{departureTime},
            </if>
        </trim>
    </insert>

    <update id="updateTbWxCustomerGroupMember" parameterType="TbWxCustomerGroupMember">
        update tb_wx_customer_group_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupId != null">group_id =
                #{groupId},
            </if>
            <if test="type != null">type =
                #{type},
            </if>
            <if test="userId != null">user_id =
                #{userId},
            </if>
            <if test="joinTime != null">join_time =
                #{joinTime},
            </if>
            <if test="joinScene != null">join_scene =
                #{joinScene},
            </if>
            <if test="unionId != null">union_id =
                #{unionId},
            </if>
            <if test="corpId != null">corp_id =
                #{corpId},
            </if>
            <if test="status != null">status =
                #{status},
            </if>
            <if test="departureTime != null">departure_time =
                #{departureTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbWxCustomerGroupMemberById" parameterType="Long">
        delete from tb_wx_customer_group_member where id = #{id}
    </delete>

    <delete id="deleteTbWxCustomerGroupMemberByIds" parameterType="String">
        delete from tb_wx_customer_group_member where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="queryMemberInfoByGroupId" resultType="CustomerMemberVo">
        SELECT type,
            case when type = '2' then
            (select max(name) from tb_wx_ext_customer where external_user_id = t.user_id and corp_id = #{corpId})
            else
            (select max(name) from tb_wx_user where userid = t.user_id and corp_id = #{corpId})
            end
            userName,join_time,join_scene
        from
          tb_wx_customer_group_member t
        where
          t.`status` = 0
          and group_id =#{groupId} and corp_id = #{corpId}
          <if test="memberName != null  and memberName != ''">
            and (user_id in(
            select external_user_id from tb_wx_ext_customer where name = #{memberName}
            and corp_id = #{corpId}
            ) or user_id in (select userid from tb_wx_user where name = #{memberName})
            )
          </if>
    </select>

    <update id="bathUpdateTbWxCustomerGroupMember" parameterType="TbWxCustomerGroupMember">
        update tb_wx_customer_group_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status =
                #{status},
            </if>
            <if test="departureTime != null">departure_time =
                #{departureTime},
            </if>
        </trim>
        where corp_id = #{corpId} and group_id = #{groupId}
    </update>

    <select id="queryMemberInfoByGroupId2" resultType="CustomerMemberVo">
        select
            name user_name,
            join_time,
            user_id,
            `owner`,
            add_type,
            member_type,
            join_scene,
            type,
            member_type_str,
            (case when member_type = 2 then (select u.avatar from tb_wx_user u where u.userid = user_id and u.corp_id = corp_id limit 1)
            else ifnull(avatar,#{avatar}) end)avatar
        from (
                 select
                     m.corp_id,
                     c.avatar,
                     IFNULL(c.`name`,m.`name`) name,
                     m.join_time,
                     m.user_id,
                     if (m.user_id = g.`owner`,1,0)`owner`,
                     (case when c.type = 1 then '@微信'
                           when c.type = 2 then concat('@',c.corp_name)
                           else '' end)add_type,
                     ifnull(c.type,0)type,
                     (case when m.type = 1 then 2
                           when m.type = 2 and c.type is not null then 1
                           else  3 end
                         )	member_type,
                     (case when m.type = 1 then '员工'
                           when m.type = 2 and c.type is not null then '客户'
                           else  '非企业客户' end
                         )	member_type_str,
                     (
                         case when m.join_scene = 1  and m.invitor_user_id is null then '由群成员邀请入群'
                              when m.join_scene = 1  and m.invitor_user_id is not null then
                                  concat('由员工',IFNULL((select name from tb_wx_user where m.invitor_user_id = userid and m.corp_id = corp_id),''),'直接邀请入群')
                              when m.join_scene = 2 then '由群成员发送邀请链接入群'
                              when m.join_scene = 3 then '通过扫描群二维码入群'
                              else '-' end
                         )join_scene
                 from tb_wx_customer_group_member m
                          left join tb_wx_customer_group g on m.group_id = g.chat_id
                          left join tb_wx_ext_customer c on m.user_id = c.external_user_id and m.corp_id = c.corp_id
                 where m.corp_id = #{corpId}
                   and m.group_id = #{groupId}
                   and m.`status` = 0
                 order by m.join_time desc
             )t
          <where>
              <if test="memberName != null and memberName != ''">
                   and t.name like concat('%',#{memberName},'%')
              </if>
              <if test="memberType != null">
                  and t.member_type =  #{memberType}
              </if>
          </where>
    </select>

    <select id="countGroupDataByWorkBenchHome" resultType="com.cenker.scrm.pojo.vo.group.GroupCustomerUserTotalVo">
        select
            count(distinct g.chat_id)groupUserChatTotal,
            ifnull(sum(m.`status` = 0 and date_format(m.join_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d') and m.type =2),0)todayGroupAddTotal,
            ifnull(sum(m.`status` != 0 and date_format(m.departure_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d') and m.type =2),0)todayGroupDecreaseTotal
        from tb_wx_customer_group_member m
                 left join tb_wx_customer_group g on m.group_id = g.chat_id
        where g.`status` = 0
          and g.dismiss_status = 0
          and g.corp_id = #{corpId}
          and g.`owner` = #{userId}
    </select>

    <select id="countGroupMemberByDate" resultType="com.cenker.scrm.pojo.vo.group.GroupCustomerUserTotalVo">
        select
            count( DISTINCT CASE WHEN `STATUS` = 0 THEN user_id END ) AS `groupUserChatTotal`,
            count( CASE WHEN type = '2' AND date( join_time ) = str_to_date(#{statDate}, '%Y-%m-%d') THEN user_id END ) AS `todayGroupAddTotal`,
            count( CASE WHEN type = '2' AND date( departure_time ) = str_to_date(#{statDate}, '%Y-%m-%d') THEN user_id END ) AS `todayGroupDecreaseTotal`
        from tb_wx_customer_group_member
        <where>
            <if test="owner != null and owner != ''">
                and group_id in (select chat_id from tb_wx_customer_group where owner = #{owner})
            </if>
        </where>
    </select>

    <select id="ge4AddGroupCount4Sop" resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        select
            (select c.id from tb_wx_ext_customer c where c.external_user_id = m.user_id limit 1)id,
            m.user_id external_user_id
        from tb_wx_customer_group_member m
                 join tb_wx_customer_group g on m.group_id = g.chat_id
        where g.`status` = 0
          and m.type = 2
        and m.`status` = 0
        and m.user_id in
        <foreach item="item" collection="triggerConditionValue.customerList" open="(" separator="," close=")">
            #{item.externalUserId}
        </foreach>
        group by m.user_id
        having count(m.group_id) >= #{triggerConditionValue.conditionCnt}
    </select>

    <select id="assignSelectAddGroup4Sop" resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        select
        (select c.id from tb_wx_ext_customer c where c.external_user_id = m.user_id limit 1)id,
        m.user_id external_user_id
        from tb_wx_customer_group_member m
        join tb_wx_customer_group g on m.group_id = g.chat_id
        where g.`status` = 0
        and m.type = 2
        and m.`status` = 0
        <if test="triggerRelationType == 1">
            and g.chat_id = #{triggerConditionValue.groupConditionList[0].chatId}
        </if>
        <if test="triggerRelationType == 2">
            and m.user_id not in (
                  select m1.user_id from tb_wx_customer_group_member m1
                  where m1.type = 2 and m1.`status` = 0
                  and m1.group_id = #{triggerConditionValue.groupConditionList[0].chatId}
            )
        </if>
        <if test="triggerRelationType == 3">
            and g.chat_id in
            <foreach item="item" collection="triggerConditionValue.groupConditionList" open="(" separator="," close=")">
                #{item.chatId}
            </foreach>
        </if>
        <if test="triggerRelationType == 4">
            and m.user_id not in (
            select m1.user_id from tb_wx_customer_group_member m1
            where m1.type = 2 and m1.`status` = 0
            and m1.group_id in
            <foreach item="item" collection="triggerConditionValue.groupConditionList" open="(" separator="," close=")">
                #{item.chatId}
            </foreach>
            )
        </if>
        and m.user_id in
        <foreach item="item" collection="triggerConditionValue.customerList" open="(" separator="," close=")">
            #{item.externalUserId}
        </foreach>
        group by m.user_id
    </select>
</mapper>