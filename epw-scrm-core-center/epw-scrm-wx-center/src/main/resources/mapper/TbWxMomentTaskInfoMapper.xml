<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.moment.TbWxMomentTaskInfoMapper">

    <resultMap id="list" type="com.cenker.scrm.pojo.vo.moment.SendMomentListVO">
        <result property="id" column="id"/>
        <result property="taskName" column="task_name"/>
        <result property="momentType" column="moment_type"/>
        <result property="content" column="content"/>
        <result property="settingTime" column="setting_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createByUser" column="create_by_user"/>
        <result property="sentCnt" column="sent_cnt"/>
        <result property="unsentCnt" column="unsent_cnt"/>
        <result property="checkStatus" column="check_status"/>
        <result property="enableApproval" column="enable_approval"/>
        <result property="approvalUser" column="approval_user"/>
        <result property="approvalUserName" column="approval_user_name"/>
        <result property="approvalRemark" column="approval_remark"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="attachmentVo" column="msg_content"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
    </resultMap>

    <resultMap id="listByCorpId" type="com.cenker.scrm.pojo.vo.moment.SendMomentListVO">
        <result property="id" column="id"/>
        <result property="momentType" column="moment_type"/>
        <result property="content" column="content"/>
        <result property="corpId" column="corp_id"/>
        <result property="settingTime" column="setting_time"/>
        <result property="attachmentVo" column="msg_content"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result property="conditionVo" column="send_condition"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
    </resultMap>

    <resultMap id="byId" type="com.cenker.scrm.pojo.vo.moment.SendMomentListVO">
        <result property="id" column="id"/>
        <result property="corpId" column="corp_id"/>
        <result property="momentType" column="moment_type"/>
        <result property="timedTask" column="timed_task"/>
        <result property="content" column="content"/>
        <result property="settingTime" column="setting_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createByUser" column="create_by_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="checkStatus" column="check_status"/>
        <result property="enableApproval" column="enable_approval"/>
        <result property="approvalUser" column="approval_user"/>
        <result property="approvalUserName" column="approval_user_name"/>
        <result property="approvalRemark" column="approval_remark"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="conditionVo" column="send_condition"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result property="attachmentVo" column="msg_content" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
    </resultMap>

    <select id="getMomentList" resultMap="list">
        select m.id,
               m.task_name,
               m.moment_type,
               m.content,
               m.setting_time,
               m.msg_content,
               m.create_by,
               u.nick_name create_by_user,
               ifnull(sum(su.publish_status = 1),0) sent_cnt,
               ifnull(sum(su.publish_status = 0),0) unsent_cnt,
               concat_ws( '',  ifnull(round(( ifnull(sum(su.publish_status = 1),0) / count(su.publish_status) ) * 100),0), '%', NULL ) as finishRate,
                m.enable_approval,
                m.approval_user,
                au.nick_name approval_user_name,
                m.approval_remark,
                m.error_msg,
               m.check_status
        from tb_wx_moment_task_info m
        left join sys_user u on m.create_by = u.user_id and m.corp_id = u.corp_id
        left join sys_user au on m.approval_user = au.user_id and m.corp_id = au.corp_id
        left join tb_wx_moment_send_user su on su.moment_task_id = m.id
        where m.corp_id = #{corpId}
        and m.del_flag = 0
        <if test="createBy != null and createBy != ''">
            and u.nick_name like concat('%',#{createBy},'%')
        </if>
        <if test="taskName != null and taskName != ''">
            and m.task_name like concat('%',#{taskName},'%')
        </if>
        <if test="momentType != null">
            and m.moment_type = #{momentType}
        </if>
        <if test="beginTime != null and beginTime != '' ">
            and date_format(m.setting_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(m.setting_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="approvalUser != null and approvalUser != ''">
            and au.nick_name like concat('%',#{approvalUser},'%')
        </if>
        <if test="checkStatus != null and checkStatus != ''">
            and m.check_status = #{checkStatus}
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and m.dept_id in
                <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and m.create_by = #{userId}
            </if>
        </if>
        <if test="userList != null and userList.size > 0">
            and su.user_id in
            <foreach item="user" collection="userList" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>
        group by m.id
        order by m.create_time desc
    </select>

    <select id="getMoment" resultMap="byId">
        select
            t.id,
            t.task_name,
            t.moment_type,
            t.timed_task,
            u.nick_name create_by_user,
            t.content,
            t.create_time,
            t.setting_time,
            t.msg_content,
            t.update_time,
            t.create_by,
            t.enable_approval,
            t.approval_user,
            t.check_status,
            au.nick_name approval_user_name,
            t.approval_remark,
            t.error_msg,
            t.send_condition,
            t.corp_id,
            (
                select count(distinct c.external_user_id) from tb_wx_moment_send_customer c
                where c.moment_task_id = t.id
            )visible_cnt
        from tb_wx_moment_task_info t
                 left join sys_user u on t.create_by = u.user_id and t.corp_id = u.corp_id
                 left join sys_user au on t.approval_user = au.user_id and t.corp_id = au.corp_id

        where t.id = #{id}
    </select>

    <select id="getDataStatistics" resultType="com.cenker.scrm.pojo.vo.moment.MomentStatisticsVO">
        select
            count(1)totalSendUserCnt,
            ifnull(sum(publish_status = 1),0)sendUserCnt,
            ifnull(sum(publish_status = 0),0)unSendUserCnt,
            ifnull(total_send_customer,0)total_send_customer_cnt,
            ifnull(send_customer,0)send_customer_cnt,
            ifnull((total_send_customer - send_customer),0)un_send_customer_cnt,
            ifnull(comment_cnt,0)comment_cnt,
            ifnull(like_cnt,0)like_cnt
        from (
                 select
                     count(distinct sc.external_user_id)total_send_customer,
                     (select count(distinct c.external_user_id) from tb_wx_moment_send_customer c
                      where c.moment_task_id = u.moment_task_id
                        and c.visible = 1)send_customer,
                     (select
                          count(1)
                      from tb_wx_moment_interact
                      where moment_task_id = u.moment_task_id
                        and type = 2)comment_cnt,
                     (select
                          count(1)
                      from tb_wx_moment_interact
                      where moment_task_id = u.moment_task_id
                        and type = 1)like_cnt,
                     u.publish_status
                 from tb_wx_moment_send_user u
                          left join tb_wx_moment_send_customer sc on u.moment_task_id = sc.moment_task_id
                 where u.moment_task_id = #{id}
                 group by u.user_id
             )t
    </select>

    <select id="getMomentTaskToDoByCorpId" resultMap="listByCorpId">
        select m.id,
        m.moment_type,
        m.content,
        m.msg_content,
        m.send_condition
        from tb_wx_moment_task_info m
        where m.corp_id = #{corpId}
        and m.check_status = 'PENDING_EXEC'
        and m.setting_time &lt;= now()
        and m.del_flag = 0
    </select>

    <select id="getMomentPredictedNum" resultType="java.lang.Integer">
        select count(1) from(
        select count(1) from tb_wx_ext_follow_user f
        left join tb_wx_ext_customer c on c.external_user_id = f.external_user_id and c.corp_id = f.corp_id
        left join tb_wx_ext_follow_user_tag tag on tag.external_user_id = f.external_user_id and tag.corp_id = f.corp_id and tag.user_id = f.user_id
        <where>
            <if test="userList != null and userList.size>0">
                and f.user_id in
                <foreach item="userId" collection="userList" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            and f.corp_id = #{corpId}
            and f.`status` = 0
            and c.type = 1
            <if test="tagList != null and tagList.size>0">
                and tag.tag_id in
                <foreach item="tagId" collection="tagList" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </if>
        </where>
        group by f.external_user_id
        )t
    </select>

    <select id="getMomentTaskToDoList" resultMap="listByCorpId">
        select m.id,
               m.moment_type,
               m.content,
               m.msg_content,
               m.send_condition,
               m.corp_id,
               m.setting_time
        from tb_wx_moment_task_info m
        where
          m.check_status = 'PENDING_EXEC'
          and (m.setting_time,'%Y-%m-%d %H:%i:%S') >= (now(),'%Y-%m-%d %H:%i:%S')
          and m.del_flag = 0
    </select>
    <select id="listUser" resultType="com.cenker.scrm.pojo.dto.condition.UserConditionDTO">
        SELECT
        userid as userId,
        `name` as userName,
        avatar as userAvatar
        FROM
        tb_wx_user
        WHERE
        userid IN
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
</mapper>