<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.TbWxCustomFunctionMenuMapper">

    <select id="getCustomMenuList" resultType="SysMenu">
        select
            distinct m.menu_id, m.parent_id, m.menu_name, m.component, m.status, ifnull(m.perms,'') as perms,
                     concat('/',(select path from sys_menu where menu_id = (select parent_id from sys_menu where menu_id  = m.parent_id)),'/',
                            (select path from sys_menu where menu_id  = m.parent_id),'/',m.path) path,
                     m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num,
                     m.remark, m.case_link, m.help_link,m.help_title,m.help_icon,
                     m.icon1,m.icon2,m.corner_mark,m.corner_mark_back_ground,m.corner_mark_color
        from sys_menu m
                 join tb_wx_custom_function_menu fm on m.menu_id = fm.menu_id
        where fm.corp_pri_id = #{corpId}
          and m.`status` = 0
          and (select `status` from sys_menu where menu_id  = m.parent_id) = 0
          and (select `status` from sys_menu where menu_id = (select parent_id from sys_menu where menu_id  = m.parent_id)) = 0
        order by fm.order_num
    </select>

</mapper>