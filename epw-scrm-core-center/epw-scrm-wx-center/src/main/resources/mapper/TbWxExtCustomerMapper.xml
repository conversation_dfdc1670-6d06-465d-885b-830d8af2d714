<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.external.TbWxExtCustomerMapper">

    <select id="queryChurnCustomer" resultType="CustomerChurnVo">
        select
            t.external_user_id AS extUserId ,
            t.name custName,
            t.corp_full_name AS corpName,
            t.birthday,
            t.avatar,
            t1.create_time AS createTime,
            t.type,
            t2.name AS corpUserName,
            t.gender,
            t3.tag,
            t1.del_time churnDate,
            (case t1.status when '2' then '客户删除员工' when '3' then '员工删除客户' else '未知' end) churnStatus
        from
            tb_wx_ext_customer t
        LEFT JOIN tb_wx_ext_follow_user t1 on t.external_user_id = t1.external_user_id and t.corp_id = t1.corp_id
        LEFT JOIN tb_wx_user t2 on t1.user_id = t2.userid and t.corp_id = t2.corp_id
        LEFT JOIN
        (
            select external_user_id, GROUP_CONCAT(tag) as tag
            from tb_wx_ext_follow_user_tag group by external_user_id
        ) t3 ON t3.external_user_id = t.external_user_id
        where t1.`status` in ('2', '3')
        and t.corp_id = #{corpId}
        <if test="userIds != null  and userIds.size()>0 ">
            and t1.user_id in
            <foreach item="item" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customerName != null  and customerName != ''">
            and t.name like concat('%',#{customerName},'%')
        </if>
        <if test="tag != null  and tag != ''">
            and t3.tag like concat('%', #{tag}, '%')
        </if>
        <if test="extUserId != null  and extUserId != ''">
            and t.external_user_id = #{extUserId}
        </if>
        <if test="beginTime != null and beginTime != '' ">
            and date_format(t1.create_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(t1.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and t2.main_department in
                <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and t2.userid = #{wxUserId}
            </if>
        </if>
        order by t1.del_time desc
    </select>

    <select id="queryCustomerInfo" resultType="CustomerInfoVo" parameterType="CustomerChurnDTO">
        select
        t.extUserId,
        t.custName,
        t.corpName,
        t.addDate, t.type, t.birthday, t.avatar, t.gender, t.tag, t.mobiles,
        t.corpUserName,
        (case when t.add_way = 1 then ifnull((select remark from tb_wx_contact where state = t.state limit 1),'扫描二维码')
        when t.add_way = 2 then '搜索手机号'
        when t.add_way = 3 then '名片分享'
        when t.add_way = 4 then '群聊'
        when t.add_way = 5 then '手机通讯录'
        when t.add_way = 6 then '微信联系人'
        when t.add_way = 7 then '来自微信的添加好友申请'
        when t.add_way = 8 then '安装应用时自动添加'
        when t.add_way = 9 then '搜索邮箱'
        when t.add_way = 10 then '视频号添加'
        when t.add_way = 11 then '通过日程参与人添加'
        when t.add_way = 12 then '通过会议参与人添加'
        when t.add_way = 13 then '添加微信好友对应的企业微信'
        when t.add_way = 14 then '通过智慧硬件专属客服添加'
        when t.add_way = 15 then '通过上门服务客服添加'
        when t.add_way = 16 then '通过获客链接添加'
        when t.add_way = 17 then '通过定制开发添加'
        when t.add_way = 18 then '通过需求回复添加'
        when t.add_way = 21 then '通过第三方售前客服添加'
        when t.add_way = 22 then '通过可能的商务伙伴添加'
        when t.add_way = 24 then '通过接受微信账号收到的好友申请添加'
        when t.add_way = 201 then '内部成员共享'
        when t.add_way = 202 then '管理员/负责人分配'
        else '未知来源' end)add_way
        from (
        select
        t.external_user_id extUserId ,
        name custName,
        IFNULL(t.corp_full_name, t.corp_name) corpName,
        t. create_time addDate,
        t.type,
        t.birthday,
        t.avatar,
        (SELECT max(name) from tb_wx_user where userid = t.first_add_user_id) corpUserName,
        t.gender,
        t.tag,
        t.mobiles,
        (
        SELECT
        f.add_way
        FROM
        tb_wx_ext_follow_user f
        WHERE
        f.corp_id = t.corp_id
        AND f.external_user_id = t.external_user_id
        AND f.user_id = t.first_add_user_id
        limit 1
        ) add_way,
        (
        SELECT
        f.state
        FROM
        tb_wx_ext_follow_user f
        WHERE
        f.corp_id = t.corp_id
        AND f.external_user_id = t.external_user_id
        AND f.user_id = t.first_add_user_id
        limit 1
        ) state
        from
        tb_wx_ext_customer t
        where
        t.`status` ='0'
        and t.corp_id = #{corpId}
        <if test="userIds != null  and userIds.size()>0 ">
            and t.external_user_id in (
            select external_user_id from tb_wx_ext_follow_user where corp_id = #{corpId}
            and status ='0'
            and user_id in
            <foreach item="item" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="customerName != null  and customerName != ''">
            and t.name like concat('%',#{customerName},'%')
        </if>
        <if test="extUserId != null  and extUserId != ''">
            and t.external_user_id = #{extUserId}
        </if>
        <if test="tag != null  and tag != ''">
            and t.tag like concat('%', #{tag}, '%')
        </if>
        <if test="beginTime != null and beginTime != '' ">
            and date_format(t.create_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(t.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="corpUserIds != null and corpUserIds.size()>0 ">
            and t.first_add_user_id in
            <foreach item="item" collection="corpUserIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t.create_time desc )t
    </select>

    <resultMap id="queryFollowUserByExtUserIdMap" type="CorpUserInfoVo">
        <result property="userName" column="userName"/>
        <result property="userId" column="userId"/>
        <collection property="tags" column="{extUserId=external_user_id,corpId=corp_id,userId=userId}" select="selectTagsByExternalUserId"/>
    </resultMap>

    <select id="queryFollowUserByExtUserId" resultMap="queryFollowUserByExtUserIdMap">
        select t1.userName,
               t1.avatar,
               t1.addDate,
               t1.deptName,
               t1.tag,
               t1.remark,
               t1.description,
               t1.userid           userId,
               (case
                    when t1.addWay = 1 then ifnull((select remark from tb_wx_contact where state = t1.state limit 1),
                                                   '扫描二维码')
                    when t1.addWay = 2 then '搜索手机号'
                    when t1.addWay = 3 then '名片分享'
                    when t1.addWay = 4 then '群聊'
                    when t1.addWay = 5 then '手机通讯录'
                    when t1.addWay = 6 then '微信联系人'
                    when t1.addWay = 7 then '来自微信的添加好友申请'
                    when t1.addWay = 8 then '安装应用时自动添加'
                    when t1.addWay = 9 then '搜索邮箱'
                    when t1.addWay = 10 then '视频号添加'
                    when t1.addWay = 11 then '通过日程参与人添加'
                    when t1.addWay = 12 then '通过会议参与人添加'
                    when t1.addWay = 13 then '添加微信好友对应的企业微信'
                    when t1.addWay = 14 then '通过智慧硬件专属客服添加'
                    when t1.addWay = 15 then '通过上门服务客服添加'
                    when t1.addWay = 16 then '通过获客链接添加'
                    when t1.addWay = 17 then '通过定制开发添加'
                    when t1.addWay = 18 then '通过需求回复添加'
                    when t1.addWay = 21 then '通过第三方售前客服添加'
                    when t1.addWay = 22 then '通过可能的商务伙伴添加'
                    when t1.addWay = 24 then '通过接受微信账号收到的好友申请添加'
                    when t1.addWay = 201 then '内部成员共享'
                    when t1.addWay = 202 then '管理员/负责人分配'
                    else '未知来源' end) addWay,
               (case
                    when (t1.`status` = 2 or t1.`status` = 3) then 2
                    when t1.staff_status = 2 then 1
                    else 0 end)    `status`,
               t1.state,
               t1.selfTag,
               t1.external_user_id,
               t1.corp_id
        from (
                 select u.`name`   userName,
                        u.userid,
                        u.avatar,
                        (select fu.create_time
                         from tb_wx_ext_follow_user fu
                         where fu.external_user_id = t.external_user_id
                           and fu.user_id = u.userid
                         order by fu.`status`, fu.create_time desc
                         limit 1)  addDate,
                        (select max(t2.`name`)
                         from tb_wx_user u
                                  left join tb_wx_department t2 on u.main_department = t2.id and u.corp_id = t2.corp_id
                         where u.userid = t.user_id
                           and u.corp_id = t.corp_id
                         limit 1)  deptName,
                        t.tag,
                        t.remark,
                        t.description,
                        (select fu.add_way
                         from tb_wx_ext_follow_user fu
                         where fu.external_user_id = t.external_user_id
                           and fu.user_id = u.userid
                         order by fu.`status`, fu.create_time desc
                         limit 1)  addWay,
                        (
                            select fu.`status`
                            from tb_wx_ext_follow_user fu
                            where fu.external_user_id = t.external_user_id
                              and fu.user_id = u.userid
                            order by fu.`status`
                            limit 1
                        )          status,
                        u.del_flag staff_status,
                        (select state
                         from tb_wx_ext_follow_user
                         where external_user_id = t.external_user_id
                           and user_id = t.user_id
                         order by `status`
                         limit 1
                        )          state,
                        t.self_tag selfTag,
                        t.external_user_id,
                        t.corp_id
                 from tb_wx_ext_follow_user t
                          left join tb_wx_user u on t.corp_id = u.corp_id and t.user_id = u.userid
                 where t.external_user_id = #{extUserId}
                   and t.corp_id = #{corpId}
                 group by t.user_id
             ) t1
        order by t1.staff_status, t1.`status`, t1.addDate desc
    </select>
    <select id="queryExistExtIdsByExtUserIds" parameterType="map" resultType="java.lang.String">
        select external_user_id from tb_wx_ext_customer where (external_user_id) in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        and corp_id = #{corpId} -- and status = '0'
    </select>

    <select id="queryRemindTags" parameterType="String" resultType="Map">
        SELECT DISTINCT ext.external_user_id extUserId,
                        ext.NAME             extUserName,
                        fol.user_id          userId,
                        (SELECT agent_id
                         FROM tb_wx_corp_permanent_token cpt
                         WHERE cpt.corp_id = ext.corp_id
                           AND `status` = 0) agentId,
                        '0'
        FROM tb_wx_ext_customer ext
                 INNER JOIN
             tb_wx_ext_follow_user fol
             ON
                 fol.external_user_id = ext.external_user_id
        WHERE ext.corp_id = #{corpId}
          AND DATE_FORMAT(ext.create_time, '%Y-%m-%d') = DATE_FORMAT(DATE_ADD(sysdate(), INTERVAL - 1 DAY), '%Y-%m-%d')
          and (ext.tag is null or ext.tag = '');
    </select>


    <select id="exprotChurnCustomer" resultType="CustomerExportVo">
        select
        t.extUserId,
        t.custName,
        t.corpName,
        t.addDate, t.type, t.birthday, t.avatar, t.gender, t.tag, t.mobiles,
        t.corpUserName,
        (case when t.add_way = 1 then ifnull((select remark from tb_wx_contact where state = t.state limit 1),'扫描二维码')
        when t.add_way = 2 then '搜索手机号'
        when t.add_way = 3 then '名片分享'
        when t.add_way = 4 then '群聊'
        when t.add_way = 5 then '手机通讯录'
        when t.add_way = 6 then '微信联系人'
        when t.add_way = 7 then '来自微信的添加好友申请'
        when t.add_way = 8 then '安装应用时自动添加'
        when t.add_way = 9 then '搜索邮箱'
        when t.add_way = 10 then '视频号添加'
        when t.add_way = 11 then '通过日程参与人添加'
        when t.add_way = 12 then '通过会议参与人添加'
        when t.add_way = 13 then '添加微信好友对应的企业微信'
        when t.add_way = 14 then '通过智慧硬件专属客服添加'
        when t.add_way = 15 then '通过上门服务客服添加'
        when t.add_way = 16 then '通过获客链接添加'
        when t.add_way = 17 then '通过定制开发添加'
        when t.add_way = 18 then '通过需求回复添加'
        when t.add_way = 21 then '通过第三方售前客服添加'
        when t.add_way = 22 then '通过可能的商务伙伴添加'
        when t.add_way = 24 then '通过接受微信账号收到的好友申请添加'
        when t.add_way = 201 then '内部成员共享'
        when t.add_way = 202 then '管理员/负责人分配'
        else '未知来源' end)add_way
        from (
        select
        t.external_user_id extUserId ,
        name custName,
        IFNULL(t.corp_full_name, t.corp_name) corpName,
        t. create_time addDate,
        t.type,
        t.birthday,
        t.avatar,
        (SELECT max(name) from tb_wx_user where userid = t.first_add_user_id) corpUserName,
        t.gender,
        t.tag,
        t.mobiles,
        (
        SELECT
        f.add_way
        FROM
        tb_wx_ext_follow_user f
        WHERE
        f.corp_id = t.corp_id
        AND f.external_user_id = t.external_user_id
        AND f.user_id = t.first_add_user_id
        limit 1
        ) add_way,
        (
        SELECT
        f.state
        FROM
        tb_wx_ext_follow_user f
        WHERE
        f.corp_id = t.corp_id
        AND f.external_user_id = t.external_user_id
        AND f.user_id = t.first_add_user_id
        limit 1
        ) state
        from
        tb_wx_ext_customer t
        where
        t.`status` ='0'
        and t.corp_id = #{corpId}
        <if test="userIds != null  and userIds.size()>0 ">
            and t.external_user_id in (
            select external_user_id from tb_wx_ext_follow_user where corp_id = #{corpId}
            and status ='0'
            and user_id in
            <foreach item="item" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="customerName != null  and customerName != ''">
            and t.name like concat('%',#{customerName},'%')
        </if>
        <if test="extUserId != null  and extUserId != ''">
            and t.external_user_id = #{extUserId}
        </if>
        <if test="tag != null  and tag != ''">
            and t.tag like concat('%', #{tag}, '%')
        </if>
        <if test="beginTime != null and beginTime != '' ">
            and date_format(t.create_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(t.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="corpUserIds != null and corpUserIds.size()>0 ">
            and t.first_add_user_id in
            <foreach item="item" collection="corpUserIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t.create_time desc )t

    </select>


    <select id="exprotChurnCustomerLost" resultType="ChurnCustomerExport">
        SELECT
            t.name AS custName,
            tg.tag AS tag,
            t2.name AS corpUserName,
            DATE_FORMAT(t1.create_time,'%Y-%m-%d') createTime,
            DATE_FORMAT(t1.del_time,'%Y-%m-%d %H:%i:%S') churnDate,
            (case t1.status when '2' then '客户删除员工' when '3' then '员工删除客户' else '未知' end) churnStatus
        FROM
            tb_wx_ext_customer t
        LEFT JOIN tb_wx_ext_follow_user t1 ON t.external_user_id = t1.external_user_id
        LEFT JOIN tb_wx_user t2 on t1.user_id = t2.userid
        LEFT JOIN (
            select
            fut.external_user_id,
            group_concat(distinct ct.name) tag
            from
            tb_wx_ext_follow_user_tag fut
            left join tb_wx_corp_tag ct on ct.tag_id = fut.tag_id
            group by fut.external_user_id
        ) tg on tg.external_user_id = t.external_user_id
        WHERE
            t1.`status` in ('2','3')
        AND t.corp_id = #{corpId}
        <if test="userIds != null  and userIds.size()>0 ">
            and t1.user_id in
            <foreach item="item" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customerName != null  and customerName != ''">
            and t.name like concat('%',#{customerName},'%')
        </if>
        <if test="tag != null  and tag != ''">
            and tg.tag like concat('%', #{tag}, '%')
        </if>
        <if test="extUserId != null  and extUserId != ''">
            and t.external_user_id = #{extUserId}
        </if>
        <if test="beginTime != null and beginTime != '' ">
            and date_format(t1.create_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(t1.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and t2.main_department in
                <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and t2.userid = #{wxUserId}
            </if>
        </if>
        order by t1.del_time desc
    </select>

    <select id="queryCustomerGroupList" resultType="CustomerGroupVo">
        SELECT g.chat_id                                                                                  chatId,
               if(g.group_name is null or g.group_name = '', '群聊', g.group_name)                          group_name,
               g.owner,
               (select name from tb_wx_user u where u.userid = g.owner and g.corp_id = u.corp_id limit 1) ownerName,
               m.join_time                                                                                createTime,
               (
                   case
                       when m.join_scene = 1 and m.invitor_user_id is null then '由群成员邀请入群'
                       when m.join_scene = 1 and m.invitor_user_id is not null then
                           concat('由员工', IFNULL((select name
                                                 from tb_wx_user
                                                 where m.invitor_user_id = userid
                                                   and m.corp_id = corp_id limit 1), ''),
                                  '直接邀请入群')
                       when m.join_scene = 2 then '由群成员发送邀请链接入群'
                       when m.join_scene = 3 then '通过扫描群二维码入群'
                       else '-' end
                   )                                                                                      join_scene,
               (SELECT if(count(1) > 1, '1', '0')
                FROM tb_wx_customer_group_member mm
                WHERE mm.group_id = g.chat_id
                  and (mm.user_id = #{extUserId} or mm.user_id = #{corpUserId})
                  and mm.`status` != 2) as                                                                commonGroup,
               (select count(1)
                from tb_wx_customer_group_member
                where group_id = g.chat_id
                  and `status` = 0)                                                                       member_cnt
        FROM tb_wx_customer_group_member m
                 LEFT JOIN tb_wx_customer_group g ON g.chat_id = m.group_id
        WHERE m.`status` = 0
          AND m.corp_id = #{corpId}
          AND m.user_id = #{extUserId}
        order by g.create_time desc
    </select>

    <select id="queryExtUserInfo" resultType="ExternalUserInfoVo">
        SELECT r.external_user_id    extUserId,
               r.`name`              extUserName,
               r.avatar,
               r.gender,
               r.`type`,
               r.`remark`,
               r.`corp_name`         extCorpName,
               r.`corp_full_name`    extCorpFullName,
               r.corp_id             corpId,
               (select GROUP_CONCAT(DISTINCT u.`name` ORDER BY f.create_time)
                from tb_wx_ext_follow_user f
                         LEFT JOIN tb_wx_user u ON u.userid = f.user_id
                WHERE f.external_user_id = r.external_user_id
                  and f.corp_id = r.corp_id
                  and f.`status` = 0
               )                     addUserNames,
               (select GROUP_CONCAT(g.`group_name` ORDER BY gm.join_time)
                from tb_wx_customer_group_member gm
                         LEFT JOIN tb_wx_customer_group g ON g.chat_id = gm.group_id
                WHERE gm.user_id = r.external_user_id
                  and gm.corp_id = r.corp_id
                  and gm.`status` = 0
               )                     chatGroupNames,
               uf.create_time        addDate,
               uf.`remark_corp_name` remartCorpName,
               uf.`remark_mobiles`   remarkMobiles
        FROM tb_wx_ext_follow_user uf
                 LEFT JOIN tb_wx_ext_customer r ON r.external_user_id = uf.external_user_id
        WHERE uf.external_user_id = #{extUserId}
          AND uf.corp_id = #{corpId}
          AND uf.user_id = #{corpUserId}
    </select>

    <select id="queryExtUserTagList" resultType="ExternalUserTagVo"
    >
        SELECT ft.id,
               t.tag_id   tagId,
               t.`name`   tagName,
               t.group_id tagGroupId
        FROM tb_wx_ext_follow_user_tag ft
                 LEFT JOIN tb_wx_corp_tag t ON t.tag_id = ft.tag_id
        WHERE ft.external_user_id = #{extUserId}
          AND ft.corp_id = #{corpId}
          AND ft.user_id = #{corpUserId}
          AND ft.type = 1
    </select>


    <select id="findWeCustomerInfo" resultType="CustomerPortraitVo">
        select c.name,
               u.remark,
               c.mobiles                                                       remark_mobiles,
               c.birthday,
               c.gender,
               c.avatar,
               u.external_user_id,
               u.user_id,
               u.id                                                            flowerCustomerRelId,
               u.description,
               u.remark_corp_name,
               c.position,
               c.corp_full_name,
               c.corp_name,
               c.type,
               c.qq,
               c.email,
               c.address,
               u.create_time,
               u.state,
               (case
                    when u.add_way = 1 then ifnull((select remark from tb_wx_contact where state = u.state limit 1),
                                                   '扫描二维码')
                    when u.add_way = 2 then '搜索手机号'
                    when u.add_way = 3 then '名片分享'
                    when u.add_way = 4 then '群聊'
                    when u.add_way = 5 then '手机通讯录'
                    when u.add_way = 6 then '微信联系人'
                    when u.add_way = 7 then '来自微信的添加好友申请'
                    when u.add_way = 8 then '安装应用时自动添加'
                    when u.add_way = 9 then '搜索邮箱'
                    when u.add_way = 10 then '视频号添加'
                    when u.add_way = 11 then '通过日程参与人添加'
                    when u.add_way = 12 then '通过会议参与人添加'
                    when u.add_way = 13 then '添加微信好友对应的企业微信'
                    when u.add_way = 14 then '通过智慧硬件专属客服添加'
                    when u.add_way = 15 then '通过上门服务客服添加'
                    when u.add_way = 16 then '通过获客链接添加'
                    when u.add_way = 17 then '通过定制开发添加'
                    when u.add_way = 18 then '通过需求回复添加'
                    when u.add_way = 21 then '通过第三方售前客服添加'
                    when u.add_way = 22 then '通过可能的商务伙伴添加'
                    when u.add_way = 24 then '通过接受微信账号收到的好友申请添加'
                    when u.add_way = 201 then '内部成员共享'
                    when u.add_way = 202 then '管理员/负责人分配'
                    else '未知来源' end)                                             add_way,
               if((select count(1)
                   from tb_wx_customer_trajectory t
                   where t.external_user_id = c.external_user_id
                     and t.trajectory_type = 1
                     and (t.behavior_type = 2 or t.behavior_type = 1)
                     and t.corp_id = u.corp_id
                     and t.content like '{"actionType":1,%') > 0,
                  concat((select u2.name user_name
                          from tb_wx_customer_trajectory t
                                   left join tb_wx_user u2 on u2.userid = t.user_id and t.corp_id = u2.corp_id
                          where t.external_user_id = c.external_user_id
                            and t.trajectory_type = 1
                            and (t.behavior_type = 2 or t.behavior_type = 1)
                            and t.corp_id = u.corp_id
                            and t.content like '{"actionType":1,%'
                          order by t.create_time desc
                          limit 1), ' ',
                         (select t.create_time
                          from tb_wx_customer_trajectory t
                          where t.external_user_id = c.external_user_id
                            and t.trajectory_type = 1
                            and (t.behavior_type = 2 or t.behavior_type = 1)
                            and t.corp_id = u.corp_id
                            and t.content like '{"actionType":1,%'
                          order by t.create_time desc
                          limit 1)),
                  c.create_time
                   )                                                           update_time,
               if((select count(1)
                   from tb_wx_customer_trajectory t
                   where t.external_user_id = c.external_user_id
                     and t.trajectory_type = 1
                     and (t.behavior_type = 2 or t.behavior_type = 1)
                     and t.corp_id = u.corp_id
                     and t.content like '{"actionType":1,%') > 0, true, false) click_customer_data
        from tb_wx_ext_customer c
                 left join tb_wx_ext_follow_user u on c.external_user_id = u.external_user_id and c.corp_id = u.corp_id
        where c.external_user_id = #{externalUserId}
          and u.user_id = #{userId}
          and u.corp_id = #{corpId}
        group by c.external_user_id
    </select>

    <select id="findAddEmployers" resultType="CustomerAddUserVo">
        SELECT wu.name   user_name,
               wu.userid user_id,
               wu.avatar head_image_url,
               u.create_time
        FROM tb_wx_ext_follow_user u
                 INNER JOIN tb_wx_user wu ON (u.user_id = wu.userid and wu.corp_id = #{corpId})
        WHERE u.external_user_id = #{externalUserId}
          AND u.corp_id = #{corpId}
          AND u.status = 0
        GROUP BY wu.userid
    </select>

    <select id="countTotalCustomer" resultType="java.lang.Integer">
        select count(1)
        from tb_wx_ext_customer t
        join tb_wx_ext_follow_user efu on efu.external_user_id = t.external_user_id and efu.corp_id = t.corp_id
        where t.`status` = '0' and efu.status = '0'
          and t.corp_id = #{corpId}
          and efu.user_id = #{userId}
    </select>

    <select id="countActiveCustomerByDay" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT wc.from_id) AS active_customers
        FROM wk_chat_archive_info wc
                 LEFT JOIN tb_wx_user twu ON wc.from_id = twu.userid
        WHERE corp_id = #{corpId}
          AND wc.chat_type = 1
          AND wc.msg_day = #{day}
          AND twu.userid IS NULL

    </select>


    <select id="countAddOrDecreaseByDay" resultType="java.lang.Integer">
        select
        count(distinct external_user_id)
        from
        tb_wx_ext_follow_user
        where
        corp_id = #{corpId}
        and user_id = #{userId}
        <if test="status != null  and status != '' and status == '0'">
            and status = #{status}
        </if>
        <if test="status != null  and status != '' and status == '2'">
            and (status = 2 or status = 3)
        </if>
        <if test="delTime != null  and delTime != ''">
            and del_time &gt;= concat(#{delTime}, " 00:00:00")
            and del_time &lt;= concat(#{delTime}, " 23:59:59")
        </if>
        <if test="createTime != null  and createTime != ''">
            and create_time &gt;= concat(#{createTime}, " 00:00:00")
            and create_time &lt;= concat(#{createTime}, " 23:59:59")
        </if>
    </select>

    <select id="countCustomerByCustomerIdsAndCorpId" resultType="java.lang.Integer">
        select
        count(1)
        from
        mp_wx_user u
        join tb_wx_ext_customer c on u.nick_name = c.`name`
        where u.id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        and c.corp_id = #{corpId}
        and c.`status` = 0
        -- group by c.external_user_id
    </select>

    <select id="selectExtCustomerByCorpIdAndNickName" resultType="java.lang.String">
        select c.external_user_id
        from tb_wx_ext_customer c
                 left join tb_wx_ext_follow_user u on c.external_user_id = u.external_user_id and c.corp_id = u.corp_id
        where c.name = #{nickName}
          and c.corp_id = #{corpId}
          and c.type = 1
        group by c.external_user_id
        order by u.update_time, c.create_time
        limit 1
    </select>

    <resultMap id="queryCustomerInfoDetailMap" type="CustomerInfoVo">
        <result column="avatar" property="avatar"/>
        <result column="name" property="custName"/>
        <result column="type" property="type"/>
        <result column="gender" property="gender"/>
        <result column="mobiles" property="mobiles"/>
        <result column="corp_name" property="corpName"/>
        <result column="birthday" property="birthday"/>
        <result column="email" property="email"/>
        <result column="address" property="address"/>
        <result column="qq" property="qq"/>
        <result column="position" property="position"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_auth" property="isAuth"/>
        <result column="custno" property="customerNo"/>
        <result column="real_name" property="realName"/>
        <collection property="followUser" ofType="CorpUserInfoVo">
            <id column="follow_id" property="id"/>
            <result column="user_name" property="userName"/>
            <result column="add_way" property="addWay"/>
            <result column="addDate" property="addDate"/>
            <result column="status" property="status"/>
            <collection property="userTagVoList" ofType="ExternalUserTagVo">
                <result column="tag" property="tagName"/>
                <result column="tag_id" property="tagId"/>
                <result column="group_name" property="groupName"/>
                <result column="remark" property="remark"/>
            </collection>
        </collection>
        <collection property="journeyNames" ofType="com.cenker.scrm.pojo.vo.external.CustomerJourneyVO" column="extUserId"
                    select="selectJourneyNamesByExternalUserId"/>
    </resultMap>

    <select id="queryCustomerInfoDetail2" resultMap="queryCustomerInfoDetailMap">
        select t.*,
               (case when g.`NAME` is null then tag.tag else g.`NAME` end )  as tag,
               tag_group.group_name,
               tag.tag_id,
               tag.remark
        from (
                 select c.id extUserId,
                        c.avatar,
                        c.`name`,
                        c.type,
                        c.gender,
                        c.mobiles,
                        c.corp_full_name,
                        c.birthday,
                        c.qq,
                        c.corp_name,
                        c.address,
                        c.email,
                        c.update_time,
                        c.position,
                        c.is_auth,
                        c.custno,
                        c.real_name,
                        max(u.create_time) as  addDate,
                        (case
                             when c.type = 1 then '@微信'
                             when c.type = 2 then concat('@', c.corp_name)
                             else '' end)   add_type,
                        u.user_id,
                        wu.name             user_name,
                        wu.del_flag         `status`,
                        c.corp_id,
                        u.external_user_id,
                        u.id                follow_id,
                        (case
                             when u.add_way = 1 then ifnull(
                                         (select remark from tb_wx_contact where state = u.state limit 1), '扫描二维码')
                             when u.add_way = 2 then '搜索手机号'
                             when u.add_way = 3 then '名片分享'
                             when u.add_way = 4 then '群聊'
                             when u.add_way = 5 then '手机通讯录'
                             when u.add_way = 6 then '微信联系人'
                             when u.add_way = 7 then '来自微信的添加好友申请'
                             when u.add_way = 8 then '安装应用时自动添加'
                             when u.add_way = 9 then '搜索邮箱'
                             when u.add_way = 10 then '视频号添加'
                             when u.add_way = 11 then '通过日程参与人添加'
                             when u.add_way = 12 then '通过会议参与人添加'
                             when u.add_way = 13 then '添加微信好友对应的企业微信'
                             when u.add_way = 14 then '通过智慧硬件专属客服添加'
                             when u.add_way = 15 then '通过上门服务客服添加'
                             when u.add_way = 16 then '通过获客链接添加'
                             when u.add_way = 17 then '通过定制开发添加'
                             when u.add_way = 18 then '通过需求回复添加'
                             when u.add_way = 21 then '通过第三方售前客服添加'
                             when u.add_way = 22 then '通过可能的商务伙伴添加'
                             when u.add_way = 24 then '通过接受微信账号收到的好友申请添加'
                             when u.add_way = 201 then '内部成员共享'
                             when u.add_way = 202 then '管理员/负责人分配'
                             else '未知来源' end) add_way
                 from tb_wx_ext_customer c
                          left join tb_wx_ext_follow_user u
                                    on u.external_user_id = c.external_user_id and u.corp_id = c.corp_id
                          left join tb_wx_user wu on wu.userid = u.user_id and wu.corp_id = u.corp_id
                 where c.external_user_id = #{extUserId}
                   and c.corp_id = #{corpId}
                   --  and u.`status` = 0
                 group by u.user_id
                 order by u.create_time desc
             ) t
                 left join tb_wx_ext_follow_user_tag tag on tag.user_id = t.user_id and tag.corp_id = t.corp_id and
                                                            tag.external_user_id = t.external_user_id and tag.type = 1
                 left join tb_wx_corp_tag g on g.tag_id = tag.tag_id
                 left join tb_wx_corp_tag_group tag_group on tag_group.group_id = g.group_id
    </select>

    <select id="selectCorpAddWayVoByExternalUserId" resultType="CorpAddWayVo">
        select distinct(case
                            when u.add_way = 1 then ifnull(
                                        (select remark from tb_wx_contact where state = u.state limit 1), '扫描二维码')
                            when u.add_way = 2 then '搜索手机号'
                            when u.add_way = 3 then '名片分享'
                            when u.add_way = 4 then '群聊'
                            when u.add_way = 5 then '手机通讯录'
                            when u.add_way = 6 then '微信联系人'
                            when u.add_way = 7 then '来自微信的添加好友申请'
                            when u.add_way = 8 then '安装应用时自动添加'
                            when u.add_way = 9 then '搜索邮箱'
                            when u.add_way = 10 then '视频号添加'
                            when u.add_way = 11 then '通过日程参与人添加'
                            when u.add_way = 12 then '通过会议参与人添加'
                            when u.add_way = 13 then '添加微信好友对应的企业微信'
                            when u.add_way = 14 then '通过智慧硬件专属客服添加'
                            when u.add_way = 15 then '通过上门服务客服添加'
                            when u.add_way = 16 then '通过获客链接添加'
                            when u.add_way = 17 then '通过定制开发添加'
                            when u.add_way = 18 then '通过需求回复添加'
                            when u.add_way = 21 then '通过第三方售前客服添加'
                            when u.add_way = 22 then '通过可能的商务伙伴添加'
                            when u.add_way = 24 then '通过接受微信账号收到的好友申请添加'
                            when u.add_way = 201 then '内部成员共享'
                            when u.add_way = 202 then '管理员/负责人分配'
                            else '未知来源' end) add_way
        from tb_wx_ext_follow_user u
        where u.corp_id = #{corpId}
          and u.external_user_id = #{extUserId}
          and u.`status` != 3
        group by u.user_id
    </select>

    <!-- 当不是完全流失客户（即还有关系才会来调用，所以不需要排除status(不等于会索引失效)）-->
    <select id="selectCorpUserInfoVoByExternalUserId" resultType="CorpUserInfoVo">
        select distinct u.user_id,
                        w.name userName
        from tb_wx_ext_follow_user u
                 join tb_wx_user w on w.userid = u.user_id and w.corp_id = u.corp_id
        where u.corp_id = #{corpId}
          and u.external_user_id = #{extUserId}
          and w.del_flag = 1
          and u.status = '0'
        order by u.create_time desc
    </select>

    <select id="selectTagsByExternalUserId" resultType="String">
        select (case when g.`name` is null then t.tag else g.`name` end ) as tag
        from tb_wx_ext_follow_user_tag t
        left join tb_wx_corp_tag g on t.tag_id = g.tag_id
        where t.corp_id = #{corpId}
          and t.external_user_id = #{extUserId}
        <if test="userId != null and userId != ''">
            and t.user_id = #{userId}
        </if>
        group by t.tag_id
        order by t.id
    </select>


    <resultMap id="getExtCustomerListMap" type="com.cenker.scrm.pojo.vo.external.CustomerInfoVo">
        <result column="avatar" property="avatar"/>
        <result column="external_user_id" property="extUserId"/>
        <!-- column传值-->
        <collection property="addWays" column="{extUserId=external_user_id,corpId=corp_id}"
                    select="selectCorpAddWayVoByExternalUserId"/>
        <collection property="followUser" column="{extUserId=external_user_id,corpId=corp_id}"
                    select="selectCorpUserInfoVoByExternalUserId"/>
        <collection property="tags" column="{extUserId=external_user_id,corpId=corp_id}"
                    select="selectTagsByExternalUserId"/>
        <collection property="journeyNames" ofType="com.cenker.scrm.pojo.vo.external.CustomerJourneyVO" column="{extUserId=id}"
                    select="selectJourneyNamesByExternalUserId"/>

    </resultMap>


    <select id="getExtCustomerList" resultMap="getExtCustomerListMap">
        select
        c.id,
        c.avatar,
        c.`name` custName,
        c.type,
        c.corp_name,
        c.external_user_id,
        c.corp_id,
        c.gender,
        c.create_time createTime,
        fu1.first_add_time as addDate,
        c.is_auth isAuth,
        c.union_id
        from tb_wx_ext_customer c
        left join tb_wx_ext_follow_user fu on c.external_user_id = fu.external_user_id and fu.status = '0' and fu.del_flag = 0
        left join (select external_user_id, min(create_time) AS first_add_time from tb_wx_ext_follow_user group by external_user_id) fu1 on fu1.external_user_id = c.external_user_id
        left join tb_wx_user u on fu.user_id = u.userid
        where c.corp_id = #{corpId}
        and c.`status` = 0
        <if test="unionId != null and unionId != ''">
            and c.union_id like concat('%', #{unionId}, '%')
        </if>
        <if test="customerName != null and customerName != ''">
            and c.`name` like concat('%',#{customerName},'%')
        </if>
        <if test="type != null">
            and c.type = #{type}
        </if>
        <if test="mobile != null and mobile != ''">
            and c.mobiles like concat('%',#{mobile},'%')
        </if>
        <if test="beginTime != null and beginTime != ''">
            and date_format(fu1.first_add_time,'%Y-%m-%d') >= date_format(#{beginTime},'%Y-%m-%d')
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(fu1.first_add_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
        </if>
        <if test="tag != null and tag != ''">
            and (select
            group_concat(distinct t.tag)
            from tb_wx_ext_follow_user_tag t
            where t.corp_id = c.corp_id
            and t.external_user_id = c.external_user_id) like concat('%',#{tag},'%')
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and u.main_department in
                <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and fu.user_id = #{wxUserId}
            </if>
        </if>
        <if test="corpUserIds != null and corpUserIds.size > 0">
            and fu.user_id in
            <foreach collection="corpUserIds" item="corpUserId" open="(" close=")" separator=",">
                #{corpUserId}
            </foreach>
        </if>
        <if test="addWay != null and addWay != ''">
            and (fu.add_way in (
            select dict_value from sys_dict_data where dict_type = 'customer_add_way' and dict_label like
            concat('%',#{addWay},'%')
            )
            OR EXISTS (select 1 from tb_wx_contact wc where wc.state = fu.state and wc.remark like concat('%',#{addWay},'%'))
            )
        </if>
        <if test="isAuth != null">
            and c.is_auth = #{isAuth}
        </if>
        <if test="stageConditionList != null and stageConditionList.size > 0">
            and c.id in (select distinct jcs.ext_customer_id
            from tb_wx_ext_journey_customer_stage jcs
            where jcs.del_flag=0 and jcs.stage_id in
            <foreach collection="stageConditionList" item="journey" open="(" close=")" separator=",">
                #{journey.journeyId}
            </foreach>)
        </if>
        <if test="externalUserIdList   != null and externalUserIdList.size > 0">
            and c.external_user_id   in
            <foreach collection="externalUserIdList" item="extUserId" open="(" close=")" separator=",">
                #{extUserId}
            </foreach>
        </if>
        group by c.external_user_id
        order by c.create_time desc, c.id desc
    </select>

    <resultMap id="getExtCustomerListMapBack" type="CustomerInfoVo">
        <result column="avatar" property="avatar"/>
        <result column="external_user_id" property="extUserId"/>
        <result property="tags" column="tag" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
    </resultMap>
    <select id="selectJourneyNamesByExternalUserId" resultType="com.cenker.scrm.pojo.vo.external.CustomerJourneyVO">
        SELECT
            ji.journey_name AS journeyName,
            js.stage_name AS stageName
        FROM (
                 SELECT DISTINCT
                     jcs.journey_info_id,
                     jcs.stage_id
                 FROM
                     tb_wx_ext_journey_customer_stage jcs
                 WHERE
                     jcs.ext_customer_id =  #{extUserId} AND
                     jcs.del_flag = 0
             ) AS unique_stages
                 JOIN
             tb_wx_ext_journey_info ji ON unique_stages.journey_info_id = ji.id AND ji.del_flag = 0
                 JOIN
             tb_wx_ext_journey_stage js ON unique_stages.stage_id = js.id AND js.del_flag = 0
        ORDER BY
            ji.order_num, js.order_num
    </select>


    <select id="getExtCustomerListBack" resultMap="getExtCustomerListMapBack">
        select * from (
        select
        t.id,
        t.avatar,
        t.`name` custName,
        t.type,
        t.tag,
        t.corp_name,
        t.external_user_id,
        t.gender,
        t.first_add_user_id,
        t.first_add_date addDate,
        -- (select create_time from tb_wx_ext_follow_user where user_id = t.first_add_user_id and external_user_id =
        -- t.external_user_id order by `status` desc limit 1)addDate,
        u1.name corpUserName,
        (case when u.add_way = 1 then ifnull((select remark from tb_wx_contact where state = u.state limit 1),'扫描二维码')
        when u.add_way = 2 then '搜索手机号'
        when u.add_way = 3 then '名片分享'
        when u.add_way = 4 then '群聊'
        when u.add_way = 5 then '手机通讯录'
        when u.add_way = 6 then '微信联系人'
        when u.add_way = 7 then '来自微信的添加好友申请'
        when u.add_way = 8 then '安装应用时自动添加'
        when u.add_way = 9 then '搜索邮箱'
        when u.add_way = 10 then '视频号添加'
        when u.add_way = 11 then '通过日程参与人添加'
        when u.add_way = 12 then '通过会议参与人添加'
        when u.add_way = 13 then '添加微信好友对应的企业微信'
        when u.add_way = 14 then '通过智慧硬件专属客服添加'
        when u.add_way = 15 then '通过上门服务客服添加'
        when u.add_way = 16 then '通过获客链接添加'
        when u.add_way = 17 then '通过定制开发添加'
        when u.add_way = 18 then '通过需求回复添加'
        when u.add_way = 21 then '通过第三方售前客服添加'
        when u.add_way = 22 then '通过可能的商务伙伴添加'
        when u.add_way = 24 then '通过接受微信账号收到的好友申请添加'
        when u.add_way = 201 then '内部成员共享'
        when u.add_way = 202 then '管理员/负责人分配'
        else '未知来源' end)add_way
        from (
        select
        c.id,
        c.avatar,
        c.`name`,
        c.type,
        c.corp_name,
        c.mobiles,
        c.external_user_id,
        c.corp_id,
        c.tag,
        c.gender,
        (select u.user_id from tb_wx_ext_follow_user u
        left join tb_wx_user twu on twu.userid = u.user_id and twu.corp_id = u.corp_id
        where u.external_user_id = c.external_user_id
        -- and u.`status` = 0
        order by twu.del_flag,u.create_time
        limit 1
        )first_add_user_id
        from tb_wx_ext_customer c
        where c.corp_id = #{corpId}
        and c.`status` = 0
        )t left join tb_wx_ext_follow_user u on u.external_user_id = t.external_user_id and u.corp_id = t.corp_id and
        u.user_id = t.first_add_user_id and u.`status` = 0
        left join tb_wx_user u1 on u1.userid = t.first_add_user_id and u1.corp_id = t.corp_id
        -- where u.`status` = 0
        <where>
        <if test="customerName != null and customerName != ''">
            and t.`name` like concat('%',#{customerName},'%')
        </if>
        <if test="type != null">
            and t.type = #{type}
        </if>
        <if test="mobile != null and mobile != ''">
            and t.mobiles like concat('%',#{mobile},'%')
        </if>
        <if test="tag != null and tag != ''">
            and t.tag like concat('%',#{tag},'%')
        </if>
        <if test="corpUserIds != null and corpUserIds.size > 0">
            and t.first_add_user_id in
            <foreach collection="corpUserIds" item="corpUserId" open="(" close=")" separator=",">
                #{corpUserId}
            </foreach>
        </if>
        <if test="beginTime != null and beginTime != ''">
            and date_format(u.create_time,'%Y-%m-%d') >= date_format(#{beginTime},'%Y-%m-%d')
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(u.create_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
        </if>
        </where>
        group by t.external_user_id
        order by u.create_time desc
        ) tt
        <where>
            <if test="addWay != null and addWay != ''">
                and add_way = #{addWay}
            </if>
        </where>
    </select>

    <select id="getAddWayList" resultType="CorpAddWayVo">
        select add_way from (
        select
        (case when u.add_way = 1 then ifnull((select remark from tb_wx_contact where state = u.state limit 1),'扫描二维码')
        when u.add_way = 2 then '搜索手机号'
        when u.add_way = 3 then '名片分享'
        when u.add_way = 4 then '群聊'
        when u.add_way = 5 then '手机通讯录'
        when u.add_way = 6 then '微信联系人'
        when u.add_way = 7 then '来自微信的添加好友申请'
        when u.add_way = 8 then '安装应用时自动添加'
        when u.add_way = 9 then '搜索邮箱'
        when u.add_way = 10 then '视频号添加'
        when u.add_way = 11 then '通过日程参与人添加'
        when u.add_way = 12 then '通过会议参与人添加'
        when u.add_way = 13 then '添加微信好友对应的企业微信'
        when u.add_way = 14 then '通过智慧硬件专属客服添加'
        when u.add_way = 15 then '通过上门服务客服添加'
        when u.add_way = 16 then '通过获客链接添加'
        when u.add_way = 17 then '通过定制开发添加'
        when u.add_way = 18 then '通过需求回复添加'
        when u.add_way = 21 then '通过第三方售前客服添加'
        when u.add_way = 22 then '通过可能的商务伙伴添加'
        when u.add_way = 24 then '通过接受微信账号收到的好友申请添加'
        when u.add_way = 201 then '内部成员共享'
        when u.add_way = 202 then '管理员/负责人分配'
        else '未知来源' end)add_way,
        u.create_time
        from (
            select
                c.external_user_id,
                c.corp_id,
                (select u.user_id from tb_wx_ext_follow_user u
                left join tb_wx_user twu on twu.userid = u.user_id and twu.corp_id = u.corp_id
                where u.external_user_id = c.external_user_id
                and u.`status` = 0
                and twu.del_flag = 1
                order by twu.del_flag,u.create_time
                limit 1
                )first_add_user_id
            from tb_wx_ext_customer c
            where c.corp_id = #{corpId}
        )t left join tb_wx_ext_follow_user u on u.external_user_id = t.external_user_id and u.corp_id = t.corp_id and
        u.user_id = t.first_add_user_id
        where u.`status` = 0
        group by t.external_user_id
        ) tt
        <where>
            <if test="addWay != null and addWay != ''">
                and add_way like concat('%',#{addWay},'%')
            </if>
        </where>
        group by add_way
        order by create_time desc
    </select>

    <resultMap id="getCustomerInfoByIdAndUserIdMap" type="CustomerPortraitVo">
        <result property="externalUserId" column="external_user_id"/>
        <result property="avatar" column="avatar"/>
        <result property="mobiles" column="mobiles"/>
        <result property="name" column="name"/>
        <result property="addWay" column="add_way"/>
        <result property="gender" column="gender"/>
        <result property="type" column="type"/>
        <result property="remarkMobiles" column="remarkMobiles"/>
        <result property="position" column="position"/>
        <result property="corpName" column="corp_name"/>
        <result property="position" column="position"/>
        <result property="addUserCnt" column="addUserCnt"/>
        <result property="addGroupCnt" column="addGroupCnt"/>
        <result property="isAuth" column="is_auth"/>
        <result property="customerNo" column="custno"/>
        <result property="realName" column="real_name"/>
        <collection property="tagVoList" ofType="ExternalUserTagVo">
            <result column="tag" property="tagName"/>
            <result column="tag_id" property="tagId"/>
            <result column="group_name" property="groupName"/>
        </collection>
    </resultMap>

    <select id="getCustomerInfoByIdAndUserId" resultMap="getCustomerInfoByIdAndUserIdMap">
        select t.*,
               (case
                    when t.add_way_status = 1 then ifnull(
                                (select remark from tb_wx_contact where state = t.state limit 1), '扫描二维码')
                    when t.add_way_status = 2 then '搜索手机号'
                    when t.add_way_status = 3 then '名片分享'
                    when t.add_way_status = 4 then '群聊'
                    when t.add_way_status = 5 then '手机通讯录'
                    when t.add_way_status = 6 then '微信联系人'
                    when t.add_way_status = 7 then '来自微信的添加好友申请'
                    when t.add_way_status = 8 then '安装应用时自动添加'
                    when t.add_way_status = 9 then '搜索邮箱'
                    when t.add_way_status = 10 then '视频号添加'
                    when t.add_way_status = 11 then '通过日程参与人添加'
                    when t.add_way_status = 12 then '通过会议参与人添加'
                    when t.add_way_status = 13 then '添加微信好友对应的企业微信'
                    when t.add_way_status = 14 then '通过智慧硬件专属客服添加'
                    when t.add_way_status = 15 then '通过上门服务客服添加'
                    when t.add_way_status = 16 then '通过获客链接添加'
                    when t.add_way_status = 17 then '通过定制开发添加'
                    when t.add_way_status = 18 then '通过需求回复添加'
                    when t.add_way_status = 21 then '通过第三方售前客服添加'
                    when t.add_way_status = 22 then '通过可能的商务伙伴添加'
                    when t.add_way_status = 24 then '通过接受微信账号收到的好友申请添加'
                    when t.add_way_status = 201 then '内部成员共享'
                    when t.add_way_status = 202 then '管理员/负责人分配'
                    else '未知来源' end) add_way
        from (
                 select c.avatar,
                        c.name,
                        c.mobiles,
                        c.type,
                        c.gender,
                        c.mobiles remarkMobiles,
                        c.position,
                        c.corp_name,
                        c.external_user_id,
                        c.is_auth,
                        c.custno,
                        c.real_name,
                        (select count(distinct u.user_id)
                         from tb_wx_ext_follow_user u
                         where u.external_user_id = c.external_user_id
                        )         addUserCnt,
                        (select count(distinct m.group_id)
                         from tb_wx_customer_group_member m
                         where m.user_id = c.external_user_id
                           and m.`status` = 0
                        )         addGroupCnt,
                        (select ifnull(fu.add_way, 0)
                         from tb_wx_ext_follow_user fu
                         where fu.external_user_id = c.external_user_id
                           and fu.corp_id = c.corp_id
                           and fu.user_id = #{userId}
                           and fu.`status` = 0
                         limit 1) add_way_status,
                        (select fu.state
                         from tb_wx_ext_follow_user fu
                         where fu.external_user_id = c.external_user_id
                           and fu.corp_id = c.corp_id
                           and fu.user_id = #{userId}
                           and fu.`status` = 0
                         limit 1) state,
                        (case when g.`NAME` is null then t.tag else g.`NAME` end )  as tag,
                        tag_group.group_name,
                        t.tag_id,
                        c.union_id
                 from tb_wx_ext_customer c
                          left join tb_wx_ext_follow_user_tag t
                                    on c.external_user_id = t.external_user_id and c.corp_id = t.corp_id and
                                       t.type = 1
                        left join tb_wx_corp_tag g on g.tag_id = t.tag_id
                        left join tb_wx_corp_tag_group tag_group on tag_group.group_id = g.group_id
                 where c.corp_id = #{corpId}
                   and c.external_user_id = #{externalUserId}
             ) t
    </select>

    <update id="setTag2Empty">
        update tb_wx_ext_customer
        set tag = null
        where corp_id = #{corpId}
          and external_user_id = #{externalUserId}
    </update>

    <update id="setTag">
        update tb_wx_ext_customer
        set tag = #{tagNames}
        where corp_id = #{corpId}
          and external_user_id = #{externalUserId}
    </update>

    <update id="setTagById">
        update tb_wx_ext_customer
        set tag = #{tagNames}
        where id = #{id}
    </update>

    <select id="statCumulativeCustomerData" resultType="ContactStatisticsDailyVo">
        SELECT
            c.date AS day,
            (
            SELECT COUNT(*)
            FROM tb_wx_ext_customer
            WHERE `status` = 0 AND DATE(create_time) <![CDATA[<=]]> c.date
            ) AS count
        FROM
        (
            SELECT DISTINCT DATE(create_time) as date FROM tb_wx_ext_customer c
            WHERE corp_id = #{corpId}
            AND `status` = 0
            <if test="startTime != null">
                and create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and create_time &lt;= #{endTime}
            </if>
        ) c
    </select>

    <select id="getCustomerTag" resultType="ExternalUserTagVO">
        select
            t.id,
            t.tag_id,
            t.tag,
            t.group_name,
            t.create_time,
            if(sum(t.user_id = #{userId}) > 0,count(1) - 1,count(1))add_tag_user_cnt,
            sum(t.user_id = #{userId}) > 0 as self_select
        from tb_wx_ext_follow_user_tag t
        where t.corp_id = #{corpId}
          and t.external_user_id = #{externalUserId}
          and t.type = 1
        group by t.tag_id
        order by sum(t.user_id = #{userId}) desc,t.create_time desc
    </select>

    <select id="getActiveCustomerCount1v1DailyGraph" resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        SELECT
            DATE(wcai.msg_day) AS day,
            COUNT(DISTINCT wcai.from_id) AS count
        FROM
            wk_chat_archive_info wcai
            LEFT JOIN tb_wx_user tbwu ON wcai.from_id = tbwu.userid
        WHERE
            wcai.chat_type = 1
          AND wcai.msg_day BETWEEN #{dto.beginTime} AND #{dto.endTime}
          AND tbwu.userid IS NULL
        GROUP BY
            DATE(wcai.msg_day)
    </select>

    <select id="statAuthCustomerData" resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        SELECT
            DISTINCT DATE(c.create_time) AS day,
            (
            SELECT COUNT(*)
            FROM tb_wx_ext_customer
            WHERE DATE(create_time) <![CDATA[<=]]> DATE(c.create_time)
            ) AS count
        FROM tb_wx_ext_customer c
        WHERE  c.corp_id = #{corpId}
        AND c.`status` = 0
        AND c.is_auth = 1
        <if test="startTime != null">
            and c.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and c.create_time &lt;= #{endTime}
        </if>
    </select>

    <select id="statActiveCustomerData" resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        select
        date_format( wcai.msg_time, '%Y-%m-%d' ) AS day,
        count( DISTINCT c.external_user_id ) AS `count`
        from wk_chat_archive_info wcai
        JOIN tb_wx_ext_customer c ON c.corp_id = wcai.corp_id AND c.external_user_id = wcai.from_id
        where c.corp_id = #{corpId} and c.`status` = '0'
        <if test="startTime != null">
            and wcai.msg_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and wcai.msg_time &lt;= #{endTime}
        </if>
        <if test="chatType != null">
            and wcai.chat_type = #{chatType}
        </if>
        group by day
    </select>

    <!--1V1群发用户查询SQL-->
    <select id="qrySendMsgCustUserList"  parameterType="com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserDTO" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer">
            select  *
            from tb_wx_ext_customer  c where c.corp_id = #{corpId} and del_flag = 0
            <if test="custName  != null and custName !=''">
                and c.name like concat('%',#{custName},'%')
            </if>
        <if test="externalUserIdList   != null and externalUserIdList.size > 0">
            and c.external_user_id   in
            <foreach collection="externalUserIdList" item="extUserId" open="(" close=")" separator=",">
                #{extUserId}
            </foreach>
        </if>
    </select>


    <select id="qryExternalUserIdByInTagId"  parameterType="com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserSqlDTO" resultType="java.lang.String">
        SELECT  distinct external_user_id   FROM tb_wx_ext_follow_user_tag g
        ${ew.customSqlSegment}
    </select>

    <select id="qryExternalUserIdByNotInTagId"  parameterType="com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserSqlDTO" resultType="java.lang.String">
        select tb.external_user_id
        from tb_wx_ext_customer tb
        where tb.external_user_id not in (SELECT distinct external_user_id
                                          FROM tb_wx_ext_follow_user_tag g ${ew.customSqlSegment}
            )
    </select>



    <select id="qryExternalUserIdByNotInStageId"  parameterType="com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserSqlDTO" resultType="java.lang.String">
        select tb.external_user_id
        from tb_wx_ext_customer tb
        where tb.external_user_id not in (select distinct r.external_user_id
        from tb_wx_ext_customer r
        left join tb_wx_ext_journey_customer_stage e on e.ext_customer_id = r.id
            ${ew.customSqlSegment}
        )
    </select>


    <select id="qryExternalUserIdByStageId"  parameterType="com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserSqlDTO" resultType="java.lang.String">
        select
            distinct r.external_user_id
        from  tb_wx_ext_customer  r join tb_wx_ext_journey_customer_stage e
        on e.ext_customer_id = r.id
            ${ew.customSqlSegment}
    </select>

    <select id="qryExternalUserIdByLabelTagId"  parameterType="com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserSqlDTO" resultType="java.lang.String">
        select  distinct r.external_user_id  from
            tb_wx_ext_customer r   join  v_f_scrm_label l on l.custno = r.custno
            ${ew.customSqlSegment}
    </select>

    <select id="qryExternalUserIdByWxBusinessTagId"  parameterType="com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserSqlDTO" resultType="java.lang.String">
        select  distinct r.external_user_id
        from tb_wx_ext_customer r
        join  tb_wx_business_tag l on l.external_user_id = r.external_user_id
        ${ew.customSqlSegment}
    </select>

    <select id="qryExternalUserIdByInUserId"  parameterType="com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserSqlDTO" resultType="java.lang.String">
        select  distinct efu.external_user_id
        from
            tb_wx_ext_customer r
                join tb_wx_ext_follow_user efu on r.external_user_id = efu.external_user_id
                JOIN tb_wx_user u ON u.userid = efu.user_id AND u.corp_id = efu.corp_id
            ${ew.customSqlSegment}
    </select>

    <select id="qryExternalUserIdByNotInUserId"  parameterType="com.cenker.scrm.pojo.dto.message.QryListSendMsgCustUserSqlDTO" resultType="java.lang.String">
        select external_user_id
        from tb_wx_ext_customer tb
        where tb.external_user_id not in
              (select distinct efu.external_user_id
               from tb_wx_ext_customer r
                        join tb_wx_ext_follow_user efu on r.external_user_id = efu.external_user_id
                        JOIN tb_wx_user u ON u.userid = efu.user_id AND u.corp_id = efu.corp_id ${ew.customSqlSegment}
            )
    </select>


    <select id="getDailyTotalAuthCustomerCountGraph" resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        SELECT
            d.date AS "day",
            (SELECT COUNT(DISTINCT union_id)
             FROM tb_wx_customer_auth_record
             WHERE DATE(bind_time) &lt;= d.date) AS count
        FROM
            (SELECT DISTINCT DATE(bind_time) AS date
            FROM tb_wx_customer_auth_record
            WHERE bind_time BETWEEN #{dto.beginTime} AND DATE_ADD(#{dto.endTime}, INTERVAL 1 DAY)) d
        ORDER BY
            d.date
    </select>


    <select id="qryExtCustomerList" resultMap="getExtCustomerListMap">
        select
        c.id,
        c.avatar,
        c.`name` custName,
        c.type,
        c.corp_name,
        c.external_user_id,
        c.corp_id,
        c.gender,
        c.create_time createTime,
        (select min(create_time) from tb_wx_ext_follow_user where external_user_id = c.external_user_id) as addDate,
        c.is_auth isAuth
        from tb_wx_ext_customer c
        where c.corp_id = #{corpId}
        and c.`status` = 0
        and c.`del_flag` = 0
        <if test="customerName != null and customerName != ''">
            and c.`name` like concat('%',#{customerName},'%')
        </if>
        <if test="type != null">
            and c.type = #{type}
        </if>
        <if test="mobile != null and mobile != ''">
            and c.mobiles like concat('%',#{mobile},'%')
        </if>
        <if test="beginTime != null and beginTime != ''">
            and date_format(c.create_time,'%Y-%m-%d') >= date_format(#{beginTime},'%Y-%m-%d')
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(c.create_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
        </if>
        <if test="tag != null and tag != ''">
            and (select
            group_concat(distinct t.tag)
            from tb_wx_ext_follow_user_tag t
            where t.corp_id = c.corp_id
            and t.external_user_id = c.external_user_id) like concat('%',#{tag},'%')
        </if>
        <if test="wkUserIds != null">
            and (select
            count(1)
            from tb_wx_ext_follow_user u
            join tb_wx_user w on w.userid = u.user_id
            where u.corp_id = c.corp_id
            and u.external_user_id = c.external_user_id
            and w.del_flag = 1
            and u.user_id in
            <foreach item="wkUserIds" collection="wkUserIds" open="(" separator="," close=")">
                #{wkUserIds}
            </foreach>
            ) > 0
        </if>
        <if test="corpUserIds != null and corpUserIds.size > 0">
            and (select
            count(1)
            from tb_wx_ext_follow_user u
            join tb_wx_user w on w.userid = u.user_id
            where u.corp_id = c.corp_id
            and c.external_user_id =  u.external_user_id
            and u.status = '0'
            -- and w.del_flag = 1
            and u.user_id in
            <foreach collection="corpUserIds" item="corpUserId" open="(" close=")" separator=",">
                #{corpUserId}
            </foreach>
            ) > 0
        </if>
        <if test="addWay != null and addWay != ''">
            and (select group_concat(distinct(case
            when u.add_way = 1 then ifnull(
            (select remark from tb_wx_contact where state = u.state limit 1), '扫描二维码')
            when u.add_way = 2 then '搜索手机号'
            when u.add_way = 3 then '名片分享'
            when u.add_way = 4 then '群聊'
            when u.add_way = 5 then '手机通讯录'
            when u.add_way = 6 then '微信联系人'
            when u.add_way = 7 then '来自微信的添加好友申请'
            when u.add_way = 8 then '安装应用时自动添加'
            when u.add_way = 9 then '搜索邮箱'
            when u.add_way = 10 then '视频号添加'
            when u.add_way = 11 then '通过日程参与人添加'
            when u.add_way = 12 then '通过会议参与人添加'
            when u.add_way = 13 then '添加微信好友对应的企业微信'
            when u.add_way = 14 then '通过智慧硬件专属客服添加'
            when u.add_way = 15 then '通过上门服务客服添加'
            when u.add_way = 16 then '通过获客链接添加'
            when u.add_way = 17 then '通过定制开发添加'
            when u.add_way = 18 then '通过需求回复添加'
            when u.add_way = 21 then '通过第三方售前客服添加'
            when u.add_way = 22 then '通过可能的商务伙伴添加'
            when u.add_way = 24 then '通过接受微信账号收到的好友申请添加'
            when u.add_way = 201 then '内部成员共享'
            when u.add_way = 202 then '管理员/负责人分配'
            else '未知来源' end))
            from tb_wx_ext_follow_user u
            where u.corp_id = c.corp_id
            and u.external_user_id = c.external_user_id
            and u.`status` != 3
            ) like  concat('%',#{addWay},'%')
        </if>
        <if test="isAuth != null">
            and c.is_auth = #{isAuth}
        </if>
        <if test="stageConditionList != null and stageConditionList.size > 0">
            and c.id in (select distinct jcs.ext_customer_id
            from tb_wx_ext_journey_customer_stage jcs
            where jcs.del_flag=0 and jcs.stage_id in
            <foreach collection="stageConditionList" item="journey" open="(" close=")" separator=",">
                #{journey.journeyId}
            </foreach>)
        </if>
        <if test="externalUserIdList   != null and externalUserIdList.size > 0">
            and c.external_user_id   in
            <foreach collection="externalUserIdList" item="extUserId" open="(" close=")" separator=",">
                #{extUserId}
            </foreach>
        </if>
        order by c.create_time desc
    </select>

    <!-- 获取无有效关系的客户Id -->
    <select id="getNullRelationshipIds" resultType="java.lang.String">
        select
            c.external_user_id
        from
        tb_wx_ext_customer c
        left join tb_wx_ext_follow_user fu on
        fu.external_user_id = c.external_user_id
        where c.`status` = '0'
        group by c.external_user_id
        having sum((case when fu.status = 0 then 1 else 0 end)) = 0
    </select>
</mapper>