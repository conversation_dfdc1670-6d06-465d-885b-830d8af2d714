<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cenker.scrm.mapper.wxmp.WxAppMapper">

    <resultMap id="wxAppMap" type="WxApp">
        <id property="id" column="id"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateId" column="update_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="organId" column="organ_id"/>
        <result property="weixinSign" column="weixin_sign"/>
        <result property="appType" column="app_type"/>
        <result property="secret" column="secret"/>
        <result property="token" column="token"/>
        <result property="aesKey" column="aes_key"/>
        <result property="name" column="name"/>
        <result property="weixinType" column="weixin_type"/>
        <result property="weixinHao" column="weixin_hao"/>
        <result property="logo" column="logo"/>
        <result property="qrCode" column="qr_code"/>
        <result property="community" column="community"/>
        <result property="remarks" column="remarks"/>
        <result property="vipCardId" column="vip_card_id"/>
        <result property="verifyType" column="verify_type"/>
        <result property="principalName" column="principal_name"/>
        <result property="isComponent" column="is_component"/>
    </resultMap>

    <sql id="wxAppSql">
          wa.`id`,
		  wa.`create_by`,
		  wa.`create_time`,
		  wa.`update_by`,
		  wa.`update_time`,
		  wa.`del_flag`,
		  wa.`corp_config_id`,
		  wa.`weixin_sign`,
		  wa.`app_type`,
		  wa.`secret`,
		  wa.`token`,
		  wa.`aes_key`,
		  wa.`name`,
		  wa.`is_component`,
		  wa.`weixin_type`,
		  wa.`weixin_hao`,
		  wa.`verify_type`,
		  wa.`logo`,
		  wa.`qr_code`,
		  wa.`principal_name`,
		  wa.`community`,
		  wa.`remarks`,
		  wa.`vip_card_id`
    </sql>

    <!--不需要租户信息约束的sql-->
    <select id="findByWeixinSign" resultMap="wxAppMap">
        SELECT
        <include refid="wxAppSql"/>
        FROM
        mp_wx_app wa
        WHERE wa.`weixin_sign` = #{weixinSign}
    </select>
    <!--不需要租户信息约束的sql-->
    <select id="findByAppId" resultMap="wxAppMap">
        SELECT
        <include refid="wxAppSql"/>
        FROM
        mp_wx_app wa
        WHERE wa.app_id = #{appId}
    </select>

    <select id="selectWxOpenList" resultType="WxOpenQueryVo">
        select m.id,
               m.name,
               m.verify_type,
               m.logo,
               m.app_type,
               m.default_use,
               m.principal_name
        from mp_wx_app m
        where m.corp_config_id = #{corpConfigId}
          and m.del_flag = 0
          and m.is_component = 1
    </select>

    <select id="findDefaultWxMaByCorpId" resultType="com.cenker.scrm.pojo.entity.wechat.wxmp.WxApp">
        select a.id,
               a.secret,
               a.app_id,
               a.principal_name,
               a.default_page,
               a.name,
               a.verify_type
        from mp_wx_app a
        where a.corp_config_id = #{corpId}
          -- and a.verify_file_name is not null
          and a.app_type = 1
          and a.del_flag = 0
          and a.default_page is not null
          -- and a.is_component = 1
          <if test="testLink == false">
              and a.default_use = 1
          </if>
          order by a.update_time desc
        limit 1
    </select>

    <select id="findInstallWxMaByCorpId" resultType="com.cenker.scrm.pojo.entity.wechat.wxmp.WxApp">
        select a.id
        from mp_wx_app a
        where a.corp_config_id = #{corpId}
          and a.default_use = 1
          and a.app_type = 1
          and a.del_flag = 0
          -- and a.is_component = 1
    </select>
</mapper>
