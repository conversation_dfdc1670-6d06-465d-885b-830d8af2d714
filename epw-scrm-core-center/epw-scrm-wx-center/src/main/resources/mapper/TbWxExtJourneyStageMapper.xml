<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.journey.TbWxExtJourneyStageMapper">

    <resultMap id="detailMap" type="com.cenker.scrm.pojo.vo.journey.ExternalJourneyCustomerVO">
        <id column="ext_customer_id" property="id"/>
        <result column="name" property="name"/>
        <result column="external_user_id" property="externalUserId"/>
        <result column="avatar" property="avatar"/>
        <result column="corp_name" property="corpName"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="add_user_name" property="addUserName"/>
        <result column="add_date" property="addDate"/>
    </resultMap>

    <select id="detail" resultMap="detailMap">
        select cs.ext_customer_id,
               c.external_user_id,
               c.name,
               c.type,
               c.avatar,
               c.corp_name,
               c.`status`,
               (
                   select twu.name
                   from tb_wx_ext_follow_user u
                            left join tb_wx_user twu on twu.userid = u.user_id and twu.corp_id = u.corp_id
                   where u.external_user_id = c.external_user_id
                   order by twu.del_flag, u.create_time
                   limit 1
               ) add_user_name
        from tb_wx_ext_journey_customer_stage cs
                 left join tb_wx_ext_customer c on c.id = cs.ext_customer_id
        where cs.stage_id = #{stageId}
          and cs.corp_id = #{corpId}
        <if test="name != null and name != ''">
            and (c.name like concat('%',#{name},'%') or c.corp_name like concat('%',#{name},'%'))
        </if>
        order by cs.update_time desc
    </select>

    <select id="neverStageCustomer" resultMap="detailMap">
        select
            t.*,
            (
                select create_time from
                    tb_wx_ext_follow_user
                where user_id = t.first_add_user_id
                  and external_user_id = t.external_user_id
                order by `status` desc limit 1)add_date,
            (select name from tb_wx_user where userid = t.first_add_user_id limit 1)add_user_name,
            (case when t.add_way_status = 1 then ifnull((select remark from tb_wx_contact where state = t.state limit 1),'扫描二维码')
                  when t.add_way_status = 2 then '搜索手机号'
                  when t.add_way_status = 3 then '名片分享'
                  when t.add_way_status = 4 then '群聊'
                  when t.add_way_status = 5 then '手机通讯录'
                  when t.add_way_status = 6 then '微信联系人'
                  when t.add_way_status = 7 then '来自微信的添加好友申请'
                  when t.add_way_status = 8 then '安装应用时自动添加'
                  when t.add_way_status = 9 then '搜索邮箱'
                  when t.add_way_status = 10 then '视频号添加'
                  when t.add_way_status = 11 then '通过日程参与人添加'
                  when t.add_way_status = 12 then '通过会议参与人添加'
                  when t.add_way_status = 13 then '添加微信好友对应的企业微信'
                  when t.add_way_status = 14 then '通过智慧硬件专属客服添加'
                  when t.add_way_status = 15 then '通过上门服务客服添加'
                  when t.add_way_status = 16 then '通过获客链接添加'
                  when t.add_way_status = 17 then '通过定制开发添加'
                  when t.add_way_status = 18 then '通过需求回复添加'
                  when t.add_way_status = 21 then '通过第三方售前客服添加'
                  when t.add_way_status = 22 then '通过可能的商务伙伴添加'
                  when t.add_way_status = 24 then '通过接受微信账号收到的好友申请添加'
                  when t.add_way_status = 201 then '内部成员共享'
                  when t.add_way_status = 202 then '管理员/负责人分配'
                  else '未知来源' end)add_way
        from (
                 select
                     c.id ext_customer_id,
                     c.external_user_id,
                     c.name,
                     c.type,
                     c.corp_name,
                     c.avatar,
                     (select u.user_id from tb_wx_ext_follow_user u
                                                left join tb_wx_user twu on twu.userid = u.user_id and twu.corp_id = u.corp_id
                      where u.external_user_id = c.external_user_id
                      order by twu.del_flag,u.create_time
                      limit 1
                     )first_add_user_id,
                     (select ifnull(fu.add_way, 0)
                      from tb_wx_ext_follow_user fu
                      where fu.external_user_id = c.external_user_id
                        and fu.corp_id = c.corp_id
                        and fu.user_id = (select u.user_id from tb_wx_ext_follow_user u
                                          left join tb_wx_user twu on twu.userid = u.user_id and twu.corp_id = u.corp_id
                                          where u.external_user_id = c.external_user_id
                                          order by twu.del_flag,u.create_time
                                          limit 1)
                        and fu.`status` = 0
                      limit 1)add_way_status,
                     (select fu.state
                      from tb_wx_ext_follow_user fu
                      where fu.external_user_id = c.external_user_id
                        and fu.corp_id = c.corp_id
                        and fu.user_id = (select u.user_id from tb_wx_ext_follow_user u
                                          left join tb_wx_user twu on twu.userid = u.user_id and twu.corp_id = u.corp_id
                                          where u.external_user_id = c.external_user_id
                                          order by twu.del_flag,u.create_time
                                          limit 1)
                        and fu.`status` = 0
                      limit 1)state
                 from tb_wx_ext_customer c
                 where c.corp_id = (select corp_id from tb_wx_corp_config where id = #{corpId})
                   and c.`status` = 0
                   <if test="wkUserIds != null and wkUserIds.size() > 0">
                       and (select
                       count(1)
                       from tb_wx_ext_follow_user u
                       join tb_wx_user w on w.userid = u.user_id
                       where u.corp_id = c.corp_id
                       and u.external_user_id = c.external_user_id
                       and w.del_flag = 1
                       and u.user_id in
                       <foreach item="wkUserIds" collection="wkUserIds" open="(" separator="," close=")">
                           #{wkUserIds}
                       </foreach>
                       ) > 0
                   </if>
                   and c.id not in
                       (
                           select
                               distinct ext_customer_id
                           from  tb_wx_ext_journey_customer_stage cs
                           where cs.corp_id = #{corpId}
                           and cs.journey_info_id = #{journeyId}
                           and cs.del_flag = 0
        )
        <if test="name != null and name != ''">
            and (c.name like concat('%',#{name},'%') or c.corp_name like concat('%',#{name},'%'))
        </if>
                 order by c.create_time desc
             )t
    </select>

    <resultMap id="getJourneyStagesByCustomerIdMap" type="ExternalJourneyVO">
        <id column="id" property="journeyId"/>
        <result column="journey_name" property="journeyName"/>
        <collection property="stages" ofType="ExternalJourneyStageVO">
            <id column="sid" property="stageId"/>
            <result column="stage_name" property="stageName"/>
            <result column="stage_desc" property="stageDesc"/>
            <result column="select_status" property="selectStatus"/>
        </collection>
    </resultMap>

    <select id="getJourneyStagesByCustomerId" resultMap="getJourneyStagesByCustomerIdMap">
        SELECT
            j.id,
            j.journey_name,
            s.stage_name,
            s.id sid,
            ( cs.stage_id = s.id ) AS select_status
        FROM
            tb_wx_ext_journey_info j
                JOIN tb_wx_ext_journey_stage s ON s.journey_info_id = j.id
                LEFT JOIN tb_wx_ext_journey_customer_stage cs ON cs.journey_info_id = j.id AND cs.del_flag = 0
                LEFT JOIN tb_wx_ext_customer c ON c.id = cs.ext_customer_id AND external_user_id = #{extUserId}
        WHERE
            j.del_flag = 0
          AND s.del_flag = 0
        ORDER BY
            j.order_num,
            s.order_num
    </select>
</mapper>