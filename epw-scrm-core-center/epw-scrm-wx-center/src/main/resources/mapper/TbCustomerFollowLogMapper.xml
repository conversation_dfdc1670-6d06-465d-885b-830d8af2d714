<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.external.TbCustomerFollowLogMapper">

    <resultMap type="TbCustomerFollowLog" id="TbCustomerFollowLogResult">
		<result property="id" column="id" />
		<result property="corpId" column="corp_id" />
		<result property="userId" column="user_id" />
		<result property="externalUserId" column="external_user_id" />
		<result property="content" column="content" />
		<result property="type" column="type" />
		<result property="source" column="source" />
		<result property="createBy" column="create_by" />
		<result property="createTime" column="create_time" />
	</resultMap>
    
    <sql id="selectTbCustomerFollowLogVo">
        select id, corp_id, user_id, external_user_id, context, type, source, create_by, create_time from tb_customer_follow_log
    </sql>

    <select id="selectTbCustomerFollowLogList" parameterType="TbCustomerFollowLog" resultMap="TbCustomerFollowLogResult">
        <include refid="selectTbCustomerFollowLogVo" />
        <where>
			<if test="corpId != null  and corpId != ''">
				and corp_id = #{corpId}
			</if>
			<if test="userId != null  and userId != ''">
				and user_id = #{userId}
			</if>
			<if test="externalUserId != null  and externalUserId != ''">
				and external_user_id = #{externalUserId}
			</if>
			<if test="content != null  and content != ''">
				and content = #{content}
			</if>
			<if test="type != null  and type != ''">
				and type = #{type}
			</if>
			<if test="source != null  and source != ''">
				and source = #{source}
			</if>
		</where>
    </select>

    <select id="selectTbCustomerFollowLogById" parameterType="Long"
        resultMap="TbCustomerFollowLogResult">
			<include refid="selectTbCustomerFollowLogVo" />
            where id = #{id}
	</select>

    <insert id="insertTbCustomerFollowLog" parameterType="TbCustomerFollowLog" useGeneratedKeys="true"
        keyProperty="id">
        insert into tb_customer_follow_log
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="corpId != null">corp_id,
			</if>
			<if test="userId != null">user_id,
			</if>
			<if test="externalUserId != null">external_user_id,
			</if>
			<if test="content != null">content,
			</if>
			<if test="type != null">type,
			</if>
			<if test="source != null">source,
			</if>
			<if test="createBy != null">create_by,
			</if>
			<if test="createTime != null">create_time,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="corpId != null">#{corpId},
			</if>
			<if test="userId != null">#{userId},
			</if>
			<if test="externalUserId != null">#{externalUserId},
			</if>
			<if test="content != null">#{content},
			</if>
			<if test="type != null">#{type},
			</if>
			<if test="source != null">#{source},
			</if>
			<if test="createBy != null">#{createBy},
			</if>
			<if test="createTime != null">#{createTime},
			</if>
		</trim>
    </insert>

    <update id="updateTbCustomerFollowLog" parameterType="TbCustomerFollowLog">
        update tb_customer_follow_log
        <trim prefix="SET" suffixOverrides=",">
			<if test="corpId != null">corp_id =
				#{corpId},
			</if>
			<if test="userId != null">user_id =
				#{userId},
			</if>
			<if test="externalUserId != null">external_user_id =
				#{externalUserId},
			</if>
			<if test="content != null">content =
				#{content},
			</if>
			<if test="type != null">type =
				#{type},
			</if>
			<if test="source != null">source =
				#{source},
			</if>
			<if test="createBy != null">create_by =
				#{createBy},
			</if>
			<if test="createTime != null">create_time =
				#{createTime},
			</if>
		</trim>
        where id = #{id}
    </update>

    <delete id="deleteTbCustomerFollowLogById" parameterType="Long">
        delete from tb_customer_follow_log where id = #{id}
    </delete>

    <delete id="deleteTbCustomerFollowLogByIds" parameterType="String">
        delete from tb_customer_follow_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


	<select id="queryFollowLogByExtUserId" resultType="CustomerFollowVO">
        SELECT
            *
        FROM (
            SELECT
                t,id,
                0 follow_type,
                (
                    SELECT max(NAME) FROM tb_wx_user
                    WHERE userid = t.user_id
                ) userName,
                (
                    SELECT max(t2.`name`)
                    FROM tb_wx_user, tb_wx_department t2
                    WHERE userid = t.user_id AND main_department = t2.id AND t2.corp_id = t.corp_id
                ) deptName,
                tag,
                selfTag,
                remark,
                description content,
                add_way addWay,
                t.`status`,
                create_time createTime
            FROM
                tb_wx_ext_follow_user t
            WHERE
                t.corp_id = #{corpId}
                <if test="extUserId != null  and extUserId != ''">
                    and t.external_user_id = #{extUserId}
                </if>

            union

            SELECT
                id,
                `type` follow_type,
                (
                    SELECT max(NAME) FROM tb_wx_user
                    WHERE userid = f.user_id
                ) userName,
                (
                    SELECT max(t2.`name`)
                    FROM tb_wx_user, tb_wx_department t2
                    WHERE userid = f.user_id AND main_department = t2.id AND t2.corp_id = f.corp_id
                ) deptName,
                '' tag,
                '' remark,
                '' selfTag,
                content,
                source addWay,
                (
                    SELECT
                        `status`
                    FROM
                        tb_wx_ext_follow_user
                    WHERE
                        corp_id = f.corp_id
                    AND user_id = f.user_id
                    AND external_user_id = f.external_user_id
                    ORDER BY
                        create_time DESC
                    LIMIT 1
                ) `status`,
                create_time createTime
            FROM
                tb_customer_follow_log f
            WHERE
              f.corp_id = #{corpId}
              <if test="extUserId != null  and extUserId != ''">
                and f.external_user_id = #{extUserId}
              </if>
        )t
        ORDER BY t.createTime desc
    </select>

</mapper>