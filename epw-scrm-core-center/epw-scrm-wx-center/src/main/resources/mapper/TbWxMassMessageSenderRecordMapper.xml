<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.message.TbWxMassMessageSenderRecordMapper">

    <select id="getSendUserDetail" resultType="com.cenker.scrm.pojo.vo.message.MassMessageSendDetailVO">
        SELECT
            user_id as memberName,
            COUNT(*) AS expect_send,
            record.send_time as setting_time,
            COUNT( record.send_status = 1 OR NULL ) AS actual_send,
            COUNT(send_status=2 OR null) as fail_send,
            send_status
        FROM
            tb_wx_mass_message_sender_record record
        WHERE
            record.del_flag = 0
          AND record.message_info_id = #{id}
        GROUP BY
            user_id
    </select>
</mapper>