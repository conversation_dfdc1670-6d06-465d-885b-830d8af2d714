<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.moment.TbWxMomentMapper">

    <select id="getPersonalMomentList" resultType="com.cenker.scrm.pojo.vo.moment.PersonMomentListVO">
        SELECT
        m.moment_id AS "momentId",
        m.content AS "content",
        u.name AS "createBy",
        m.visible_type AS "visibleType",
        m.moment_create_time_date AS "momentCreateTimeDate",
        m.comment_cnt AS "commentCnt",
        m.like_cnt AS "likeCnt",
        m.image_urls AS "imageUrls",
        m.video_url AS "videoUrl",
        m.video_thumb_url AS "videoThumbUrl",
        m.link_title AS "linkTitle",
        m.link_url AS "linkUrl",
        m.location_latitude AS "locationLatitude",
        m.location_longitude AS "locationLongitude",
        m.location_name AS "locationName"
        FROM
        tb_wx_moment m
        INNER JOIN
        tb_wx_user u ON m.creator = u.userid
        WHERE
        m.corp_id = #{corpId}
        AND m.create_type = 1
        <if test="condition.publisherName != null and condition.publisherName != ''">
            AND u.name LIKE CONCAT('%', #{condition.publisherName}, '%')
        </if>
        <if test="condition.publisherId != null and condition.publisherId != ''">
            AND m.creator = #{condition.publisherId}
        </if>
        <if test="condition.content != null and condition.content != ''">
            AND m.content LIKE CONCAT('%', #{condition.content}, '%')
        </if>
        <if test="condition.beginTime != null and condition.beginTime != ''">
            AND m.moment_create_time_date >= #{condition.beginTime}
        </if>
        <if test="condition.endTime != null and condition.endTime != ''">
            AND m.moment_create_time_date <![CDATA[<=]]> CONCAT(#{condition.endTime}, ' 23:59:59')
        </if>
        <if test="condition.wkUserIds != null and condition.wkUserIds.size() > 0">
            AND m.creator in
            <foreach item="wkUserId" collection="condition.wkUserIds" open="(" separator="," close=")">
                #{wkUserId}
            </foreach>
        </if>

        ORDER BY
        m.moment_create_time_date DESC
    </select>


    <select id="getMomentVisibleUserList" resultType="com.cenker.scrm.pojo.vo.moment.MomentUserListVO">
        SELECT
        m.external_user_id AS "userId",
        c.name AS "userName",
        c.avatar AS "avatar"
        FROM
        tb_wx_moment_customer m
        INNER JOIN
        tb_wx_ext_customer c ON m.external_user_id = c.external_user_id
        WHERE
        m.moment_id = #{condition.momentId}
        <if test="condition.commentUserName != null and condition.commentUserName != ''">
            AND c.name LIKE CONCAT('%', #{condition.commentUserName}, '%')
        </if>

    </select>

</mapper>
