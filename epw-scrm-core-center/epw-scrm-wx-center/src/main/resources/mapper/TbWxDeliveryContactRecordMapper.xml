<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.sitecontact.TbWxDeliveryContactRecordMapper">

    <select id="getDeliveryContactDataStatistics" resultType="AcquisitionDataStatisticsVO">
        select
            count(r.external_user_id)acquisitionTotalCnt,
            ifnull(sum(date_format(r.create_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')),0)acquisitionTodayCnt,
            ifnull(sum(date_format(r.create_time, '%Y-%m-%d') = date_format(date_sub(curdate(), interval 1 day), '%Y-%m-%d')),0)acquisitionYesterdayCnt
        from tb_wx_delivery_contact_record r
        where r.user_pri_id = #{userPriId}
          and r.new_flag = 1
    </select>

    <select id="getDeliveryRankData" resultType="ContactDeliveryRankDetailVO">
        select
            (case when @score = t.acquisition_cnt then @rank
                  when @score := t.acquisition_cnt then @rank := @rank + 1
                end)final_rank,
            t.*
        from
            (
                select
                        (select name from tb_wx_user where id = r.user_pri_id)delivery_user_name,
                        (select avatar from tb_wx_user where id = r.user_pri_id)delivery_user_avatar,
                        r.delivery_user_id delivery_user_id,
                        r.store_id,
                        a.area_name store_name,
                        (select area_name from contact_area where id = r.site_id)site_name,
                        count(r.id) acquisition_cnt
                from tb_wx_delivery_contact_record r
                         left join contact_delivery_user du on r.delivery_user_id = du.id
                         left join contact_area a on r.store_id = a.id
                where r.new_flag = 1
                  and du.del_flag = 0
                  and a.del_flag = 0
                  <choose>
                      <when test="siteId != null and siteId != 0">
                          and r.site_id = #{siteId}
                      </when>
                      <otherwise>

                      </otherwise>
                  </choose>
                  <choose>
                      <when test="startTime != null and endTime != null and startTime == endTime">
                          and date_format(r.create_time,'%Y-%m-%d') = #{startTime}
                      </when>
                      <otherwise>
                          <if test="startTime != null and startTime != ''">
                              and date_format(r.create_time,'%Y-%m-%d') >= #{startTime}
                          </if>
                          <if test="endTime != null and endTime != ''">
                              and date_format(r.create_time,'%Y-%m-%d') &lt;= #{endTime}
                          </if>
                      </otherwise>
                  </choose>
                group by r.delivery_user_id,r.store_id
                order by count(r.id) desc
            )t,(select @rank := 0 ,@score := null) r
    </select>

    <select id="getCityRankData" resultType="ContactCityRankDetailVO">
        select
            (case when @score = t.acquisition_cnt then @rank
                  when @score := t.acquisition_cnt then @rank := @rank + 1
                end)final_rank,
            t.*
        from
            (
                select
                    (select area_name from contact_area where id = r.site_id)site_name,
                    count(r.id) acquisition_cnt
                from tb_wx_delivery_contact_record r
                         join contact_delivery_user du on r.delivery_user_id = du.id and du.del_flag = 0
                         join contact_area a on r.store_id = a.id and a.del_flag = 0
                where r.new_flag = 1
                <if test="startTime != null and startTime != ''">
                    and date_format(r.create_time,'%Y-%m-%d') >= #{startTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    and date_format(r.create_time,'%Y-%m-%d') &lt;= #{endTime}
                </if>
                group by r.site_id
                order by count(r.id) desc
            )t,(select @rank := 0 ,@score := null) r
    </select>

    <select id="getAcquisitionDailyData" resultType="AcquisitionDailyVO">
        select
            date_format(r.create_time, '%Y-%m-%d') `day`,
            count(r.id) acquisition_cnt
        from tb_wx_delivery_contact_record r
                 join contact_delivery_user du on r.delivery_user_id = du.id and du.del_flag = 0
                 join contact_area a on r.store_id = a.id and a.del_flag = 0
        where r.new_flag = 1
        <choose>
            <when test="siteId != null and siteId != 0">
                and r.site_id = #{siteId}
            </when>
            <otherwise>

            </otherwise>
        </choose>
        group by date_format(r.create_time, '%Y-%m-%d')
        -- order by r.create_time
    </select>

    <select id="getDeliveryAcquisitionData" resultType="ContactDeliveryAcquisitionDataVO">
       select
              (@num := @num + 1)num,
              t.* from (
        select
        (select name from tb_wx_user where id = r.user_pri_id)delivery_user_name,
        (select avatar from tb_wx_user where id = r.user_pri_id)delivery_user_avatar,
        (select mobile from tb_wx_user where id = r.user_pri_id)delivery_user_mobile,
        r.delivery_user_id delivery_user_id,
        r.store_id,
        (case when b.parent_id = -1 then '默认'
        when b.parent_id &lt; 35 then (select area_name from contact_area where id = b.parent_id)
        when b.parent_id &lt; 1000 then concat((select area_name from contact_area where id = (select parent_id from contact_area where id = b.parent_id)),(select area_name from contact_area where id = b.parent_id))
        when b.parent_id &lt; 10000 then concat(
        (select area_name from contact_area where id = (select parent_id from contact_area where id = (select parent_id from contact_area where id = b.parent_id)))
        ,(select area_name from contact_area where id = (select parent_id from contact_area where id = b.parent_id)),(select area_name from contact_area where id = b.parent_id))
        else '默认' end)area_name,
        a.area_name store_name,
        b.area_name site_name,
        if(a.del_flag,-1,a.area_status)store_status,
        count(r.id) acquisition_cnt
        from tb_wx_delivery_contact_record r
        join contact_delivery_user du on r.delivery_user_id = du.id and du.del_flag = 0
        join contact_area a on r.store_id = a.id
        join contact_area b on r.site_id = b.id
        where r.new_flag = 1
        <if test="areaId != null and areaId != 0">
            <choose>
                <when test="areaId == -1">
                    and b.parent_id = #{areaId}
                </when>
                <when test="areaId &lt;= 34">
                    and (b.parent_id = #{areaId}
                    or  b.parent_id in (select city.id from contact_area city where city.parent_id = #{areaId})
                    or b.parent_id in (select id from contact_area where parent_id in (select id from contact_area where parent_id = #{areaId})))
                </when>
                <when test="areaId &lt;= 1000">
                    and (b.parent_id = #{areaId}
                    or b.parent_id in (select id from contact_area where parent_id = #{areaId}))
                </when>
                <otherwise>
                    and b.parent_id = #{areaId}
                </otherwise>
            </choose>
        </if>
        <if test="siteName != null and siteName != ''">
            and b.area_name like concat('%',#{siteName},'%')
        </if>
        <if test="deliveryUserMobile != null and deliveryUserMobile != ''">
            and (select mobile from tb_wx_user where id = r.user_pri_id) like concat ('%',#{deliveryUserMobile},'%')
        </if>
        <if test="deliveryUserName != null and deliveryUserName != ''">
            and (select name from tb_wx_user where id = r.user_pri_id) like concat ('%',#{deliveryUserName},'%')
        </if>
        <if test="storeId != null and storeId != ''">
            and r.store_id = #{storeId}
        </if>
        <if test="storeStatus != null">
            <choose>
                <when test="storeStatus == -1">
                    and a.del_flag = 1
                </when>
                <otherwise>
                    and a.del_flag = 0
                    and a.area_status = #{storeStatus}
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="beginTime != null and endTime != null and beginTime == endTime">
                and date_format(r.create_time,'%Y-%m-%d') = #{beginTime}
            </when>
            <otherwise>
                <if test="beginTime != null and beginTime != ''">
                    and date_format(r.create_time,'%Y-%m-%d') >= #{beginTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    and date_format(r.create_time,'%Y-%m-%d') &lt;= #{endTime}
                </if>
            </otherwise>
        </choose>
        group by r.delivery_user_id,r.store_id
        order by count(r.id) desc
      )t,(select @num := 0)i
    </select>
</mapper>