<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.statistic.WkChatConversationMapper">

    <resultMap id="BaseResultMap" type="com.cenker.scrm.pojo.entity.statistic.WkChatConversation">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="statisticDate" column="statistic_date" jdbcType="DATE"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="staffId" column="staff_id" jdbcType="VARCHAR"/>
            <result property="originMsgId" column="origin_msg_id" jdbcType="VARCHAR"/>
            <result property="originMsgTime" column="origin_msg_time" jdbcType="TIMESTAMP"/>
            <result property="originMsgContent" column="origin_msg_content" jdbcType="VARCHAR"/>
            <result property="replyMsgId" column="reply_msg_id" jdbcType="VARCHAR"/>
            <result property="replyMsgTime" column="reply_msg_time" jdbcType="TIMESTAMP"/>
            <result property="replyMsgContent" column="reply_msg_content" jdbcType="VARCHAR"/>
            <result property="duration" column="duration" jdbcType="INTEGER"/>
            <result property="timeout" column="timeout" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,statistic_date,customer_id,
        staff_id,origin_msg_id,origin_msg_time,
        origin_msg_content,reply_msg_id,reply_msg_time,
        reply_msg_content,duration,timeout
    </sql>

    <insert id="saveStatisticDateByDay">
            insert into wk_chat_conversation (statistic_date,customer_id,staff_id,origin_msg_id,origin_msg_time,origin_msg_content,reply_msg_id,reply_msg_time,reply_msg_content,duration,timeout)
            SELECT
                str_to_date(#{statDate}, '%Y-%m-%d') as statistic_date,
                o.from_id AS customer_id,
                o.consume_id AS staff_id,
                o.msg_id AS origin_msg_id,
                o.msg_time AS origin_msg_time,
                o.msg_content AS origin_msg_content,
                min( r.msg_id ) AS reply_msg_id,
                r.msg_time AS reply_msg_time,
                r.msg_content AS reply_msg_content,
                TIMESTAMPDIFF( SECOND, o.msg_time, r.msg_time ) AS duration,
                IF(r.msg_time IS NULL,
                    IF( TIMESTAMPDIFF( MINUTE, o.msg_time, now()) &lt;= ts.time_num, 0, 1 ),
                    IF( TIMESTAMPDIFF( MINUTE, o.msg_time, r.msg_time) > ts.time_num, 1, 0 )
                ) AS timeout
            FROM
                wk_chat_archive_info o
                    JOIN tb_wx_ext_customer c ON c.external_user_id = o.from_id
                    LEFT JOIN wk_chat_archive_info r ON r.consume_id = o.from_id
                    AND r.from_id = o.consume_id
                    AND r.msg_day = o.msg_day
                    AND r.msg_time > o.msg_time
                    LEFT JOIN ck_session_timeset_check_mapping cm ON cm.check_user_id = o.consume_id
                    LEFT JOIN ck_session_timeout_set ts ON ts.set_id = cm.set_id
                    AND DATE_FORMAT( o.msg_time, '%H:%i' ) BETWEEN ts.start_time AND ts.end_time
            WHERE
                o.chat_type = 1
                AND o.msg_time  >= #{statDateStart}
                AND o.msg_time  &lt; #{statDateEnd}
        GROUP BY
                o.msg_id,
                o.from_id,
                o.consume_id
    </insert>
</mapper>
