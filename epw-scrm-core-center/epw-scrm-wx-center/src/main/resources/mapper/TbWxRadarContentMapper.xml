<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.radar.TbWxRadarContentMapper">

    <select id="getRadarContentH5" resultType="com.cenker.scrm.pojo.vo.radar.RadarContentVO">
        select
            c.id,
            c.cover,
            c.digest,
            c.title,
            c.content,
            c.create_time,
            c.author,
            c.base_url,
            c.base_read_num,
            c.remark,
            r.type  radar_type,
            c.pdf_image pdf_image_source,
            c.tips,
            r.view_perm
        from tb_wx_radar_content c
                 left join tb_wx_radar_interact r on c.radar_id = r.id
        where c.id = #{id}
          and c.show_status = 1
          and c.del_flag = 0
          and r.del_flag = 0
    </select>

</mapper>