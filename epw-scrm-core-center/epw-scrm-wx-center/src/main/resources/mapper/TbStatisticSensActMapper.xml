<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cenker.scrm.mapper.statistic.TbStatisticSensActMapper">
    <insert id="saveStatisticDateByDay">
        INSERT INTO tb_statistic_sens_act (statistic_date, act_type, act_type_name, rule_id, rule_name, userid, user_name, dept_id, dept_name, trigger_times, create_time)
        SELECT
            str_to_date(#{statDate}, '%Y-%m-%d') as statistic_date,
            r.act_type,
            dict.dict_label as act_type_name,
            r.rule_id,
            ru.rule_name,
            u.userid,
            u.name as user_name,
            u.main_department as dept_id,
            d.name as dept_name,
            COUNT(r.id) as trigger_times,
            NOW() as create_time
        FROM
            ck_session_sens_act_alarm_record r
            JOIN tb_wx_user u ON r.send_user_id = u.userid
            LEFT JOIN tb_wx_department d on d.id = u.main_department
            LEFT JOIN ck_session_sens_rule_info ru ON ru.rule_id = r.rule_id
            LEFT JOIN sys_dict_data dict ON dict.dict_type = 'sens_rule_act_type' AND dict.dict_value = r.act_type
        WHERE DATE(r.trigger_time) = str_to_date(#{statDate}, '%Y-%m-%d')
        GROUP BY
            r.act_type, r.rule_id, r.send_user_id;
    </insert>

    <select id="summary" resultType="com.cenker.scrm.pojo.vo.statistic.StatisticSensActSummaryVo">
        SELECT
            COUNT(DISTINCT userid) AS triggerNum,
            IFNULL(SUM(trigger_times), 0) AS triggerTimes
        FROM
            tb_statistic_sens_act
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and dept_id in
                <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and userid = #{wxUserId} and dept_id = #{deptId}
            </if>
        </if>
    </select>

  <select id="graph" resultType="com.cenker.scrm.pojo.vo.statistic.StatisticSensActSummaryVo">
        SELECT
                statistic_date AS statisticDate,
                COUNT(DISTINCT userid) AS triggerNum,
            IFNULL(SUM(trigger_times), 0) AS triggerTimes
        FROM
            tb_statistic_sens_act
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
      <!-- 权限控制 -->
      <if test="dataScope == null or dataScope != '1'.toString()">
          <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
              and dept_id in
              <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
          <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
              and userid = #{wxUserId} and dept_id = #{deptId}
          </if>
      </if>
            GROUP BY statistic_date
		    ORDER BY statistic_date
    </select>
</mapper>
