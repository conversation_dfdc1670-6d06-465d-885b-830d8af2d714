<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cenker.scrm.mapper.statistic.TbStatisticCustomerDetailMapper">

  <resultMap id="BaseResultMap" type="com.cenker.scrm.pojo.entity.statistic.TbStatisticCustomerDetail">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="statistic_customer_id" jdbcType="BIGINT" property="statisticCustomerId"/>
    <result column="statistic_date" jdbcType="DATE" property="statisticDate"/>
    <result column="userid" jdbcType="VARCHAR" property="userid"/>
    <result column="user_name" jdbcType="VARCHAR" property="userName"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, statistic_customer_id, statistic_date, userid, user_name
  </sql>

  <insert id="saveStatisticDateByDay">
    INSERT INTO tb_statistic_customer_detail (statistic_customer_id, userid, user_name, thumb_avatar, dept_id)
    SELECT distinct sc.id, wxu.userid, wxu.name, wxu.thumb_avatar, wxu.main_department
    FROM tb_statistic_customer sc
    JOIN tb_wx_ext_follow_user wefu ON sc.external_user_id = wefu.external_user_id
    and date(wefu.create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
    JOIN tb_wx_user wxu ON wefu.user_id = wxu.userid
    and date(wxu.create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
    WHERE sc.statistic_date = str_to_date(#{statDate}, '%Y-%m-%d')
    AND EXISTS (
    SELECT 1
    FROM tb_wx_ext_follow_user
    WHERE date(create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
    AND (`status` = 0 or date(update_time) >= str_to_date(#{statDate}, '%Y-%m-%d') or date(del_time) >= str_to_date(#{statDate}, '%Y-%m-%d'))
    and external_user_id = sc.external_user_id and user_id = wefu.user_id
    GROUP BY external_user_id, user_id HAVING max(update_time) = wefu.update_time)

  </insert>

  <delete id="removeStatisticDateByDay">
    DELETE tscd
    FROM tb_statistic_customer_detail tscd
           JOIN tb_statistic_customer tsc ON tscd.statistic_customer_id = tsc.id
    WHERE tsc.statistic_date = str_to_date(#{statDate}, '%Y-%m-%d');
  </delete>
</mapper>
