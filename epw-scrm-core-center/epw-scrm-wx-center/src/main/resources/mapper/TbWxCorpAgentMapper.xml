<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.deprecated.TbWxCorpAgentConfigMapper">

    <resultMap type="com.cenker.scrm.pojo.entity.wechat.TbWxCorpAgentConfig" id="TbWxCorpAgentConfigResult">
        <result property="id" column="id" />
        <result property="corpId" column="corp_id" />
        <result property="agentId" column="agent_id" />
        <result property="agentName" column="agent_name" />
        <result property="agentType" column="agent_type" />
        <result property="agentKey" column="agent_key" />
        <result property="agentSecret" column="agent_secret" />
        <result property="agentCallBackToken" column="agent_call_back_token" />
        <result property="agentCallBackSecret" column="agent_call_back_secret" />
        <result property="status" column="status" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="selectTbWxCorpAgentConfigVo">
        select id, corp_id, agent_id, agent_key, agent_type ,agent_name, agent_secret,
               agent_call_back_token, agent_call_back_secret,
               status, create_by, create_time, update_by, update_time
        from tb_wx_corp_agent_config
    </sql>

    <select id="selectTbWxCorpAgentConfigList" parameterType="TbWxCorpAgentConfig" resultMap="TbWxCorpAgentConfigResult">
        <include refid="selectTbWxCorpAgentConfigVo" />
        <where>
            <if test="corpId != null  and corpId != ''">
                and corp_id = #{corpId}
            </if>
            <if test="agentId != null  and agentId != ''">
                and agent_id = #{agentId}
            </if>
            <if test="agentName != null  and agentName != ''">
                and agent_name like concat('%', #{agentName}, '%')
            </if>
            <if test="agentType != null">
                and agent_type = #{agentType}
            </if>
            <if test="agentKey != null  and agentKey != ''">
                and agent_key = #{agentKey}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
        </where>
    </select>

    <select id="selectTbWxCorpAgentConfigById" parameterType="java.lang.Long" resultMap="TbWxCorpAgentConfigResult">
        <include refid="selectTbWxCorpAgentConfigVo" />
        where id = #{id}
    </select>

    <insert id="insertTbWxCorpAgentConfig" parameterType="TbWxCorpAgentConfig" useGeneratedKeys="true" keyProperty="id">
        insert into tb_wx_corp_agent_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">corp_id,
            </if>
            <if test="agentId != null and agentId != ''">agent_id,
            </if>
            <if test="agentName != null and agentName != ''">agent_name,
            </if>
            <if test="agentType != null">agent_type,
            </if>
            <if test="agentKey != null and agentKey != ''">agent_key,
            </if>
            <if test="agentSecret != null and agentSecret != ''">agent_secret,
            </if>
            <if test="agentCallBackToken != null and agentCallBackToken != ''">agent_call_back_token,
            </if>
            <if test="agentCallBackSecret != null and agentCallBackSecret != ''">agent_call_back_secret,
            </if>
            <if test="status != null">status,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">#{corpId},
            </if>
            <if test="agentId != null and agentId != ''">#{agentId},
            </if>
            <if test="agentName != null and agentName != ''">#{agentName},
            </if>
            <if test="agentType != null">#{agentType},
            </if>
            <if test="agentKey != null and agentKey != ''">#{agentKey},
            </if>
            <if test="agentSecret != null and agentSecret != ''">#{agentSecret},
            </if>
            <if test="agentCallBackToken != null and agentCallBackToken != ''">#{agentCallBackToken},
            </if>
            <if test="agentCallBackSecret != null and agentCallBackSecret != ''">#{agentCallBackSecret},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
        </trim>
    </insert>

    <update id="updateTbWxCorpAgentConfig" parameterType="TbWxCorpAgentConfig">
        update tb_wx_corp_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="corpId != null  and corpId != ''">
                corp_id = #{corpId},
            </if>
            <if test="agentId != null  and agentId != ''">
                agent_id = #{agentId},
            </if>
            <if test="agentName != null  and agentName != ''">
                agent_name = #{agentName},
            </if>
            <if test="agentType != null  and agentType != ''">
                agent_type = #{agentType},
            </if>
            <if test="agentKey != null  and agentKey != ''">
                agent_key = #{agentKey},
            </if>
            <if test="agentSecret != null and agentSecret != ''">
                agent_secret = #{agentSecret},
            </if>
            <if test="agentCallBackToken != null and agentCallBackToken != ''">
                agent_call_back_token = #{agentCallBackToken},
            </if>
            <if test="agentCallBackSecret != null and agentCallBackSecret != ''">
                agent_call_back_secret = #{agentCallBackSecret},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbWxCorpAgentConfigById" parameterType="Long">
        delete from tb_wx_corp_agent_config where id = #{id}
    </delete>

    <delete id="deleteTbWxCorpAgentConfigByIds" parameterType="String">
        delete from tb_wx_corp_agent_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>