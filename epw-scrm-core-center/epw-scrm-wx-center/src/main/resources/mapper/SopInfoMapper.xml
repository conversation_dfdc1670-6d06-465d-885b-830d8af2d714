<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.sop.SopInfoMapper">

    <resultMap id="listConditionSopMap" type="com.cenker.scrm.pojo.vo.sop.ConditionSopListVO">
        <id column="id" property="sopId"/>
        <result column="sop_name" property="sopName"/>
        <result column="sop_type" property="sopType"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="is_alive" property="alive"/>
        <result column="createTime" property="createTime"/>
        <result column="completeTaskCnt" property="completeTaskCnt"/>
        <result column="messageReachChurnRate" property="messageReachChurnRate"/>
        <result column="customerReachChurnRate" property="customerReachChurnRate"/>
        <result column="enableApproval" property="enableApproval"/>
        <result column="approvalUser" property="approvalUser"/>
        <result column="approvalUserName" property="approvalUserName"/>
        <result column="approvalRemark" property="approvalRemark"/>
   <!--     <collection ofType="com.cenker.scrm.pojo.vo.sop.ConditionSopDataVO"
                    property="taskCompleteDetailList"
                    select="com.cenker.scrm.mapper.sop.SopInfoMapper.queryTaskCompleteDetailListBySopId"
                    column="id"/>-->
    </resultMap>
    
    <select id="queryTaskCompleteDetailListBySopId" resultType="com.cenker.scrm.pojo.vo.sop.ConditionSopDataVO">
        select  mr.id msgId,
                (select name from tb_wx_user where userid = mr.user_id) userName,
                mr.send_time taskExecuteTime
        from tb_wx_mass_message_sender_record mr
                 join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                 join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.sop_id = #{sopId}
          and mr.send_status = 1
        group by mr.message_info_id,mr.user_id
        order by mr.send_time desc
        limit 200
    </select>

    <select id="queryTotalTaskCntBySopId" resultType="java.lang.Integer">
        (select count(1) from (
                                  select
                                      count(1)
                                  from tb_wx_mass_message_sender_record mr
                                           join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                           join ck_sop_content_info sci on mi.scene_id = sci.id
                                  where sci.sop_id = #{sopId}
                                  and sci.is_alive_version = 1
                                  group by mr.message_info_id,mr.user_id
                              )t1)
    </select>

    <select id="queryCompleteTaskCntBySopId" resultType="java.lang.Integer">
        (select count(1) from (
                                  select
                                      count(1)
                                  from tb_wx_mass_message_sender_record mr
                                           join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                           join ck_sop_content_info sci on mi.scene_id = sci.id
                                  where sci.sop_id = #{sopId}
                                  and (mr.send_status = 1 or mr.send_status = 3)
                                  and sci.is_alive_version = 1
                                  group by mr.message_info_id,mr.user_id
                              )t1)
    </select>

    <select id="queryDelCustomerCntBySopId" resultType="java.lang.Integer">
        select
        count(1)
        from (
        select
        mr.id,
        (select f.del_time from tb_wx_ext_follow_user f
        where f.external_user_id = mr.external_user_id
        and f.user_id = mr.user_id and f.`status` = 2
        and f.del_time &gt;= mr.send_time
        -- 这段时间内（发送-截止时间有没有流失记录）
        and f.del_time &lt;= date_add(mr.send_time,interval (select del_attribute_sop_minute from ck_sop_content_info where id = sci.id) minute)
        order by f.del_time desc limit 1
        )del_time,
        mr.external_user_id
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.sop_id = #{sopId}
        and sci.is_alive_version = 1
        and mr.send_status = 1
        group by mr.message_info_id,mr.user_id
        )t
        where t.del_time is not null
        group by t.external_user_id
    </select>

    <select id="querySendCustomerCntBySopId" resultType="java.lang.Integer">
        select
            count(1)
        from tb_wx_mass_message_sender_record mr
                 join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                 join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.sop_id = #{sopId}
          and sci.is_alive_version = 1
          and mr.send_status = 1
    </select>

    <select id="queryReachCustomerCntBySopId" resultType="java.lang.Integer">
        select count(1) from (
                                 select
                                     count(1)
                                 from tb_wx_mass_message_sender_record mr
                                          join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                          join ck_sop_content_info sci on mi.scene_id = sci.id
                                 where sci.sop_id = #{sopId}
                                   and mr.send_status = 1
                                   and sci.is_alive_version = 1
                                 group by mr.external_user_id
                             )t
    </select>

    <select id="listConditionSop" resultMap="listConditionSopMap">
        select t.* from (
        select sop.id,
        sop.sop_name,
        sop.sop_type,
        sop.remark,
        sop.create_by,
        (select nick_name from sys_user s where s.user_id = sop.create_by) create_user_name,
        sop.is_alive,
        sop.create_time createTime,
        sop.enable_approval enableApproval,
        sop.approval_user approvalUser,
        u.nick_name approvalUserName,
        sop.approval_remark approvalRemark,
        (
        select
        count(distinct mr.message_info_id ,mr.user_id)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.sop_id = sop.id
        and (mr.send_status = 1 or mr.send_status = 3)
        and sci.is_alive_version = 1
        )completeTaskCnt,
        (
        select
        count(distinct mr.message_info_id ,mr.user_id)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.sop_id = sop.id
        and sci.is_alive_version = 1
        )totalTaskCnt,
        ifnull(concat(round(((
        select
        count(distinct mr.message_info_id,mr.user_id,external_user_id)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.sop_id = sop.id
        and sci.is_alive_version = 1
        and mr.send_status = 1
        and (select f.del_time from tb_wx_ext_follow_user f
        where f.external_user_id = mr.external_user_id
        and f.user_id = mr.user_id and f.`status` = 2
        and f.del_time &gt;= mr.send_time
        and f.del_time &lt;= date_add(mr.send_time,interval (select del_attribute_sop_minute from ck_sop_content_info
        where id = sci.id) minute)
        order by f.del_time desc limit 1
        ) is not null
        )/
        (select
        count(distinct mr.external_user_id)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.sop_id = sop.id
        and mr.send_status = 1
        and sci.is_alive_version = 1))*100,2),'%'),'0.00%')customerReachChurnRate,

        ifnull(concat(round(((
        select
        count(distinct mr.message_info_id,mr.user_id,external_user_id)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.sop_id = sop.id
        and sci.is_alive_version = 1
        and mr.send_status = 1
        and (select f.del_time from tb_wx_ext_follow_user f
        where f.external_user_id = mr.external_user_id
        and f.user_id = mr.user_id and f.`status` = 2
        and f.del_time &gt;= mr.send_time
        and f.del_time &lt;= date_add(mr.send_time,interval (select del_attribute_sop_minute from ck_sop_content_info
        where id = sci.id) minute)
        order by f.del_time desc limit 1
        ) is not null
        )/
        (select
        count(1)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.sop_id = sop.id
        and sci.is_alive_version = 1
        and mr.send_status = 1))*100,2),'%'),'0.00%')messageReachChurnRate
        from ck_sop_info sop
        left join sys_user u on sop.approval_user = u.user_id
        where sop.is_deleted = 0
        <if test="sopType != null">
            and sop.sop_type = #{sopType}
        </if>
        <if test="sopName != null and sopName != ''">
            and sop.sop_name like concat('%',#{sopName},'%')
        </if>
        <if test="alive != null and alive != ''">
            and sop.is_alive = #{alive}
        </if>
        <if test="beginTime != null">
            and date_format(sop.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null">
            and date_format(sop.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        <if test="approvalUser != null and approvalUser != ''">
            and u.nick_name like concat('%',#{approvalUser},'%')
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and sop.dept_id in
                <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and sop.create_by = #{userId}
            </if>
        </if>
        )t
       <choose>
           <when test="orderColumn != null and orderColumn != ''">
               order by ${orderColumn} ${sort}
           </when>
          <otherwise>
              order by t.createTime desc
          </otherwise>
       </choose>
    </select>

    <resultMap id="detailConditionSopMap" type="com.cenker.scrm.pojo.vo.sop.ConditionSopDetailVO">
        <id column="id" property="sopId"/>
        <result column="remark" property="remark"/>
        <result column="sop_name" property="sopName"/>
        <result column="sop_type" property="sopType"/>
        <result column="start_time" property="startTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_alive" property="alive"/>
        <result column="enable_approval" property="enableApproval"/>
        <result column="approval_user" property="approvalUser"/>
        <result column="approval_user_name" property="approvalUserName"/>
        <result column="approval_remark" property="approvalRemark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="job_id" property="jobId"/>
        <result column="corp_id" property="corpId"/>
        <result column="send_condition" property="sendCondition"/>
        <result column="view_send_condition" property="viewSendCondition"/>
        <collection property="sopContentList" ofType="com.cenker.scrm.pojo.dto.sop.SopConditionContentDTO">
            <id column="contentId" property="contentId"/>
            <result column="content_version" property="contentVersion"/>
            <result column="content_sort" property="contentSort"/>
            <result column="contentStartTime" property="startTime"/>
            <result column="content_text" property="contentText"/>
            <result column="content_attachment" property="attachments" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
            <result column="del_attribute_sop_minute" property="delAttributeSopMinute"/>
            <result column="stop_task_hour" property="stopTaskHour"/>
            <result column="contentJobId" property="jobId"/>
        </collection>
    </resultMap>

    <select id="detailConditionSop" resultMap="detailConditionSopMap">
        select
               sop.id,
               sop.remark,
               sop.sop_name,
               sop.sop_type,
               sop.start_time,
               sop.create_time,
               sop.update_time,
               sop.is_alive,
               sop.send_condition,
               sop.job_id,
               sop.corp_id,
               sop.enable_approval,
               sop.approval_user,
               (select nick_name from sys_user s where s.user_id = sop.approval_user) approval_user_name,
               sop.approval_remark,
               sop.view_send_condition,
               sop.create_by,
               (select nick_name from sys_user s where s.user_id = sop.create_by) create_user_name,
               (select nick_name from sys_user s where s.user_id = sop.update_by) update_user_name,
               content.id contentId,
               content.content_version,
               content.content_sort,
               content.start_time contentStartTime,
               content.content_text,
               content.content_attachment,
               content.del_attribute_sop_minute,
               content.stop_task_hour,
               content.job_id contentJobId
        from ck_sop_info sop
        left join  ck_sop_content_info content on sop.id = content.sop_id
        where sop.id = #{sopId}
        and sop.is_deleted = 0
        and content.is_deleted = 0
        group by content.content_sign
        order by content.content_sort
    </select>


    <resultMap id="getDataStatisticsMap" type="com.cenker.scrm.pojo.vo.sop.ConditionSopDataStatisticsVO">
        <id column="id" property="sopId"/>
        <collection ofType="java.lang.Integer" property="completeTaskCnt" select="queryCompleteTaskCntBySopId"
                    column="{sopId=id}"/>
        <collection ofType="java.lang.Integer" property="totalTaskCnt" select="queryTotalTaskCntBySopId"
                    column="{sopId=id}"/>
        <collection ofType="java.lang.Integer" property="delCustomerTotalCnt" select="queryDelCustomerCntBySopId"
                    column="{sopId=id}"/>
        <collection ofType="java.lang.Integer" property="reachCustomerTotalCnt" select="querySendCustomerCntBySopId"
                    column="{sopId=id}"/>
        <collection ofType="java.lang.Integer" property="sendCustomerTotalCnt" select="queryReachCustomerCntBySopId"
                    column="{sopId=id}"/>
    </resultMap>

    <select id="getDataStatistics" resultMap="getDataStatisticsMap">
        select
               sop.id,
               '0.00%' completeTaskRate,
               '0.00%' messageReachChurnRate,
               '0.00%' customerReachChurnRate
        from ck_sop_info sop
        where sop.id = #{sopId}
    </select>

    <resultMap id="detailJourneySopMap" type="com.cenker.scrm.pojo.vo.sop.JourneySopDetailVO">
        <id column="id" property="sopId"/>
        <result column="corp_id" property="corpId"/>
        <result column="remark" property="remark"/>
        <result column="sop_name" property="sopName"/>
        <result column="sop_type" property="sopType"/>
        <result column="start_time" property="startTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_alive" property="alive"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="journey_id" property="journeyId"/>
        <result column="journey_name" property="journeyName"/>
        <result column="job_id" property="jobId"/>
        <result column="enable_approval" property="enableApproval"/>
        <result column="approval_user" property="approvalUser"/>
        <result column="approval_user_name" property="approvalUserName"/>
        <result column="approval_remark" property="approvalRemark"/>
        <result column="create_by" property="createBy"/>
        <collection property="sopStageList" column="" ofType="com.cenker.scrm.pojo.dto.sop.JourneySopStageDTO">
            <result column="stage_id" property="stageId"/>
            <result column="stage_desc" property="stageDesc"/>
            <result column="stage_name" property="stageName"/>
            <result column="view_send_condition" property="viewSendCondition"/>
            <result column="send_condition" property="sendCondition"/>
            <collection column="" property="sopContentList"  ofType="com.cenker.scrm.pojo.dto.sop.SopJourneyContentDTO">
                <id column="contentId" property="contentId"/>
                <result column="content_version" property="contentVersion"/>
                <result column="content_sort" property="contentSort"/>
                <result column="contentStartTime" property="startTime"/>
                <result column="content_text" property="contentText"/>
                <result column="content_attachment" property="attachments" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
                <result column="del_attribute_sop_minute" property="delAttributeSopMinute"/>
                <result column="stop_task_hour" property="stopTaskHour"/>
                <result column="contentJobId" property="jobId"/>
                <result column="contentStageId" property="stageId"/>
            </collection>
        </collection>
    </resultMap>


    <select id="detailJourneySop" resultMap="detailJourneySopMap">
        select
            sop.id,
            sop.corp_id,
            ifnull(sop.remark,'')remark,
            sop.sop_name,
            sop.sop_type,
            sop.is_alive,
            sop.journey_id,
            sop.job_id,
            sop.start_time,
            sop.create_time,
            sop.update_time,
            (select nick_name from sys_user s where s.user_id = sop.create_by) create_user_name,
            (select nick_name from sys_user s where s.user_id = sop.update_by) update_user_name,
            sop.enable_approval,
            sop.approval_user,
            (select nick_name from sys_user s where s.user_id = sop.approval_user) approval_user_name,
            sop.approval_remark,
            sop.create_by,
            ji.journey_name,
            js.id stage_id,
            js.stage_name,
            ifnull(js.stage_desc,'')stage_desc,
            ci.view_send_condition,
            ci.send_condition,
            content.id contentId,
            content.content_version,
            content.content_sort,
            content.start_time contentStartTime,
            content.content_text,
            content.content_attachment,
            content.del_attribute_sop_minute,
            content.stop_task_hour,
            content.job_id contentJobId,
            content.stage_id contentStageId
        from ck_sop_info sop
        join tb_wx_ext_journey_info ji on ji.id = sop.journey_id
        join tb_wx_ext_journey_stage js on js.journey_info_id = ji.id
        join ck_sop_condition_info ci on ci.sop_id = sop.id and ci.stage_id = js.id
        join ck_sop_content_info content on content.sop_id = sop.id and content.stage_id = js.id
        where sop.id = #{sopId}
          and sop.sop_type = 2
          and sop.is_deleted = 0
          and content.is_deleted = 0
          and js.del_flag=0
          order by js.order_num,content.content_sort
    </select>

    <select id="getJourneySopDataStatistics" resultType="com.cenker.scrm.pojo.vo.sop.JourneySopDataStatisticsVO">
        select
            sop.id sopId,
            (
                select
                    count(distinct mr.message_info_id ,mr.user_id)
                from tb_wx_mass_message_sender_record mr
                         join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                         join ck_sop_content_info sci on mi.scene_id = sci.id
                where sci.sop_id = sop.id
                  and (mr.send_status = 1 or mr.send_status = 3)
                  and sci.is_alive_version = 1
                  and sci.stage_id = #{stageId}
            )completeTaskCnt,
            (
                select
                    count(distinct mr.message_info_id ,mr.user_id)
                from tb_wx_mass_message_sender_record mr
                         join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                         join ck_sop_content_info sci on mi.scene_id = sci.id
                where sci.sop_id = sop.id
                  and sci.is_alive_version = 1
                  and sci.stage_id = #{stageId}
            )totalTaskCnt,
            '0.00%' completeTaskRate,
            ifnull(concat(round(((
                                     select
                                         count(distinct mr.message_info_id,mr.user_id,external_user_id)
                                     from tb_wx_mass_message_sender_record mr
                                              join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                              join ck_sop_content_info sci on mi.scene_id = sci.id
                                     where sci.sop_id = sop.id
                                       and sci.is_alive_version = 1
                                       and sci.stage_id = #{stageId}
                                       and mr.send_status = 1
                                       and (select f.del_time from tb_wx_ext_follow_user f
                                            where f.external_user_id = mr.external_user_id
                                              and f.user_id = mr.user_id and f.`status` = 2
                                              and f.del_time &gt;= mr.send_time
                                              and f.del_time &lt;= date_add(mr.send_time,interval (select del_attribute_sop_minute from ck_sop_content_info
                                                                                                   where id = sci.id) minute)
                                            order by f.del_time desc limit 1
                                     ) is not null
                                 )/
                                 (select
                                      count(distinct mr.external_user_id)
                                  from tb_wx_mass_message_sender_record mr
                                           join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                           join ck_sop_content_info sci on mi.scene_id = sci.id
                                  where sci.sop_id = sop.id
                                    and sci.stage_id = #{stageId}
                                    and mr.send_status = 1
                                    and sci.is_alive_version = 1))*100,2),'%'),'0.00%')customerReachChurnRate,

            ifnull(concat(round(((
                                     select
                                         count(distinct mr.message_info_id,mr.user_id,external_user_id)
                                     from tb_wx_mass_message_sender_record mr
                                              join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                              join ck_sop_content_info sci on mi.scene_id = sci.id
                                     where sci.sop_id = sop.id
                                       and sci.is_alive_version = 1
                                       and sci.stage_id = #{stageId}
                                       and mr.send_status = 1
                                       and (select f.del_time from tb_wx_ext_follow_user f
                                            where f.external_user_id = mr.external_user_id
                                              and f.user_id = mr.user_id and f.`status` = 2
                                              and f.del_time &gt;= mr.send_time
                                              and f.del_time &lt;= date_add(mr.send_time,interval (select del_attribute_sop_minute from ck_sop_content_info
                                                                                                   where id = sci.id) minute)
                                            order by f.del_time desc limit 1
                                     ) is not null
                                 )/
                                 (select
                                      count(1)
                                  from tb_wx_mass_message_sender_record mr
                                           join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                           join ck_sop_content_info sci on mi.scene_id = sci.id
                                  where sci.sop_id = sop.id
                                    and sci.is_alive_version = 1
                                    and sci.stage_id = #{stageId}
                                    and mr.send_status = 1))*100,2),'%'),'0.00%')messageReachChurnRate
        from ck_sop_info sop
        where sop.id = #{sopId}
    </select>

    <resultMap id="getStageListMap" type="com.cenker.scrm.pojo.vo.journey.ExternalJourneyStageVO">
        <id column="id" property="stageId"/>
        <result column="stage_name" property="stageName"/>
        <association property="journeySopDataStatistics" column="{stageId=id,sopId=sopId}" select="getJourneySopDataStatistics"/>
    </resultMap>

    <select id="getJourneySopStageList" resultMap="getStageListMap">
        select
               s.id,
               s.stage_name,
               #{sopId} sopId
        from tb_wx_ext_journey_stage s
        where s.journey_info_id = #{journeyId}
    </select>

    <resultMap id="listJourneySopMap" type="com.cenker.scrm.pojo.vo.sop.JourneySopListVO">
        <id column="id" property="sopId"/>
        <result column="sop_name" property="sopName"/>
        <result column="remark" property="remark"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="is_alive" property="alive"/>
        <result column="createTime" property="createTime"/>
        <result column="stage_cnt" property="stageCnt"/>
        <result column="enable_approval" property="enableApproval"/>
        <result column="approval_user" property="approvalUser"/>
        <result column="approval_user_name" property="approvalUserName"/>
        <result column="approval_remark" property="approvalRemark"/>
        <result column="create_by" property="createBy"/>
        <collection property="sopStageList" column="{journeyId=journey_id,sopId=id}" select="getJourneySopStageList" ofType="com.cenker.scrm.pojo.vo.journey.ExternalJourneyStageVO"/>
    </resultMap>

    <select id="listJourneySop" resultMap="listJourneySopMap">
            select
               sop.id,
               sop.sop_name,
               sop.remark,
               (select nick_name from sys_user s where s.user_id = sop.create_by) create_user_name,
               sop.is_alive,
               sop.create_time createTime,
               sop.journey_id,
                sop.enable_approval,
                sop.approval_user,
                (select nick_name from sys_user s where s.user_id = sop.approval_user) approval_user_name,
                sop.approval_remark,
                sop.create_by,
               (select count(1) from ck_sop_condition_info ci where ci.is_deleted = 0 and ci.sop_id = sop.id)stage_cnt
            from ck_sop_info sop
            where sop.is_deleted = 0
            <if test="sopType != null">
                and sop.sop_type = #{sopType}
            </if>
            <if test="sopName != null and sopName != ''">
                and sop.sop_name like concat('%',#{sopName},'%')
            </if>
            <if test="alive != null and alive != ''">
                and sop.is_alive = #{alive}
            </if>
            <if test="beginTime != null">
                and sop.create_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                and sop.create_time &lt;= #{endTime}
            </if>

            <if test="approvalUser != null and approvalUser != ''">
                and exists (select 1 from sys_user u where u.user_id = sop.approval_user and u.nick_name like concat('%',#{approvalUser},'%'))
            </if>
            <!-- 权限控制 -->
            <if test="dataScope == null or dataScope != '1'.toString()">
                <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                    and sop.dept_id in
                    <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                </if>
                <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                    and sop.create_by = #{userId}
                </if>
            </if>
            <choose>
                <when test="orderColumn != null and orderColumn != ''">
                    order by ${orderColumn} ${sort}
                </when>
                <otherwise>
                    order by sop.create_time desc
                </otherwise>
            </choose>
    </select>

    <resultMap id="detailMassCustomerSopMap" type="com.cenker.scrm.pojo.vo.sop.MassCustomerSopDetailVO">
        <id column="id" property="sopId"/>
        <result column="remark" property="remark"/>
        <result column="sop_name" property="sopName"/>
        <result column="sop_type" property="sopType"/>
        <result column="start_time" property="startTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_alive" property="alive"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="job_id" property="jobId"/>
        <result column="send_condition" property="sendCondition"/>
        <result column="view_send_condition" property="viewSendCondition"/>
        <result column="enable_approval" property="enableApproval"/>
        <result column="approval_user" property="approvalUser"/>
        <result column="approval_user_name" property="approvalUserName"/>
        <result column="approval_remark" property="approvalRemark"/>
        <result column="create_by" property="createBy"/>
        <collection property="sopContentList" ofType="com.cenker.scrm.pojo.dto.sop.SopMassCustomerContentDTO">
            <id column="contentId" property="contentId"/>
            <result column="content_version" property="contentVersion"/>
            <result column="content_sort" property="contentSort"/>
            <result column="contentStartTime" property="startTime"/>
            <result column="content_text" property="contentText"/>
            <result column="content_attachment" property="attachments" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
            <result column="del_attribute_sop_minute" property="delAttributeSopMinute"/>
            <result column="stop_task_hour" property="stopTaskHour"/>
            <result column="contentJobId" property="jobId"/>
            <result column="repeat_type" property="repeatType"/>
            <result column="repeat_expire" property="repeatExpire"/>
            <result column="end_time" property="endTime"/>
        </collection>
    </resultMap>


    <select id="detailMassCustomerSop" resultMap="detailMassCustomerSopMap">
        select
            sop.id,
            ifnull(sop.remark,'')remark,
            sop.sop_name,
            sop.sop_type,
            sop.start_time,
            sop.create_time,
            sop.update_time,
            sop.is_alive,
            sop.job_id,
            sop.enable_approval,
            sop.approval_user,
            (select nick_name from sys_user s where s.user_id = sop.approval_user) approval_user_name,
            sop.approval_remark,
            sop.create_by,
            condition_info.send_condition,
            condition_info.view_send_condition,
            (select nick_name from sys_user s where s.user_id = sop.create_by) create_user_name,
            ifnull((select nick_name from sys_user s where s.user_id = sop.update_by),'') update_user_name,
            content.id contentId,
            content.content_version,
            content.content_sort,
            content.start_time contentStartTime,
            content.end_time,
            content.content_text,
            content.content_attachment,
            content.del_attribute_sop_minute,
            content.stop_task_hour,
            content.job_id contentJobId,
            content.repeat_type,
            content.repeat_expire
        from ck_sop_info sop
                 left join  ck_sop_content_info content on sop.id = content.sop_id
                 left join ck_sop_condition_info condition_info on condition_info.sop_id = sop.id
        where sop.id = #{sopId}
          and sop.sop_type = 3
          and sop.is_deleted = 0
          and content.is_deleted = 0
        group by content.content_sign
        order by content.content_sort
    </select>
</mapper>