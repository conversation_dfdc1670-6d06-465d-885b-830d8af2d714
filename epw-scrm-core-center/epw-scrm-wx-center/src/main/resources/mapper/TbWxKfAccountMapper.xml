<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.kf.TbWxKfAccountMapper">

    <resultMap type="TbWxKfAccount" id="TbWxKfAccountResult">
        <result property="id" column="id"/>
        <result property="corpId" column="corp_id"/>
        <result property="name" column="name"/>
        <result property="headImageUrl" column="head_image_url"/>
        <result property="avatar" column="avatar"/>
        <result property="openKfId" column="open_kfid"/>
        <result property="welcomeMsg" column="welcome_msg"/>
        <result property="baseUrl" column="base_url" />
        <result property="status" column="status" />
        <result property="delFlag" column="del_flag" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="managePrivilege" column="manage_privilege" />
    </resultMap>


    <sql id="selectKfAccountVo">
        select id, corp_id, `name`, head_image_url, avatar, open_kfid,
            welcome_msg, base_url, status, del_flag, remark,
            create_by, create_time, update_by, update_time, manage_privileg
        from tb_wx_kf_account

    </sql>

    <resultMap type="KfAccountVo" id="KfAccountResult">
        <result property="id" column="id"/>
        <result property="corpId" column="corp_id"/>
        <result property="name" column="name"/>
        <result property="headImageUrl" column="head_image_url"/>
        <result property="avatar" column="avatar"/>
        <result property="openKfId" column="open_kfid"/>
        <result property="welcomeMsg" column="welcome_msg"/>
        <result property="baseUrl" column="base_url" />
        <result property="status" column="status" />
        <result property="delFlag" column="del_flag" />
        <result property="remark" column="remark" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="serviceNum" column="serviceNum" />
        <result property="managePrivilege" column="manage_privilege" />
    </resultMap>

    <select id="selectKfAccountList" parameterType="KfAccountQuery" resultMap="KfAccountResult">
        select
            id,
            corp_id,
            `name`,
            (case when head_image_url='' or head_image_url=null then avatar
            else head_image_url
            end) head_image_url,
            (case when avatar='' or avatar=null then head_image_url
            else avatar
            end) avatar,
            open_kf_id,
            welcome_msg,
            base_url,
            status,
            del_flag,
            remark,
            (case when create_by = 'SYS' then '系统'
                else (select user_name from sys_user u where u.user_id=a.create_by)
            end) create_by,
            create_time,
            update_by,
            update_time,
            manage_privilege,
            (select count(1) from tb_wx_kf_services s where s.kf_id=a.id) serviceNum
        from tb_wx_kf_account a
        where
            del_flag = 0
            <if test="name != null  and name != ''">
                and `name` like concat('%', #{name}, '%')
            </if>
            <if test="status != null ">
                and status = #{status}
            </if>
            <if test="corpId != null  and corpId != ''">
                and corp_id = #{corpId}
            </if>
        order by create_time desc
    </select>

    <select id="selectKfAccountById" parameterType="java.lang.Long" resultMap="KfAccountResult">
        select
            a.id,
            a.corp_id,
            a.`name`,
            (case when a.head_image_url='' or a.head_image_url=null then a.avatar
                else a.head_image_url
            end) head_image_url,
            (case when a.avatar='' or a.avatar=null then a.head_image_url
                else a.avatar
            end) avatar,
            a.open_kf_id,
            a.welcome_msg,
            a.base_url,
            a.status,
            a.del_flag,
            a.remark,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.manage_privilege
        from tb_wx_kf_account a
        where id = #{id}
    </select>

    <select id="selectKfAccountByOpenKfId" parameterType="java.lang.String" resultMap="KfAccountResult">
        select
            a.id,
            a.corp_id,
            a.`name`,
            (case when a.head_image_url='' or a.head_image_url=null then a.avatar
                  else a.head_image_url
                end) head_image_url,
            (case when a.avatar='' or a.avatar=null then a.head_image_url
                  else a.avatar
                end) avatar,
            a.open_kf_id,
            a.welcome_msg,
            a.base_url,
            a.status,
            a.del_flag,
            a.remark,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.manage_privilege
        from tb_wx_kf_account a
        where open_kf_id = #{openKfId}
    </select>

    <select id="selectKfServiceById" resultType="UserConditionDTO">
        select
           s.id,
           u.`name` userName,
           u.userid userId,
           u.del_flag delFalg,
           u.avatar userAvatar
        from tb_wx_kf_account a
        left join tb_wx_kf_services s on s.kf_id = a.id
        left join tb_wx_user u on u.userid = s.user_id and u.corp_id = a.corp_id
        where a.id = #{id} and s.del_flag = 0
    </select>

</mapper>