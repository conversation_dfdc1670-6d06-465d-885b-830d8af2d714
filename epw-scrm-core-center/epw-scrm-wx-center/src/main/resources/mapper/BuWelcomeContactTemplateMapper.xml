<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.content.material.BuWelcomeContactTemplateMapper">
    
    <resultMap type="com.cenker.scrm.pojo.vo.content.material.BuWelcomeContactTemplateVO" id="BuWelcomeContactTemplateResult">
        <result property="id"    column="id"    />
        <result property="corpId"    column="corpId"    />
        <result property="name"    column="name"    />
        <result property="content"    column="content"    />
        <result property="attachments" column="attachments"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result property="delFlag"    column="delFlag"    />
        <result property="status"    column="status"    />
        <result property="createByName"    column="createByName"    />
        <result property="createTime"    column="createTime"    />
        <result property="updateByName"    column="updateByName"    />
        <result property="updateTime"    column="updateTime"    />
    </resultMap>

    <select id="selectBuWelcomeContactTemplateList" resultMap="BuWelcomeContactTemplateResult">
        select
            t.id,
            t.corp_id corpId,
            t.name,
            t.content,
            t.attachments,
            t.del_flag delFlag,
            t.status,
            c.user_name  createByName,
            t.create_time createTime,
            u.user_name updateByName,
            t.update_time updateTime
        from
            bu_welcome_contact_template t
        left join sys_user c on c.user_id  = t.create_by
        left join sys_user u on u.user_id  = t.update_by
        where t.del_flag = 0
            <if test="param.name != null  and param.name != ''"> and t.name like concat('%', #{param.name}, '%')</if>
            <if test="param.status != null "> and t.status = #{param.status}</if>
            <if test="param.creator != null "> and c.user_name like concat('%', #{param.creator}, '%')</if>
            <!-- 权限控制 -->
            <if test="param.dataScope == null or param.dataScope != '1'.toString()">
                <if test="param.permissionDeptIds != null and param.permissionDeptIds.size() > 0">
                    and t.dept_id in
                    <foreach item="deptId" collection="param.permissionDeptIds" open="(" separator="," close=")">
                        #{param.deptId}
                    </foreach>
                </if>
                <if test="param.permissionDeptIds == null or param.permissionDeptIds.size() == 0">
                    and t.create_by = #{param.userId}
                </if>
            </if>
        order by t.status, t.create_time desc
    </select>

    <select id="selectBuWelcomeContactTemplateById" resultMap="BuWelcomeContactTemplateResult">
        select
            t.id,
            t.corp_id corpId,
            t.name,
            t.content,
            t.attachments,
            t.del_flag delFlag,
            t.status,
            c.user_name  createByName,
            t.create_time createTime,
            u.user_name updateByName,
            t.update_time updateTime
        from
            bu_welcome_contact_template t
        left join sys_user c on c.user_id  = t.create_by
        left join sys_user u on u.user_id  = t.update_by
        where t.del_flag = 0 and t.id = #{param.id}
        <!-- 权限控制 -->
        <if test="param.dataScope == null or param.dataScope != '1'.toString()">
            <if test="param.permissionDeptIds != null and param.permissionDeptIds.size() > 0">
                and t.dept_id in
                <foreach item="deptId" collection="param.permissionDeptIds" open="(" separator="," close=")">
                    #{param.deptId}
                </foreach>
            </if>
            <if test="param.permissionDeptIds == null or param.permissionDeptIds.size() == 0">
                and t.create_by = #{param.userId}
            </if>
        </if>
    </select>

    <select id="getUsedCountById" resultType="java.lang.Integer">
        select
            sum(counts) total_counts
        from
        (
            select count(1) counts from tb_wx_user u where u.status=1 and  u.wel_tpl_id =#{id}
            union all
            select count(1) counts from tb_wx_custlink k where k.status=1 and k.wel_tpl_id = #{id}
            union all
            select count(1) counts from tb_wx_contact c where c.use_status = 1 and c.wel_tpl_id = #{id}
        )t
    </select>

    <select id="selectUsedSceneList" resultType="com.cenker.scrm.pojo.vo.content.material.BuWelcomeTemplateSenceVO">
        select
            *
        from (
            select
                concat('个人名片：【', u.name,'】') sceneName,
                '权限管理' moduleName,
                wct.status useStatus,
                u.create_time
            from
                bu_welcome_contact_template wct
            join tb_wx_user u on u.wel_tpl_id = wct.id
            where
                u.status = 1
                and wct.id = #{param.id}
                <if test="param.name != null  and param.name != ''">
                    and u.name like concat('%', #{param.name}, '%')
                </if>

            union all
            select
                concat('获客链接：【', k.link_name,'】') sceneName,
                '获客链接' moduleName,
                wct.status useStatus,
                k.create_time
            from
                bu_welcome_contact_template wct
            join tb_wx_custlink k on k.wel_tpl_id = wct.id
            where
                k.status = 1
                and wct.id =  #{param.id}
                <if test="param.name != null  and param.name != ''">
                    and k.link_name like concat('%', #{param.name}, '%')
                </if>

            union all
            select
                concat('渠道活码：【', c.remark ,'】') sceneName,
                '渠道活码' moduleName,
                wct.status useStatus,
                c.create_time
            from
                bu_welcome_contact_template wct
            join tb_wx_contact c on c.wel_tpl_id = wct.id
            where
                c.use_status = 1
                and wct.id = #{param.id}
                <if test="param.name != null  and param.name != ''">
                    and c.remark like concat('%', #{param.name}, '%')
                </if>
        )d
        order by d.create_time desc
    </select>
</mapper>