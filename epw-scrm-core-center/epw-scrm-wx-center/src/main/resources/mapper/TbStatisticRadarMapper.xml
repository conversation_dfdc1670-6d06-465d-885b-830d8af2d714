<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cenker.scrm.mapper.statistic.TbStatisticRadarMapper">
    <insert id="saveStatisticDateByDay">
        INSERT INTO tb_statistic_radar (statistic_date, radar_id, radar_type, cover, digest, radar_name, title, first_category, second_category, send_times, click_times, forward_times, average_read_time, new_flag, create_time, create_by, dept_id)
        SELECT
               str_to_date(#{statDate}, '%Y-%m-%d') AS statistic_date,
               i.id AS radar_id,
               i.type AS radar_type,
               c.cover AS cover,
               c.digest AS digest,
               i.title AS radar_name,
               c.title AS title,
               NULL AS first_category,
               NULL AS second_category,
               NULL AS send_times,
               count(DISTINCT r.id) AS click_times,
               IFNULL(sum(r.forward_num), 0) AS forward_times,
               FLOOR(IFNULL(AVG(r.read_time), 0)) AS average_read_time,
               IF(date(i.create_time) = str_to_date(#{statDate}, '%Y-%m-%d'), 1, 0) AS new_flag,
               i.create_time AS create_time,
               i.create_by,
               i.dept_id
        FROM tb_wx_radar_interact i
        left join `tb_wx_radar_content` c on c.radar_id = i.id
        left join tb_wx_radar_content_record r ON r.content_id = c.id and date(r.create_time) = str_to_date(#{statDate}, '%Y-%m-%d')
        WHERE date(i.create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        and (i.del_flag = 0 or (i.del_flag = 1 and i.update_time >= str_to_date(#{statDate}, '%Y-%m-%d')))
        GROUP BY i.id;
    </insert>

    <select id="summary" resultType="StatisticRadarSummaryVo">
        SELECT
          COUNT(DISTINCT radar_id) AS radarTotal,
          IFNULL(SUM(send_times), 0) AS sendTimesTotal,
          IFNULL(SUM(click_times), 0) AS clickTimesTotal,
          IFNULL(SUM(forward_times), 0) AS forwardTimesTotal,
          IFNULL(SUM(CASE WHEN new_flag = 1 THEN 1 ELSE NULL END), 0) AS newTotal
        FROM
		    tb_statistic_radar
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
            <!-- 权限控制 -->
            <if test="dataScope == null or dataScope != '1'.toString()">
                <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                    and dept_id in
                    <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                    and create_by = #{userId} and dept_id = #{deptId}
                </if>
            </if>
    </select>

  <select id="graph" resultType="StatisticRadarSummaryVo">
        SELECT
            statistic_date AS statisticDate,
            COUNT(DISTINCT radar_id) AS radarTotal,
            IFNULL(SUM(send_times), 0) AS sendTimesTotal,
            IFNULL(SUM(click_times), 0) AS clickTimesTotal,
            IFNULL(SUM(forward_times), 0) AS forwardTimesTotal,
            IFNULL(SUM(CASE WHEN new_flag = 1 THEN 1 ELSE NULL END), 0) AS newTotal
        FROM
		    tb_statistic_radar
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
          <!-- 权限控制 -->
          <if test="dataScope == null or dataScope != '1'.toString()">
              <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                  and dept_id in
                  <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                      #{item}
                  </foreach>
              </if>
              <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                  and create_by = #{userId} and dept_id = #{deptId}
              </if>
          </if>
            GROUP BY statistic_date
		    ORDER BY statistic_date
    </select>

</mapper>
