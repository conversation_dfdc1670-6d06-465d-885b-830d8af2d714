<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.external.TbWxExtFollowUserMapper">

    <resultMap type="com.cenker.scrm.pojo.entity.wechat.TbWxExtFollowUser" id="TbWxExtFollowUserResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="remark" column="remark"/>
        <result property="description" column="description"/>
        <result property="createTime" column="create_time"/>
        <result property="remarkCorpName" column="remark_corp_name"/>
        <result property="remarkMobiles" column="remark_mobiles"/>
        <result property="operatorUserId" column="oper_userid"/>
        <result property="addWay" column="add_way"/>
        <result property="state" column="state"/>
        <result property="externalUserId" column="external_user_id"/>
        <result property="status" column="status"/>
        <result property="corpId" column="corp_id"/>
        <result property="delTime" column="del_time"/>
    </resultMap>

    <sql id="selectTbWxExtFollowUserVo">
        select id,
               user_id,
               remark,
               description,
               create_time,
               remark_corp_name,
               remark_mobiles,
               oper_userid,
               add_way,
               state,
               external_userid,
               status,
               corp_id,
               del_time
        from tb_wx_ext_follow_user
    </sql>

    <select id="selectTbWxExtFollowUserList" parameterType="TbWxExtFollowUser" resultMap="TbWxExtFollowUserResult">
        <include refid="selectTbWxExtFollowUserVo"/>
        <where>
            <if test="userId != null  and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="description != null  and description != ''">
                and description = #{description}
            </if>
            <if test="remarkCorpName != null  and remarkCorpName != ''">
                and remark_corp_name like concat('%', #{remarkCorpName}, '%')
            </if>
            <if test="remarkMobiles != null  and remarkMobiles != ''">
                and remark_mobiles = #{remarkMobiles}
            </if>
            <if test="operUserid != null  and operUserid != ''">
                and oper_userid = #{operUserid}
            </if>
            <if test="addWay != null  and addWay != ''">
                and add_way = #{addWay}
            </if>
            <if test="state != null  and state != ''">
                and state = #{state}
            </if>
            <if test="externalUserid != null  and externalUserid != ''">
                and external_userid = #{externalUserid}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
            <if test="corpId != null  and corpId != ''">
                and corp_id = #{corpId}
            </if>
            <if test="delTime != null ">
                and del_time = #{delTime}
            </if>
        </where>
    </select>

    <select id="selectTbWxExtFollowUserById" parameterType="Long" resultMap="TbWxExtFollowUserResult">
        <include refid="selectTbWxExtFollowUserVo"/>
        where id = #{id}
    </select>

    <insert id="insertTbWxExtFollowUser" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxExtFollowUser">
        insert into tb_wx_ext_follow_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="userId != null">user_id,
            </if>
            <if test="remark != null">remark,
            </if>
            <if test="description != null">description,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="remarkCorpName != null">remark_corp_name,
            </if>
            <if test="remarkMobiles != null">remark_mobiles,
            </if>
            <if test="operUserid != null">oper_userid,
            </if>
            <if test="addWay != null">add_way,
            </if>
            <if test="state != null">state,
            </if>
            <if test="externalUserid != null">external_userid,
            </if>
            <if test="status != null">status,
            </if>
            <if test="corpId != null">corp_id,
            </if>
            <if test="delTime != null">del_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="userId != null">#{userId},
            </if>
            <if test="remark != null">#{remark},
            </if>
            <if test="description != null">#{description},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="remarkCorpName != null">#{remarkCorpName},
            </if>
            <if test="remarkMobiles != null">#{remarkMobiles},
            </if>
            <if test="operUserid != null">#{operUserid},
            </if>
            <if test="addWay != null">#{addWay},
            </if>
            <if test="state != null">#{state},
            </if>
            <if test="externalUserid != null">#{externalUserid},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="corpId != null">#{corpId},
            </if>
            <if test="delTime != null">#{delTime},
            </if>
        </trim>
    </insert>

    <update id="updateTbWxExtFollowUser" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxExtFollowUser">
        update tb_wx_ext_follow_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id =
                #{userId},
            </if>
            <if test="remark != null">remark =
                #{remark},
            </if>
            <if test="description != null">description =
                #{description},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="remarkCorpName != null">remark_corp_name =
                #{remarkCorpName},
            </if>
            <if test="remarkMobiles != null">remark_mobiles =
                #{remarkMobiles},
            </if>
            <if test="operUserid != null">oper_userid =
                #{operUserid},
            </if>
            <if test="addWay != null">add_way =
                #{addWay},
            </if>
            <if test="state != null">state =
                #{state},
            </if>
            <if test="externalUserid != null">external_userid =
                #{externalUserid},
            </if>
            <if test="status != null">status =
                #{status},
            </if>
            <if test="corpId != null">corp_id =
                #{corpId},
            </if>
            <if test="delTime != null">del_time =
                #{delTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbWxExtFollowUserById" parameterType="Long">
        delete
        from tb_wx_ext_follow_user
        where id = #{id}
    </delete>

    <delete id="deleteTbWxExtFollowUserByIds" parameterType="String">
        delete from tb_wx_ext_follow_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryUserIdByCorpIdAndExtUserId" parameterType="map" resultType="com.cenker.scrm.pojo.vo.external.CustomerFollowVO">
        select user_id userId,external_user_id extUserId,id from tb_wx_ext_follow_user where corp_id = #{corpId}
        -- and status = '0'
        and (external_user_id) in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getUserAddCustomerStat" resultType="java.util.HashMap">
        select count(t.external_user_id) num, t.create_time createTime
        from (
                 select external_user_id,
                        left(create_time, 10) create_time
                 from tb_wx_ext_follow_user
                 where state = #{state}
                   and add_way = '1'
                 GROUP BY external_user_id
                 ORDER BY create_time desc
             ) t
        where t.create_time &gt;= #{startTime}
          and t.create_time &lt;= #{endTime}
        GROUP BY left(t.create_time, 10)
    </select>

    <select id="getCustomerByName" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxExtFollowUser">
        select u.external_user_id,
               c.`name`
        from tb_wx_ext_follow_user u
                 left join tb_wx_ext_customer c on u.external_user_id = c.external_user_id and u.corp_id = c.corp_id
        where u.corp_id = #{corpId}
          and u.user_id = #{staffId}
          and u.`status` = 0
          and c.name = #{nickName}
        group by u.external_user_id
    </select>

    <select id="selectFissionRecordByAddRecordUser" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer">
        select c.external_user_id,
               c.name,
               c.avatar,
               c.type,
               c.gender,
               c.union_id,
               c.birthday,
               c.corp_name,
               c.corp_full_name,
               c.position,
               c.status,
               c.corp_id,
               u.create_time create_time,
               c.create_by,
               c.remark,
               c.source,
               c.tag,
               c.first_add_user_id,
               c.mobiles
        from tb_wx_ext_customer c
                 left join tb_wx_ext_follow_user u on u.external_user_id = c.external_user_id
        where u.state = #{fissionTag}
          and u.corp_id = #{corpId}
          and u.`status` = 0
        group by u.external_user_id
    </select>

    <select id="getCustomerListByWork" resultType="CustomerFollowUserVo">
        select
        u.remark name,
        c.avatar,
        c.external_user_id,
        c.type,
        (
        case when c.type = 1 then '@微信'
        when c.type = 2 then concat('@',c.corp_name)
        else '@企业微信' end
        )add_type,
        (case when u.add_way = 1 then ifnull((select remark from tb_wx_contact where state = u.state limit 1),'扫描二维码')
        when u.add_way = 2 then '搜索手机号'
        when u.add_way = 3 then '名片分享'
        when u.add_way = 4 then '群聊'
        when u.add_way = 5 then '手机通讯录'
        when u.add_way = 6 then '微信联系人'
        when u.add_way = 7 then '来自微信的添加好友申请'
        when u.add_way = 8 then '安装应用时自动添加'
        when u.add_way = 9 then '搜索邮箱'
        when u.add_way = 10 then '视频号添加'
        when u.add_way = 11 then '通过日程参与人添加'
        when u.add_way = 12 then '通过会议参与人添加'
        when u.add_way = 13 then '添加微信好友对应的企业微信'
        when u.add_way = 14 then '通过智慧硬件专属客服添加'
        when u.add_way = 15 then '通过上门服务客服添加'
        when u.add_way = 16 then '通过获客链接添加'
        when u.add_way = 17 then '通过定制开发添加'
        when u.add_way = 18 then '通过需求回复添加'
        when u.add_way = 21 then '通过第三方售前客服添加'
        when u.add_way = 22 then '通过可能的商务伙伴添加'
        when u.add_way = 24 then '通过接受微信账号收到的好友申请添加'
        when u.add_way = 201 then '内部成员共享'
        when u.add_way = 202 then '管理员/负责人分配'
        else '未知来源' end)add_way,
        (
        select group_concat(wu.`name` order by wu.del_flag separator '、') from tb_wx_user wu where wu.userid in (
        select distinct uu.user_id from tb_wx_ext_follow_user uu
        where uu.external_user_id = u.external_user_id
        and uu.corp_id = u.corp_id
        -- and uu.`status` = 0
        ) and wu.corp_id = u.corp_id
        ) add_user,
        (
        select group_concat(t.`name`) from tb_wx_corp_tag t where t.tag_id in(
        select distinct ut.tag_id from tb_wx_ext_follow_user_tag ut
        where ut.corp_id = u.corp_id
        and ut.external_user_id = u.external_user_id
        and ut.user_id = u.user_id
        )
        )tag,
        (
        select `status` from tb_wx_ext_follow_user
        where external_user_id = c.external_user_id
        and user_id = u.user_id
        order by `status`
        limit 1
        ) `status`
        from
        tb_wx_ext_follow_user u
        left join tb_wx_ext_customer c on u.external_user_id = c.external_user_id and u.corp_id = c.corp_id
        where u.corp_id = #{corpId}
        and u.user_id = #{userId}
        and u.`status` in ('0', '2')
        and c.status = '0'
        <if test="name != null and name != ''">
            and (u.remark like concat('%',#{name},'%') or c.name like concat('%',#{name},'%'))
        </if>
        group by u.external_user_id
        order by u.create_time desc
    </select>

    <select id="listUser4AddExternalCustomerExcludeUserId" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxUser">
        select wu.userid,
               wu.avatar
        from tb_wx_ext_follow_user u
                 left join tb_wx_user wu on wu.corp_id = u.corp_id and wu.userid = u.user_id
        where u.corp_id = #{corpId}
          and u.external_user_id = #{externalUserId}
          and wu.userid != #{userId}
          and u.`status` = 0
          and wu.del_flag = 1
        group by wu.userid
    </select>

    <select id="selectAllUserAndExternalUser" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxExtFollowUser">
        select f.id,
               f.user_id,
               f.external_user_id
        from tb_wx_ext_follow_user f
                 join tb_wx_user u on u.userid = f.user_id and u.corp_id = f.corp_id and u.status = 1
        where f.corp_id = #{corpId}
          and u.del_flag = 1
          and f.status != 3
    </select>

    <select id="selectExternalUserIdsByCondition" resultType="String">
        select
        distinct(u.external_user_id)
        from tb_wx_ext_follow_user u
        join tb_wx_user u1 on u1.userid = u.user_id and u1.corp_id = u.corp_id and u1.status = 1
        LEFT JOIN tb_wx_ext_customer c ON c.external_user_id = u.external_user_id
        LEFT JOIN tb_wx_ext_journey_customer_stage cs ON cs.ext_customer_id = c.id
        where u.corp_id = #{corpId}
        and u1.del_flag = 1
        <if test="userConditionList != null and userConditionList.size > 0">
            and u.user_id in
            <foreach item="item" collection="userConditionList" open="(" separator="," close=")">
                #{item.userId}
            </foreach>
        </if>
        <if test="tagConditionList != null and tagConditionList.size > 0">
            and u.external_user_id  in
            (select external_user_id from tb_wx_ext_follow_user_tag where type = 1 and tag_id  in
            <foreach item="item" collection="tagConditionList" open="(" separator="," close=")">
                #{item.tagId}
            </foreach>  )
        </if>
        <if test="stageConditionList != null and stageConditionList.size > 0">
            and cs.stage_id in
            <foreach item="item" collection="stageConditionList" open="(" separator="," close=")">
                #{item.stageId}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and date_format(u.create_time,'%Y-%m-%d') >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(u.create_time,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        and u.`status` != 3
    </select>

    <resultMap id="selectAllAddUserByExternalUserIdsMap" type="com.cenker.scrm.pojo.vo.external.UserExtCustomerListVO">
        <result property="userId" column="user_id"/>
        <collection property="extCustomerInfoList" ofType="com.cenker.scrm.pojo.vo.external.ExtCustomerInfoVO">
            <result property="externalUserId" column="external_user_id"/>
        </collection>
    </resultMap>

    <select id="selectAllAddUserByExternalUserIds" resultMap="selectAllAddUserByExternalUserIdsMap">
        select
            u.user_id,
            u.external_user_id
        from tb_wx_ext_follow_user u
        join tb_wx_user u1 on u1.userid = u.user_id and u1.corp_id = u.corp_id and u1.status = 1
        where u.corp_id = #{corpId}
        and u.`status` != 3
        and u1.del_flag = 1
        and u.external_user_id in
        <foreach item="item" collection="externalList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="ge4AddUserCount" resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        select
            (select c.id from tb_wx_ext_customer c where c.external_user_id = f.external_user_id limit 1)id,
            f.external_user_id
        from tb_wx_ext_follow_user f
        where f.`status` = 0
        and f.external_user_id in
        <foreach item="item" collection="triggerConditionValue.customerList" open="(" separator="," close=")">
            #{item.externalUserId}
        </foreach>
        group by f.external_user_id
        having count(f.user_id) >= #{triggerConditionValue.conditionCnt}
    </select>
    <select id="assignSelectAddUser4Sop"
            resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        select
        (select c.id from tb_wx_ext_customer c where c.external_user_id = f.external_user_id limit 1)id,
        f.external_user_id
        from tb_wx_ext_follow_user f
        where f.`status` = 0
        <if test="triggerRelationType == 1">
            and f.user_id = #{triggerConditionValue.userConditionList[0].userId}
        </if>
        <if test="triggerRelationType == 2">
            and f.external_user_id not in(
               select
               f.external_user_id
               from tb_wx_ext_follow_user f
               where f.`status` = 0
               and f.user_id = #{triggerConditionValue.userConditionList[0].userId}
            )
        </if>
        <if test="triggerRelationType == 3">
            and f.user_id in
            <foreach item="item" collection="triggerConditionValue.userConditionList" open="(" separator="," close=")">
                #{item.userId}
            </foreach>
        </if>
        <if test="triggerRelationType == 4">
            and f.external_user_id not in(
            select
            f.external_user_id
            from tb_wx_ext_follow_user f
            where f.`status` = 0
            and f.user_id not in
            <foreach item="item" collection="triggerConditionValue.userConditionList" open="(" separator="," close=")">
                #{item.userId}
            </foreach>
            )
        </if>
        and f.external_user_id in
        <foreach item="item" collection="triggerConditionValue.customerList" open="(" separator="," close=")">
            #{item.externalUserId}
        </foreach>
        group by f.external_user_id
    </select>

    <select id="statLostCustomerData" resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        select
            date_format(del_time, '%Y-%m-%d') day,
            count(external_user_id) `count`
        from tb_wx_ext_follow_user
        where corp_id = #{corpId}
        <if test="startTime != null">
            and del_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and del_time &lt;= #{endTime}
        </if>
        group by day
    </select>

    <select id="statNewCustomerData" resultType="com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO">
        select
        date_format(create_time, '%Y-%m-%d') day,
        count(external_user_id) `count`
        from tb_wx_ext_follow_user
        where corp_id = #{corpId}
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        group by day
    </select>

    <select id="getCustomerByUnionId" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer">
        select c.external_user_id,
            c.name,
            c.avatar,
            c.type,
            c.gender,
            c.union_id,
            c.birthday,
            c.corp_name,
            c.corp_full_name,
            c.position,
            c.status,
            c.corp_id,
            u.create_time create_time,
            c.create_by,
            c.remark,
            c.source,
            c.tag,
            c.first_add_user_id,
            c.mobiles
        from tb_wx_ext_customer c
            join tb_wx_ext_follow_user u on u.external_user_id = c.external_user_id and u.corp_id = c.corp_id
        where u.corp_id = #{corpId}
          and u.user_id = #{staffId}
          and u.`status` = 0
          and c.status = '0'
          and c.union_id = #{unionId}
        limit 1
    </select>

    <select id="selectNormalFollowUser" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxExtFollowUser">
        select
            efu.*
        from tb_wx_ext_follow_user efu
        join tb_wx_user u on u.userid = efu.user_id and u.corp_id = efu.corp_id
        where u.status = 1 AND u.del_flag = 1
          AND efu.status = 0
        and efu.external_user_id in
        <foreach item="item" collection="externalList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>