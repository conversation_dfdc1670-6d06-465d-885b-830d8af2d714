<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.message.TbWxCorpMassMessageInfoMapper">
    <resultMap type="TbWxCorpMassMessageInfo" id="TbWxCorpMassMessageInfoResult">
        <result property="id" column="id"/>
        <result property="chatType" column="chat_type"/>
        <result property="checkStatus" column="check_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="settingTime" column="setting_time"/>
        <result property="expectSend" column="expect_send"/>
        <result property="actualSend" column="actual_send"/>
        <result property="timedTask" column="timed_task"/>
        <result property="corpId" column="corp_id"/>
    </resultMap>

    <sql id="selectTbWxCorpMassMessageInfoVo">
        select id,
               chat_type,
               check_status,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag,
               setting_time,
               expect_send,
               actual_send,
               timed_task,
               corp_id
        from tb_wx_corp_mass_message_info
    </sql>

    <select id="selectTbWxCorpMassMessageInfoList" parameterType="TbWxCorpMassMessageInfo"
            resultMap="TbWxCorpMassMessageInfoResult">
        <include refid="selectTbWxCorpMassMessageInfoVo"/>
        <where>
            <if test="chatType != null  and chatType != ''">
                and chat_type = #{chatType}
            </if>
            <if test="checkStatus != null  and checkStatus != ''">
                and check_status = #{checkStatus}
            </if>
            <if test="settingTime != null ">
                and setting_time = #{settingTime}
            </if>
            <if test="expectSend != null ">
                and expect_send = #{expectSend}
            </if>
            <if test="actualSend != null ">
                and actual_send = #{actualSend}
            </if>
            <if test="timedTask != null ">
                and timed_task = #{timedTask}
            </if>
            <if test="corpId != null  and corpId != ''">
                and corp_id = #{corpId}
            </if>
        </where>
    </select>

    <select id="selectTbWxCorpMassMessageInfoById" parameterType="Long"
            resultMap="TbWxCorpMassMessageInfoResult">
        <include refid="selectTbWxCorpMassMessageInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertTbWxCorpMassMessageInfo" parameterType="TbWxCorpMassMessageInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into tb_wx_corp_mass_message_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="targetSelectedStr != null">target_selected_str,
            </if>
            <if test="chatType != null">chat_type,
            </if>
            <if test="checkStatus != null">check_status,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
            <if test="settingTime != null">setting_time,
            </if>
            <if test="expectSend != null">expect_send,
            </if>
            <if test="actualSend != null">actual_send,
            </if>
            <if test="timedTask != null">timed_task,
            </if>
            <if test="corpId != null and corpId != ''">corp_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="targetSelectedStr != null">#{targetSelectedStr},
            </if>
            <if test="chatType != null">#{chatType},
            </if>
            <if test="checkStatus != null">#{checkStatus},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
            <if test="settingTime != null">#{settingTime},
            </if>
            <if test="expectSend != null">#{expectSend},
            </if>
            <if test="actualSend != null">#{actualSend},
            </if>
            <if test="timedTask != null">#{timedTask},
            </if>
            <if test="corpId != null and corpId != ''">#{corpId},
            </if>
        </trim>
    </insert>

    <update id="updateTbWxCorpMassMessageInfo" parameterType="TbWxCorpMassMessageInfo">
        update tb_wx_corp_mass_message_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatType != null">chat_type =
                #{chatType},
            </if>
            <if test="checkStatus != null">check_status =
                #{checkStatus},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="delFlag != null">del_flag =
                #{delFlag},
            </if>
            <if test="settingTime != null">setting_time =
                #{settingTime},
            </if>
            <if test="expectSend != null">expect_send =
                #{expectSend},
            </if>
            <if test="actualSend != null">actual_send =
                #{actualSend},
            </if>
            <if test="timedTask != null">timed_task =
                #{timedTask},
            </if>
            <if test="corpId != null and corpId != ''">corp_id =
                #{corpId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbWxCorpMassMessageInfoById" parameterType="Long">
        delete
        from tb_wx_corp_mass_message_info
        where id = #{id}
    </delete>

    <delete id="deleteTbWxCorpMassMessageInfoByIds" parameterType="String">
        delete from tb_wx_corp_mass_message_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="queryMassMessageInfo" resultType="com.cenker.scrm.pojo.vo.message.MassMessageInfoVO">
        select t.chat_type chatType,t.check_status checkStatus,u.nick_name createBy,t.create_time createTime,
        t.setting_time settingTime,t.actual_send actualSend,t.expect_send expectSend,t1.content,t.id,t1.id detailId,
        case when t1.media_id is not null then '1'
        when t1.mini_program_media_id is not null then '3'
        when t1.link_url is not null then '2'
        else 0
        end type,t.target_selected_str targetSelectedStr,t1.pic_url picUrl,t1.media_id media
        from tb_wx_corp_mass_message_info t
        join tb_wx_corp_mass_message_detail t1 on t.id = t1.message_info_id
        left join sys_user u on t.create_by = u.user_id and t.corp_id = u.corp_id
        where t.del_flag = 0
        and t.corp_id =#{corpId}
        <if test=" chatType!= null  and chatType != ''">
            and t.chat_type = #{chatType}
        </if>
        <if test=" content!= null  and content != ''">
            and t1.content = #{content}
        </if>
        <if test="beginTime != null and beginTime != '' ">
            and date_format(t.create_time, '%Y-%m-%d') >= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(t.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test=" massType!= null  and massType != ''">
            and t.mass_type = #{massType}
        </if>
        <if test=" id!= null  and id != ''">
            and t.id = #{id}
        </if>
        order by t.create_time desc
    </select>



    <select id="queryExtUserIdByCondition" resultType="MassWxSenderVO" parameterType="map">
        select external_user_id senderId,user_id userId
        from tb_wx_ext_follow_user
        where corp_id = #{corpId}
        and `status` ='0'
        <if test="externalUserId != null  and externalUserId.size() != 0">
            and external_user_id in
            <foreach item="item" collection="externalUserId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and user_id in (
        select userid from tb_wx_user t
        where t.corp_id = #{corpId} and t.del_flag = '1'
        and ( 1 = 1
        <if test="userIds != null  and userIds.size() != 0">
            and t.userid in
            <foreach item="item" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds != null  and deptIds.size() != 0">
            -- 存在指定员工和部门使用或者 否则共同规则会查询错误
            <choose>
                <when test="userIds != null  and userIds.size() != 0">
                    or t.main_department in
                    <foreach item="item" collection="deptIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and t.main_department in
                    <foreach item="item" collection="deptIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </otherwise>
            </choose>
        </if>
        ))
        <choose>
            <when test="tagIds != null  and tagIds.size() != 0 and noTagIds != null and noTagIds.size() > 0 ">
                and external_user_id in ( select external_user_id from tb_wx_ext_follow_user_tag where corp_id =
                #{corpId}
                and tag_id in
                <foreach item="item" collection="tagIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and tag_id not in
                <foreach item="item" collection="noTagIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </when>
            <otherwise>
                <if test="tagIds != null  and tagIds.size() != 0 ">
                    and external_user_id in (select external_user_id from tb_wx_ext_follow_user_tag where corp_id =
                    #{corpId}
                    and tag_id in
                    <foreach item="item" collection="tagIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    -- 限定用户id 否则会出现不同人都有同一客户 但标签不一样导致都发送的情况
                    <if test="userIds != null  and userIds.size() != 0">
                        and user_id in
                        <foreach item="item" collection="userIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    )
                </if>
                <if test="noTagIds != null  and noTagIds.size() != 0 ">
                    and external_user_id in (select external_user_id from tb_wx_ext_follow_user_tag where corp_id =
                    #{corpId}
                    and tag_id not in
                    <foreach item="item" collection="noTagIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    union ALL
                    select external_user_id from tb_wx_ext_customer where corp_id = #{corpId} and `status` ='0' and tag
                    is NULL
                    )
                </if>
            </otherwise>
        </choose>

    </select>
    <select id="queryChatIdsByCorpInfo" resultType="MassWxSenderVO" parameterType="map">
        select chat_id senderId ,owner userId from tb_wx_customer_group t where t.`owner` in (
        select userid from tb_wx_user where del_flag ='1' and corp_id = #{corpId}
        and ( 1 = 1
        <if test="userIds != null  and userIds.size() != 0">
            and userid in
            <foreach item="item" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptIds != null  and deptIds.size() != 0">
            and main_department in
            <foreach item="item" collection="deptIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ))
        and t.dismiss_date is null and t.corp_id =#{corpId}
    </select>
    <select id="queryMassMessageSenderCustDetail" resultType="MassMessageSenderVO">
        select min(id) minId,max(id) maxId,max(user_id) userId
        ,(select max(name) from tb_wx_user where userid = t.user_id) userName from tb_wx_corp_mass_message_log t
        where
        t.corp_id = #{corpId} and t.del_flag ='0' and t.send_type = 'single'
        <if test="checkStatus != null  and checkStatus != '' and checkStatus !=0 ">
            and t.status != '0'
        </if>
        <if test="checkStatus != null  and checkStatus ==0 ">
            and t.status = '0'
        </if>
        and t.message_info_id =#{messageInfoId}
        group by t.user_id
    </select>

    <select id="queryTop3ToUserName" resultType="MassMessageSenderVO">
        select MAX(user_id) userId,
        GROUP_CONCAT((select name from tb_wx_ext_customer where sender_id = external_user_id and corp_id =#{corpId}))
        toUserName
        from tb_wx_corp_mass_message_log t where
        id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY user_id
    </select>

    <select id="queryMassMessageSenderChatDetail" resultType="MassMessageSenderVO">
        select count(sender_id) chatNum
        ,(select max(name) from tb_wx_user where userid = t.user_id) userName from tb_wx_corp_mass_message_log t
        where 1= 1
        AND t.corp_id = #{corpId} and t.del_flag ='0' and t.send_type = 'group'
        <if test="checkStatus != null  and checkStatus != '' and checkStatus !=0 ">
            and t.status != '0'
        </if>
        <if test="checkStatus != null  and checkStatus ==0 ">
            and t.status = '0'
        </if>
        and t.message_info_id =#{messageInfoId}
        group by t.user_id
    </select>
    <select id="queryWaitToSendMassInfoByCorpId" resultType="TbWxCorpMassMessageDetail">
        select t.id messageInfoId,t1.content,t1.media_id mediaId,t1.pic_url picUrl from
        tb_wx_corp_mass_message_info t,tb_wx_corp_mass_message_detail t1
        where t.id = t1.message_info_id and t.setting_time is not NULL
        and t.check_status ='PENDING_EXEC'
        <if test="corpId = null and copId='' ">
            and t.corp_id = #{corpId}
        </if>
    </select>
    <select id="queryWaitToSendMessages" resultType="MassWxSenderVO" parameterType="map">
        select message_id messageInfoId,t.id,sender_id senderId,user_id userId,send_type chatType from
        tb_wx_corp_mass_message_log t
        where corp_id = #{corp_id} and status='0'
        and message_info_id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryRemindSender" resultType="MassWxSenderVO" parameterType="map">
        select distinct user_id userId from tb_wx_corp_mass_message_log t
        where corp_id = #{corpId} and status='0'
        and message_info_id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="exportMassMessageSenderCustDetail" resultType="com.cenker.scrm.pojo.vo.message.MassMessageExportVo">
        select (select name
                from tb_wx_ext_customer
                where t.sender_id = external_user_id
                  and corp_id = #{corpId})                                 toUserName
                ,
               (select max(name) from tb_wx_user where userid = t.user_id) userName,
               case when t.status = '0' then '未发送' else '已发送' end          status
        from tb_wx_corp_mass_message_log t
        where 1 = 1
          AND t.corp_id = #{corpId}
          and t.del_flag = '0'
          and t.send_type = 'single'
          and t.message_info_id = #{messageInfoId}
    </select>





</mapper>