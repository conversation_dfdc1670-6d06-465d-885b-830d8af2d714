<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.bu.BuOperTrackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cenker.scrm.pojo.entity.bu.BuOperTrack">
        <id column="id" property="id" />
        <result column="oper_id" property="operId" />
        <result column="track_type" property="trackType" />
        <result column="event_type" property="eventType" />
        <result column="sub_event_type" property="subEventType" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="related_resource" property="relatedResource" />
        <result column="external_user_id" property="externalUserId" />
        <result column="user_id" property="userId" />
        <result column="corp_id" property="corpId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <resultMap id="BuOperTrackVOMap" type="com.cenker.scrm.pojo.vo.bu.BuOperTrackVO">
        <id column="id" property="id" />
        <result column="event_type" property="eventType" />
        <result column="sub_event_type" property="subEventType" />
        <result column="track_type" property="trackType" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result property="relatedResource" column="related_resource"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>

        <association property="customerInfo" javaType="com.cenker.scrm.pojo.vo.bu.CustomerInfo">
            <result column="external_user_id" property="id" />
            <result column="customer_avatar" property="avatar" jdbcType="VARCHAR" />
            <result column="customer_name" property="name" />
            <result column="corp_name" property="corpName" />
        </association>

        <association property="staffInfo" javaType="com.cenker.scrm.pojo.vo.bu.StaffInfo">
            <result column="user_id" property="id" />
            <result column="staff_avatar" property="avatar" jdbcType="VARCHAR"/>
            <result column="staff_name" property="name" />
        </association>
    </resultMap>

    <select id="selectList" resultMap="BuOperTrackVOMap">
        select bot.event_type, bot.sub_event_type, bot.title,
            bot.create_time, bot.update_time,bot.track_type,
            <choose>
                <when test="externalUserId != null and externalUserId != ''">
                    (case when bot.external_user_id is null or bot.external_user_id = '' then concat(bot.id, tl.oper_type) else bot.id end) as id,
                    (case when bot.external_user_id is null or bot.external_user_id = '' then tl.external_user_id else bot.external_user_id end) external_user_id,
                    (case when bot.external_user_id is null or bot.external_user_id = '' then wec1.avatar else wec.avatar end) as customer_avatar,
                    (case when bot.external_user_id is null or bot.external_user_id = '' then wec1.`name` else wec.`name` end) as customer_name,
                    (case when bot.external_user_id is null or bot.external_user_id = '' then wec1.`type` else wec.`type` end) as corp_name,
                    (case when bot.external_user_id is null or bot.external_user_id = '' then tl.content else bot.content end) as content,
                </when>
                <otherwise>
                    bot.id,
                    bot.external_user_id,
                    wec.avatar as customer_avatar,
                    wec.`name` as customer_name,
                    wec.`corp_name` as corp_name,
                    bot.content,
                </otherwise>
            </choose>
            bot.user_id,
            bot.related_resource,
            wu.avatar as staff_avatar,
            su.nick_name as staff_name
        from bu_oper_track bot
        left join sys_user su on bot.create_by = su.user_id
        left join tb_wx_ext_customer wec on bot.external_user_id = wec.external_user_id
        left join tb_wx_user wu on bot.user_id = wu.userid and bot.corp_id = wu.corp_id
        <if test="externalUserId != null and externalUserId != ''">
            <![CDATA[
            left join
            (select tag_log.oper_id, tag_log.external_user_id  , tag_log.oper_type,
                    CONCAT((
                            case when tag_log.oper_type = 'add' then '批量添加企业标签'
                            when tag_log.oper_type = 'del' then '批量删除企业标签'
                            end
                    ), GROUP_CONCAT('<span class="tag">',tag_log.tag,'</span>' SEPARATOR '')) as content
            from
            tb_wx_customer_tag_log tag_log
           where
            tag_log.external_user_id = #{externalUserId}
            and tag_log.oper_id is not null and tag_log.oper_id != ''
            group by tag_log.oper_id, tag_log.external_user_id, tag_log.oper_type) tl
            on bot.track_type = 'business' and bot.event_type = 'BULK_EDIT_TAG' and bot.oper_id = tl.oper_id
            left join tb_wx_ext_customer wec1 on tl.external_user_id = wec1.external_user_id
            ]]>
        </if>
        <where>
            <if test="trackType != null and trackType != ''">
                and bot.track_type = #{trackType}
            </if>
            <if test="eventType != null and eventType != ''">
                and bot.event_type = #{eventType}
            </if>
            <if test="subEventType != null and subEventType != ''">
                and bot.sub_event_type = #{subEventType}
            </if>
            <if test="keyword != null and keyword != ''">
                and (
                    bot.title like concat('%', #{keyword}, '%')
                    or bot.content like concat('%', #{keyword}, '%')
                    or su.nick_name like concat('%', #{keyword}, '%')
                    or wec.name like concat('%', #{keyword}, '%')
                    <if test="externalUserId != null and externalUserId != ''">
                        or tl.content like concat('%', #{keyword}, '%')
                    </if>
                )
            </if>
            <if test="startTimeStr != null and startTimeStr != ''">
                and bot.create_time &gt;= #{startTimeStr}
            </if>
            <if test="endTimeStr != null and endTimeStr != ''">
                and bot.create_time &lt;= #{endTimeStr}
            </if>
            <if test="externalUserId != null and externalUserId != ''">
                and (bot.external_user_id = #{externalUserId} or  tl.external_user_id = #{externalUserId})
            </if>
            <if test="userId != null and userId != ''">
                and bot.user_id = #{userId}
            </if>
        </where>
        order by bot.create_time desc
    </select>
    <select id="getTrajectoryCountToday" resultType="java.util.Map">
        select
        count(t.external_user_id) todayTrajectoryCnt,
        count(distinct t.external_user_id) trajectoryUserCnt
        from bu_oper_track t
        where 1=1
        and t.corp_id = #{corpId}
        and t.external_user_id
        in (select f.external_user_id
        from tb_wx_ext_follow_user f
        where f.corp_id = t.corp_id
        <if test="userId != null and userId != ''">
            and f.user_id = #{userId}
        </if>
        and f.`status` = 0
        group by f.external_user_id)
        <if test="trackType != null and trackType != ''">
            and t.track_type = #{trackType}
        </if>
        and date(t.create_time) = date_format(#{startTime},'%Y-%m-%d' )
    </select>
</mapper>