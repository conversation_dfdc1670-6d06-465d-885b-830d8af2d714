<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.category.TbWxCategoryMapper">

    <resultMap type="TbWxCategory" id="TbWxCategoryResult">
        <result property="id" column="id" />
        <result property="mediaType" column="media_type" />
        <result property="name" column="name" />
        <result property="parentId" column="parent_id" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
        <result property="corpId" column="corp_id" />
    </resultMap>

    <sql id="selectTbWxCategoryVo">
        select id, media_type, name, parent_id, create_by, create_time, update_by, update_time, del_flag, corp_id from tb_wx_category
    </sql>

    <select id="selectTbWxCategoryList" parameterType="TbWxCategory" resultMap="TbWxCategoryResult">
        <include refid="selectTbWxCategoryVo" />
        <where>
            <if test="mediaType != null ">
                and media_type = #{mediaType}
            </if>
            <if test="name != null  and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="parentId != null ">
                and parent_id = #{parentId}
            </if>
            <if test="corpId != null  and corpId != ''">
                and corp_id = #{corpId}
            </if>
        </where>
        order by order_num
    </select>

    <select id="selectTbWxCategoryById" parameterType="Long" resultMap="TbWxCategoryResult">
        <include refid="selectTbWxCategoryVo" />
        where id = #{id}
    </select>

    <insert id="insertTbWxCategory" parameterType="TbWxCategory">
        insert into tb_wx_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="mediaType != null">media_type,
            </if>
            <if test="name != null">name,
            </if>
            <if test="parentId != null">parent_id,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
            <if test="corpId != null">corp_id,
            </if>
            <if test="orderNum != null">order_num,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="mediaType != null">#{mediaType},
            </if>
            <if test="name != null">#{name},
            </if>
            <if test="parentId != null">#{parentId},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
            <if test="corpId != null">#{corpId},
            </if>
            <if test="orderNum != null">#{orderNum},
            </if>
        </trim>
    </insert>

    <update id="updateTbWxCategory" parameterType="TbWxCategory">
        update tb_wx_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="mediaType != null">media_type =
                #{mediaType},
            </if>
            <if test="name != null">name =
                #{name},
            </if>
            <if test="parentId != null">parent_id =
                #{parentId},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="delFlag != null">del_flag =
                #{delFlag},
            </if>
            <if test="corpId != null">corp_id =
                #{corpId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbWxCategoryById" parameterType="Long">
        delete from tb_wx_category where id = #{id}
    </delete>

    <delete id="deleteTbWxCategoryByIds" parameterType="String">
        delete from tb_wx_category where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>