<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.session.CkSessionSensRuleInfoMapper">
    
    <resultMap type="CkSessionSensRuleInfo" id="CkSessionSensRuleInfoResult">
        <result property="ruleId"    column="rule_id"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="interceptType"    column="intercept_type"    />
        <result property="actTypes"    column="act_types"    />
        <result property="sensitiveWords"    column="sensitive_words"    />
        <result property="sensitiveWordNum"    column="sensitive_word_num"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="status"    column="status"    />
        <result property="nickName"    column="nick_name"    />
        <result property="wxRuleId"    column="wx_rule_id"    />
        <result property="wxRuleSyncFlag"    column="wx_rule_sync_flag"    />
    </resultMap>

    <sql id="selectCkSessionSensRuleInfoVo">
        select g.wx_rule_id,g.wx_rule_sync_flag,g.rule_id, g.rule_name, g.intercept_type, g.act_types, g.sensitive_words, g.sensitive_word_num,
               g.create_time, g.create_by, g.update_time, g.update_by, u.nick_name, g.status
        from ck_session_sens_rule_info g, sys_user u where g.create_by = u.user_id
    </sql>

    <select id="selectCkSessionSensRuleInfoList" parameterType="QrySensRuleDto" resultMap="CkSessionSensRuleInfoResult">
        <include refid="selectCkSessionSensRuleInfoVo"/>
            <if test="ruleName != null  and ruleName != ''"> and g.rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="interceptType != null  and interceptType != ''"> and g.intercept_type = #{interceptType}</if>
            <if test="statInterceptTypes != null  and statInterceptTypes != ''"> and g.intercept_type in ('2','3')</if>
            <if test="actTypes != null  and actTypes != ''"> and g.act_types like concat('%', #{actTypes}, '%')</if>
        <if test="status != null  and status != ''"> and g.status = #{status}</if>
         <if test="sensitiveWords != null  and sensitiveWords != ''"> and g.sensitive_words like concat('%', #{sensitiveWords}, '%')</if>    </select>
    
    <select id="selectCkSessionSensRuleInfoByRuleId" parameterType="Long" resultMap="CkSessionSensRuleInfoResult">
        <include refid="selectCkSessionSensRuleInfoVo"/>
        and g.rule_id = #{ruleId}
    </select>





        
    <insert id="insertCkSessionSensRuleInfo" parameterType="CkSessionSensRuleInfo" useGeneratedKeys="true" keyProperty="ruleId">
        insert into ck_session_sens_rule_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="interceptType != null">intercept_type,</if>
            <if test="actTypes != null">act_types,</if>
            <if test="sensitiveWords != null">sensitive_words,</if>
            <if test="sensitiveWordNum != null">sensitive_word_num,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="status != null">status,</if>
            <if test="wxRuleId != null">wx_rule_id,</if>
            <if test="wxRuleSyncFlag != null">wx_rule_sync_flag,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="interceptType != null">#{interceptType},</if>
            <if test="actTypes != null">#{actTypes},</if>
            <if test="sensitiveWords != null">#{sensitiveWords},</if>
            <if test="sensitiveWordNum != null">#{sensitiveWordNum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="status != null">#{status},</if>
            <if test="wxRuleId != null">#{wxRuleId},</if>
            <if test="wxRuleSyncFlag != null">#{wxRuleSyncFlag},</if>

        </trim>
    </insert>

    <update id="updateCkSessionSensRuleInfo" parameterType="CkSessionSensRuleInfo">
        update ck_session_sens_rule_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="interceptType != null">intercept_type = #{interceptType},</if>
            <if test="actTypes != null">act_types = #{actTypes},</if>
            <if test="sensitiveWords != null">sensitive_words = #{sensitiveWords},</if>
            <if test="sensitiveWordNum != null">sensitive_word_num = #{sensitiveWordNum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="wxRuleId != null">wx_rule_id = #{wxRuleId},</if>
            <if test="wxRuleSyncFlag != null">wx_rule_sync_flag = #{wxRuleSyncFlag},</if>
        </trim>
        where rule_id = #{ruleId}
    </update>

    <delete id="deleteCkSessionSensRuleInfoByRuleId" parameterType="Long">
        delete from ck_session_sens_rule_info where rule_id = #{ruleId}
    </delete>

    <delete id="deleteCkSessionSensRuleInfoByRuleIds" parameterType="String">
        delete from ck_session_sens_rule_info where rule_id in 
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>

    <select id="cntRuleNameIsRep" parameterType="CkSessionSensRuleInfo" resultType="java.lang.Long">
       select count(*) as cnt  from ck_session_sens_rule_info g
           <where>
               <if test="ruleName != null and ruleName != ''">and g.rule_name = #{ruleName}</if>
               <if test=" notEpRuleId!= null and notEpRuleId != ''">and g.rule_id != #{notEpRuleId}</if>
               <if test="status != null  and status != ''"> and g.status = #{status}</if>
               <if test="interceptType != null  and interceptType != ''"> and g.intercept_type = #{interceptType}</if>
           </where>
    </select>

</mapper>