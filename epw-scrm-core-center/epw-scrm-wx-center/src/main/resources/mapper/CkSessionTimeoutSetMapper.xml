<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.session.CkSessionTimeoutSetMapper">
    
    <resultMap type="CkSessionTimeoutSet" id="CkSessionTimeoutSetResult">
        <result property="setId"    column="set_id"    />
        <result property="timeNum"    column="time_num"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />

    </resultMap>

    <sql id="selectCkSessionTimeoutSetVo">
        select set_id, time_num, create_time, update_time, create_by, update_by,
               start_time,end_time
        from ck_session_timeout_set
    </sql>

    <select id="selectCkSessionTimeoutSetList" parameterType="CkSessionTimeoutSet" resultMap="CkSessionTimeoutSetResult">
        <include refid="selectCkSessionTimeoutSetVo"/>
        <where>  
            <if test="timeNum != null "> and time_num = #{timeNum}</if>
        </where>
    </select>
    
    <select id="selectCkSessionTimeoutSetBySetId" parameterType="Long" resultMap="CkSessionTimeoutSetResult">
        <include refid="selectCkSessionTimeoutSetVo"/>
        where set_id = #{setId}
    </select>
        
    <insert id="insertCkSessionTimeoutSet" parameterType="CkSessionTimeoutSet" useGeneratedKeys="true" keyProperty="setId">
        insert into ck_session_timeout_set
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="timeNum != null">time_num,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="timeNum != null">#{timeNum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>

        </trim>
    </insert>

    <update id="updateCkSessionTimeoutSet" parameterType="CkSessionTimeoutSet">
        update ck_session_timeout_set
        <trim prefix="SET" suffixOverrides=",">
            <if test="timeNum != null">time_num = #{timeNum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
        </trim>
        where set_id = #{setId}
    </update>

    <delete id="deleteCkSessionTimeoutSetBySetId" parameterType="Long">
        delete from ck_session_timeout_set where set_id = #{setId}
    </delete>

    <delete id="deleteCkSessionTimeoutSetBySetIds" parameterType="String">
        delete from ck_session_timeout_set where set_id in 
        <foreach item="setId" collection="array" open="(" separator="," close=")">
            #{setId}
        </foreach>
    </delete>
</mapper>