<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.fission.TbWxFissionMapper">

    <sql id="selectTbWxFission">
        select id,
               fission_type,
               task_name,
               fiss_info,
               fiss_num,
               start_time,
               end_time,
               customer_tag_id,
               customer_tag,
               posters_id,
               posters_url,
               fission_target_id,
               fission_target,
               fiss_qrcode,
               reward_url,
               reward_image_url,
               reward_rule,
               fiss_status,
               welcome_msg,
               create_by,
               create_time,
               update_by,
               update_time
        from tb_wx_fission
    </sql>

    <select id="selectTbWxFissionList" resultType="TbWxFission">
        <include refid="selectTbWxFission"/>
        <where>
            <if test="corpId != null and corpId != ''">and corp_id = #{corpId}</if>
            <if test="fissionType != null ">and fission_type = #{fissionType}</if>
            <if test="taskName != null  and taskName != ''">and task_name like concat('%', #{taskName}, '%')</if>
            <if test="fissInfo != null  and fissInfo != ''">and fiss_info = #{fissInfo}</if>
            <if test="fissNum != null ">and fiss_num = #{fissNum}</if>
            <if test="startTime != null ">and start_time >= #{startTime}</if>
            <if test="endTime != null "> <![CDATA[and end_time <= #{endTime}]]></if>
            <if test="customerTagId != null  and customerTagId != ''">and customer_tag_id = #{customerTagId}</if>
            <if test="customerTag != null  and customerTag != ''">and customer_tag = #{customerTag}</if>
            <if test="postersId != null and postersId != ''">and posters_id = #{postersId}</if>
            <if test="postersUrl != null  and postersUrl != ''">and posters_url = #{postersUrl}</if>
            <if test="fissionTargetId != null  and fissionTargetId != ''">and fission_target_id = #{fissionTargetId}
            </if>
            <if test="fissionTarget != null  and fissionTarget != ''">and fission_target = #{fissionTarget}</if>
            <if test="fissQrcode != null  and fissQrcode != ''">and fiss_qrcode = #{fissQrcode}</if>
            <if test="rewardUrl != null  and rewardUrl != ''">and reward_url = #{rewardUrl}</if>
            <if test="rewardImageUrl != null  and rewardImageUrl != ''">and reward_image_url = #{rewardImageUrl}</if>
            <if test="rewardRule != null  and rewardRule != ''">and reward_rule = #{rewardRule}</if>
            <if test="fissStatus != null ">and fiss_status = #{fissStatus}</if>
            <if test="welcomeMsg != null  and welcomeMsg != ''">and welcome_msg = #{welcomeMsg}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectFissionById" resultType="TbWxFission">
        select f.id,
               f.fission_type,
               f.task_name,
               f.fiss_info,
               f.fiss_num,
               f.start_time,
               f.end_time,
               f.customer_tag_id,
               if(f.customer_tag = '', '全部', f.customer_tag) customer_tag,
               f.posters_id,
               f.posters_url,
               f.fission_target_id,
               f.fission_target,
               f.fiss_qrcode,
               f.reward_url,
               f.reward_image_url,
               f.reward_rule,
               f.fiss_status,
               f.welcome_msg,
               f.del_flag,
               f.corp_id,
               u.nick_name                                   create_by,
               f.create_time,
               f.update_by,
               f.update_time
        from tb_wx_fission f
                 left join sys_user u on f.create_by = u.user_id and u.corp_id = f.corp_id
        where f.id = #{id}
    </select>

    <select id="getFissionRecordStatistics" resultType="TaskFissionRecordStatisticVO">
        select
        id record_id,
        customer_id,
        head_img_url,
        nick_name,
        if(fission_num>=fiss_num,'已完成',concat('还差',fiss_num-fission_num,'人达标'))complete_status,
        fission_num,
        fission_num-retention chum_num,
        ifnull(concat(round((retention/fission_num)*100,0),'%'),'0%') retention_rate
        from (
        select
        u.id customer_id,
        r.id,
        u.head_img_url,
        u.nick_name,
        f.fiss_num,
        (select count(distinct customer_id) from tb_wx_fission_complete_record where r.id =
        fission_record_id)fission_num,
        (select count(distinct fu.external_user_id) from tb_wx_ext_follow_user fu where fu.external_user_id in
        (select cr.customer_id from tb_wx_fission_complete_record cr where r.id = cr.fission_record_id group by
        cr.customer_id)
        and fu.user_id = f.fission_target_id
        and fu.`status` = 0
        )retention
        from tb_wx_task_fission_record r
        left join mp_wx_user u on r.customer_id = u.id
        left join tb_wx_fission f on r.task_fission_id = f.id
        where r.task_fission_id = #{taskFissionId}
        <if test="nickName != null and nickName != ''">
            and u.nick_name like concat('%',#{nickName},'%')
        </if>
        )t
        <where>
            <if test="status != null and status == 1">
                and fission_num >= fiss_num
            </if>
            <if test="status != null and status == 2">
                and fission_num &lt; fiss_num
            </if>
        </where>
        order by fission_num desc
    </select>

    <select id="getFissionRecordDetail" resultType="TaskFissionRecordStatisticVO">
        select c.avatar                head_img_url,
               c.`name`                nick_name,
               u.`name`                user_name,
               c.type,
               c.corp_name,
               r.create_time,
               if((select count(1)
                   from tb_wx_ext_follow_user fu
                   where fu.corp_id = f.corp_id
                     and fu.external_user_id = r.customer_id
                     and fu.user_id = f.fission_target_id
                     and fu.`status` = 0
                  ) > 0, '助力成功', '流失') complete_status
        from tb_wx_fission_complete_record r
                 left join tb_wx_fission f on r.task_fission_id = f.id
                 left join tb_wx_ext_customer c on c.external_user_id = r.customer_id
                 left join tb_wx_user u on u.userid = f.fission_target_id and u.corp_id = f.corp_id
        where r.fission_record_id = #{recordId}
        group by r.customer_id
    </select>

    <select id="getWorkFissionList" resultType="java.util.Map">
        select
        f.id,
        f.fiss_status fissStatus,
        f.task_name taskName,
        f.posters_url cover,
        f.fiss_info `desc`,
        date_format(f.start_time,'%Y-%m-%d %H:%i:%S')startTime,
        date_format(f.end_time,'%Y-%m-%d %H:%i:%S')endTime,
        (select count(1) from tb_wx_task_fission_record where task_fission_id = f.id)joinCustomerCnt,
        (select count(distinct customer_id) from tb_wx_fission_complete_record where type = 1 and task_fission_id =
        f.id)fissionNewCnt,
        (select count(distinct external_user_id) from tb_wx_ext_follow_user where `status` = 0 and external_user_id in
        (select distinct customer_id from tb_wx_fission_complete_record where type = 1 and task_fission_id = f.id) and
        corp_id = f.corp_id)retentionCnt,
        concat(#{taskFissionPage},'?fissionId=',f.id,'&amp;fissionTargetId=',fission_target_id) url
        from tb_wx_fission f
        where f.corp_id = #{corpDTO.corpId}
        and f.del_flag = 0
        and f.fission_type = 1
        <if test="corpDTO.corpName != null and corpDTO.corpName != ''">
            and f.task_name like concat('%',#{corpDTO.corpName},'%')
        </if>
        order by f.create_time desc
    </select>

    <select id="getWorkFissionGroupList" resultType="java.util.Map">
        select
        f.id,
        f.fiss_status fissStatus,
        f.task_name taskName,
        f.posters_url cover,
        f.fiss_info `desc`,
        date_format(f.start_time,'%Y-%m-%d %H:%i:%S')startTime,
        date_format(f.end_time,'%Y-%m-%d %H:%i:%S')endTime,
        (select count(1) from tb_wx_task_fission_record where task_fission_id = f.id and
        date_format(create_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d'))addCustomerCnt,
        (select count(1) from tb_wx_task_fission_record where task_fission_id = f.id)joinCustomerCnt,
        (select count(distinct customer_id) from tb_wx_fission_complete_record where type = 1 and task_fission_id =
        f.id)fissionCompleteCnt,
        concat(#{taskFissionPage},'?fissionId=',f.id,'&amp;fissionTargetId=',fission_target_id) url
        from tb_wx_fission f
        where f.corp_id = #{corpDTO.corpId}
        and f.del_flag = 0
        and f.fission_type = 2
        <if test="corpDTO.corpName != null and corpDTO.corpName != ''">
            and f.task_name like concat('%',#{corpDTO.corpName},'%')
        </if>
        order by f.create_time desc
    </select>
</mapper>