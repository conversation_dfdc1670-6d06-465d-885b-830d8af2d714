<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.custlink.TbWxCustlinkUserMappingMapper">

    <resultMap type="TbWxCustlinkUserMapping" id="TbWxCustlinkUserMappingResult">
        <result property="id" column="id"/>
        <result property="linkId" column="link_id"/>
        <result property="userId" column="user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectTbWxCustlinkUserMappingVo">
        select id,
               link_id,
               user_id,
               create_by,
               create_time
        from tb_wx_custlink_user_mapping
    </sql>

    <select id="selectTbWxCustlinkUserMappingVo" resultMap="TbWxCustlinkUserMappingResult">
        <include refid="selectTbWxCustlinkUserMappingVo" />
        <if test="linkId != null  and linkId != ''">
            and link_id = #{linkId}
        </if>
    </select>

    <select id="qryUserListByLinkId" parameterType="Long"  resultType="UserConditionDTO">
        select r.id,
        r.userid userId,
        r.name userName,
        r.avatar userAvatar,
        r.del_flag delFlag
        from tb_wx_custlink_user_mapping g join  tb_wx_user r on g.user_id = r.userid
        where   g.link_id = #{linkId}
    </select>


    <insert id="insertTbWxCustlinkUserMapping" parameterType="TbWxCustlinkUserMapping" useGeneratedKeys="true" keyProperty="id">
        insert into tb_wx_custlink_user_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="linkId != null">link_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="linkId != null">#{linkId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>
</mapper>