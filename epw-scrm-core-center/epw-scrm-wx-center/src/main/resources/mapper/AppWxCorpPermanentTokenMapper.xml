<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.corp.AppWxCorpPermanentTokenMapper">

   <select id="countCorpAppInstallUser" resultType="java.lang.Integer">
      select count(1) from app_corp_permanent_token where corp_id = #{corpId} and user_id = #{userId} and `status` = 0
   </select>

   <select id="selectAppCorpAdmin" resultType="java.lang.String">
      select user_id from app_corp_permanent_token where corp_id = #{corpId} and `status` = 0
   </select>

</mapper>