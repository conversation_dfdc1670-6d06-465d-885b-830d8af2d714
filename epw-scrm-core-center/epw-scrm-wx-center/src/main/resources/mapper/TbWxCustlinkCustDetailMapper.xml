<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.custlink.TbWxCustlinkCustDetailMapper">

    <resultMap type="TbWxCustlinkCustDetail" id="TbWxCustlinkCustDetailResult">
        <result property="id" column="id"/>
        <result property="externalUserid" column="external_userid"/>
        <result property="userid" column="userid"/>
        <result property="chatStatus" column="chat_status"/>
        <result property="state" column="state"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
        <result property="wxLinkId" column="wx_link_id"/>
        <result property="linkId" column="link_id"/>
        <result property="hour" column="hour"/>
        <result property="day" column="day"/>

        <result property="addNum" column="add_num"/>

    </resultMap>

    <sql id="selectTbWxCustlinkCustDetailVo">
        select id,
               external_userid,
               userid,
               chat_status,
               state,
               update_time,
               create_time,
               wx_link_id,
               link_id,
               hour,
               day
        from tb_wx_custlink_cust_detail
    </sql>

    <select id="selectTbWxCustlinkCustDetailVo"  parameterType="TbWxCustlinkCustDetail"  resultMap="TbWxCustlinkCustDetailResult">
        <include refid="selectTbWxCustlinkCustDetailVo" />
    <where>
        <if test="linkId != null  and linkId != ''">
            and link_id = #{linkId}
        </if>
        <if test="wxLinkId != null  and wxLinkId != ''">
            and wx_link_id = #{wxLinkId}
        </if>
        <if test="externalUserid != null  and externalUserid != ''">
            and external_userid = #{externalUserid}
        </if>
        <if test="userid != null  and userid != ''">
            and userid = #{userid}
        </if>
    </where>
    </select>


    <select id="qryAddChatStatData"  parameterType="CustlinkVO"  resultMap="TbWxCustlinkCustDetailResult">
        select
            count(*)  add_num ,
        <if test="statHour != null  and statHour != ''">
            hour
        </if>
        <if test="statDay != null  and statDay != ''">
            day
        </if>
        from
            tb_wx_custlink_cust_detail l
        where l.link_id = #{id}
        <if test="chatStatus != null  and chatStatus != ''">
          and l.chat_status = #{chatStatus}
        </if>
        <if test="statDay != null  and statDay != ''">
             <![CDATA[  and l.day >= #{beginDate}  and  l.day <= #{endDate}]]>
        </if>
        <if test="statHour != null  and statHour != ''">
            and  l.day  = #{day}
        </if>
        group by
        <if test="statHour != null  and statHour != ''">
            hour
        </if>
        <if test="statDay != null  and statDay != ''">
            day
        </if>
    </select>

    <select id="qryLossChatStatData"  parameterType="CustlinkVO"  resultMap="TbWxCustlinkCustDetailResult">
    SELECT
        count(1) as add_num,
        <if test="statHour != null  and statHour != ''">
            DATE_FORMAT(del_time, '%H') hour
        </if>
        <if test="statDay != null  and statDay != ''">
            DATE_FORMAT(u.del_time, '%Y-%m-%d') AS day
        </if>
    FROM
    tb_wx_custlink_cust_detail l
    JOIN tb_wx_ext_follow_user u ON u.user_id = l.userid
    AND u.external_user_id = l.external_userid
    WHERE
    u.del_time IS NOT NULL
    and u.STATUS != 0
    and link_id = #{id}
    <if test="statHour != null  and statHour != ''">
        and DATE_FORMAT(del_time, '%Y-%m-%d') =  #{day}
    </if>
        <if test="statDay != null  and statDay != ''">
            and <![CDATA[ DATE_FORMAT(u.del_time,'%Y-%m-%d')  >= #{beginDate}]]>
            and <![CDATA[ DATE_FORMAT(u.del_time,'%Y-%m-%d')  <= #{endDate}]]>
        </if>
    group by
        <if test="statDay != null  and statDay != ''">
             DATE_FORMAT(u.del_time, '%Y-%m-%d')
        </if>
        <if test="statHour != null  and statHour != ''">
                DATE_FORMAT(u.del_time, '%Y-%m-%d %H')
        </if>
</select>



    <select id="qryCustList" parameterType="CustlinkVO"   resultType="QryCustlinkCustDetailBakVO">
        SELECT
            r.external_user_id externalUserid,
            r.name custName,
            IF((corp_name is null or corp_name ='') ,'微信',concat('',corp_name)) corpName,
            u.name addUserName,
            DATE_FORMAT(l.create_time, '%Y-%m-%d %H:%i')    createTime
        FROM
            tb_wx_custlink_cust_detail l left join tb_wx_ext_customer r  on l.external_userid = r.external_user_id
                                         left join  tb_wx_user u  on l.userid = u.userid
        <where>
            <if test="id != null  and id != ''">
                and l.link_id = #{id}
            </if>
            <if test="userId != null  and userId != ''">
                and u.userid =#{userId}
            </if>
            <if test="beginDate != null  and beginDate != ''">  and <![CDATA[ DATE_FORMAT(l.create_time,'%Y-%m-%d')  >= #{beginDate}]]></if>
            <if test="endDate != null  and endDate != ''">  and <![CDATA[ DATE_FORMAT(l.create_time,'%Y-%m-%d')  <= #{endDate}]]></if>
        </where>
        order by l.create_time desc
    </select>

    <select id="countUserAddTimesByLinkId" resultType="java.lang.Long">
        SELECT
            count( 1 )
        FROM tb_wx_custlink_cust_detail d
        JOIN tb_wx_ext_follow_user u ON u.user_id = d.userid AND u.external_user_id = d.external_userid
        <where>
            <if test="linkId != null">
                and d.link_id = #{linkId}
            </if>
            <if test="beginTime != null">
                and u.create_time >= #{beginTime}
            </if>
            <if test="endTime != null">
                and u.create_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
    </select>

    <select id="countUserDelTimesByLinkId" resultType="java.lang.Long">
        SELECT
            count( 1 )
        FROM tb_wx_custlink_cust_detail d
            JOIN tb_wx_ext_follow_user u ON u.user_id = d.userid AND u.external_user_id = d.external_userid
        WHERE u.`status` != '0'
        <if test="linkId != null">
            and d.link_id = #{linkId}
        </if>
        <if test="beginTime != null">
            and u.del_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and u.del_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>

    <select id="countChatUserByLinkId" resultType="java.lang.Long">
        SELECT
            count( DISTINCT d.id )
        FROM tb_wx_custlink_cust_detail d
            JOIN tb_wx_ext_follow_user u ON u.user_id = d.userid AND u.external_user_id = d.external_userid
        WHERE d.chat_status = '1'
        <if test="linkId != null">
            and d.link_id = #{linkId}
        </if>
        <if test="beginTime != null">
            and d.update_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and d.update_time <![CDATA[<=]]> #{endTime}
        </if>
    </select>

    <select id="statUserAddTimesGroupByHour" resultType="com.cenker.scrm.pojo.vo.custlink.StatDataVO">
        SELECT
            DATE_FORMAT(u.create_time, '%H') AS date,
            count( 1 ) AS num
        FROM tb_wx_custlink_cust_detail l
        JOIN tb_wx_ext_follow_user u ON u.user_id = l.userid AND u.external_user_id = l.external_userid
        WHERE l.link_id = #{linkId}
        <if test="beginTime != null">
            and u.create_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and u.create_time <![CDATA[<=]]> #{endTime}
        </if>
        GROUP BY
            DATE_FORMAT( u.create_time, '%Y-%m-%d %H' )
        ORDER BY date ASC
    </select>
    <select id="statUserAddTimesGroupByDay" resultType="com.cenker.scrm.pojo.vo.custlink.StatDataVO">
        SELECT
            DATE_FORMAT(u.create_time, '%Y-%m-%d') AS date,
            count( 1 ) AS num
        FROM tb_wx_custlink_cust_detail l
        JOIN tb_wx_ext_follow_user u ON u.user_id = l.userid AND u.external_user_id = l.external_userid
        WHERE l.link_id = #{linkId}
        <if test="beginTime != null">
            and u.create_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and u.create_time <![CDATA[<=]]> #{endTime}
        </if>
        GROUP BY
            DATE_FORMAT( u.create_time, '%Y-%m-%d' )
        ORDER BY date ASC
    </select>
    <select id="statUserDelTimesGroupByHour" resultType="com.cenker.scrm.pojo.vo.custlink.StatDataVO">
        SELECT
            DATE_FORMAT(u.del_time, '%H') AS date,
            count( 1 ) AS num
        FROM tb_wx_custlink_cust_detail l
            JOIN tb_wx_ext_follow_user u ON u.user_id = l.userid AND u.external_user_id = l.external_userid
        WHERE l.link_id = #{linkId} and u.`status` != '0'
        <if test="beginTime != null">
            and u.del_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and u.del_time <![CDATA[<=]]> #{endTime}
        </if>
        GROUP BY
            DATE_FORMAT( u.del_time, '%Y-%m-%d %H' )
        ORDER BY date ASC
    </select>
    <select id="statUserDelTimesGroupByDay" resultType="com.cenker.scrm.pojo.vo.custlink.StatDataVO">
        SELECT
            DATE_FORMAT(u.del_time, '%Y-%m-%d') AS date,
            count( 1 ) AS num
        FROM tb_wx_custlink_cust_detail l
            JOIN tb_wx_ext_follow_user u ON u.user_id = l.userid AND u.external_user_id = l.external_userid
        WHERE l.link_id = #{linkId} and u.`status` != '0'
        <if test="beginTime != null">
            and u.del_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and u.del_time <![CDATA[<=]]> #{endTime}
        </if>
        GROUP BY
            DATE_FORMAT( u.del_time, '%Y-%m-%d' )
        ORDER BY date ASC
    </select>
    <select id="statChatUserGroupByHour" resultType="com.cenker.scrm.pojo.vo.custlink.StatDataVO">
        SELECT
            DATE_FORMAT(l.update_time, '%H') AS date,
            count( DISTINCT l.id ) AS num
        FROM tb_wx_custlink_cust_detail l
            JOIN tb_wx_ext_follow_user u ON u.user_id = l.userid AND u.external_user_id = l.external_userid
        WHERE l.link_id = #{linkId} and l.chat_status = '1'
        <if test="beginTime != null">
            and l.update_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and l.update_time <![CDATA[<=]]> #{endTime}
        </if>
        GROUP BY
            DATE_FORMAT( l.update_time, '%Y-%m-%d %H' )
        ORDER BY date ASC
    </select>
    <select id="statChatUserGroupByDay" resultType="com.cenker.scrm.pojo.vo.custlink.StatDataVO">
        SELECT
            DATE_FORMAT(l.update_time, '%Y-%m-%d') AS date,
            count( DISTINCT l.id) AS num
        FROM tb_wx_custlink_cust_detail l
            JOIN tb_wx_ext_follow_user u ON u.user_id = l.userid AND u.external_user_id = l.external_userid
        WHERE l.link_id = #{linkId} and l.chat_status = '1'
        <if test="beginTime != null">
            and l.update_time >= #{beginTime}
        </if>
        <if test="endTime != null">
            and l.update_time <![CDATA[<=]]> #{endTime}
        </if>
        GROUP BY
            DATE_FORMAT( l.update_time, '%Y-%m-%d' )
        ORDER BY date ASC
    </select>



    <insert id="insertTbWxCustlinkCustDetail" parameterType="TbWxCustlinkCustDetail" useGeneratedKeys="true" keyProperty="id">
        insert into tb_wx_custlink_cust_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="externalUserid != null">external_userid,</if>
            <if test="userid != null">userid,</if>
            <if test="chatStatus != null">chat_status,</if>
            <if test="state != null">state,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="wxLinkId != null">wx_link_id,</if>
            <if test="linkId != null">link_id,</if>
            <if test="hour != null">hour,</if>
            <if test="day != null">day,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="externalUserid != null">#{externalUserid},</if>
            <if test="userid != null">#{userid},</if>
            <if test="chatStatus != null">#{chatStatus},</if>
            <if test="state != null">#{state},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="wxLinkId != null">#{wxLinkId},</if>
            <if test="linkId != null">#{linkId},</if>
            <if test="hour != null">#{hour},</if>
            <if test="day != null">#{day},</if>

        </trim>
    </insert>

    <update id="updateTbWxCustlinkCustDetail" parameterType="TbWxCustlinkCustDetail">
        update tb_wx_custlink_cust_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="externalUserid != null">external_userid = #{externalUserid},</if>
            <if test="userid != null">userid = #{userid},</if>
            <if test="chatStatus != null">chat_status = #{chatStatus},</if>
            <if test="state != null">state = #{state},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="wxLinkId != null">wx_link_id = #{wxLinkId},</if>
            <if test="linkId != null">link_id = #{linkId},</if>
        </trim>
        where  id = #{id}
    </update>
</mapper>