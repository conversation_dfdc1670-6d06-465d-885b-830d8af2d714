<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.session.CkSessionHotWordInfoMapper">
    
    <resultMap type="CkSessionHotWordInfo" id="CkSessionHotWordInfoResult">
        <result property="hotId"    column="hot_id"    />
        <result property="hotWord"    column="hot_word"    />
        <result property="synonWords"    column="synon_words"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="nickName"    column="nick_name"    />

    </resultMap>

    <sql id="selectCkSessionHotWordInfoVo">
        select o.hot_id, o.hot_word, o.synon_words, o.create_time, o.update_time, o.create_by, o.update_by,u.nick_name
        from ck_session_hot_word_info o, sys_user u where o.create_by = u.user_id
    </sql>

    <select id="selectCkSessionHotWordInfoList" parameterType="QryHotWordDto" resultMap="CkSessionHotWordInfoResult">
        <include refid="selectCkSessionHotWordInfoVo"/>
        <if test="keyWord != null  and keyWord != ''">
            and (o.hot_word  like concat('%',#{keyWord},'%') or  o.synon_words  like concat('%',#{keyWord},'%') )
        </if>
        <if test="createUser != null  and createUser != ''">
            and u.nick_name  like concat('%', #{createUser}, '%')
        </if>
        <if test="beginTime != null  and beginTime != ''">
            and <![CDATA[ DATE_FORMAT(o.create_time,'%Y-%m-%d')  >= #{beginTime}]]>
        </if>
        <if test="endTime != null  and endTime != ''">
            and <![CDATA[ DATE_FORMAT(o.create_time,'%Y-%m-%d')  <= #{endTime}]]>
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and o.dept_id in
                <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and o.create_by = #{userId}
            </if>
        </if>
      </select>
    
    <select id="selectCkSessionHotWordInfoByHotId" parameterType="Long" resultMap="CkSessionHotWordInfoResult">
        <include refid="selectCkSessionHotWordInfoVo"/>
         and o.hot_id = #{hotId}
    </select>
        
    <insert id="insertCkSessionHotWordInfo" parameterType="CkSessionHotWordInfo" useGeneratedKeys="true" keyProperty="hotId">
        insert into ck_session_hot_word_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hotWord != null and hotWord != ''">hot_word,</if>
            <if test="synonWords != null">synon_words,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hotWord != null and hotWord != ''">#{hotWord},</if>
            <if test="synonWords != null">#{synonWords},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateCkSessionHotWordInfo" parameterType="CkSessionHotWordInfo">
        update ck_session_hot_word_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="hotWord != null and hotWord != ''">hot_word = #{hotWord},</if>
            <if test="synonWords != null">synon_words = #{synonWords},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where hot_id = #{hotId}
    </update>

    <delete id="deleteCkSessionHotWordInfoByHotId" parameterType="Long">
        delete from ck_session_hot_word_info where hot_id = #{hotId}
    </delete>

    <delete id="deleteCkSessionHotWordInfoByHotIds" parameterType="String">
        delete from ck_session_hot_word_info where hot_id in 
        <foreach item="hotId" collection="array" open="(" separator="," close=")">
            #{hotId}
        </foreach>
    </delete>



    <select id="cntHotWordRep" parameterType="CkSessionHotWordInfo" resultType="java.lang.Long">
        select count(*) as cnt  from ck_session_hot_word_info g
        <where>
            <if test="hotWord != null and hotWord != ''">and g.hot_word = #{hotWord}</if>
            <if test=" notEqHotId!= null and notEqHotId != ''">and g.hot_id != #{notEqHotId}</if>
        </where>
    </select>
</mapper>