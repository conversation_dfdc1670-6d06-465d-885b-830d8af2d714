<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.sitecontact.ContactSiteMapper">

    <select id="selectSiteList" resultType="ContactSiteVO">
        select
            a.id site_id,
            s.id site_contact_id,
            a.area_name site_name,
            (select count(1) from contact_area s where s.parent_id = a.id and s.del_flag = 0)store_cnt,
            (select count(distinct (external_user_id))
             from tb_wx_ext_follow_user
             where substring(state,1,8) = s.state) add_count,
            group_concat(wu.name order by wu.userid) user_name,
            (select u.nick_name from sys_user u where a.create_by = u.user_id) createBy,
            a.create_time,
        (case when a.parent_id = -1 then '默认'
        when a.parent_id &lt; 35 then (select area_name from contact_area where id = a.parent_id)
        when a.parent_id &lt; 1000 then concat((select area_name from contact_area where id = (select parent_id from contact_area where id = a.parent_id)),(select area_name from contact_area where id = a.parent_id))
        when a.parent_id &lt; 10000 then concat(
        (select area_name from contact_area where id = (select parent_id from contact_area where id = (select parent_id from contact_area where id = a.parent_id)))
        ,(select area_name from contact_area where id = (select parent_id from contact_area where id = a.parent_id)),(select area_name from contact_area where id = a.parent_id))
        else '默认' end)area_name
        from contact_area a
                 join contact_site s on a.id = s.site_id
                 left join tb_wx_contact_user_rel r on r.contact_id = s.id and r.type = 2
                 left join tb_wx_user wu on wu.userid = r.user_id
        where a.area_level = 4
          and a.del_flag = 0
        <if test="siteName != null and siteName != ''">
            and a.area_name like concat('%',#{siteName},'%')
        </if>
        <if test="corpUserId != null  and corpUserId.size > 0">
            and r.user_id in
            <foreach item="userId" collection="corpUserId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="areaId != null and areaId != 0">
            <choose>
                <when test="areaId == -1">
                    and a.parent_id = #{areaId}
                </when>
                <when test="areaId &lt;= 34">
                    and (a.parent_id = #{areaId}
                    or  a.parent_id in (select city.id from contact_area city where city.parent_id = #{areaId})
                    or a.parent_id in (select id from contact_area where parent_id in (select id from contact_area where parent_id = #{areaId})))
                </when>
                <when test="areaId &lt;= 1000">
                    and (a.parent_id = #{areaId}
                    or a.parent_id in (select id from contact_area where parent_id = #{areaId}))
                </when>
                <otherwise>
                    and a.parent_id = #{areaId}
                </otherwise>
            </choose>
        </if>
        <if test="createBy != null  and createBy != ''">
            and (select u.nick_name from sys_user u where a.create_by = u.user_id) like concat('%',#{createBy},'%')
        </if>
        group by s.id
        order by a.create_time desc
    </select>

    <select id="selectAllSiteNameList" resultType="String">
        select
        a.area_name site_name
        from contact_area a
        where a.area_level = 4
        and a.del_flag = 0
        <if test="siteName != null and siteName != ''">
            and a.area_name like concat('%',#{siteName},'%')
        </if>
        <if test="areaId != null and areaId != 0">
            <choose>
                <when test="areaId == -1">
                    and a.parent_id = #{areaId}
                </when>
                <when test="areaId &lt;= 34">
                    and (a.parent_id = #{areaId}
                    or  a.parent_id in (select city.id from contact_area city where city.parent_id = #{areaId})
                    or a.parent_id in (select id from contact_area where parent_id in (select id from contact_area where parent_id = #{areaId})))
                </when>
                <when test="areaId &lt;= 1000">
                    and (a.parent_id = #{areaId}
                    or a.parent_id in (select id from contact_area where parent_id = #{areaId}))
                </when>
                <otherwise>
                    and a.parent_id = #{areaId}
                </otherwise>
            </choose>
        </if>
        order by a.create_time desc
    </select>


    <resultMap id="getInfoMap" type="ContactSiteInfoVO">
        <id property="id" column="id"/>
        <result property="skipVerify" column="skip_verify"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="welContent" column="wel_content"/>
        <result property="createBy" column="create_by"/>
        <result property="siteId" column="site_id"/>
        <result property="siteName" column="site_name"/>
        <result property="siteParentId" column="site_parent_id"/>
        <result property="areaName" column="area_name"/>
        <result property="tagList" column="weEmpleCodeTags"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result property="attachments" column="welcome_attachment"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <collection property="userList" ofType="UserVo">
            <result property="name" column="name"/>
            <result property="userId" column="userid"/>
            <result property="delFlag" column="del_flag"/>
            <result property="avatar" column="avatar"/>
        </collection>
    </resultMap>

    <select id="getInfo" resultMap="getInfoMap">
        select c.id,
               c.skip_verify,
               c.remark,
               c.wel_content,
               c.weEmpleCodeTags,
               c.welcome_attachment,
               c.create_time,
               u.`name`,
               u.userid,
               u.del_flag,
               u.avatar,
               c.site_id,
               a.parent_id site_parent_id,
               a.area_name site_name,
               (case when a.parent_id = -1 then '默认'
                     when a.parent_id &lt; 35 then (select area_name from contact_area where id = a.parent_id)
                     when a.parent_id &lt; 1000 then concat((select area_name from contact_area where id = (select parent_id from contact_area where id = a.parent_id)),(select area_name from contact_area where id = a.parent_id))
                     when a.parent_id &lt; 10000 then concat(
                             (select area_name from contact_area where id = (select parent_id from contact_area where id = (select parent_id from contact_area where id = a.parent_id)))
                         ,(select area_name from contact_area where id = (select parent_id from contact_area where id = a.parent_id)),(select area_name from contact_area where id = a.parent_id))
                     else '默认' end)area_name,
                   (select u.nick_name from sys_user u where c.create_by = u.user_id)create_by
        from contact_site c
                 join contact_area a on a.id = c.site_id
                 left join tb_wx_contact_user_rel r on c.id = r.contact_id and r.type = 2
                 left join tb_wx_user u on u.userid = r.user_id
        where c.id = #{id}
          and c.del_flag = 0
    </select>

    <select id="getDataStatistics" resultType="ContactStatisticsVo">
        select (select count(distinct external_user_id)
                from tb_wx_ext_follow_user
                where c.sign_id = substring(state,1,8)
                  and c.corp_id = corp_id
               )            total_add_cnt,
               ifnull((select count(distinct external_user_id)
                       from tb_wx_ext_follow_user
                       where c.sign_id = substring(state,1,8)
                         and c.corp_id = corp_id
                         and `status` != 0
                         and external_user_id not in (
                           select u2.external_user_id
                           from tb_wx_ext_follow_user u2
                           where c.sign_id = substring(u2.state,1,8)
                             and c.corp_id = u2.corp_id
                             and u2.`status` = 0
                           group by u2.external_user_id
                       )
                      ), 0) total_del_cnt,
               (select count(distinct external_user_id)
                from tb_wx_ext_follow_user
                where c.sign_id = substring(state,1,8)
                  and c.corp_id = corp_id
                  and date_format(create_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
               )            add_cnt,
               (select count(distinct external_user_id)
                from tb_wx_ext_follow_user
                where c.sign_id = substring(state,1,8)
                  and `status` != 0
                  and c.corp_id = corp_id
                  and external_user_id not in (
                    select u2.external_user_id
                    from tb_wx_ext_follow_user u2
                    where c.sign_id = substring(u2.state,1,8)
                      and c.corp_id = u2.corp_id
                      and u2.`status` = 0
                    group by u2.external_user_id
                )
                  and date_format(create_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
               )            del_cnt
        from contact_site c
        where c.id = #{id}
    </select>

    <select id="getAddCustomerInfo4Web" resultType="ContactCustomerStatisticsVo">
        select e.external_user_id,
               e.avatar,
               e.name,
               e.type,
               e.corp_name,
               u.create_time,
               (select name from tb_wx_user where id = (select r.user_pri_id from tb_wx_delivery_contact_record r where r.new_flag = 1
                                                                                                                    and  r.external_user_id = u.external_user_id and r.contact_id = c.id limit 1))delivery_user_name,
               (
                   select if(`status` = 0, 0, 2)
                   from tb_wx_ext_follow_user
                   where external_user_id = e.external_user_id
                     and user_id = u.user_id
                   order by `status`
                   limit 1
               )          `status`,
               e.`status` complete_del,
               wu.`name`  staff_name
        from tb_wx_ext_follow_user u
                 left join contact_site c on substring(u.state,1,8) = c.sign_id and u.corp_id = c.corp_id
                 left join tb_wx_ext_customer e on u.external_user_id = e.external_user_id
                 left join tb_wx_user wu on wu.userid = u.user_id and wu.corp_id = c.corp_id
        where c.id = #{id}
        group by e.external_user_id
        order by u.create_time desc
    </select>

    <select id="getBehaviorData4TotalAddCount" resultType="ContactStatisticsDailyVo">
        select date_format(u.create_time, '%Y-%m-%d')              date,
               (@sum := @sum + count(distinct u.external_user_id)) `count`
        from contact_site c
                 join tb_wx_ext_follow_user u on substring(u.state,1,8) = c.sign_id and u.corp_id = c.corp_id
                 join (select @sum := 0) i
        where c.id = #{id}
          and date_format(u.create_time, '%Y-%m-%d') &lt;= #{endTime}
        group by date_format(u.create_time, '%Y-%m-%d')
    </select>

    <select id="getBehaviorData4AddAndDelCount" resultType="ContactStatisticsDailyVo">
        select
        u.create_time date,
        count(distinct u.external_user_id) `count`
        from contact_site c
        join tb_wx_ext_follow_user u on substring(u.state,1,8) = c.sign_id and u.corp_id = c.corp_id
        where c.id = #{id}
        <if test="type == 3">
            and u.status != 0
            and u.external_user_id not in (
            select u2.external_user_id from tb_wx_ext_follow_user u2
            where c.state = u2.state
            and c.corp_id = u2.corp_id
            and u2.`status` = 0
            group by u2.external_user_id
            )
        </if>
        and date_format(u.create_time,'%Y-%m-%d') >= #{startTime}
        and date_format(u.create_time,'%Y-%m-%d') &lt;= #{endTime}
        group by date_format(u.create_time,'%Y-%m-%d')
    </select>
</mapper>