<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.journey.TbWxExtJourneyInfoMapper">

    <resultMap id="externalJourneyMap" type="com.cenker.scrm.pojo.vo.journey.ExternalJourneyVO">
        <id column="id" property="journeyId"/>
        <result column="journey_name" property="journeyName"/>
        <result column="is_automation" property="automation"/>
        <result column="sop_id" property="sopId"/>
        <collection property="stages" ofType="com.cenker.scrm.pojo.vo.journey.ExternalJourneyStageVO">
            <id column="sid" property="stageId"/>
            <result column="stage_name" property="stageName"/>
            <result column="stage_desc" property="stageDesc"/>
            <result column="delete_remind" property="deleteRemind"/>
        </collection>
    </resultMap>

    <select id="selectJourneyList" resultMap="externalJourneyMap">
        select j.id,
               j.journey_name,
               j.is_automation,
               (select sop.id from ck_sop_info sop where sop.journey_id = j.id) sop_id
        from tb_wx_ext_journey_info j
        where j.corp_id = #{corpId}
          and j.del_flag = 0
        order by j.order_num
    </select>

    <select id="getJourneyInfo" resultMap="externalJourneyMap">
        select j.id,
               j.journey_name,
               s.id sid,
               s.stage_name,
               s.stage_desc,
               ((select count(1) from tb_wx_ext_journey_customer_stage where stage_id = s.id and del_flag = 0) > 0)delete_remind
        from tb_wx_ext_journey_info j
                 left join tb_wx_ext_journey_stage s on j.id = s.journey_info_id
        where j.del_flag = 0
          and s.del_flag = 0
          and j.corp_id = #{corpId}
          and j.id = #{journeyId}
    </select>

    <resultMap id="detailMap" type="com.cenker.scrm.pojo.vo.journey.ExternalJourneyStageVO">
        <id column="id" property="stageId"/>
        <result column="stage_name" property="stageName"/>
        <result column="stage_desc" property="stageDesc"/>
        <collection property="row" ofType="com.cenker.scrm.pojo.vo.journey.ExternalJourneyCustomerVO">
            <id column="ext_customer_id" property="id"/>
            <result column="name" property="name"/>
            <result column="external_user_id" property="externalUserId"/>
            <result column="avatar" property="avatar"/>
            <result column="corp_name" property="corpName"/>
            <result column="type" property="type"/>
            <result column="status" property="status"/>
            <result column="add_user_name" property="addUserName"/>
        </collection>
    </resultMap>

    <select id="detail" resultMap="detailMap">
        select
            t.*,
            cs.ext_customer_id,
            c.external_user_id,
            c.name,
            c.type,
            c.avatar,
            c.corp_name,
            if((select sum(`status` = 0) from tb_wx_ext_follow_user where c.external_user_id = external_user_id and corp_id = c.corp_id)>0,0,2)`status`,
            (
                select twu.name from tb_wx_ext_follow_user u
                left join tb_wx_user twu on twu.userid = u.user_id and twu.corp_id = u.corp_id
                where u.external_user_id = c.external_user_id
                order by twu.del_flag,u.create_time
                limit 1
            )  add_user_name,
            cs.join_time
        from
        (
            select
                s.id,
                s.stage_name,
                s.stage_desc,
                s.order_num
            from tb_wx_ext_journey_stage s
            where s.journey_info_id = #{journeyId}
                and s.del_flag = 0
                and s.corp_id = #{corpId}
            order by s.order_num
        )t left join tb_wx_ext_journey_customer_stage cs on cs.stage_id = t.id and cs.del_flag = 0
        left join tb_wx_ext_customer c on c.id = cs.ext_customer_id
        <if test="name != null and name != ''">
            and (c.name like concat('%',#{name},'%') or c.corp_name like concat('%',#{name},'%'))
        </if>
        order by t.order_num,cs.update_time desc
    </select>

    <select id="getJourneySource" resultMap="externalJourneyMap">
        select j.id,
               j.journey_name,
               s.id sid,
               s.stage_name
        from tb_wx_ext_journey_info j
        left join tb_wx_ext_journey_stage s on j.id = s.journey_info_id
        where j.corp_id = #{corpId}
          and j.del_flag = 0
        order by j.order_num
    </select>

    <select id="selectStageNameByStageId" resultType="java.lang.String">
        SELECT
            concat( j.journey_name, ": ", s.stage_name ) AS stage_name
        FROM
            `tb_wx_ext_journey_stage` s
        JOIN tb_wx_ext_journey_info j ON s.journey_info_id = j.id
        where s.del_flag = 0 and j.del_flag = 0
        and s.id in
        <foreach collection="stageIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>