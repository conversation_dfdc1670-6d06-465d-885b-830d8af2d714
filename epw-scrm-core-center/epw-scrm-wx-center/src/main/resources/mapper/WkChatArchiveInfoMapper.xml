<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.chatarchive.WkChatArchiveInfoMapper">

    <select id="selectChatArchiveMessageCount" resultType="java.lang.Long">
        <if test="param.queryType == 1">
            select
                count(distinct c.msg_id)
            from
                wk_chat_archive_info c
            where
                c.msg_type != 9
                <if test="param.chatType != null">
                    and c.chat_type = #{param.chatType}
                </if>
                <if test="param.chatId != null">
                    <if test="param.chatType == 2">
                        and c.room_id = #{param.chatId}
                        <if test="param.isSelf != null and param.isSelf == 1">
                            and c.from_id = #{param.fromId}
                        </if>
                    </if>
                    <if test="param.chatType == 1">
                        and (c.from_id = #{param.fromId} and c.consume_id = #{param.chatId})
                    </if>
                </if>
                <if test="param.msgType != null">
                    and c.msg_type = #{param.msgType}
                </if>
                <if test="param.msgDate != null and param.msgDate != ''">
                    and c.msg_day = #{param.msgDate}
                </if>
                <if test="param.msgDateStart != null and param.msgDateStart != ''">
                    and c.msg_day &gt;= #{param.msgDateStart}
                </if>
                <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
                    and c.msg_day &lt;= #{param.msgDateEnd}
                </if>
                <if test="param.content != null and param.content != ''">
                    and c.msg_content like concat('%', #{param.content}, '%')
                </if>
                <if test="param.name != null and param.name !=''">
                    and ext.`name` like concat('%', #{param.name}, '%')
                </if>
        </if>
        <if test="param.queryType == 2">
            select
                count(distinct c.msg_id)
            from
                wk_chat_archive_info c
            where
                c.msg_type != 9
                <if test="param.chatType != null">
                    and c.chat_type = #{param.chatType}
                </if>
                <if test="param.chatId != null">
                    <if test="param.chatType == 2">
                        and c.room_id = #{param.chatId}
                        <if test="param.isSelf != null and param.isSelf == 1">
                            and c.from_id = #{param.fromId}
                        </if>
                    </if>
                    <if test="param.chatType == 1">
                        and (c.from_id = #{param.chatId} and c.consume_id = #{param.fromId})
                    </if>
                </if>
                <if test="param.msgType != null">
                    and c.msg_type = #{param.msgType}
                </if>
                <if test="param.msgDate != null and param.msgDate != ''">
                    and c.msg_day = #{param.msgDate}
                </if>
                <if test="param.msgDateStart != null and param.msgDateStart != ''">
                    and c.msg_day &gt;= #{param.msgDateStart}
                </if>
                <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
                    and c.msg_day &lt;= #{param.msgDateEnd}
                </if>
                <if test="param.content != null and param.content != ''">
                    and c.msg_content like concat('%', #{param.content}, '%')
                </if>
                <if test="param.name != null and param.name !=''">
                    and ext.`name` like concat('%', #{param.name}, '%')
                </if>
        </if>
    </select>

    <select id="selectChatArchiveMessage" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveMessageVo">
        <if test="param.queryType == 1">
            SELECT
                c.id,
                c.corp_id corpId,
                c.seq,
                c.msg_id msgId,
                c.chat_type chatType,
                c.msg_type msgType,
                c.action,
                c.from_id fromId,
                c.consume_id consumeId,
                c.room_id roomId,
                cg.group_name roomName,
                c.msg_time msgTime,
                c.msg_day msgDay,
                c.msg_content msgContent,
                c.create_time createTime,
                c.content_data content,
                c.file_url fileUrl,
                -- 获取属于撤回消息的消息标识
                (select count(1) from wk_chat_archive_info wcac where wcac.msg_type = 9 and wcac.pre_msg_id=c.msg_id) preMsg,
                <if test="param.sendType == 2">
                    ext.corp_name fromCorpName,
                </if>
                <if test="param.sendType == 1">
                    corp.company_name fromCorpName,
                </if>
                ext.`name` fromName,
                ext.avatar fromAvatar
            FROM
                wk_chat_archive_info c
                <if test="param.sendType == 1">
                    LEFT JOIN tb_wx_user ext ON ext.userid = c.from_id
                    LEFT JOIN tb_wx_corp_config corp on corp.corp_id = c.corp_id
                </if>
                <if test="param.sendType == 2">
                    LEFT JOIN tb_wx_ext_customer ext ON ext.external_user_id = c.from_id
                </if>
                LEFT JOIN tb_wx_customer_group cg on cg.chat_id = c.room_id
            WHERE
                c.msg_type != 9
                <if test="param.sendType == 1">
                    and ext.userid IS NOT NULL
                </if>
                <if test="param.sendType == 2">
                    and ext.external_user_id IS NOT NULL
                </if>

                <if test="param.chatType != null">
                    and c.chat_type = #{param.chatType}
                </if>
                <if test="param.chatId != null">
                    <if test="param.chatType == 2">
                        and c.room_id = #{param.chatId}
                        <if test="param.isSelf != null and param.isSelf == 1">
                            and c.from_id = #{param.fromId}
                        </if>
                    </if>
                    <if test="param.chatType == 1">
                        and (c.from_id = #{param.fromId} and c.consume_id = #{param.chatId})
                    </if>
                </if>
                <if test="param.msgType != null">
                    and c.msg_type = #{param.msgType}
                </if>
                <if test="param.msgDate != null and param.msgDate != ''">
                    and c.msg_day = #{param.msgDate}
                </if>
                <if test="param.msgDateStart != null and param.msgDateStart != ''">
                    and c.msg_day &gt;= #{param.msgDateStart}
                </if>
                <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
                    and c.msg_day &lt;= #{param.msgDateEnd}
                </if>
                <if test="param.content != null and param.content != ''">
                    and c.msg_content like concat('%', #{param.content}, '%')
                </if>
                <if test="param.name != null and param.name !=''">
                    and ext.`name` like concat('%', #{param.name}, '%')
                </if>
            GROUP By c.msg_id
            <if test="param.startNum != null">
                limit #{param.startNum}, #{param.pageSize}
            </if>
        </if>
        <if test="param.queryType == 2">
            SELECT
                c.id,
                c.corp_id corpId,
                c.seq,
                c.msg_id msgId,
                c.chat_type chatType,
                c.msg_type msgType,
                c.action,
                c.from_id fromId,
                c.consume_id consumeId,
                c.room_id roomId,
                cg.group_name roomName,
                c.msg_time msgTime,
                c.msg_day msgDay,
                c.msg_content msgContent,
                c.create_time createTime,
                c.content_data content,
                c.file_url fileUrl,
                -- 获取属于撤回消息的消息标识
                (select count(1) from wk_chat_archive_info wcac where wcac.msg_type = 9 and wcac.pre_msg_id=c.msg_id) preMsg,
                <if test="param.sendType == 1">
                    ext.corp_name fromCorpName,
                </if>
                <if test="param.sendType == 2">
                    corp.company_name fromCorpName,
                </if>
                ext.`name` fromName,
                ext.avatar fromAvatar
            FROM
                wk_chat_archive_info c
                <if test="param.sendType == 1">
                    LEFT JOIN tb_wx_ext_customer ext ON ext.external_user_id = c.from_id
                </if>
                <if test="param.sendType == 2">
                    LEFT JOIN tb_wx_user ext ON ext.userid = c.from_id
                    LEFT JOIN tb_wx_corp_config corp on corp.corp_id = c.corp_id
                </if>
                LEFT JOIN tb_wx_customer_group cg on cg.chat_id = c.room_id
            WHERE
                c.msg_type != 9
                <if test="param.sendType == 1">
                    and ext.external_user_id IS NOT NULL
                </if>
                <if test="param.sendType == 2">
                    and ext.userid IS NOT NULL
                </if>
                <if test="param.chatType != null">
                    and c.chat_type = #{param.chatType}
                </if>
                <if test="param.chatId != null">
                    <if test="param.chatType == 2">
                        and c.room_id = #{param.chatId}
                        <if test="param.isSelf != null and param.isSelf == 1">
                            and c.from_id = #{param.fromId}
                        </if>
                    </if>
                    <if test="param.chatType == 1">
                        and (c.from_id = #{param.chatId} and c.consume_id = #{param.fromId})
                    </if>
                </if>
                <if test="param.msgType != null">
                    and c.msg_type = #{param.msgType}
                </if>
                <if test="param.msgDate != null and param.msgDate != ''">
                    and c.msg_day = #{param.msgDate}
                </if>
                <if test="param.msgDateStart != null and param.msgDateStart != ''">
                    and c.msg_day &gt;= #{param.msgDateStart}
                </if>
                <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
                    and c.msg_day &lt;= #{param.msgDateEnd}
                </if>
                <if test="param.content != null and param.content != ''">
                    and c.msg_content like concat('%', #{param.content}, '%')
                </if>
                <if test="param.name != null and param.name !=''">
                    and ext.`name` like concat('%', #{param.name}, '%')
                </if>
            GROUP By c.msg_id
            <if test="param.startNum != null">
                limit #{param.startNum},#{param.pageSize}
            </if>
        </if>
    </select>

    <select id="selectPrivateChatMessage" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveMessageVo">
        SELECT
        c.id,
        c.corp_id corpId,
        c.seq,
        c.msg_id msgId,
        c.chat_type chatType,
        c.msg_type msgType,
        c.action,
        c.from_id fromId,
        c.consume_id consumeId,
        c.room_id roomId,
        c.msg_time msgTime,
        c.msg_day msgDay,
        c.msg_content msgContent,
        c.create_time createTime,
        c.content_data content,
        c.file_url fileUrl,
        -- 获取属于撤回消息的消息标识
        (select count(1) from wk_chat_archive_info wcac where wcac.msg_type = 9 and wcac.pre_msg_id=c.msg_id) preMsg,
        IF(ext.external_user_id IS NOT NULL, ext.corp_name, corp.company_name) AS fromCorpName,
        IF(ext.external_user_id IS NOT NULL, ext.`name`, u.`name`) AS fromName,
        IF(ext.external_user_id IS NOT NULL, ext.`avatar`, u.`avatar`) AS fromAvatar
        FROM
        wk_chat_archive_info c
        LEFT JOIN tb_wx_ext_customer ext ON ext.external_user_id = c.from_id OR ext.external_user_id = c.consume_id
        LEFT JOIN tb_wx_user u ON u.userid = c.from_id OR u.userid = c.consume_id
        LEFT JOIN tb_wx_corp_config corp on corp.corp_id = c.corp_id
        WHERE c.msg_type != 9
          and ((c.from_id = #{param.fromId} and c.consume_id = #{param.chatId}) OR (c.from_id = #{param.chatId} and c.consume_id = #{param.fromId}))
        <if test="param.chatType != null">
            and c.chat_type = #{param.chatType}
        </if>
        <if test="param.msgType != null">
            and c.msg_type = #{param.msgType}
        </if>
        <if test="param.msgDate != null and param.msgDate != ''">
            and c.msg_day = #{param.msgDate}
        </if>
        <if test="param.msgDateStart != null and param.msgDateStart != ''">
            and c.msg_day &gt;= #{param.msgDateStart}
        </if>
        <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
            and c.msg_day &lt;= #{param.msgDateEnd}
        </if>
        <if test="param.content != null and param.content != ''">
            and c.msg_content like concat('%', #{param.content}, '%')
        </if>
        GROUP By c.msg_id
        ORDER BY c.msg_time desc
        <if test="param.startNum != null">
            limit #{param.startNum},#{param.pageSize}
        </if>
    </select>

    <select id="selectGroupChatMessage" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveMessageVo">
        SELECT
            c.id,
            c.corp_id corpId,
            c.seq,
            c.msg_id msgId,
            c.chat_type chatType,
            c.msg_type msgType,
            c.action,
            c.from_id fromId,
            c.consume_id consumeId,
            c.room_id roomId,
            cg.group_name roomName,
            c.msg_time msgTime,
            c.msg_day msgDay,
            c.msg_content msgContent,
            c.create_time createTime,
            c.content_data content,
            c.file_url fileUrl,
            -- 获取属于撤回消息的消息标识
            (select count(1) from wk_chat_archive_info wcac where wcac.msg_type = 9 and wcac.pre_msg_id=c.msg_id) preMsg,
            IF(ext.external_user_id IS NOT NULL, ext.corp_name, corp.company_name) AS fromCorpName,
            IF(ext.external_user_id IS NOT NULL, ext.`name`, u.`name`) AS fromName,
            IF(ext.external_user_id IS NOT NULL, ext.`avatar`, u.`avatar`) AS fromAvatar
        FROM
        wk_chat_archive_info c
            LEFT JOIN tb_wx_customer_group cg on cg.chat_id = c.room_id
            LEFT JOIN tb_wx_ext_customer ext ON ext.external_user_id = c.from_id
            LEFT JOIN tb_wx_user u ON u.userid = c.from_id
            LEFT JOIN tb_wx_corp_config corp on corp.corp_id = c.corp_id
        WHERE c.msg_type != 9 and c.room_id = #{param.chatId}
        <if test="param.chatType != null">
            and c.chat_type = #{param.chatType}
        </if>
        <if test="param.isSelf != null and param.isSelf == 1">
            and c.from_id = #{param.fromId}
        </if>
        <if test="param.msgType != null">
            and c.msg_type = #{param.msgType}
        </if>
        <if test="param.msgDate != null and param.msgDate != ''">
            and c.msg_day = #{param.msgDate}
        </if>
        <if test="param.msgDateStart != null and param.msgDateStart != ''">
            and c.msg_day &gt;= #{param.msgDateStart}
        </if>
        <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
            and c.msg_day &lt;= #{param.msgDateEnd}
        </if>
        <if test="param.content != null and param.content != ''">
            and c.msg_content like concat('%', #{param.content}, '%')
        </if>
        GROUP By c.msg_id
        ORDER BY c.msg_time desc
        <if test="param.startNum != null">
            limit #{param.startNum},#{param.pageSize}
        </if>
    </select>

    <select id="selectChatMessageCount" resultType="java.lang.Long">
        SELECT
            count(distinct c.msg_id) as count
        FROM
        wk_chat_archive_info c
        WHERE c.msg_type != 9
        <if test="param.chatType != null">
            and c.chat_type = #{param.chatType}
        </if>
        <if test="param.chatType == 1">
            and ((c.from_id = #{param.fromId} and c.consume_id = #{param.chatId}) OR (c.from_id = #{param.chatId} and c.consume_id = #{param.fromId}))
        </if>
        <if test="param.chatType == 2">
            and c.room_id = #{param.chatId}
            <if test="param.isSelf != null and param.isSelf == 1">
                and c.from_id = #{param.fromId}
            </if>
        </if>
        <if test="param.chatType != null">
            and c.chat_type = #{param.chatType}
        </if>
        <if test="param.msgType != null">
            and c.msg_type = #{param.msgType}
        </if>
        <if test="param.msgDate != null and param.msgDate != ''">
            and c.msg_day = #{param.msgDate}
        </if>
        <if test="param.msgDateStart != null and param.msgDateStart != ''">
            and c.msg_day &gt;= #{param.msgDateStart}
        </if>
        <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
            and c.msg_day &lt;= #{param.msgDateEnd}
        </if>
        <if test="param.content != null and param.content != ''">
            and c.msg_content like concat('%', #{param.content}, '%')
        </if>
    </select>

    <select id="selectChatArchiveMessageBySendTypeCount" resultType="java.lang.Long">
        <if test="param.sendType == 1">
            SELECT
                count(distinct c.msg_id)
            FROM
                wk_chat_archive_info c
            LEFT JOIN tb_wx_user wu ON wu.userid = c.from_id
            WHERE wu.userid IS NOT NULL
            -- 过滤撤回的消息内容
            And c.msg_type != 9
            <if test="param.chatType != null">
                and c.chat_type = #{param.chatType}
            </if>
            <if test="param.msgType != null">
                and c.msg_type = #{param.msgType}
            </if>
            <if test="param.msgDate != null and param.msgDate != ''">
                and c.msg_day = #{param.msgDate}
            </if>
            <if test="param.msgDateStart != null and param.msgDateStart != ''">
                and c.msg_day &gt;= #{param.msgDateStart}
            </if>
            <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
                and c.msg_day &lt;= #{param.msgDateEnd}
            </if>
            <if test="param.content != null and param.content != ''">
                and c.msg_content like concat('%', #{param.content}, '%')
            </if>
            <if test="param.name != null and param.name !=''">
                and wu.`name` like concat('%', #{param.name}, '%')
            </if>
        </if>
        <if test="param.sendType == 2">
            SELECT
                count(distinct c.msg_id)
            FROM
                wk_chat_archive_info c
            LEFT JOIN tb_wx_ext_customer ext ON ext.external_user_id = c.from_id
            WHERE ext.external_user_id IS NOT NULL And c.msg_type != 9
            <if test="param.msgType != null">
                and c.msg_type = #{param.msgType}
            </if>
            <if test="param.msgDate != null and param.msgDate != ''">
                and c.msg_day = #{param.msgDate}
            </if>
            <if test="param.msgDateStart != null and param.msgDateStart != ''">
                and c.msg_day &gt;= #{param.msgDateStart}
            </if>
            <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
                and c.msg_day &lt;= #{param.msgDateEnd}
            </if>
            <if test="param.content != null and param.content != ''">
                and c.msg_content like concat('%', #{param.content}, '%')
            </if>
            <if test="param.name != null and param.name !=''">
                and ext.`name` like concat('%', #{param.name}, '%')
            </if>
        </if>
    </select>

    <select id="selectChatArchiveMessageBySendType" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveMessageVo">
        <if test="param.sendType == 1">
            SELECT
                c.id,
                c.corp_id corpId,
                c.seq,
                c.msg_id msgId,
                c.chat_type chatType,
                c.msg_type msgType,
                c.action,
                c.room_id roomId,
                cg.group_name roomName,
                c.msg_time msgTime,
                c.msg_day msgDay,
                c.msg_content msgContent,
                c.create_time createTime,
                c.content_data content,
                c.file_url fileUrl,
                c.from_id fromId,
                corp.company_name fromCorpName,
                wu.`name` fromName,
                wu.avatar fromAvatar,
                c.consume_id consumeId,
                IF(ext.external_user_id IS NULL, cu.`name`, ext.`name`) AS consumeName,
                IF(ext.external_user_id IS NULL, cu.avatar, ext.avatar) AS consumeAvatar,
                IF(ext.external_user_id IS NULL, corp.company_name, ext.corp_name) AS consumeCorpName,
                -- 获取属于撤回消息的消息标识
                (select count(1) from wk_chat_archive_info wcac where wcac.msg_type = 9 and wcac.pre_msg_id=c.msg_id) preMsg
            FROM
                wk_chat_archive_info c
            JOIN tb_wx_user wu ON wu.userid = c.from_id
            LEFT JOIN tb_wx_corp_config corp on corp.corp_id = c.corp_id
            LEFT JOIN tb_wx_customer_group cg on cg.chat_id = c.room_id
            LEFT JOIN tb_wx_ext_customer ext ON ext.external_user_id = c.consume_id
            LEFT JOIN tb_wx_user cu ON cu.userid = c.consume_id
            WHERE wu.userid IS NOT NULL
                -- 过滤撤回的消息内容
                And c.msg_type != 9
            <if test="param.chatType != null">
                and c.chat_type = #{param.chatType}
            </if>
            <if test="param.msgType != null">
                and c.msg_type = #{param.msgType}
            </if>
            <if test="param.msgDate != null and param.msgDate != ''">
                and c.msg_day = #{param.msgDate}
            </if>
            <if test="param.msgDateStart != null and param.msgDateStart != ''">
                and c.msg_day &gt;= #{param.msgDateStart}
            </if>
            <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
                and c.msg_day &lt;= #{param.msgDateEnd}
            </if>
            <if test="param.content != null and param.content != ''">
                and c.msg_content like concat('%', #{param.content}, '%')
            </if>
            <if test="param.name != null and param.name !=''">
                and wu.`name` like concat('%', #{param.name}, '%')
            </if>
            GROUP By c.msg_id
            ORDER BY c.msg_time DESC
            <if test="param.startNum != null">
                limit #{param.startNum},#{param.pageSize}
            </if>
        </if>
        <if test="param.sendType == 2">
            SELECT
                c.id,
                c.corp_id corpId,
                c.seq,
                c.msg_id msgId,
                c.chat_type chatType,
                c.msg_type msgType,
                c.action,
                c.room_id roomId,
                cg.group_name roomName,
                c.msg_time msgTime,
                c.msg_day msgDay,
                c.msg_content msgContent,
                c.create_time createTime,
                c.content_data content,
                c.file_url fileUrl,
                c.from_id fromId,
                ext.corp_name fromCorpName,
                ext.`name` fromName,
                ext.avatar fromAvatar,
                c.consume_id consumeId,
                wu.name consumeName,
                wu.avatar consumeAvatar,
                corp.company_name consumeCorpName,
                (select count(1) from wk_chat_archive_info wcac where wcac.msg_type = 9 and wcac.pre_msg_id=c.msg_id) preMsg
            FROM
                wk_chat_archive_info c
            LEFT JOIN tb_wx_ext_customer ext ON ext.external_user_id = c.from_id
            LEFT JOIN tb_wx_user wu ON wu.userid = c.consume_id
            LEFT JOIN tb_wx_corp_config corp on corp.corp_id = c.corp_id
            LEFT JOIN tb_wx_customer_group cg on cg.chat_id = c.room_id
            WHERE ext.external_user_id IS NOT NULL And c.msg_type != 9
            <if test="param.msgType != null">
                and c.msg_type = #{param.msgType}
            </if>
            <if test="param.msgDate != null and param.msgDate != ''">
                and c.msg_day = #{param.msgDate}
            </if>
            <if test="param.msgDateStart != null and param.msgDateStart != ''">
                and c.msg_day &gt;= #{param.msgDateStart}
            </if>
            <if test="param.msgDateEnd != null and param.msgDateEnd != ''">
                and c.msg_day &lt;= #{param.msgDateEnd}
            </if>
            <if test="param.content != null and param.content != ''">
                and c.msg_content like concat('%', #{param.content}, '%')
            </if>
            <if test="param.name != null and param.name !=''">
                and ext.`name` like concat('%', #{param.name}, '%')
            </if>
            GROUP By c.msg_id
            ORDER BY c.msg_time DESC
            <if test="param.startNum != null">
                limit #{param.startNum},#{param.pageSize}
            </if>
        </if>
    </select>

    <!--查询客户维度的开启会话存档的客户列表-->
    <select id="selectExtCustomerListByPage" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveConsumeVo">
        select
            ext.external_user_id id,
            ext.name,
            ext.avatar,
            ext.corp_name corpName
        from
            tb_wx_ext_customer ext
        left join wk_chat_archive_relation r on r.from_id = ext.external_user_id
        where 1 = 1 and r.from_id is not null
        <if test="param.name != null and param.name !='' ">
            and ext.`name` like concat('%', #{param.name}, '%')
        </if>
        group by ext.external_user_id
        order by ext.external_user_id
    </select>

    <!--查询员工维度的客户会话列表-->
    <select id="selectChatExtCustomerListByUser" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveConsumeVo">
        select
            ext.external_user_id id,
            ext.name,
            ext.avatar,
            ext.corp_name corpName,
            ca.chat_type chatType,
            ext.create_time createTime
        from
            wk_chat_archive_relation ca
        left join tb_wx_ext_customer ext on ext.external_user_id = ca.consume_id
        where ext.external_user_id is not null
            and ca.from_id = #{chatId}
            and ca.chat_type = 1
            <if test="name != null and name !='' ">
                and ext.`name` like concat('%', #{name}, '%')
            </if>
        group by ext.external_user_id

        union

        select
            ext.external_user_id id,
            ext.name,
            ext.avatar,
            ext.corp_name corpName,
            ca.chat_type chatType,
            ext.create_time createTime
        from
            wk_chat_archive_relation ca
        left join tb_wx_ext_customer ext on ext.external_user_id = ca.from_id
        where ext.external_user_id is not null
            and ca.consume_id = #{chatId}
            and ca.chat_type = 1
            <if test="name != null and name !='' ">
                and ext.`name` like concat('%', #{name}, '%')
            </if>
        group by ext.external_user_id
    </select>

    <!--查询客户维度的员工会话列表-->
    <select id="selectChatUserListByExtCustomer" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveConsumeVo">
        select
            wu.userid id,
            wu.name,
            wu.avatar,
            ca.chat_type chatType,
            wu.create_time createTime,
            corp.company_name corpName
        from
            wk_chat_archive_relation ca
        left join tb_wx_user wu on wu.userid = ca.consume_id
        left join tb_wx_corp_config corp on corp.corp_id = ca.corp_id
        where wu.userid is not null
            and ca.from_id = #{chatId}
            and ca.chat_type = 1
            <if test="name != null and name !='' ">
                and wu.`name` like concat('%', #{name}, '%')
            </if>
        group by wu.userid

        union

        select
            wu.userid id,
            wu.name,
            wu.avatar,
            ca.chat_type chatType,
            wu.create_time createTime,
            corp.company_name corpName
        from
            wk_chat_archive_relation ca
        left join tb_wx_user wu on wu.userid = ca.from_id
        left join tb_wx_corp_config corp on corp.corp_id = ca.corp_id
        where wu.userid is not null
            and ca.consume_id = #{chatId}
            and ca.chat_type = 1
            <if test="name != null and name !='' ">
                and wu.`name` like concat('%', #{name}, '%')
            </if>
        group by wu.userid
    </select>

    <!--获取指定人员所在群聊-->
    <select id="selectChatRoomList" resultType="com.cenker.scrm.pojo.vo.chatarchive.ChatArchiveConsumeVo">
        select
            cg.chat_id id,
            cg.group_name name,
            ca.chat_type chatType
        from
            wk_chat_archive_relation ca
        left join tb_wx_customer_group cg on cg.chat_id = ca.room_id
        left join tb_wx_customer_group_member cgm on cgm.group_id = cg.chat_id
        where cgm.user_id = #{chatId}
            and ca.chat_type = 2
            <if test="name != null and name !='' ">
                and cg.`group_name` like concat('%', #{name}, '%')
            </if>
        group by ca.room_id
        order by cg.create_time
    </select>
    
    <select id="selectChatTimeList" resultType="java.lang.String">
        <if test="chatType==1">
            <if test="queryType == 1">
                select
                    msg_day
                from
                    wk_chat_archive_info
                where from_id =#{fromId} and consume_id =#{chatId}
                group by msg_day
            </if>

            <if test="queryType == 2">
                select
                    msg_day
                from
                    wk_chat_archive_info
                where from_id =#{chatId} and consume_id =#{fromId}
                group by msg_day
            </if>
        </if>
        <if test="chatType == 2">
            select
                msg_day
            from
                wk_chat_archive_info
            where room_id = #{chatId}
            <if test="isSelf != null and isSelf == 1">
                and from_id = #{fromId}
            </if>
            group by msg_day
        </if>
    </select>

    <select id="selectMsgByHotId" resultType="com.cenker.scrm.pojo.entity.chatarchive.WkChatArchiveInfo">
        SELECT
            c.*
        FROM
            wk_chat_archive_info c
        WHERE
            c.msg_time between #{beginTime} and #{endTime}
          AND EXISTS (
                SELECT
                    1
                FROM
                    ck_session_hot_check_mapping m
                        LEFT JOIN tb_wx_customer_group g ON g.`owner` = m.check_user_id
                WHERE
                    m.hot_id = #{hotId}
                  AND ( c.from_id = m.check_user_id OR c.consume_id = m.check_user_id OR c.room_id = g.chat_id )
            )
        ORDER BY
            c.msg_time ASC
    </select>

</mapper>