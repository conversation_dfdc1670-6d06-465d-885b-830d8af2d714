<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.radar.TbWxRadarContentRecordMapper">
   <select id="getReadRecordByParamId" resultType="TbWxRadarContentRecord">
       select
           count(1) total_read_num,
           ifnull(sum(r.read_time),0) total_read_time
       from tb_wx_radar_content_record r
       where r.content_id = #{contentId}
         <if test="staffId!= null and staffId!= ''">
         and r.staff_id = #{staffId}
         </if>
         and r.customer_id = #{customerId}
   </select>

    <select id="getEndDateByData" resultType="java.util.Date">
        select max(create_time) from tb_wx_radar_content_record where content_id = #{id}
    </select>

    <select id="getRadarReadRecordDetail" resultType="RadarCustomerDetailVo">
        select
            concat('第',(@i:=@i+1),'次') num,
            read_rate,
            (case when read_time &lt; 60 then concat(read_time,'s')
                  when read_time > 60 and read_time &lt; 3600 then concat(round(floor(read_time/60),0),'m',read_time - (round(floor(read_time/60),0)*60),'s')
                  when read_time >= 3600 then concat(round(read_time/3600,0),'h+')
                end) read_time,
            create_time
        from tb_wx_radar_content_record,(SELECT @i:=#{pageSize} * #{pageNum}) as i
        where content_id = #{contentId}
          and customer_id = #{customerId}
        order by create_time
    </select>

    <select id="getRadarReadRecordDetail4Chat" resultType="RadarCustomerDetailVo">
        select
        read_rate,
        (case when read_time &lt; 60 then concat(read_time,'s')
        when read_time > 60 and read_time &lt; 3600 then concat(round(floor(read_time/60),0),'m',read_time - (round(floor(read_time/60),0)*60),'s')
        when read_time >= 3600 then concat(round(read_time/3600,0),'h+')
        end) read_time,
        create_time,
        (case when date_format(create_time, '%Y-%m-%d') = curdate() then concat('今天 ',date_format(create_time, '%H:%i:%S'))
              when date_format(create_time, '%Y-%m-%d') = date_add(date_format(curdate(),'%Y-%m-%d'), interval -1 day) then concat('昨天 ',date_format(create_time, '%H:%i:%S'))
              else create_time end
            ) create_time_str
        from tb_wx_radar_content_record
        where content_id = #{contentId}
        and customer_id = #{customerId}
        order by create_time desc
    </select>

    <select id="sumForWordCount" resultType="java.lang.Integer">
        select ifnull(sum(forward_num),0) from tb_wx_radar_content_record
        where content_id = #{contentId}
          <if test="staffId!= null and staffId!= ''">
          and staff_id = #{staffId}
          </if>
          and customer_id = #{customerId}
    </select>

    <select id="countCorpInteractByExtUserId" resultType="CorpInteractVo">
        select
        if(click_cnt > 9999,'9999+',click_cnt) click_cnt,
        ifnull(if(forward_cnt > 9999,'9999+',forward_cnt),0) forward_cnt,
        ifnull((case when read_time &lt; 60 then concat(read_time,'秒')
        when read_time > 60 and read_time &lt; 3600 then concat(round(floor(read_time/60),0),'分钟') -- ,read_time - (round(floor(read_time/60),0)*60),'秒'
        when read_time >= 3600 then concat(round(read_time/3600,1),'小时')
        end),0) read_time
        from (
        select
        count(distinct r.id)click_cnt,
        sum(r.forward_num)forward_cnt,
        sum(read_time)read_time
        from tb_wx_ext_customer c
        left join mp_wx_user u on c.name = u.nick_name
        left join tb_wx_radar_content_record r on r.customer_id = u.id
        left join tb_wx_radar_content rc on rc.id = r.content_id
        left join tb_wx_radar_interact i on i.id = rc.radar_id
        where c.external_user_id = #{extUserId}
        and i.corp_id = c.corp_id
        and c.corp_id = #{corpId}
        )t
    </select>

    <resultMap id="testRepairLinkVoAddTypeMap" type="TbWxCustomerTrajectory">
        <id property="id" column="id"/>
        <result property="customerTrajectoryContentVo" column="content"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
    </resultMap>

    <select id="testRepairLinkVoAddType" resultMap="testRepairLinkVoAddTypeMap">
        select
            id,
            content
        from tb_wx_customer_trajectory
        where trajectory_type  != 4
          and behavior_type = 3
          and content like '%MB%'
    </select>

    <select id="allSelectViewCondition4Sop" resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        select
            t.id,
            t.external_user_id
        from (
                 select
                     c.id,
                     r.customer_id,
                     c.external_user_id,
                     count(1) view_count,
                     sum(r.read_time) read_time,
                     sum(r.forward_num) forward_num
                 from tb_wx_radar_content_record r
                          join mp_wx_user u on r.customer_id = u.id
                          join tb_wx_ext_customer c on u.union_id = c.union_id
                 where c.union_id is not null
                 and c.external_user_id in
                 <foreach item="item" collection="triggerConditionValue.customerList" open="(" separator="," close=")">
                     #{item.externalUserId}
                 </foreach>
                 group by r.customer_id
             )t
              <where>
                  <if test="triggerConditionType == 3 and triggerRelationType == 6">
                      and t.forward_num &lt; #{triggerConditionValue.conditionCnt}
                  </if>
                  <if test="triggerConditionType == 3 and triggerRelationType == 7">
                      and t.forward_num >= #{triggerConditionValue.conditionCnt}
                  </if>
                  <if test="triggerConditionType == 4 and triggerRelationType == 6">
                      and t.view_count &lt; #{triggerConditionValue.conditionCnt}
                  </if>
                  <if test="triggerConditionType == 4 and triggerRelationType == 7">
                      and t.view_count >= #{triggerConditionValue.conditionCnt}
                  </if>
                  <if test="triggerConditionType == 5 and triggerRelationType == 6">
                      and t.read_time &lt; #{triggerConditionValue.conditionCnt}
                  </if>
                  <if test="triggerConditionType == 5 and triggerRelationType == 7">
                      and t.read_time >= #{triggerConditionValue.conditionCnt}
                  </if>
              </where>
    </select>

    <select id="assignSelectViewAndForward4Sop"
            resultType="com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO">
        select
            c.id,
            c.external_user_id
        from tb_wx_radar_content_record r
                 join tb_wx_radar_content content on content.id = r.content_id
                 join tb_wx_radar_interact i on content.radar_id = i.id
                 join mp_wx_user u on r.customer_id = u.id
                 join tb_wx_ext_customer c on u.union_id = c.union_id
        where c.union_id is not null
        <if test="triggerRelationType == 1">
            and i.id = #{triggerConditionValue.radarConditionList[0].radarId}
        </if>
        <if test="triggerRelationType == 2">
            and i.id != #{triggerConditionValue.radarConditionList[0].radarId}
        </if>
        <if test="triggerRelationType == 3">
            and i.id in
            <foreach item="item" collection="triggerConditionValue.radarConditionList" open="(" separator="," close=")">
                #{item.radarId}
            </foreach>
        </if>
        <if test="triggerRelationType == 4">
            and i.id not in
            <foreach item="item" collection="triggerConditionValue.radarConditionList" open="(" separator="," close=")">
                #{item.radarId}
            </foreach>
        </if>
        and c.external_user_id in
        <foreach item="item" collection="triggerConditionValue.customerList" open="(" separator="," close=")">
            #{item.externalUserId}
        </foreach>
        <if test="triggerConditionType == 2">
            and r.forward_num > 0
        </if>
        group by r.customer_id
    </select>

    <update id="updateStaffId">
         update tb_wx_radar_content_record set staff_id = #{openUserId}
         where content_id in (select id from tb_wx_radar_content where radar_id in (select id from tb_wx_radar_interact where corp_id = #{corpId}))
         and staff_id = #{userId}
    </update>
</mapper>