<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cenker.scrm.mapper.MpWxCustomerAuthRecordMapper">

    <select id="selectAuthRecoreds" resultType="com.cenker.scrm.entity.MpWxCustomerAuthRecord">
        select
        c.union_Id,
        r.cust_No,
        r.app_Code,
        r.is_Auth,
        r.update_Time,
        r.real_name
        from
        tb_wx_ext_customer c
        join mp_wx_customer_auth_record r on r.union_id = c.union_id
        <where>
            <if test="lastDate!= null and lastDate!= ''">
                and r.sync_Time >= #{lastDate}
            </if>
            <if test="now!= null and now!= ''">
                and r.sync_time &lt;= #{now}
            </if>
        </where>
    </select>
</mapper>