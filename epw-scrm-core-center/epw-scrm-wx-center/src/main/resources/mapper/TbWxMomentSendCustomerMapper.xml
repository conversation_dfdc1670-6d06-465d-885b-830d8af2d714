<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.moment.TbWxMomentSendCustomerMapper">

    <select id="countCorpInteractByExtUserId" resultType="CorpInteractVo">
        select
            if(sum(type = 1)>9999,'9999+',ifnull(sum(type = 1),0))like_cnt,
            if(sum(type = 2)>9999,'9999+',ifnull(sum(type = 2),0))comment_cnt
        from tb_wx_moment_interact
        where external_user_id = #{extUserId}
          and corp_id = #{corpId}
    </select>

</mapper>