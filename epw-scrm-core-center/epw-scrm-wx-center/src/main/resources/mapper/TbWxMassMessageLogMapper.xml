<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.message.TbWxCorpMassMessageLogMapper">
    <select id="queryExistMessageId" resultType="MassWxSenderVO">
      select DISTINCT message_id wxMessageId,message_info_id messageInfoId from tb_wx_corp_mass_message_log t
      where t.corp_id = #{corpId} and t.message_id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        UNION ALL
        select distinct msg_id wxMessageId,id messageInfoId from tb_wx_corp_mass_message_info where corp_id = #{corpId}
        and msg_id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryExistUserId" resultType="MassWxSenderVO">
        select distinct message_id wxMessageId,user_id userId from tb_wx_corp_mass_message_log t
        where t.corp_id = #{corpId} and t.message_id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <update id="updateActualSendNum">
        update tb_wx_corp_mass_message_info set actual_send = (select count(id) from tb_wx_corp_mass_message_log where message_info_id = #{messageInfoId} and status !=0 )
        where id=#{messageInfoId}
    </update>
</mapper>