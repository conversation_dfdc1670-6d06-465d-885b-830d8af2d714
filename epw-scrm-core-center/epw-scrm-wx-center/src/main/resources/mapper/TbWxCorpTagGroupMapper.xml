<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.tag.TbWxCorpTagGroupMapper">

    <resultMap type="com.cenker.scrm.pojo.entity.wechat.TbWxCorpTagGroup" id="TbWxCorpTagGroupResult">
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="order" column="order"/>
        <result property="corpId" column="corp_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
        <result property="synStatus" column="syn_status"/>
        <result property="categoryId" column="category_id"/>
        <result property="groupTagType" column="group_tag_type"/>
    </resultMap>

    <sql id="selectTbWxCorpTagGroupVo">
        select group_id,
               group_name,
               create_by,
               create_time,
               status,
               `order`,
               corp_id,
               update_time,
               update_by,
               remark,
               syn_status,
               category_id,
               group_tag_type
        from tb_wx_corp_tag_group
    </sql>

    <select id="selectTbWxCorpTagGroupList" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxCorpTagGroup" resultMap="TbWxCorpTagGroupResult">
        select * from (
        select
        g.group_id,
        g.group_name,
        if(g.create_by = 'SYS','微信同步生成',u.nick_name)create_by,
        g.create_time,
        g.status,
        g.`order`,
        g.corp_id,
        g.update_time,
        g.update_by,
        g.remark,
        g.syn_status,
        g.category_id,
        g.group_tag_type
        from tb_wx_corp_tag_group g
        left join sys_user u on g.create_by = u.user_id and u.corp_id = #{corpId}
        where 1=1
        <if test="groupId != null  and groupId != ''">
            and g.group_id = #{groupId}
        </if>
        <if test="groupName != null  and groupName != ''">
            and g.group_name like concat('%', #{groupName}, '%')
        </if>
        <if test="status != null  and status != ''">
            and g.status = #{status}
        </if>
        <if test="corpId != null  and corpId != ''">
            and g.corp_id = #{corpId}
        </if>
        <if test="synStatus != null  and synStatus != ''">
            and g.syn_status = #{synStatus}
        </if>
        <if test="groupTagType != null  and groupTagType != ''">
            and g.group_tag_type = #{groupTagType}
        </if>
        <if test="categoryScope != null and categoryScope == 'NONE'">
            and g.category_id is null
        </if>
        <if test="categoryScope != null and categoryScope == 'ALL'">
            and (g.category_id is null
            <if test="categoryIdList != null  and categoryIdList.size() > 0">
                or g.category_id in
                <foreach item="item" collection="categoryIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>)
        </if>
        <if test="categoryScope != null and categoryScope == 'CUSTOM'">
            <if test="categoryIdList != null  and categoryIdList.size() > 0">
                and g.category_id in
                <foreach item="item" collection="categoryIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        order by  g.`order`,g.create_time desc
        )t
        <where>
            <if test="createBy != null  and createBy != ''">
                and t.create_by like concat('%',#{createBy}, '%')
            </if>
        </where>
    </select>

    <select id="selectTbWxCorpTagGroupById" parameterType="String"
            resultMap="TbWxCorpTagGroupResult">
        <include refid="selectTbWxCorpTagGroupVo"/>
        where group_id = #{groupId}
    </select>

    <insert id="insertTbWxCorpTagGroup" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxCorpTagGroup">
        insert into tb_wx_corp_tag_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupId != null">group_id,
            </if>
            <if test="groupName != null">group_name,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="status != null">status,
            </if>
            <if test="order != null">order,
            </if>
            <if test="corpId != null">corp_id,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="remark != null">remark,
            </if>
            <if test="synStatus != null">syn_status,
            </if>
            <if test="categoryId != null">category_id,</if>
            <if test="groupTagType != null">group_tag_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupId != null">#{groupId},
            </if>
            <if test="groupName != null">#{groupName},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="order != null">#{order},
            </if>
            <if test="corpId != null">#{corpId},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="remark != null">#{remark},
            </if>
            <if test="synStatus != null">#{synStatus},
            </if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="groupTagType != null">#{groupTagType},</if>
        </trim>
    </insert>

    <update id="updateTbWxCorpTagGroup" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxCorpTagGroup">
        update tb_wx_corp_tag_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupName != null">group_name =
                #{groupName},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="status != null">status =
                #{status},
            </if>
            <if test="order != null">order =
                #{order},
            </if>
            <if test="corpId != null">corp_id =
                #{corpId},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="remark != null">remark =
                #{remark},
            </if>
            <if test="synStatus != null">syn_status =
                #{synStatus},
            </if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="groupTagType != null">group_tag_type = #{groupTagType},</if>
        </trim>
        where group_id = #{groupId}
    </update>

    <delete id="deleteTbWxCorpTagGroupById" parameterType="String">
        delete
        from tb_wx_corp_tag_group
        where group_id = #{groupId}
    </delete>

    <delete id="deleteTbWxCorpTagGroupByIds" parameterType="String">
        delete from tb_wx_corp_tag_group where group_id in
        <foreach item="groupId" collection="array" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>

    <select id="selectTagGroupListName" resultType="java.lang.String">
        select group_name
        from tb_wx_corp_tag_group
        where corp_id = #{corpId}
          and `status` = 0
    </select>

    <select id="selectTbWxCorpTagGroupNameByTagId" resultType="java.lang.String">
        select group_name
        from tb_wx_corp_tag_group
        where  group_id = (select tb_wx_corp_tag.group_id from tb_wx_corp_tag where tag_id = #{tag_id} limit 1)
    </select>

    <select id="selectTbWxCorpTagByTagId" resultType="com.cenker.scrm.pojo.vo.tag.ExternalUserTagVO">
        select
            t.name tag_name,
            g.group_name
        from tb_wx_corp_tag t
                 left join tb_wx_corp_tag_group g on g.group_id = t.group_id
        where t.tag_id = #{tagId}
          and t.corp_id = #{corpId}
    </select>
</mapper>