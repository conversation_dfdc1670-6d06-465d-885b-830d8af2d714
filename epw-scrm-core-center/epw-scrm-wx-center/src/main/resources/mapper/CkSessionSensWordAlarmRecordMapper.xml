<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.session.CkSessionSensWordAlarmRecordMapper">
    
    <resultMap type="CkSessionSensWordAlarmRecord" id="CkSessionSensWordAlarmRecordResult">
        <result property="id"    column="id"    />
        <result property="contentValue"    column="content_value"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="sensitiveWord"    column="sensitive_word"    />
        <result property="sendUserId"    column="send_user_id"    />
        <result property="acceptUserId"    column="accept_user_id"    />
        <result property="sendUserType"    column="send_user_type"    />
        <result property="acceptUserType"    column="accept_user_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="triggerTime"    column="trigger_time"    />
        <result property="msgId"    column="msg_id"    />
        <result property="chatType"    column="chat_type"    />
        <result property="roomId"    column="room_id"    />
        <result property="roomConsumeId"    column="room_consume_id"    />

        <result property="ruleName"    column="rule_name"    />
        <result property="sendUserName"    column="send_user_name"    />
        <result property="acceptUserName"    column="accept_user_name"    />
        <result property="sendCorpName"    column="send_corp_name"    />
        <result property="acceptCorpName"    column="accept_corp_name"    />
    </resultMap>

    <sql id="selectCkSessionSensWordAlarmRecordVo">
        select * from (
                          SELECT
                              d.*,
                              IF(send_user_type='2',
                                 (select wx.name from  tb_wx_ext_customer wx where wx.external_user_id = d.send_user_id limit 1),
                                 (select wx.name from  tb_wx_user wx where wx.userid = d.send_user_id limit 1)) as send_user_name,
                              IF(send_user_type='2',
                                 (select wx.corp_name from  tb_wx_ext_customer wx where wx.external_user_id = d.send_user_id limit 1),
                                (select g.company_name from  tb_wx_user wx,tb_wx_corp_config g where wx.userid = d.send_user_id  and wx.corp_id=g.corp_id  limit 1)) as send_corp_name,

                              IF(accept_user_type='2',
                                 (select wx.name from  tb_wx_ext_customer wx where wx.external_user_id = d.accept_user_id limit 1),
		(IF(accept_user_type ='1',
		(select wx.name from  tb_wx_user wx where wx.userid = d.accept_user_id limit 1),
(select wx.group_name from  tb_wx_customer_group wx where wx.chat_id = d.room_id limit 1)))) as accept_user_name,
                              IF(accept_user_type='2',
                                 (select wx.corp_name from  tb_wx_ext_customer wx where wx.external_user_id = d.accept_user_id limit 1),
	 if(accept_user_type ='1',(select g.company_name from  tb_wx_user wx,tb_wx_corp_config g where wx.userid = d.accept_user_id  and wx.corp_id=g.corp_id  limit 1),
	 (select r.name from  tb_wx_customer_group wx,tb_wx_user r where wx.chat_id = d.room_id and wx.owner =r.userid limit 1)
	 )
                                ) as accept_corp_name,
                              r.rule_name
                          from ck_session_sens_word_alarm_record d,ck_session_sens_rule_info r
                          where d.rule_id = r.rule_id
                      ) o
    </sql>

    <select id="selectCkSessionSensWordAlarmRecordList" parameterType="QryWordAlarmDto" resultMap="CkSessionSensWordAlarmRecordResult">
        SELECT * FROM (
        SELECT
        d.*,
        r.rule_name,
        IF(d.send_user_type = '2', u1.name, c1.`name`) as send_user_name,
        IF(d.send_user_type = '1', c1.corp_name, null) as send_corp_name,
        IF(d.accept_user_type = '3', g.group_name, IFNULL(u2.name, c2.`name`)) as accept_user_name,
        IF(d.accept_user_type = '3', '群聊', c2.corp_name) as accept_corp_name
        FROM
        ck_session_sens_word_alarm_record d
        LEFT JOIN ck_session_sens_rule_info r ON r.rule_id = d.rule_id
        LEFT JOIN tb_wx_user u1 ON u1.userid = d.send_user_id
        LEFT JOIN tb_wx_ext_customer c1 ON c1.external_user_id = d.send_user_id
        LEFT JOIN tb_wx_user u2 ON u2.userid = d.accept_user_id
        LEFT JOIN tb_wx_ext_customer c2 ON c2.external_user_id = d.accept_user_id
        LEFT JOIN tb_wx_customer_group g ON g.chat_id = d.room_id
        ) o
        <where>
        <if test="sensitiveWord != null  and sensitiveWord != ''"> and  o.sensitive_word  like concat('%',#{sensitiveWord},'%' )</if>
        <if test="ruleName != null  and ruleName != ''"> and o.rule_name  like concat('%', #{ruleName}, '%')</if>
        <if test="sendName != null  and sendName != ''"> and o.send_user_name  like concat('%', #{sendName}, '%')</if>
        <if test="acceptName != null  and acceptName != ''"> and o.accept_user_name  like concat('%', #{acceptName}, '%')</if>
        <if test="beginTime != null  and beginTime != ''">  and <![CDATA[ DATE_FORMAT(o.trigger_time,'%Y-%m-%d')  >= #{beginTime}]]></if>
        <if test="endTime != null  and endTime != ''">  and <![CDATA[ DATE_FORMAT(o.trigger_time,'%Y-%m-%d')  <= #{endTime}]]></if>
        </where>
      </select>
        
    <insert id="insertCkSessionSensWordAlarmRecord" parameterType="CkSessionSensWordAlarmRecord" useGeneratedKeys="true" keyProperty="id">
        insert into ck_session_sens_word_alarm_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contentValue != null and contentValue != ''">content_value,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="sensitiveWord != null">sensitive_word,</if>
            <if test="sendUserId != null">send_user_id,</if>
            <if test="acceptUserId != null">accept_user_id,</if>
            <if test="sendUserType != null">send_user_type,</if>
            <if test="acceptUserType != null">accept_user_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="triggerTime != null">trigger_time,</if>
            <if test="msgId != null">msg_id,</if>
            <if test="chatType != null">chat_type,</if>
            <if test="roomId != null">room_id,</if>
            <if test="roomConsumeId != null">room_consume_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contentValue != null and contentValue != ''">#{contentValue},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="sensitiveWord != null">#{sensitiveWord},</if>
            <if test="sendUserId != null">#{sendUserId},</if>
            <if test="acceptUserId != null">#{acceptUserId},</if>
            <if test="sendUserType != null">#{sendUserType},</if>
            <if test="acceptUserType != null">#{acceptUserType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="triggerTime != null">#{triggerTime},</if>
            <if test="msgId != null">#{msgId},</if>
            <if test="chatType != null">#{chatType},</if>
            <if test="roomId != null">#{roomId},</if>
            <if test="roomConsumeId != null">#{roomConsumeId},</if>

        </trim>
    </insert>

    <update id="updateCkSessionSensWordAlarmRecord" parameterType="CkSessionSensWordAlarmRecord">
        update ck_session_sens_word_alarm_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="contentValue != null and contentValue != ''">content_value = #{contentValue},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="sensitiveWord != null">sensitive_word = #{sensitiveWord},</if>
            <if test="sendUserId != null">send_user_id = #{sendUserId},</if>
            <if test="acceptUserId != null">accept_user_id = #{acceptUserId},</if>
            <if test="sendUserType != null">send_user_type = #{sendUserType},</if>
            <if test="acceptUserType != null">accept_user_type = #{acceptUserType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="triggerTime != null">trigger_time = #{triggerTime},</if>
            <if test="msgId != null">msg_id = #{msgId},</if>
            <if test="chatType != null">chat_type = #{chatType},</if>
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="roomConsumeId != null">room_consume_id = #{roomConsumeId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCkSessionSensWordAlarmRecordByHotId" parameterType="Long">
        delete from ck_session_sens_word_alarm_record where id = #{id}
    </delete>

    <delete id="deleteCkSessionSensWordAlarmRecordByHotIds" parameterType="String">
        delete from ck_session_sens_word_alarm_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="saveSenWordAlarmRecord">
        insert into ck_session_sens_word_alarm_record(content_value,rule_id,sensitive_word,send_user_id,accept_user_id,send_user_type,accept_user_type,create_time,trigger_time,msg_id,chat_type,room_id,room_consume_id)
        SELECT
            cai.msg_content AS content_value,
            r.rule_id,
            r.sensitive_word,
            cai.from_id AS send_user_id,
            cai.consume_id AS accept_user_id,
            2 AS send_user_type,
            IF(cai.chat_type = 2, 3, (select IF(count(1) > 0, 2, 1) from tb_wx_user where userid = cai.consume_id)) AS accept_user_type,
            CURRENT_TIMESTAMP AS create_time,
            cai.msg_time AS trigger_time,
            cai.msg_id,
            cai.chat_type,
            cai.room_id,
            cai.room_consume_id
        FROM
            wk_chat_archive_info cai
                LEFT JOIN ck_session_sens_check_mapping m ON m.check_user_id = cai.from_id
                OR m.check_user_id = cai.consume_id
                LEFT JOIN (
                SELECT
                    rule_id,
                    jt.sensitive_word
                FROM
                    ck_session_sens_rule_info,
                    JSON_TABLE ( CONCAT( '["', REPLACE ( sensitive_words, ',', '","' ), '"]' ), '$[*]' COLUMNS (sensitive_word VARCHAR (255) COLLATE utf8mb4_general_ci PATH '$')) AS jt
                WHERE
                    STATUS = '1'
                  AND intercept_type IN ( '2', '3' )) r ON r.rule_id = m.rule_id
                JOIN tb_wx_user u ON u.userid = cai.from_id
        WHERE cai.msg_time >= #{beginTime} AND cai.msg_time <![CDATA[<=]]> #{endTime}
        AND cai.msg_content LIKE concat( '%', r.sensitive_word, '%' )
    </insert>
</mapper>