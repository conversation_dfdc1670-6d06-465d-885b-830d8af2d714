<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.subscr.BuSubscriptionMenuMapper">
    <!-- 自定义SQL查询 -->

    <resultMap id="BuSubscriptionMenuVOMap" type="com.cenker.scrm.pojo.vo.subscr.BuSubscriptionMenuVO">
        <id column="id" property="id"/>
        <result column="menu_name" property="menuName"/>
        <result column="release_status" property="releaseStatus"/>
        <result column="del_flag" property="delFlag"/>
        <result column="main_title" property="mainTitle"/>
        <result column="sub_title" property="subTitle"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="create_time" property="createTime"/>
        <result column="subscr_upgrade_time" property="subscrUpgradeTime"/>
    </resultMap>

    <select id="selectMenuList" resultMap="BuSubscriptionMenuVOMap">
        SELECT
        cast(m.id as char) id,
        m.menu_name,
        m.release_status,
        m.del_flag,
        m.main_title,
        m.sub_title,
        m.remark,
        m.create_by,
        u.nick_name create_by_name,
        m.create_time,
        m.update_time,
        m.subscr_upgrade_time
        FROM bu_subscription_menu m
        left join sys_user u on m.create_by = u.user_id
        <where>
            m.del_flag = 0
            <if test="name != null and name != ''">
                AND m.menu_name LIKE CONCAT('%', #{name}, '%')
            </if>
            <!-- 权限控制 -->
            <if test="dataScope == null or dataScope != '1'.toString()">
                <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                    and m.dept_id in
                    <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                </if>
                <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                    and m.create_by = #{userId}
                </if>
            </if>
        </where>
        ORDER BY m.release_status DESC, m.create_time DESC
    </select>
</mapper>