<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.radar.TbWxRadarInteractMapper">

    <resultMap id="getRadarListMap" type="com.cenker.scrm.pojo.vo.radar.InteractRadarVo">
        <id column="id" property="id"/>
        <result column="radar_title" property="title"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="click_num" property="clickNum"/>
        <result column="showStatus" property="showStatus"/>
        <result column="view_perm" property="viewPerm"/>
        <association property="tbWxRadarContent" javaType="com.cenker.scrm.pojo.entity.wechat.TbWxRadarContent">
            <result column="content_id" property="id"/>
            <result column="digest" property="digest"/>
            <result column="title" property="title"/>
            <result column="cover" property="cover"/>
            <result column="content" property="content"/>
            <result column="url" property="url"/>
            <result column="show_status" property="showStatus"/>
        </association>
    </resultMap>


    <select id="getRadarList" resultMap="getRadarListMap">
        select r.id,
        r.title radar_title,
        r.create_time,
        r.type,
        r.show_status as showStatus,
        r.view_perm,
        c.show_status,
        c.title ,
        c.digest,
        c.cover,
        c.id as content_id,
        if(c.digest is null or c.digest = '',c.content,c.digest) as content,
        if(u.nick_name is null, wu.name, u.nick_name) as create_by,
        (select count(distinct customer_id) from tb_wx_radar_content_record where content_id = c.id) click_num
        from tb_wx_radar_interact r
        left join tb_wx_radar_content c on r.id = c.radar_id
        left join sys_user u on u.user_id = r.create_by
        left join tb_wx_user wu on wu.id = r.create_by
        where r.del_flag = 0
        <if test="scope != null">
            and r.scope = #{scope}
        </if>
        <if test="title != null and title != ''">
            and r.title like concat('%',#{title},'%')
        </if>
        <if test="contentTitle != null and contentTitle != ''">
            and c.title like concat('%',#{contentTitle},'%')
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and r.dept_id in
                <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and r.create_by = #{userId}
            </if>
        </if>
        <if test="type != null">
            and r.type = #{type}
        </if>
        <if test="createBy != null and createBy != ''">
            and (if (r.since = 1,
            (select u.nick_name from sys_user u where c.create_by = u.user_id and u.corp_id = r.corp_id)
            ,(select name from tb_wx_user u where c.create_by = u.id and u.corp_id = r.corp_id)
            )) like concat('%',#{createBy},'%')
        </if>
        <if test="showStatus != null">
            and r.show_status = #{showStatus}
        </if>
        <if test="categoryScope != null and categoryScope == 'NONE'">
            and r.category_id is null
        </if>
        <if test="categoryId != null and categoryId != ''">
            and r.category_id in
            <foreach collection="categoryId.split(',')" item="cId" index="index" open="(" close=")" separator=",">
                #{cId}
            </foreach>
        </if>
        order by r.create_time desc
    </select>

    <select id="getRadarStatistics" resultType="com.cenker.scrm.pojo.vo.radar.RadarStatisticsVO">
        select
        count(1) totalClickNum,
        count(distinct r.customer_id) totalClickNCnt,
        ifnull((
        select count(distinct fu.external_user_id)
        from tb_wx_ext_follow_user fu
        where fu.state in (
        select concat(#{radarPrefix},wrc.id) from tb_wx_radar_contact wrc
        where wrc.content_id = rc.id
        )
        and fu.corp_id = ra.corp_id
        and fu.`status` = 0
        <if test="date != null and date != ''">
            and date_format(fu.create_time, '%Y-%m-%d') = date_format(#{date}, '%Y-%m-%d')
        </if>
        ),0)totalNewContactCnt
        FROM `tb_wx_radar_content_record` r
        join tb_wx_radar_content rc on rc.id = r.content_id
        join tb_wx_radar_interact ra on ra.id = rc.radar_id
        where ra.corp_id = #{corpId}
        and ra.id = #{radarId}
        <if test="date != null and date != ''">
            and date_format(r.create_time, '%Y-%m-%d') = date_format(#{date}, '%Y-%m-%d');
        </if>
    </select>

    <select id="getRadarReadRecordStatistics" resultType="com.cenker.scrm.pojo.vo.radar.RadarCustomerStatisticsVO">
        SELECT
            t1.content_id,
            t1.customer_id,
            u.nick_name AS customer_name,
            u.head_img_url,
            wu.NAME AS staff_name,
            t1.create_time,
            t1.read_rate,
            t1.read_time,
            t1.click_source,
            t2.click_num,
            t2.forward_num,
            IF( c.id IS NULL, 2, 1 ) AS customer_type
        FROM tb_wx_radar_content_record t1
        JOIN (
            SELECT
            content_id,
            customer_id,
            count( DISTINCT id ) click_num,
            sum( forward_num ) forward_num,
            MAX( id ) AS record_id
            FROM
            tb_wx_radar_content_record
            GROUP BY
            content_id, customer_id
        ) t2 ON t1.content_id = t2.content_id AND t1.customer_id = t2.customer_id AND t1.id = t2.record_id
        LEFT JOIN mp_wx_user u ON t1.customer_id = u.id
        LEFT JOIN tb_wx_user wu ON t1.staff_id = wu.userid
        LEFT JOIN tb_wx_ext_customer c ON c.union_id = u.union_id
        where t1.content_id = #{contentId}
            <if test="customerName != null and customerName != ''">
                and u.nick_name like concat('%',#{customerName},'%')
            </if>
            <if test="clickSource != null">
                and t1.click_source = #{clickSource}
            </if>
            <if test="staffName != null and staffName != ''">
                and wu.NAME like concat('%',#{staffName},'%')
            </if>
            <if test="customerType == 1">
                and c.id IS NOT NULL
            </if>
            <if test="customerType == 2">
                and c.id IS NULL
            </if>
            <if test="beginTime != null and beginTime != ''">
                and date_format(t1.create_time, '%Y-%m-%d') >= date_format(#{beginTime}, '%Y-%m-%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(t1.create_time, '%Y-%m-%d') <![CDATA[ <= ]]> date_format(#{endTime}, '%Y-%m-%d')
            </if>
        order by create_time desc
    </select>

    <select id="getRadarChatList" resultType="com.cenker.scrm.pojo.vo.radar.InteractRadarChatVO">
        select
        r.id radar_id,
        r.title,
        c.cover,
        c.digest,
        c.title contentTitle,
        ifnull((select count(cr.customer_id) from tb_wx_radar_content_record cr where cr.content_id = c.id),0)total_click_num,
        ifnull((select count(crt.customer_id) from tb_wx_radar_content_record crt where crt.content_id = c.id and date_format(crt.create_time, '%Y-%m-%d') =
        date_format(now(), '%Y-%m-%d')),0)today_click_num,
        c.id,
        concat(#{page},'?id=',c.id,'&amp;staffId=',#{staffId},'&amp;clickSource=',#{clickSource}) url,
        if (r.since = 1,
        (select u.nick_name from sys_user u where c.create_by = u.user_id and u.corp_id = r.corp_id)
        ,(select name from tb_wx_user u where c.create_by = u.id and u.corp_id = r.corp_id)
        ) create_by,
        r.type radarType,
        r.view_perm
        from
        tb_wx_radar_content c
        left join tb_wx_radar_interact r on r.id = c.radar_id
        where r.corp_id = #{corpId}
        and r.type= #{type}
        and r.scope = #{scope}
        and r.del_flag = 0
        and c.show_status = 1
        <if test="title != null and title != ''">
            and r.title like concat('%',#{title},'%')
        </if>
        <if test="scope != null and scope == 2">
            and r.create_by = #{userId}
        </if>
        <if test="scope != null and scope == 1">
            and r.show_status = 1
            <!-- 权限控制 -->
            <if test="dataScope == null or dataScope != '1'.toString()">
                <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                    and r.dept_id in
                    <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                </if>
                <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                    and r.create_by = #{userId}
                </if>
            </if>
        </if>
        group by r.id
        order by r.create_time desc
    </select>


    <select id="getOneById" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxRadarInteract">
        select r.id,
               r.corp_id,
               r.scope,
               r.type,
               r.title,
               r.match_type,
               r.contact_status,
               r.behavior_inform,
               r.dynamic_inform,
               r.customer_tag,
               r.remark,
               r.create_time,
               r.update_time,
               r.show_status,
               r.category_id,
               r.naming_rule,
               r.view_perm,
               u.nick_name create_by
        from tb_wx_radar_interact r
                 left join sys_user u on r.create_by = u.user_id and r.corp_id = u.corp_id
        where r.id = #{id}
    </select>

    <select id="getForwardUserById" resultType="java.lang.String">
        select nick_name
        from mp_wx_user
        where id = #{forwardUser}
    </select>

    <select id="selectRadarByContentId" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxRadarInteract">
        select r.corp_id, r.contact_status, r.id, r.title, r.view_perm
        from tb_wx_radar_interact r
                 left join tb_wx_radar_content c on r.id = c.radar_id
        where c.id = #{contentId}
          and c.del_flag = 0
        limit 1
    </select>

    <select id="getRadarSource" resultMap="getRadarListMap">
        select
        r.id,
        r.title radar_title,
        r.create_time,
        c.title ,
        c.digest,
        c.cover,
        r.type,
        r.view_perm,
        <if test="clickSource != null">
            concat(#{page},'?id=',c.id,'&amp;clickSource=',#{clickSource}) url,
        </if>
        u.nick_name create_by,
        (select count(distinct customer_id) from tb_wx_radar_content_record where content_id = c.id) click_num
        from tb_wx_radar_interact r
        left join tb_wx_radar_content c on r.id = c.radar_id
        left join sys_user u on r.create_by = u.user_id and u.corp_id = r.corp_id
        where r.corp_id = #{corpId}
        and r.del_flag = 0
        and c.show_status = 1
        <if test="scope != null">
            and r.scope = #{scope}
        </if>
        <if test="title != null and title != ''">
            and (r.title like concat('%',#{title},'%') or c.title like concat('%',#{title},'%'))
        </if>
        <if test="scope != null and scope == 2 and administrator == 0">
            -- 企业管理员看到所有个人雷达 否则看到自己创建
            and r.create_by = #{userId}
        </if>
        <if test="type != null">
            and r.type = #{type}
        </if>
        order by r.create_time desc
    </select>

    <select id="getRadarStatisticsRank" resultType="com.cenker.scrm.pojo.vo.radar.RadarStatisticsRankVO">
        SELECT
        rc.id AS "contentId",
        rc.title AS "title",
        rc.cover AS "cover",
        rc.digest AS "digest",
        rc.author AS "author",
        rc.type AS "type",
        rc.create_time AS "contentCreateTime",
        COUNT(DISTINCT r.customer_id) AS "totalClickNCnt",
        COUNT(1) AS "totalClickNum",
        AVG(r.read_time) AS "avgReadTime"
        FROM
        tb_wx_radar_content_record r
        JOIN tb_wx_radar_content rc ON r.content_id = rc.id
        JOIN tb_wx_radar_interact ri ON rc.radar_id = ri.id
        WHERE
        ri.corp_id = #{corpId}
        <if test="condition.beginTime != null and condition.beginTime != ''">
            AND r.create_time &gt;= #{condition.beginTime}
        </if>
        <if test="condition.endTime != null and condition.endTime != ''">
            AND r.create_time &lt; DATE_ADD(#{condition.endTime}, INTERVAL 1 DAY)
        </if>
        AND r.del_flag = 0
        GROUP BY
        rc.id, rc.title, rc.cover, rc.digest, rc.author, rc.type, rc.create_time
        ORDER BY
        totalClickNCnt DESC
    </select>


</mapper>