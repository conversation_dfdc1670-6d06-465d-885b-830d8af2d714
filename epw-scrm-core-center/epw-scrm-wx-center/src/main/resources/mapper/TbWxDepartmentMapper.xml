<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.contact.TbWxDepartmentMapper">

    <resultMap type="TbWxDepartment" id="TbWxDepartmentResult">
        <result property="id" column="id" />
        <result property="name" column="name" />
        <result property="parentId" column="parent_id" />
        <result property="corpId" column="corp_id" />
    </resultMap>

    <sql id="selectTbWxDepartmentVo">
        select id, name, parent_id, corp_id from tb_wx_department
    </sql>

    <select id="selectTbWxDepartmentList" parameterType="TbWxDepartment" resultMap="TbWxDepartmentResult">
        <include refid="selectTbWxDepartmentVo" />
        where del_flag = '1'
        <if test="name != null  and name != ''">
            and name like concat('%', #{name}, '%')
        </if>
        <if test="parentId != null ">
            and parent_id = #{parentId}
        </if>
        <if test="corpId != null  and corpId != ''">
            and corp_id = #{corpId}
        </if>
        <if test="ids != null and ids.length > 0">
            and id in
            <foreach item="item" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getTbWxDepartmentList" resultMap="TbWxDepartmentResult">
        select
            id,
            name,
            parent_id,
            corp_id
        from
          tb_wx_department
        where
        del_flag = '1'
          and
          corp_id = #{corpId}
          and id in
          <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
          </foreach>
    </select>

    <insert id="batchAddTbWxDepartments" parameterType="TbWxDepartment">
        insert into tb_wx_department(id, name, parent_id, corp_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.name}, #{item.parentId}, #{item.corpId})
        </foreach>
    </insert>

    <select id="queryRootDeptByCorpId" parameterType="string" resultType="long">
        select id from tb_wx_department where corp_id = #{corpId} and parent_id = 0 and del_flag = '1'
    </select>
</mapper>