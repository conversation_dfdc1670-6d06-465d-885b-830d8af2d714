<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.session.CkSessionHotCheckMappingMapper">
    
    <resultMap type="CkSessionHotCheckMapping" id="CkSessionHotCheckMappingResult">
        <result property="id"    column="id"    />
        <result property="hotId"    column="hot_id"    />
        <result property="checkUserId"    column="check_user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="name"    column="name"    />
        <result property="avatar"    column="avatar"    />

    </resultMap>

    <sql id="selectCkSessionHotCheckMappingVo">
        select g.id, g.hot_id, g.check_user_id, g.create_time, g.create_by, g.update_time, g.update_by,
               u.name,u.avatar
        from ck_session_hot_check_mapping g,
             tb_wx_user u where   g.check_user_id = u.userid
    </sql>

    <select id="selectCkSessionHotCheckMappingList" parameterType="CkSessionHotCheckMapping" resultMap="CkSessionHotCheckMappingResult">
        <include refid="selectCkSessionHotCheckMappingVo"/>
        <if test="hotId != null "> and g.hot_id = #{hotId}</if>
        <if test="checkUserId != null "> and g.check_user_id = #{checkUserId}</if>
    </select>
    
    <select id="selectCkSessionHotCheckMappingById" parameterType="Long" resultMap="CkSessionHotCheckMappingResult">
        <include refid="selectCkSessionHotCheckMappingVo"/>
        and g.id = #{id}
    </select>
        
    <insert id="insertCkSessionHotCheckMapping" parameterType="CkSessionHotCheckMapping" useGeneratedKeys="true" keyProperty="id">
        insert into ck_session_hot_check_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hotId != null">hot_id,</if>
            <if test="checkUserId != null">check_user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hotId != null">#{hotId},</if>
            <if test="checkUserId != null">#{checkUserId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateCkSessionHotCheckMapping" parameterType="CkSessionHotCheckMapping">
        update ck_session_hot_check_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="hotId != null">hot_id = #{hotId},</if>
            <if test="checkUserId != null">check_user_id = #{checkUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCkSessionHotCheckMappingById" parameterType="Long">
        delete from ck_session_hot_check_mapping where id = #{id}
    </delete>

    <delete id="deleteCkSessionHotCheckMappingByIds" parameterType="String">
        delete from ck_session_hot_check_mapping where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCkSessionHotCheckMappingByHotId" parameterType="Long">
        delete from ck_session_hot_check_mapping where hot_id = #{hotId}
    </delete>
</mapper>