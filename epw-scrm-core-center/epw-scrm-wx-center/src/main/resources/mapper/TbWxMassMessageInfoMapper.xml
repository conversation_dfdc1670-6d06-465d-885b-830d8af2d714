<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.message.TbWxMassMessageInfoMapper">

    <resultMap id="selectMassMessageListMap" type="com.cenker.scrm.pojo.vo.message.MassMessageListVO">
        <id property="id" column="id"/>
        <result property="enableApproval" column="enable_approval"/>
        <result property="approvalUser" column="approval_user"/>
        <result property="approvalUserName" column="approval_user_name"/>
        <result property="approvalRemark" column="approval_remark"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="createBy" column="create_by"/>
        <result property="createByUser" column="create_by_user"/>
        <result property="finishRate" column="finishRate"/>
        <result property="attachments" column="mass_attachment" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
    </resultMap>

    <select id="selectMassMessageList" resultMap="selectMassMessageListMap">
        SELECT
        m.id,
        m.mass_name,
        m.timed_task,
        m.check_status,
        m.setting_time AS send_time,
        m.mass_content,
        m.enable_approval,
        m.approval_user,
        au.nick_name approval_user_name,
        m.approval_remark,
        m.error_msg,
        m.create_by,
        u.nick_name AS create_by_user,
        COALESCE ( r.sendUserTotalCnt, 0 ) AS sendUserTotalCnt,
        COALESCE ( r.sendUserCnt, 0 ) AS sendUserCnt,
        COALESCE ( r.unSendUserCnt, 0 ) AS unSendUserCnt,
        COALESCE ( r.preSendCustomerCnt, 0 ) AS preSendCustomerCnt,
        COALESCE ( r.sendCustomerCnt, 0 ) AS sendCustomerCnt,
        COALESCE ( r.preSendCustomerCnt - r.sendCustomerCnt, 0 ) AS unSendCustomerCnt,
        concat_ws( '',  ifnull(round((COALESCE ( r.sendUserCnt, 0 ) / COALESCE ( r.sendUserTotalCnt, 0 ) ) * 100),0), '%', NULL ) as finishRate,
        m.mass_attachment
        FROM
        tb_wx_mass_message_info m
        LEFT JOIN (
            SELECT
            message_info_id,
            COUNT( DISTINCT user_id ) AS sendUserTotalCnt,
            COUNT( DISTINCT CASE WHEN send_status > 0 THEN user_id END ) AS sendUserCnt,
            COUNT( DISTINCT CASE WHEN send_status = 0 THEN user_id END ) AS unSendUserCnt,
            COUNT( DISTINCT external_user_id ) AS preSendCustomerCnt,
            COUNT( DISTINCT CASE WHEN send_status = 1 THEN external_user_id END ) AS sendCustomerCnt
            FROM
            tb_wx_mass_message_sender_record
            GROUP BY
            message_info_id
        ) r ON r.message_info_id = m.id
        LEFT JOIN sys_user u ON u.user_id = m.create_by
        LEFT JOIN sys_user au ON au.user_id = m.approval_user
        where m.del_flag = 0 and m.mass_scene = 1
        <if test="type != null and type == 1">
            and m.chat_type = 'single'
        </if>
        <if test="type != null and type == 2">
            and m.chat_type = 'group'
        </if>
        <if test="checkStatus != null and checkStatus != ''">
            and m.check_status = #{checkStatus}
        </if>
        <if test="name != null and name != ''">
            and m.mass_name like concat('%',#{name},'%')
        </if>
        <if test="creator != null and creator != ''">
            and u.nick_name like concat('%',#{creator},'%')
        </if>
        <if test="beginTime != null and beginTime != ''">
            and date_format(m.setting_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(m.setting_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        <if test="approvalUser != null and approvalUser != ''">
            and au.nick_name like concat('%',#{approvalUser},'%')
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and m.dept_id in
                <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and m.create_by = #{userId}
            </if>
        </if>
        order by m.create_time desc
    </select>

    <select id="sendUserDetailList" resultType="com.cenker.scrm.pojo.vo.message.MassMessageSenderListVO">
        select
        t.*,
        round((t.send_customer_cnt / t.pre_send_customer_cnt) * 100,0)send_rate
        from (
        select
        r.id,
        r.user_id,
        (select name from tb_wx_user where userid = r.user_id)name,
        (select avatar from tb_wx_user where userid = r.user_id)avatar,
        count(r.external_user_id)pre_send_customer_cnt,
        <if test="type != null and type != 2">
            sum(r.send_status = 1) send_customer_cnt,
        </if>
        <if test="type != null and type == 2">
            sum(r.send_status != 1) send_customer_cnt,
        </if>
        ifnull(max(r.send_time),'-')send_time,
        max(send_status)max_type,
        min(send_status)min_type
        from tb_wx_mass_message_sender_record r
        where r.message_info_id = #{id}
        group by r.user_id
        order by r.send_time desc
        )t
        <where>
            <if test="type != null and type == 0">
                and t.max_type = #{type}
            </if>
            <if test="type != null and type == 1">
                and t.min_type = #{type}
            </if>
            <if test="type != null and type == 2">
                and t.max_type != 0
                and t.max_type != 1
            </if>
            <if test="name != null and name != ''">
                and t.name like concat('%',#{name},'%')
            </if>
        </where>
    </select>

    <resultMap id="getMassMessageInfoMap" type="com.cenker.scrm.pojo.vo.message.MassMessageDetailVO">
        <id column="id" property="id"/>
        <result column="mass_name" property="massName"/>
        <result column="user_distinct" property="userDistinct"/>
        <result property="enableApproval" column="enable_approval"/>
        <result property="approvalUser" column="approval_user"/>
        <result property="approvalUserName" column="approval_user_name"/>
        <result property="approvalRemark" column="approval_remark"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="createByUser" column="create_by_user"/>
        <result column="create_by" property="createBy"/>
        <result column="mass_content" property="massContent"/>
        <result column="timed_task" property="timedTask"/>
        <result column="setting_time" property="settingTime"/>
        <result column="send_condition" property="condition"/>
        <result column="check_status" property="checkStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="mass_attachment" property="attachments" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <!--<result column="send_condition" property="messageCondition" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>-->
    </resultMap>

    <select id="getMassMessageInfo" resultMap="getMassMessageInfoMap">
        select
            m.id,
            m.mass_name,
            m.check_status,
            m.expect_send_cnt,
            m.user_distinct,
            m.enable_approval,
            m.approval_user,
            au.nick_name approval_user_name,
            m.approval_remark,
            m.error_msg,
            m.create_by,
            (select u.nick_name from sys_user u where m.create_by = u.user_id) create_by_user,
            m.send_condition,
            m.mass_content,
            m.mass_attachment,
            m.timed_task,
            m.setting_time,
            m.create_time,
            m.update_time
        from tb_wx_mass_message_info m
        LEFT JOIN sys_user au ON au.user_id = m.approval_user
        where m.id = #{id}
    </select>

    <select id="exportSendUserDetailList" resultType="MassMessageExportVo">
        select
            r.id,
            (select name from tb_wx_user where userid = r.user_id and corp_id = #{corpId}) userName,
            ifnull((SELECT max(name) FROM tb_wx_department WHERE (select main_department from tb_wx_user where userid = r.user_id)= id and corp_id = #{corpId}),
              (select company_name from tb_wx_corp_config where id = r.corp_config_id))userDepartment,
            if (min(r.send_status) != 0,'已发送','未发送')`status`,
            count(distinct r.external_user_id) expect_send_cnt,
            ifnull(max(r.send_time),'-') userSendTime,
            sum(r.send_status = 1) sendCnt,
            sum(r.send_status = 2 or r.send_status = 3)sendFailCnt,
            (select setting_time from tb_wx_mass_message_info where id = r.message_info_id) sendTime
        from tb_wx_mass_message_sender_record r
        where r.message_info_id = #{id}
        group by r.user_id
    </select>

    <select id="selectOneMonthAgoValidMassMessageList" resultType="TbWxMassMessageInfo">
        select
               id
        from
            tb_wx_mass_message_info
        where del_flag = 0
            and check_status = 'EXECUTING'
            and corp_config_id = #{corpConfigId}
            and is_expired = 0
            and mass_scene != 1
            and (date_format(setting_time,'%Y-%m-%d') >= #{pastDate} or date_format(create_time,'%Y-%m-%d') >= #{pastDate})
    </select>

    <resultMap id="getGroupInfoByIdMap" type="com.cenker.scrm.pojo.vo.message.MassMessageContentVO">
        <result property="id" column="id"/>
        <result column="mass_name" property="massName"/>
        <result column="mass_json" property="condition"/>
        <result column="timed_task" property="timedTask"/>
        <result column="setting_time" property="settingTime"/>
        <result column="expect_send" property="expectSend"/>
        <result property="checkStatus" column="check_status"/>
        <result property="enableApproval" column="enable_approval"/>
        <result property="approvalUser" column="approval_user"/>
        <result property="approvalUserName" column="approval_user_name"/>
        <result property="approvalRemark" column="approval_remark"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="createByUser" column="create_by_user"/>
        <result column="create_by" property="createBy"/>
        <result column="mass_attachment" property="attachments" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
    </resultMap>

    <select id="getInfoById" resultMap="getGroupInfoByIdMap">
        SELECT
            info.id,
            info.check_status,
            info.mass_name,
            info.setting_time,
            info.create_time,
            info.timed_task,
            USER.nick_name as create_by_user,
            info.mass_content,
            info.expect_send_cnt as expect_send,
            info.mass_attachment,
            info.enable_approval,
            info.approval_user,
            au.nick_name approval_user_name,
            info.approval_remark,
            info.error_msg,
            info.mass_json,
            info.create_by
        FROM
            tb_wx_mass_message_info info
                LEFT JOIN  sys_user AS USER ON USER.user_id = info.create_by
                LEFT JOIN  sys_user AS au ON au.user_id = info.approval_user
        WHERE
            info.id = #{infoId}
    </select>

<!--    <resultMap id="getMassMessageList" type="com.cenker.scrm.pojo.vo.message.MassMessageInfoGroupVO">-->
<!--        <id column="infoId" property="infoId"/>-->
<!--        <result column="massContent" property="massContent"/>-->
<!--        <result column="taskName" property="taskName"/>-->
<!--        <result column="createBy" property="createBy"/>-->
<!--        <result column="createTime" property="createTime"/>-->
<!--        <result column="actualSend" property="actualSend"/>-->
<!--        <result column="finishRate" property="finishRate"/>-->
<!--        <result column="expectSend" property="expectSend"/>-->
<!--        <result column="detailId" property="detailId"/>-->
<!--&lt;!&ndash;        <collection property="userList" ofType="string">&ndash;&gt;-->
<!--&lt;!&ndash;            <result column="userName" javaType="string"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </collection>&ndash;&gt;-->
<!--    </resultMap>-->
<!--&lt;!&ndash; 社群群发  wangjie&ndash;&gt;-->
<!--    <select id="queryMassMessageInfoGroup2" resultMap="getMassMessageList">-->
<!--        SELECT-->
<!--        info.id as infoId,-->
<!--        info.mass_content as massContent,-->
<!--        info.mass_name as taskName,-->
<!--        user.user_name as createBy,-->
<!--        info.create_time as createTime,-->
<!--        detail.actual_send as actualSend,-->
<!--        detail.id as detailId,-->
<!--        detail.expect_send as expectSend,-->
<!--        (select extcustomer.`name` from tb_wx_ext_customer as extcustomer where extcustomer.external_user_id = record.external_user_id) as  userName,-->
<!--        &#45;&#45;         extcustomer.`name` as  userName,-->
<!--        concat_ws( '', ifnull( round(( detail.actual_send / detail.expect_send ) * 100, 0 ), 0 ), '%', NULL ) as finishRate-->
<!--        FROM-->
<!--        tb_wx_mass_message_info AS info-->
<!--        LEFT JOIN sys_user AS USER ON USER.user_id = info.create_by-->
<!--        LEFT JOIN tb_wx_mass_message_sender_record AS record ON record.message_info_id = info.id-->
<!--        LEFT JOIN tb_wx_mass_message_detail AS detail ON detail.message_info_id = info.id-->
<!--&#45;&#45;         LEFT JOIN tb_wx_ext_customer AS extcustomer ON extcustomer.external_user_id = record.external_user_id-->
<!--        WHERE-->
<!--        info.del_flag = 0-->
<!--        AND record.del_flag = 0-->
<!--        AND detail.del_flag = 0-->
<!--        <if test="corpId!=null and corpId!=''">-->
<!--            and info.corp_config_id=#{corpId}-->
<!--        </if>-->
<!--        <if test="taskName!=null and taskName!=''">-->
<!--            and info.mass_name=#{taskName}-->
<!--        </if>-->
<!--        <if test="createBy!=null and createBy!=''">-->
<!--            and info.create_by=#{createBy}-->
<!--        </if>-->
<!--        <if test="beginTime!=null and beginTime!=''">-->
<!--            and info.setting_time &gt;= #{beginTime}-->
<!--        </if>-->
<!--        <if test="endTime!=null and endTime!=''">-->
<!--            and info.setting_time &lt;= #{endTime}-->
<!--        </if>-->
<!--    </select>-->

    <resultMap id="queryMassMessageInfoGroupMap" type="com.cenker.scrm.pojo.vo.message.MassMessageInfoGroupVO">
        <id column="info_id" property="infoId"/>
        <result property="enableApproval" column="enable_approval"/>
        <result property="approvalUser" column="approval_user"/>
        <result property="approvalUserName" column="approval_user_name"/>
        <result property="approvalRemark" column="approval_remark"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="createBy" column="create_by"/>
        <result column="mass_attachment" property="attachments" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
    </resultMap>

    <select id="queryMassMessageInfoGroup" resultMap="queryMassMessageInfoGroupMap">
        select
        m.id info_id,
        m.mass_name as task_name,
        m.timed_task,
        m.check_status,
        m.enable_approval,
        m.approval_user,
        au.nick_name approval_user_name,
        m.approval_remark,
        m.error_msg,
        m.create_by,
        m.setting_time create_time,
        m.mass_content,
        (select nick_name from sys_user where user_id = m.create_by)create_by_user,
        (@sendUserCnt := (select count(distinct user_id) from tb_wx_mass_message_sender_record r where r.message_info_id
        = m.id and r.send_status &lt;> 0))sendUserCnt,
        (@sendUserTotalCnt := (select count(distinct user_id) from tb_wx_mass_message_sender_record r where
        r.message_info_id = m.id))sendUserTotalCnt,
        (select count(*) from tb_wx_mass_message_detail detail where detail.message_info_id=m.id) as group_num,
        concat_ws( '',  ifnull(round(( @sendUserCnt / @sendUserTotalCnt ) * 100),0), '%', NULL ) as finishRate,
        m.mass_attachment
        from tb_wx_mass_message_info m
        LEFT JOIN sys_user au ON au.user_id = m.approval_user
        join (select @sendUserCnt := 0) sendUserCnt
        join (select @sendUserTotalCnt := 0) sendUserTotalCnt
        where m.del_flag = 0
        and m.mass_scene = 1
        and m.chat_type = 'group'
        <if test="checkStatus != null and checkStatus != ''">
            and m.check_status = #{checkStatus}
        </if>
        <if test="taskName != null and taskName != ''">
            and m.mass_name like concat('%',#{taskName},'%')
        </if>
        <if test="createBy != null and createBy != ''">
            and (select nick_name from sys_user where user_id = m.create_by) like concat('%',#{createBy},'%')
        </if>
        <if test="beginTime != null and beginTime != ''">
            and date_format(m.setting_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(m.setting_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        <if test="approvalUser != null and approvalUser != ''">
            and au.nick_name like concat('%',#{approvalUser},'%')
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and m.dept_id in
                <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and m.create_by = #{userId}
            </if>
        </if>
        <if test="orderType != null and orderType == 1">
            order by m.setting_time desc
        </if>
        <if test="orderType != null and orderType == 2">
            order by m.create_time desc
        </if>
    </select>
    <select id="getMassMessageInfoByRecordId"
            resultType="com.cenker.scrm.pojo.vo.message.MassMessageContentVO">
        select
            i.id,
            i.create_time,
            date_add(i.create_time, interval i.stop_task_hour hour)stopTime
        from tb_wx_mass_message_info i
        where i.id = (select r.message_info_id from tb_wx_mass_message_sender_record r where r.id = #{id})
    </select>

    <select id="sendGroupDetailList" resultType="com.cenker.scrm.pojo.vo.message.MassMessageSenderListVO">
        select
            g.chat_id,
            if(g.group_name = '','群聊',g.group_name) chatGroupName,
            g.`owner`,
            (select name from tb_wx_user where userid = g.`owner` limit 1) chatOwnerName,
            ifnull(r.send_time,'-') sendTime,
            (case when r.send_status=1 then '已发送'
                when r.send_status=0 then '未发送'
                when r.send_status is null and g.create_time &lt; d.create_time then '未发送'
            else '不在范围内' end) send_status_str,
            (select count(gm.user_id) from tb_wx_customer_group_member gm where gm.group_id = g.chat_id and gm.`status` = 0) chatMemberCnt
        from
            tb_wx_mass_message_detail d
        left join tb_wx_customer_group g on g.owner = d.sender
        left join tb_wx_mass_message_sender_record r on r.user_id =d.sender and r.chat_id = g.chat_id  AND r.message_info_id = d.message_info_id
        where
            d.message_info_id = #{id}
            and g.dismiss_status =0
        group by g.chat_id
        order by r.send_time desc
    </select>

    <select id="getMassMessageGroupOverview" resultType="com.cenker.scrm.pojo.vo.message.MassMessageGroupOverviewVO">
        select
            count(distinct t.`owner`) expectSendMemberNum,
            ifnull((select count(distinct user_id) from tb_wx_mass_message_sender_record  where message_info_id = t.message_info_id and send_status =1),0) sendMemberNum,
            0 unSendMemberNum,
            count(distinct t.chat_id) expectSendGroupNum,
            ifnull(sum(t.send_status = 1),0) sendGroupNum,
            ifnull(sum(t.send_status != 1),0) unSendGroupNum
        from
        (
            select
            d.message_info_id,
            g.owner,
            g.chat_id,
            ifnull(r.send_status, 0) send_status
            from
            tb_wx_mass_message_detail d
            left join tb_wx_customer_group g on g.owner = d.sender
            left join tb_wx_mass_message_sender_record r on r.message_info_id=d.message_info_id and r.user_id =d.sender and r.chat_id = g.chat_id
            where
            d.message_info_id = #{id}
            and g.dismiss_status =0
            group by g.chat_id
        )t
    </select>

    <select id="sendUserDetailListGroup"
            resultType="com.cenker.scrm.pojo.vo.message.MassMessageGroupSenderListVO">
        select
            wu.name name,
            wu.avatar avatar,
            count(t.chat_id) preSendGroupCnt,
            ifnull(sum(t.send_status != 1), 0) unSendGroupCnt,
            t.sender user_id,
            ifnull(sum(t.send_status = 1), 0) sendGroupCnt,
            max(t.send_status) send_status,
            ifnull(max(t.send_time), '-') sendTime,
            t.message_info_id,
            0 overSendGroupCnt
        from (
            select
                d.message_info_id,
                d.sender,
                g.chat_id,
                ifnull(r.send_status, 0) send_status,
                r.send_time
            from
            tb_wx_mass_message_detail d
            left join tb_wx_customer_group g on g.owner = d.sender
            left join tb_wx_mass_message_sender_record r on r.message_info_id=d.message_info_id and r.user_id =d.sender and r.chat_id = g.chat_id
            where
            d.message_info_id = #{id}
            and g.dismiss_status =0
            group by d.sender,g.chat_id
        )t
        left join tb_wx_user wu on wu.userid = t.sender
        group by t.sender
        order by t.send_time
    </select>

    <select id="listSenderDetail" resultType="com.cenker.scrm.pojo.vo.message.MassMessageSenderDetailVo">
        SELECT
            user_id,
            NAME,
            avatar,
            send_time,
            COALESCE ( preSendCustomerCnt, 0 ) AS preSendCustomerCnt,
            COALESCE ( sendCustomerCnt, 0 ) AS sendCustomerCnt,
            COALESCE ( sendFailCustomerCnt, 0 ) AS sendFailCustomerCnt,
            ROUND(( sendCustomerCnt / preSendCustomerCnt ) * 100, 0) send_rate
        FROM
            (
                SELECT
                    r.user_id,
                    u.`name`,
                    u.avatar,
                    max( r.send_time ) AS send_time,
                    COUNT( DISTINCT r.external_user_id ) AS preSendCustomerCnt,
                    COUNT( DISTINCT CASE WHEN r.send_status = 1 THEN r.external_user_id END ) AS sendCustomerCnt,
                    COUNT( DISTINCT CASE WHEN r.send_status > 1 THEN r.external_user_id END ) AS sendFailCustomerCnt
                FROM
                    tb_wx_mass_message_sender_record r
                        LEFT JOIN tb_wx_user u ON u.userid = r.user_id
                WHERE
                    r.message_info_id = #{id}
                <if test="type != null and type == 0">
                    and r.send_status = 0
                </if>
                <if test="type != null and type == 1">
                    and r.send_status >= 1
                </if>
                <if test="name != null and name != ''">
                    and u.name like concat('%', #{name}, '%')
                </if>
                GROUP BY
                    r.user_id
                ORDER BY send_time DESC
            ) AS t
    </select>

    <select id="listCustomerDetail" resultType="com.cenker.scrm.pojo.vo.message.MassMessageCustomerDetailVo">
        SELECT
            r.external_user_id,
            c.`name` AS customerName,
            c.avatar AS customerAvatar,
            c.corp_name AS customerCorpName,
            r.user_id,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            r.send_time
        FROM
            tb_wx_mass_message_sender_record r
            LEFT JOIN tb_wx_user u ON u.userid = r.user_id
            LEFT JOIN tb_wx_ext_customer c ON r.external_user_id = c.external_user_id
        WHERE
        r.message_info_id = #{id}
        <if test="type != null and type == 0">
            and r.send_status = 0
        </if>
        <if test="type != null and type == 1">
            and r.send_status = 1
        </if>
        <if test="type != null and type == 2">
            and r.send_status > 1
        </if>
        <if test="name != null and name != ''">
            and u.name like concat('%', #{name}, '%')
        </if>
        order by r.send_time desc, r.id desc
    </select>

    <select id="listUser" resultType="com.cenker.scrm.pojo.dto.condition.UserConditionDTO">
        SELECT
        userid as userId,
        `name` as userName,
        avatar as userAvatar
        FROM
        tb_wx_user
        WHERE
        userid IN
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
</mapper>