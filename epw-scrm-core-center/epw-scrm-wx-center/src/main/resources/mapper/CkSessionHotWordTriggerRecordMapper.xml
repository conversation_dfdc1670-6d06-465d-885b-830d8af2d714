<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.session.CkSessionHotWordTriggerRecordMapper">

    <resultMap id="BaseResultMap" type="com.cenker.scrm.pojo.entity.session.CkSessionHotWordTriggerRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ruleId" column="rule_id" jdbcType="BIGINT"/>
            <result property="hotWord" column="hot_word" jdbcType="VARCHAR"/>
            <result property="synonWords" column="synon_words" jdbcType="VARCHAR"/>
            <result property="word" column="word" jdbcType="VARCHAR"/>
            <result property="msgId" column="msg_id" jdbcType="VARCHAR"/>
            <result property="msgContent" column="msg_content" jdbcType="VARCHAR"/>
            <result property="chatType" column="chat_type" jdbcType="TINYINT"/>
            <result property="fromId" column="from_id" jdbcType="VARCHAR"/>
            <result property="roomId" column="room_id" jdbcType="VARCHAR"/>
            <result property="consumeId" column="consume_id" jdbcType="VARCHAR"/>
            <result property="roomConsumeId" column="room_consume_id" jdbcType="VARCHAR"/>
            <result property="msgTime" column="msg_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,rule_id,hot_word,
        synon_words,word,msg_id,
        msg_content,chat_type,from_id,
        room_id,consume_id,room_consume_id,
        msg_time,create_time
    </sql>

    <insert id="saveHotWordTriggerRecord">
        insert into ck_session_hot_word_trigger_record(rule_id,hot_word,synon_words, word,msg_id,msg_content,chat_type,from_id,consume_id,msg_time,create_time,room_id,room_consume_id)
        SELECT
            r.hot_id AS rule_id,
            r.hot_word,
            r.synon_words,
            r.trigger_word AS word,
            cai.msg_id,
            cai.msg_content,
            cai.chat_type,
            cai.from_id,
            cai.consume_id,
            cai.msg_time,
            CURRENT_TIMESTAMP AS create_time,
            cai.room_id,
            cai.room_consume_id
        FROM
            wk_chat_archive_info cai
                LEFT JOIN ck_session_hot_check_mapping m ON m.check_user_id = cai.from_id
                OR m.check_user_id = cai.consume_id
                LEFT JOIN (
                SELECT
                    hot_id,
                    hot_word,
                    synon_words,
                    jt.trigger_word
                FROM
                    ck_session_hot_word_info,
                    JSON_TABLE ( CONCAT( '["', REPLACE ( concat(hot_word, ',', synon_words), ',', '","' ), '"]' ), '$[*]' COLUMNS (trigger_word VARCHAR (255) COLLATE utf8mb4_general_ci PATH '$')) AS jt) r ON r.hot_id = m.hot_id
                LEFT JOIN tb_wx_customer_group g ON g.`owner` = m.check_user_id
                AND cai.room_id = g.chat_id
        WHERE cai.msg_time >= #{beginTime} AND cai.msg_time <![CDATA[<=]]> #{endTime}
          AND cai.msg_content like concat('%', r.trigger_word, '%')
    </insert>
</mapper>
