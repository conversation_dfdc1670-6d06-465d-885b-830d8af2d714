<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.contact.TbWxContactMapper">

    <resultMap type="com.cenker.scrm.pojo.entity.wechat.TbWxContact" id="TbWxContactResult">
        <result property="id" column="id"/>
        <result property="corpId" column="corp_id"/>
        <result property="userId" column="user_id"/>
        <result property="type" column="type"/>
        <result property="scene" column="scene"/>
        <result property="remark" column="remark"/>
        <result property="skipVerify" column="skip_verify"/>
        <result property="state" column="state"/>
        <result property="party" column="party"/>
        <result property="configId" column="config_id"/>
        <result property="useStatus" column="use_status"/>
        <result property="qrCode" column="qr_code"/>
        <result property="qrStyle" column="qr_style"/>
        <result property="qrStyleCode" column="qr_style_code"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="welTplId" column="wel_tpl_id"/>
        <result property="welContent" column="wel_content"/>
        <result property="tagIds" column="tag_ids"/>
        <result property="imgUri" column="img_uri"/>
        <result property="userName" column="user_name"/>
        <result property="weEmpleCodeTags" column="weEmpleCodeTags"/>
        <result property="weEmpleCodeUseScops" column="weEmpleCodeUseScops"/>
    </resultMap>

    <sql id="selectTbWxContactVo">
        select id,
               corp_id,
               user_id,
               type,
               scene,
               remark,
               skip_verify,
               state,
               party,
               config_id,
               qr_code,
               create_by,
               create_time,
               del_flag,
               wel_tpl_id,
               wel_content,
               tag_ids,
               weEmpleCodeTags,
               weEmpleCodeUseScops
        from tb_wx_contact
    </sql>


    <select id="selectTbWxContactList" resultType="ContactVo">
        select
        c.id,
        c.remark,
        c.type,
        c.qr_code,
        if (c.since = 1,
        (select u.nick_name from sys_user u where c.create_by = u.user_id and u.corp_id = c.corp_id)
        ,(select name from tb_wx_user u where c.create_by = u.id and u.corp_id = c.corp_id)
        ) createBy,
        c.create_time,
        c.qr_style,
        c.qr_style_code,
        c.use_status,
        group_concat(wu.name order by wu.userid)  user_name,
        (select count(distinct (external_user_id))
        from tb_wx_ext_follow_user
        where state = c.state
        and corp_id = c.corp_id
        ) add_count
        from
        tb_wx_contact c
        left join tb_wx_contact_user_rel r on r.contact_id = c.id
        left join tb_wx_user wu ON wu.userid = r.user_id and wu.corp_id = c.corp_id
        where
        c.del_flag = 0
        and c.`show` = 1
        and c.corp_id = #{corpId}
        <if test="corpUserId != null  and corpUserId.size > 0">
            and r.user_id in
            <foreach item="userId" collection="corpUserId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="remark != null  and remark != ''">
            and c.remark like concat('%',#{remark},'%')
        </if>
        <if test="type != null">
            and c.type = #{type}
        </if>
        <if test="createBy != null and createBy != ''">
            and	c.create_by in (
            (select user_id from sys_user where nick_name like concat('%',#{createBy},'%'))
            union (select id from tb_wx_user where name like concat('%',#{createBy},'%'))
            )
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and c.dept_id in
                <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and c.create_by = #{userId}
            </if>
        </if>
        <if test="sysUserIds != null and sysUserIds.size() > 0 ">
            and c.create_by  in
            <foreach item="sysUserId" collection="sysUserIds" open="(" separator="," close=")">
                #{sysUserId}
            </foreach>
        </if>
        <if test="useStatus != null">
            and c.use_status = #{useStatus}
        </if>
        group by c.id
        order by c.create_time desc
    </select>


    <select id="selectTbWxContactByState" parameterType="String" resultMap="TbWxContactResult">
        select *  from tb_wx_contact   where  state = #{state}
    </select>


    <select id="selectTbWxContactByConfigId" parameterType="String" resultMap="TbWxContactResult">
        SELECT a.id,
               a.corp_id,
               a.user_id,
               a.type,
               a.scene,
               a.remark,
               a.skip_verify,
               a.state,
               a.party,
               a.config_id,
               a.qr_code,
               u.nick_name create_by,
               a.create_time,
               a.del_flag,
               a.wel_tpl_id,
               a.wel_content,
               a.img_uri,
               a.tag_ids,
               a.weEmpleCodeTags,
               a.weEmpleCodeUseScops,
               a.qr_style_code,
               a.qr_style
        FROM tb_wx_contact a
                 left join sys_user u on u.user_id = a.create_by and u.corp_id = a.corp_id
        WHERE a.config_id = #{configId}
          and a.del_flag = '0'
        order by a.create_time desc
    </select>

    <insert id="insertTbWxContact" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxContact" useGeneratedKeys="true" keyProperty="id">
        insert into tb_wx_contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,
            </if>
            <if test="corpId != null">corp_id,
            </if>
            <if test="userId != null">user_id,
            </if>
            <if test="type != null">type,
            </if>
            <if test="scene != null">scene,
            </if>
            <if test="remark != null">remark,
            </if>
            <if test="skipVerify != null">skip_verify,
            </if>
            <if test="state != null">state,
            </if>
            <if test="party != null">party,
            </if>
            <if test="configId != null">config_id,
            </if>
            <if test="qrCode != null">qr_code,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
            <if test="welTplId != null">wel_tpl_id,
            </if>
            <if test="welContent != null">wel_content,
            </if>
            <if test="tagIds != null">tag_ids,
            </if>
            <if test="weEmpleCodeTags != null">weEmpleCodeTags,
            </if>
            <if test="weEmpleCodeUseScops != null">weEmpleCodeUseScops,
            </if>
            <if test="imgUri != null">img_uri,
            </if>
            <if test="show != null">`show`,
            </if>
            <if test="qrStyleCode != null">`qr_style_code`,
            </if>
            <if test="qrStyle != null">`qr_style`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},
            </if>
            <if test="corpId != null">#{corpId},
            </if>
            <if test="userId != null">#{userId},
            </if>
            <if test="type != null">#{type},
            </if>
            <if test="scene != null">#{scene},
            </if>
            <if test="remark != null">#{remark},
            </if>
            <if test="skipVerify != null">#{skipVerify},
            </if>
            <if test="state != null">#{state},
            </if>
            <if test="party != null">#{party},
            </if>
            <if test="configId != null">#{configId},
            </if>
            <if test="qrCode != null">#{qrCode},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
            <if test="welTplId != null">#{welTplId},
            </if>
            <if test="welContent != null">#{welContent},
            </if>
            <if test="tagIds != null">#{tagIds},
            </if>
            <if test="weEmpleCodeTags != null">#{weEmpleCodeTags},
            </if>
            <if test="weEmpleCodeUseScops != null">#{weEmpleCodeUseScops},
            </if>
            <if test="imgUri != null">#{imgUri},
            </if>
            <if test="show != null">#{show},
            </if>
            <if test="qrStyleCode != null">#{qrStyleCode},
            </if>
            <if test="qrStyle != null">#{qrStyle},
            </if>
        </trim>
    </insert>

    <delete id="deleteTbWxContactByConfigIds" parameterType="String">
        delete from tb_wx_contact where config_id in
        <foreach item="item" collection="array" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateTbWxContact" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxContact">
        update tb_wx_contact
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="scene != null">
                scene = #{scene},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="skipVerify != null">
                skip_verify = #{skipVerify},
            </if>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="party != null">
                party = #{party},
            </if>
            <if test="configId != null">
                config_id = #{configId},
            </if>
            <if test="qrCode != null">
                qr_code = #{qrCode},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="welTplId != null">
                wel_tpl_id = #{welTplId},
            </if>
            <if test="welContent != null">
                wel_content = #{welContent},
            </if>
            <if test="tagIds != null">
                tag_ids = #{tagIds},
            </if>
            <if test="weEmpleCodeTags != null">
                weEmpleCodeTags = #{weEmpleCodeTags},
            </if>
            <if test="weEmpleCodeUseScops != null">
                weEmpleCodeUseScops = #{weEmpleCodeUseScops},
            </if>
            <if test="qrStyle != null">
                qr_style = #{qrStyle},
            </if>
        </trim>
        where
        corp_id = #{corpId}
        and
        config_id = #{configId}
    </update>

    <!--
    <update id="updateTbWxContact" parameterType="TbWxContact">
        update tb_wx_contact set user_id = #{userId}, party = #{party}, skip_verify = #{skipVerify}, state = #{state}, wel_tpl_id = #{welTplId}, wel_content = #{welContent}, tag_ids = #{tagIds}, weEmpleCodeTags = #{weEmpleCodeTags}, weEmpleCodeUseScops = #{weEmpleCodeUseScops} where corp_id = #{corpId} and config_id = #{configId}
    </update>
    -->

    <select id="selectTbWxContactListByWork" resultType="WorkContactVo">
        select * from (
        select
        c.id,
        c.remark,
        c.qr_code,
        c.type,
        c.config_id,
        c.create_time,
        c.qr_style,
        c.qr_style_code,
        c.use_status,
        if (#{sysUserId} = c.create_by or #{tbWxUserId} = c.create_by,'我',
        if (c.since = 1,
        (select u.nick_name from sys_user u where c.create_by = u.user_id and u.corp_id = c.corp_id)
        ,(select name from tb_wx_user u where c.create_by = u.id and u.corp_id = c.corp_id)
        ))nick_name,
        if (#{sysUserId} = c.create_by or #{tbWxUserId} = c.create_by,1,0)setting,
        group_concat(u.name order by u.userid)user_id,
        group_concat(u.userid)user_ids,
        (select count(distinct (external_user_id))
        from tb_wx_ext_follow_user
        where add_way = 1
        and state = c.state
        and corp_id = c.corp_id
        ) add_count,
        (select count(distinct (u2.external_user_id))
        from tb_wx_ext_follow_user u2
        where u2.add_way = 1
        and u2.state = c.state
        and u2.corp_id = c.corp_id
        and u2.status = 0
        and date_format(u2.create_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
        ) today_add_count
        from tb_wx_contact c
        left join tb_wx_contact_user_rel r on r.contact_id = c.id
        left join tb_wx_user u on r.user_id = u.userid and r.corp_id = u.corp_id
        where c.corp_id = #{corpId}
        and c.show = 1
        and c.del_flag = 0
        group by c.id
        order by c.create_time desc
        )t
        <where>
            <if test="scope == 1">
                and user_ids like concat('%', #{userId},'%')
            </if>
            <if test="remark != null and remark != ''">
                and (remark like concat('%',#{remark},'%') or user_id like concat('%',#{remark},'%'))
            </if>
        </where>
    </select>

    <select id="selectTbWxContactRemarkByState" resultType="java.lang.String">
        select remark
        from tb_wx_contact
        where state = #{state}
        limit 1
    </select>

    <select id="getAddCustomerInfo" resultType="CustomerFollowUserVo">
        select e.external_user_id,
               e.avatar,
               e.name,
               e.type,
               (
                   case
                       when e.type = 1 then '@微信'
                       when e.type = 2 then concat('@', e.corp_name)
                       else '@企业微信' end) add_type,
               e.corp_name,
               u.create_time,
               (
                   select if(`status` = 0, 0, 2)
                   from tb_wx_ext_follow_user
                   where external_user_id = e.external_user_id
                     and user_id = u.user_id
                   order by `status`
                   limit 1
               )                         `status`,
               e.`status`                complete_del,
               wu.`name`                 add_user
        from tb_wx_ext_follow_user u
                 left join tb_wx_contact c on u.state = c.state and u.corp_id = c.corp_id
                 left join tb_wx_ext_customer e on u.external_user_id = e.external_user_id
                 left join tb_wx_user wu on wu.userid = u.user_id and wu.corp_id = c.corp_id
        where c.id = #{id}
        group by e.external_user_id
        order by u.create_time desc
    </select>

    <resultMap id="getInfoMap" type="com.cenker.scrm.pojo.vo.contact.ContactVO">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="skipVerify" column="skip_verify"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="welContent" column="wel_content"/>
        <result property="qrStyle" column="qr_style"/>
        <result property="qrCode" column="qr_code"/>
        <result property="useStatus" column="use_status"/>
        <result property="createBy" column="create_by"/>
        <result property="welTplId" column="wel_tpl_id"/>
        <result property="tagList" column="weEmpleCodeTags"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result property="attachments" column="welcome_attachment"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <collection property="userConditionList" ofType="com.cenker.scrm.pojo.dto.condition.UserConditionDTO">
            <id property="id" column="uid"/>
            <result property="userName" column="name"/>
            <result property="userId" column="userid"/>
            <result property="delFlag" column="del_flag"/>
            <result property="userAvatar" column="avatar"/>
        </collection>
    </resultMap>

    <select id="getInfo" resultMap="getInfoMap">
        select c.id,
               c.type,
               c.skip_verify,
               c.remark,
               c.wel_content,
               c.weEmpleCodeTags,
               c.welcome_attachment,
               c.wel_tpl_id,
               c.create_time,
               c.use_status,
               u.`name`,
               u.id uid,
               u.userid,
               u.del_flag,
               c.qr_code,
               c.qr_style,
               u.avatar,
               -- (select nick_name from sys_user where user_id = c.create_by limit 1) create_by
               if (c.since = 1,
                      (select u.nick_name from sys_user u where c.create_by = u.user_id and u.corp_id = c.corp_id)
                      ,(select name from tb_wx_user u where c.create_by = u.id and u.corp_id = c.corp_id)
                  ) create_by
        from tb_wx_contact c
                 left join tb_wx_contact_user_rel r on c.id = r.contact_id
                 left join tb_wx_user u on u.userid = r.user_id and u.corp_id = r.corp_id
        where c.id = #{id}
          and c.corp_id = #{corpId}
          and c.del_flag = 0
          and c.`show` = 1
    </select>

    <select id="getGenerateContact" resultType="TbWxContact">
        select c.id,
               c.qr_code,
               c.corp_id,
               c.type,
               c.qr_style_code,
               r.user_id
        from tb_wx_contact c
                 left join tb_wx_contact_user_rel r on r.contact_id = c.id
        where c.id = #{id}
        limit 1
    </select>

    <select id="getDataStatistics" resultType="ContactStatisticsVo">
        select (select count(distinct external_user_id)
                from tb_wx_ext_follow_user
                where c.state = state
                  and c.corp_id = corp_id
               )            total_add_cnt,
               ifnull((select count(distinct external_user_id)
                       from tb_wx_ext_follow_user
                       where c.state = state
                         and c.corp_id = corp_id
                         and `status` != 0
                         and external_user_id not in (
                           select u2.external_user_id
                           from tb_wx_ext_follow_user u2
                           where c.state = u2.state
                             and c.corp_id = u2.corp_id
                             and u2.`status` = 0
                           group by u2.external_user_id
                       )
                      ), 0) total_del_cnt,
               (select count(distinct external_user_id)
                from tb_wx_ext_follow_user
                where c.state = state
                  and c.corp_id = corp_id
                  and date_format(create_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
               )            add_cnt,
               (select count(distinct external_user_id)
                from tb_wx_ext_follow_user
                where c.state = state
                  and `status` != 0
                  and c.corp_id = corp_id
                  and external_user_id not in (
                    select u2.external_user_id
                    from tb_wx_ext_follow_user u2
                    where c.state = u2.state
                      and c.corp_id = u2.corp_id
                      and u2.`status` = 0
                    group by u2.external_user_id
                )
                  and date_format(create_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')
               )            del_cnt
        from tb_wx_contact c
        where c.id = #{id}
          and c.corp_id = #{corpId}
    </select>

    <select id="getAddCustomerInfo4Web" resultType="ContactCustomerStatisticsVo">
        select e.external_user_id,
               e.avatar,
               e.name,
               e.type,
               e.corp_name,
               u.create_time,
               (
                   select if(`status` = 0, 0, 2)
                   from tb_wx_ext_follow_user
                   where external_user_id = e.external_user_id
                     and user_id = u.user_id
                   order by `status`
                   limit 1
               )          `status`,
               e.`status` complete_del,
               wu.`name`  staff_name
        from tb_wx_ext_follow_user u
                 left join tb_wx_contact c on u.state = c.state and u.corp_id = c.corp_id
                 left join tb_wx_ext_customer e on u.external_user_id = e.external_user_id
                 left join tb_wx_user wu on wu.userid = u.user_id and wu.corp_id = c.corp_id
        where c.id = #{id}
        group by e.external_user_id
        order by u.create_time desc
    </select>

    <select id="getBehaviorData4TotalAddCount" resultType="ContactStatisticsDailyVo">
        select date_format(u.create_time, '%Y-%m-%d')              date,
               (@sum := @sum + count(distinct u.external_user_id)) `count`
        from tb_wx_contact c
                 join tb_wx_ext_follow_user u on u.state = c.state and u.corp_id = c.corp_id
                 join (select @sum := 0) i
        where c.id = #{id}
          and date_format(u.create_time, '%Y-%m-%d') &lt;= #{endTime}
        group by date_format(u.create_time, '%Y-%m-%d')
    </select>

    <select id="getBehaviorData4AddAndDelCount" resultType="ContactStatisticsDailyVo">
        select
        u.create_time date,
        count(distinct u.external_user_id) `count`
        from tb_wx_contact c
        join tb_wx_ext_follow_user u on u.state = c.state and u.corp_id = c.corp_id
        where c.id = #{id}
        <if test="type == 3">
            and u.status != 0
            and u.external_user_id not in (
            select u2.external_user_id from tb_wx_ext_follow_user u2
            where c.state = u2.state
            and c.corp_id = u2.corp_id
            and u2.`status` = 0
            group by u2.external_user_id
            )
        </if>
        and date_format(u.create_time,'%Y-%m-%d') >= #{startTime}
        and date_format(u.create_time,'%Y-%m-%d') &lt;= #{endTime}
        group by date_format(u.create_time,'%Y-%m-%d')
    </select>
</mapper>