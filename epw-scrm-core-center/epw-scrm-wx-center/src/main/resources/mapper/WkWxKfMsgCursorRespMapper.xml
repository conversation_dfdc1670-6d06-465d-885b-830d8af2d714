<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.kf.WkWxKfMsgCursorRespMapper">

    <resultMap type="WkWxKfMsgCursorResp" id="TbWxKfCursorResult">
        <result property="id" column="id"/>
        <result property="corpId" column="corp_id"/>
        <result property="openKfId" column="open_kf_id"/>
        <result property="cursor" column="cursor"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectUnavailableWxKfCursor" parameterType="java.lang.String" resultMap="TbWxKfCursorResult">
        select
            a.id,
            a.corp_id,
            a.open_kf_id,
            a.cursor,
            a.status,
            a.create_time,
            a.update_time
        from wk_wx_kf_msg_cursor_resp a
        where open_kf_id = #{openKfId}
        and status = '0'
        order by create_time desc
        limit 1
    </select>

    <select id="selectAvailableWxKfCursor" parameterType="java.lang.String" resultMap="TbWxKfCursorResult">
        select
            a.id,
            a.corp_id,
            a.open_kf_id,
            a.cursor,
            a.status,
            a.create_time,
            a.update_time
        from wk_wx_kf_msg_cursor_resp a
        where open_kf_id = #{openKfId}
        and status = '1'
        order by create_time asc
        limit 1
    </select>
</mapper>