<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.reply.TbWxQuickReplyMapper">

    <resultMap id="selectQuickReplyListMap" type="QuickReplyQuery">
        <id column="id" property="id"/>
        <result column="attachment" property="attachments" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result column="title" property="title"/>
        <result column="send_cnt" property="sendCnt"/>
        <result column="category_name" property="categoryName"/>
        <result column="type" property="type"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="order_num" property="orderNum"/>
    </resultMap>

    <select id="selectQuickReplyList" resultMap="selectQuickReplyListMap">
        select
        r.id,
        r.attachment,
        r.title,
        r.send_cnt,
        (select name from tb_wx_category_info where id = r.category_info_id)category_name,
        (case when r.type = 0 then '组合话术'
              when r.type = 1 then '文本'
              when r.type = 2 then '图片'
              when r.type = 3 then '链接'
              when r.type = 4 then '视频'
              when r.type = 5 then '小程序'
              when r.type = 6 then '智能物料'
          end)type,
        u.nick_name create_by,
        r.create_time
        from tb_wx_quick_reply r
        left join sys_user u on r.create_by = u.user_id
        where r.corp_config_id = #{corpConfigId}
        and r.del_flag = 0
        <if test="id != null and id != ''">
          and r.id = #{id}
        </if>
        <if test="createBy != null and createBy != ''">
            and u.nick_name like concat('%',#{createBy},'%')
        </if>
        <if test="title != null and title != ''">
            and (r.attachment like concat('%',#{title},'%') or r.title like concat('%',#{title},'%'))
        </if>
        <if test="type != null">
            and r.type = #{type}
        </if>
        <if test="categoryInfoId != null">
            and r.category_info_id = #{categoryInfoId}
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and r.dept_id in
                <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and r.create_by = #{userId}
            </if>
        </if>
        order by r.create_time desc
    </select>

    <select id="selectQuickReplyGroupList" resultMap="selectQuickReplyListMap">
        select
        r.id,
        r.attachment,
        r.title,
        i.name category_name,
        i.id category_id,
        i.order_num
        from tb_wx_quick_reply r
        left join tb_wx_category_info i on r.category_info_id = i.id
        where r.del_flag = 0
        and r.corp_config_id = #{corpConfigId}
        and (r.attachment like concat('%',#{title},'%') or r.title like concat('%',#{title},'%'))
        <if test="categoryInfoId != null">
            and i.id = #{categoryInfoId}
        </if>
        order by r.create_time desc
    </select>

    <select id="selectQuickReplyList4Quartz" resultMap="selectQuickReplyListMap">
        select
        r.id,
        r.attachment
        from tb_wx_quick_reply r
        where r.corp_config_id = #{corpConfigId}
        <if test="id != null">
            and r.id = #{id}
        </if>
        <if test="type != null">
            and r.type = #{type}
        </if>

        and r.del_flag = 0
    </select>

    <resultMap id="getInfoMap" type="com.cenker.scrm.pojo.vo.relpy.QuickReplyVo">
        <id column="id" property="id"/>
        <result column="attachment" property="attachments" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result column="send_condition" property="quickReplyCondition" typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result column="title" property="title"/>
        <result column="category_info_id" property="categoryInfoId"/>
        <result column="send_scope" property="sendScope"/>
    </resultMap>


    <select id="getInfo" resultMap="getInfoMap">
        select r.id,
               r.category_info_id,
               r.title,
               r.attachment,
               r.send_condition,
               r.update_inform,
               r.send_scope
        from tb_wx_quick_reply r
        where r.id = #{id}
    </select>
</mapper>