<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.external.TbWxCustomerTrajectoryMapper">

    <resultMap id="selectTrajectoryAndCustomerInfoResult" type="CustomerTrajectoryVo">
        <result property="id" column="id"/>
        <result property="externalUserId" column="external_user_id"/>
        <result property="content" column="content"/>
        <result property="createDate" column="create_date"/>
        <result property="status" column="status"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="behaviorType" column="behavior_type"/>
        <result property="corpId" column="corp_id"/>
        <result property="avatar" column="avatar"/>
        <result property="name" column="name"/>
    </resultMap>

    <resultMap id="list" type="TbWxCustomerTrajectory">
        <result property="id" column="id"/>
        <result property="externalUserId" column="external_user_id"/>
        <result property="trajectoryType" column="trajectory_type"/>
        <result property="createDate" column="create_date"/>
        <result property="status" column="status"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="userId" column="user_id"/>
        <result property="userId" column="user_id"/>
        <result property="corpId" column="corp_id"/>
        <result property="behaviorType" column="behavior_type"/>
        <result property="userHeadImg" column="user_head_img"/>
        <result property="userName" column="user_name"/>
        <result property="deleted" column="deleted"/>
        <result property="type" column="type"/>
        <result property="externalUserName" column="externalUserName"/>
        <result property="externalUserAvatar" column="externalUserAvatar"/>
        <result property="externalUserType" column="externalUserType"/>
        <result property="externalUserCorpName" column="externalUserCorpName"/>
        <result property="customerTrajectoryContentVo" column="content"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
    </resultMap>

    <select id="selectTrajectoryAndCustomerInfo" resultMap="selectTrajectoryAndCustomerInfoResult">
        select
        t.id,
        t.external_user_id,
        t.content,
        t.create_date,
        t.start_time,
        t.end_time,
        t.`status`,
        t.user_id,
        t.corp_id,
        c.avatar,
        c.`name`
        from tb_wx_customer_trajectory t
        left join tb_wx_ext_customer c on t.external_user_id = c.external_user_id
        where t.user_id = #{userId}
        and t.corp_id = #{corpId}
        and trajectory_type = 4
        <choose>
            <when test="status != null and status != ''">
                and t.`status` = #{status}
            </when>
            <otherwise>
                and t.`status` != 2
            </otherwise>
        </choose>
        order by t.create_time desc
    </select>

    <select id="selectTrajectoryList" resultMap="list">
        select t1.* ,if(t1.user_id = #{userId},1,0)deleted from (
        select
        t.id, t.trajectory_type, t.external_user_id, t.content, t.create_date, t.start_time,t.behavior_type,
        t.end_time, t.create_time, t.`status`, t.user_id,t.corp_id, t.agent_id,u.avatar user_head_img,
        u.name user_name
        from tb_wx_customer_trajectory t
        left join tb_wx_user u on t.user_id = u.userid and t.corp_id = u.corp_id
        where t.external_user_id = #{externalUserId}
        and t.corp_id = #{corpId}
        <!--   <if test="userId != null">
               and t.user_id = #{userId}
           </if>-->
        and t.trajectory_type = #{trajectoryType}
        and t.status = 0
        and t.create_date in
        <foreach item="date" collection="createDateList" open="(" separator="," close=")">
            #{date}
        </foreach>
        <!--union all
        select
        t.id, t.trajectory_type, t.external_user_id, t.content, t.create_date, t.start_time,t.behavior_type,
        t.end_time, t.create_time, t.`status`, t.user_id,t.corp_id, t.agent_id,u.avatar user_head_img,
        u.name user_name
        from tb_wx_customer_trajectory t
        left join tb_wx_user u on t.user_id = u.userid and t.corp_id = u.corp_id
        where t.external_user_id = #{externalUserId}
        and t.corp_id = #{corpId}
        and t.trajectory_type = #{trajectoryType}
        <if test="userId != null">
            and t.user_id != #{userId}
        </if>
        and t.behavior_type = 5
        and t.status = 0
        and t.create_date in
        <foreach item="date" collection="createDateList" open="(" separator="," close=")">
            #{date}
        </foreach>-->
        )t1
        order by t1.create_time desc
    </select>

    <select id="selectTrajectoryCreateDateGroupList" resultType="String">
        select * from (
        select create_date
        from tb_wx_customer_trajectory
        where external_user_id = #{externalUserId}
        and corp_id = #{corpId}
        <!--    <if test="userId != null">
                and user_id = #{userId}
            </if>-->
        and trajectory_type = #{trajectoryType}
        and status = 0
        -- group by create_date
        -- order by create_date desc 注释以下为适配客户跟进
        union all
        select create_date from tb_wx_customer_trajectory
        where external_user_id = #{externalUserId}
        and corp_id = #{corpId}
        and trajectory_type = #{trajectoryType}
        and behavior_type = 5
        and status = 0
        )t2
        group by t2.create_date
        order by t2.create_date desc
    </select>

    <select id="getStaffLogTrajectory" resultMap="list">
        select t.id,
               t.content,
               t.create_time,
               u.name user_name,
               t.behavior_type
        from tb_wx_customer_trajectory t
                 left join tb_wx_user u on u.userid = t.user_id and t.corp_id = u.corp_id
        where t.external_user_id = #{extUserId}
          and t.trajectory_type = 1
          and (t.behavior_type = 2 or t.behavior_type = 1)
          and t.corp_id = #{corpId}
          and t.content like '{"actionType":1,%'
        order by t.create_time desc
    </select>

    <select id="selectTrajectoryListV3" resultMap="list">
        select
        t.id,
        t.create_date,
        t.create_time,
        t.content,
        t.behavior_type,
        (select c.name from tb_wx_ext_customer c where t.external_user_id = c.external_user_id limit 1)externalUserName,
        <if test="actionType == 1">
            (select c.avatar from tb_wx_user c where t.user_id = c.userid and t.corp_id = c.corp_id limit
            1)user_head_img,
            (select c.avatar from tb_wx_ext_customer c where t.external_user_id = c.external_user_id limit
            1)externalUserAvatar,
            (select c.type from tb_wx_ext_customer c where t.external_user_id = c.external_user_id limit
            1)externalUserType,
            (select c.corp_name from tb_wx_ext_customer c where t.external_user_id = c.external_user_id limit
            1)externalUserCorpName,
        </if>
        <if test="actionType == 2">
            (select c.avatar from tb_wx_ext_customer c where t.external_user_id = c.external_user_id limit
            1)user_head_img,
            (select c.type from tb_wx_ext_customer c where t.external_user_id = c.external_user_id limit 1)type,
        </if>
        t.external_user_id
        from tb_wx_customer_trajectory t
        where t.`status` = 0
        and t.corp_id = #{corpId}
        and t.external_user_id
        in (select f.external_user_id
        from tb_wx_ext_follow_user f
        where f.corp_id = t.corp_id
        <if test="userId != null and userId != ''">
            and f.user_id = #{userId}
        </if>
        and f.`status` = 0
        group by f.external_user_id)
        and t.trajectory_type = 1
        <if test="actionType == 2">
            and t.behavior_type != 1
            and (JSON_EXTRACT(t.content, '$.actionType') != 1 or t.behavior_type = 4)
            and t.behavior_type != 5
        </if>
        <if test="actionType == 1">
            and (t.behavior_type = 1 or t.behavior_type = 2 or t.behavior_type = 5 or t.behavior_type = 8)
            and JSON_EXTRACT(t.content, '$.actionType') = 1
        </if>
        order by create_time desc
    </select>

    <select id="getTrajectoryCountToday" resultType="java.util.Map">
        select
        count(t.external_user_id) todayTrajectoryCnt,
        count(distinct t.external_user_id) trajectoryUserCnt
        from tb_wx_customer_trajectory t
        where t.`status` = 0
        and t.corp_id = #{corpId}
        and t.external_user_id
        in (select f.external_user_id
        from tb_wx_ext_follow_user f
        where f.corp_id = t.corp_id
        <if test="userId != null and userId != ''">
            and f.user_id = #{userId}
        </if>
        and f.`status` = 0
        group by f.external_user_id)
        and t.trajectory_type = 1
        <if test="actionType == 2">
            and t.behavior_type != 1
            and (JSON_EXTRACT(t.content, '$.actionType') != 1 or t.behavior_type = 4)
            and t.behavior_type != 5
        </if>
        <if test="actionType == 1">
            and (t.behavior_type = 1 or t.behavior_type = 2 or t.behavior_type = 5)
            and JSON_EXTRACT(t.content, '$.actionType') = 1
        </if>
        and date(t.create_date) = date_format(#{createDate},'%Y-%m-%d' )
    </select>

</mapper>