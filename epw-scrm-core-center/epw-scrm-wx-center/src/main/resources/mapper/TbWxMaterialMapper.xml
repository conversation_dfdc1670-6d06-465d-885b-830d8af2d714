<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.material.TbWxMaterialMapper">

    <resultMap type="com.cenker.scrm.pojo.entity.wechat.TbWxMaterial" id="TbWxMaterialResult">
        <result property="id" column="id"/>
        <result property="categoryId" column="category_id"/>
        <result property="materialUrl" column="material_url"/>
        <result property="materialName" column="material_name"/>
        <result property="digest" column="digest"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="audioTime" column="audio_time"/>
        <result property="corpId" column="corp_id"/>
        <result property="validityType" column="validity_type"/>
        <result property="mediaId" column="media_id"/>
    </resultMap>

    <sql id="selectTbWxMaterialVo">
        select m.id,
               m.category_id,
               m.material_url,
               m.material_name,
               m.digest,
               m.create_by,
               m.create_time,
               m.update_by,
               m.update_time,
               m.del_flag,
               m.audio_time,
               m.corp_id,
               m.validity_type,
               m.media_id,
               g.name                          category_name,
               m.frame_url,
               m.media_type,
               m.size,
               m.width,
               m.height,
               m.naming_rule
               -- if(i.material_id is null, 0, 1) is_grab
        from tb_wx_material m
                 left join tb_wx_category g on g.id = m.category_id
                 -- left join tb_wx_chat_item i on i.material_id = m.id
    </sql>

    <select id="selectTbWxMaterialList" parameterType="com.cenker.scrm.pojo.dto.material.MaterialListDto" resultMap="TbWxMaterialResult">
        <include refid="selectTbWxMaterialVo"/>
        where m.del_flag = '0'
        <if test="categoryId != null and categoryId != ''">
            and m.category_id = #{categoryId}
        </if>
        <if test="materialUrl != null  and materialUrl != ''">
            and m.material_url = #{materialUrl}
        </if>
        <if test="materialName != null  and materialName != ''">
            and m.material_name like concat('%', #{materialName}, '%')
        </if>
        <if test="digest != null  and digest != ''">
            and m.digest = #{digest}
        </if>
        <if test="audioTime != null  and audioTime != ''">
            and m.audio_time = #{audioTime}
        </if>
        <if test="corpId != null  and corpId != ''">
            and m.corp_id = #{corpId}
        </if>
        <if test="categoryName != null  and categoryName != ''">
            and g.name like concat('%', #{categoryName}, '%')
        </if>
        <if test="mediaType != null ">
            and g.media_type = #{mediaType}
        </if>
        /* 查询已抓取的素材 */
        <if test="sideId != null and sideId != ''">
            and i.side_id = #{sideId}
        </if>
        <if test="sideIds != null and sideIds != ''">
            and i.side_id is null
        </if>
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and m.dept_id in
                <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and m.create_by = #{userId}
            </if>
        </if>
        order by m.create_time desc
    </select>

    <select id="selectTbWxMaterialById" parameterType="Long" resultMap="TbWxMaterialResult">
        <include refid="selectTbWxMaterialVo"/>
        where m.id = #{id}
    </select>

    <insert id="insertTbWxMaterial" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxMaterial">
        insert into tb_wx_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="categoryId != null">category_id,
            </if>
            <if test="materialUrl != null">material_url,
            </if>
            <if test="materialName != null">material_name,
            </if>
            <if test="digest != null">digest,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
            <if test="audioTime != null">audio_time,
            </if>
            <if test="corpId != null">corp_id,
            </if>
            <if test="validityType != null">validity_type,
            </if>
            <if test="mediaId != null">media_id,
            </if>
            <if test="frameUrl != null">frame_url,
            </if>
            <if test="mediaType != null">media_type,
            </if>
            <if test="size != null">size,
            </if>
            <if test="width != null">width,
            </if>
            <if test="height != null">height,
            </if>
            <if test="namingRule != null">naming_rule,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="categoryId != null">#{categoryId},
            </if>
            <if test="materialUrl != null">#{materialUrl},
            </if>
            <if test="materialName != null">#{materialName},
            </if>
            <if test="digest != null">#{digest},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
            <if test="audioTime != null">#{audioTime},
            </if>
            <if test="corpId != null">#{corpId},
            </if>
            <if test="validityType != null">#{validityType},
            </if>
            <if test="mediaId != null">#{mediaId},
            </if>
            <if test="frameUrl != null">#{frameUrl},
            </if>
            <if test="mediaType != null">#{mediaType},
            </if>
            <if test="size != null">#{size},
            </if>
            <if test="width != null">#{width},
            </if>
            <if test="height != null">#{height},
            </if>
            <if test="namingRule != null">#{namingRule},
            </if>
        </trim>
    </insert>

    <update id="updateTbWxMaterial" parameterType="com.cenker.scrm.pojo.entity.wechat.TbWxMaterial">
        update tb_wx_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id =
                #{categoryId},
            </if>
            <if test="materialUrl != null">material_url =
                #{materialUrl},
            </if>
            <if test="materialName != null">material_name =
                #{materialName},
            </if>
            <if test="digest != null">digest =
                #{digest},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="delFlag != null">del_flag =
                #{delFlag},
            </if>
            <if test="audioTime != null">audio_time =
                #{audioTime},
            </if>
            <if test="corpId != null">corp_id =
                #{corpId},
            </if>
            <if test="validityType != null">validity_type =
                #{validityType},
            </if>
            <if test="mediaId != null">media_id =
                #{mediaId},
            </if>
            <if test="frameUrl != null">frame_url =
                #{frameUrl},
            </if>
            <if test="namingRule != null">naming_rule =
                #{namingRule},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbWxMaterialById" parameterType="Long">
        delete
        from tb_wx_material
        where id = #{id}
    </delete>

    <delete id="deleteTbWxMaterialByIds" parameterType="String">
        delete from tb_wx_material where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectTbWxMaterialTopList" resultMap="TbWxMaterialResult">
        <include refid="selectTbWxMaterialVo"/>
        where m.del_flag = '0'
        <if test="queryCategoryIds != null  and queryCategoryIds.size > 0">
            and m.category_id in
            <foreach item="id" collection="queryCategoryIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="materialUrl != null  and materialUrl != ''">
            and m.material_url = #{materialUrl}
        </if>
        <if test="materialName != null  and materialName != ''">
            and m.material_name like concat('%', #{materialName}, '%')
        </if>
        <if test="digest != null  and digest != ''">
            and m.digest = #{digest}
        </if>
        <if test="audioTime != null  and audioTime != ''">
            and m.audio_time = #{audioTime}
        </if>
        <if test="corpId != null  and corpId != ''">
            and m.corp_id = #{corpId}
        </if>
        <if test="categoryName != null  and categoryName != ''">
            and g.name like concat('%', #{categoryName}, '%')
        </if>
        <if test="mediaType != null ">
            and g.media_type = #{mediaType}
        </if>
        order by m.create_time desc
    </select>

</mapper>