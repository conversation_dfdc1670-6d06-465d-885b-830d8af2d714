<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.custlink.TbWxCustlinkMapper">

    <resultMap type="TbWxCustlink" id="TbWxCustlinkResult">
        <result property="id" column="id"/>
        <result property="corpId" column="corp_id"/>
        <result property="linkName" column="link_name"/>
        <result property="linkUrl" column="link_url"/>
        <result property="tagIds" column="tag_ids"/>
        <result property="welContent" column="wel_content"/>
        <result property="attachments" column="attachments"/>
        <result property="totalAddNum" column="total_add_num"/>
        <result property="totalTallNum" column="total_tall_num"/>
        <result property="totalGrowNum" column="total_grow_num"/>
        <result property="totalLossNum" column="total_loss_num"/>
        <result property="status" column="status"/>
        <result property="skipVerify" column="skip_verify"/>
        <result property="showFlag" column="show_flag"/>
        <result property="state" column="state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="tagListJson" column="tag_list_json"/>
        <result property="wxLinkId" column="wx_link_id"/>
        <result property="urlCreateTime" column="url_create_time"/>
        <result property="corpId" column="corp_id"/>

        <result property="createByName" column="create_by_name"/>


    </resultMap>

    <sql id="selectTbWxCustlinkVo">
        select k.id,
               k.corp_id,
               k.link_name,
               k.link_url,
               k.tag_ids,
               k.wel_content,
               k.attachments,
               k.total_add_num,
               k.total_tall_num,
               k.total_grow_num,
               k.total_loss_num,
               k.status,
               k.skip_verify,
               k.show_flag,
               k.create_by,
               r.nick_name create_by_name,
               k.update_by,
               k.create_time,
               k.update_time,
               k.tag_list_json,
               k.wx_link_id,
               k.corp_id,
               k.url_create_time
        from tb_wx_Custlink k join sys_user r on k.create_by = r.user_id
    </sql>

    <select id="selectTbWxCustlinkById"  parameterType="Long"  resultMap="TbWxCustlinkResult">
        <include refid="selectTbWxCustlinkVo" />
         where k.id = #{id}
    </select>


    <select id="qryStatData"  parameterType="CustlinkVO"  resultMap="TbWxCustlinkResult">

        select
        ifnull(total_add_num,0) total_add_num,
        ifnull(total_loss_num,0) total_loss_num,
        ifnull(total_tall_num,0) total_tall_num,
        (ifnull(total_add_num,0) - ifnull(total_loss_num,0))  total_grow_num
        from (
        select
        (select count(*)  from tb_wx_custlink_cust_detail l
        where l.link_id = k.id
        <if test="beginDate != null  and beginDate != ''">  and <![CDATA[ DATE_FORMAT(l.create_time,'%Y-%m-%d')  >= #{beginDate}]]></if>
        <if test="endDate != null  and endDate != ''">  and <![CDATA[ DATE_FORMAT(l.create_time,'%Y-%m-%d')  <= #{endDate}]]></if>
        ) as total_add_num,
        (select count(*)  from tb_wx_custlink_cust_detail l ,tb_wx_ext_follow_user r where r.user_id = l.userid
                                    and l.external_userid = r.external_user_id and r.status !=0   and link_id =  k.id and r.del_time is not null
        <if test="beginDate != null  and beginDate != ''">  and <![CDATA[ DATE_FORMAT(r.del_time,'%Y-%m-%d')  >= #{beginDate}]]></if>
        <if test="endDate != null  and endDate != ''">  and <![CDATA[ DATE_FORMAT(r.del_time,'%Y-%m-%d')  <= #{endDate}]]></if>
        ) as total_loss_num,
        (select count(*)  from tb_wx_custlink_cust_detail l where link_id =  k.id and chat_status = 1
        <if test="beginDate != null  and beginDate != ''">  and <![CDATA[ DATE_FORMAT(l.create_time,'%Y-%m-%d')  >= #{beginDate}]]></if>
        <if test="endDate != null  and endDate != ''">  and <![CDATA[ DATE_FORMAT(l.create_time,'%Y-%m-%d')  <= #{endDate}]]></if>
        ) as total_tall_num
        from tb_wx_custlink k where k.id = #{id}
        ) a
    </select>




    <select id="selectTbWxCustlinkList" parameterType="com.cenker.scrm.pojo.dto.custlink.CustLinkListDto" resultMap="TbWxCustlinkResult">
        <include refid="selectTbWxCustlinkVo" />
        <where>
        <if test="linkName != null  and linkName != ''">
            and k.link_name like concat('%',#{linkName},'%')
        </if>
        <if test="status != null  and status != ''">
            and k.status = #{status}
        </if>
            <if test="createByName != null  and createByName != ''">
                and r.nick_name  like concat('%',#{createByName},'%')
            </if>
            <if test="bindUserId != null  and bindUserId != ''">
                and k.id in(select link_id from tb_wx_custlink_user_mapping g where g.user_id in
                <foreach item="uid" collection="bindUserId.split(',')" open="(" separator="," close=")">
                    #{uid}
                </foreach>
                )
            </if>
            <!-- 权限控制 -->
            <if test="dataScope == null or dataScope != '1'.toString()">
                <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                    and k.dept_id in
                    <foreach item="deptId" collection="permissionDeptIds" open="(" separator="," close=")">
                        #{deptId}
                    </foreach>
                </if>
                <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                    and k.create_by = #{userId}
                </if>
            </if>
       </where>
        order by k.create_time desc
    </select>





    <insert id="insertTbWxCustlink" parameterType="TbWxCustlink" useGeneratedKeys="true" keyProperty="id">
        insert into tb_wx_custlink
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="corpId != null">corp_id,</if>
            <if test="linkName != null">link_name,</if>
            <if test="linkUrl != null">link_url,</if>
            <if test="tagIds != null">tag_ids,</if>
            <if test="welContent != null">wel_content,</if>
            <if test="attachments != null">attachments,</if>
            <if test="totalAddNum != null">total_add_num,</if>
            <if test="totalTallNum != null">total_tall_num,</if>
            <if test="totalGrowNum != null">total_grow_num,</if>
            <if test="totalLossNum != null">total_loss_num,</if>
            <if test="status != null">status,</if>
            <if test="skipVerify != null">skip_verify,</if>
            <if test="showFlag != null">show_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tagListJson != null">tag_list_json,</if>
            <if test="wxLinkId != null">wx_link_id,</if>
            <if test="urlCreateTime != null">url_create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="corpId != null">#{corpId},</if>
            <if test="linkName != null">#{linkName},</if>
            <if test="linkUrl != null">#{linkUrl},</if>
            <if test="tagIds != null">#{tagIds},</if>
            <if test="welContent != null">#{welContent},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="totalAddNum != null">#{totalAddNum},</if>
            <if test="totalTallNum != null">#{totalTallNum},</if>
            <if test="totalGrowNum != null">#{totalGrowNum},</if>
            <if test="totalLossNum != null">#{totalLossNum},</if>
            <if test="status != null">#{status},</if>
            <if test="skipVerify != null">#{skipVerify},</if>
            <if test="showFlag != null">#{showFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tagListJson != null">#{tagListJson},</if>
            <if test="wxLinkId != null">#{wxLinkId},</if>
            <if test="urlCreateTime != null">#{urlCreateTime},</if>
        </trim>
    </insert>

    <delete id="deleteTbWxCustlinkCustUser" parameterType="Long">
        delete from tb_wx_custlink_user_mapping where link_id  = #{linkId}
    </delete>

    <update id="updateTbWxCustlink" parameterType="TbWxCustlink">
        update tb_wx_Custlink
        <trim prefix="SET" suffixOverrides=",">
            <if test="corpId != null  and corpId !=''">corp_id = #{corpId},</if>
            <if test="linkName != null  and linkName !=''">link_name = #{linkName},</if>
            <if test="linkUrl != null  and linkUrl !=''">link_url = #{linkUrl},</if>
            <if test="tagIds != null  and tagIds !=''">tag_ids = #{tagIds},</if>
            <if test="welContent != null  and welContent !=''">wel_content = #{welContent},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="totalAddNum != null">total_add_num = #{totalAddNum},</if>
            <if test="totalTallNum != null">total_tall_num = #{totalTallNum},</if>
            <if test="totalGrowNum != null">total_grow_num = #{totalGrowNum},</if>
            <if test="totalLossNum != null">total_loss_num = #{totalLossNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="skipVerify != null  and skipVerify !=''">skip_verify = #{skipVerify},</if>
            <if test="showFlag != null  and showFlag !=''">show_flag = #{showFlag},</if>
            <if test="createBy != null  and createBy !=''">create_by = #{createBy},</if>
            <if test="updateBy != null  and updateBy !=''">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tagListJson != null  and tagListJson !=''">tag_list_json = #{tagListJson},</if>
            <if test="urlCreateTime != null">url_create_time = #{urlCreateTime},</if>
            <if test="wxLinkId != null and wxLinkId !=''">wx_link_id = #{wxLinkId},</if>

        </trim>
        where  id = #{id}
    </update>

    <select id="cntLinkNameRep" parameterType="TbWxCustlink" resultType="Long">
        select count(*) cnt from tb_wx_Custlink
        <where>
        <if test="linkName != null  and linkName != ''">
            and link_name = #{linkName}
        </if>
        <if test="showFlag != null  and showFlag != ''">
            and show_flag = #{showFlag}
        </if>
        <if test="id != null  and id != ''">
            and id = #{id}
        </if>
        <if test="notEqId != null  and notEqId != ''">
            and id != #{id}
        </if>
        <if test="status != null  and status != ''">
            and status = #{status}
        </if>
        </where>
    </select>


</mapper>