<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cenker.scrm.mapper.statistic.TbStatisticCustomerGroupMapper">
    <insert id="saveStatisticDateByDay">
        INSERT INTO `tb_statistic_customer_group`(
        `statistic_date`, `group_id`, `group_name`, `owner`,
        `owner_name`, `create_time`, `dismiss_date`, `group_chat_num`,
        `member_num`, `staff_num`, `member_in_num`, `member_out_num`,
        `net_new_num`, `active_customer_num`, `dept_id`
        )
        SELECT
        str_to_date(#{statDate}, '%Y-%m-%d') AS statistic_date,
        gb.group_id,
        gb.group_name,
        gb.owner,
        gb.owner_name,
        gb.create_time,
        gb.dismiss_date,
        IFNULL(gcs.group_chat_num, 0) AS group_chat_num,
        IFNULL(gms.member_num, 0) AS member_num,
        IFNULL(gms.staff_num, 0) AS staff_num,
        IFNULL(gms.member_in_num, 0) AS member_in_num,
        IFNULL(gms.member_out_num, 0) AS member_out_num,
        (IFNULL(gms.member_in_num, 0) - IFNULL(gms.member_out_num, 0)) AS net_new_num,
        IFNULL(gcs.active_customer_num, 0) AS active_customer_num,
        gb.dept_id
        FROM
        (
        SELECT
        g.chat_id AS group_id,
        g.group_name,
        g.`owner`,
        u.`name` AS owner_name,
        g.create_time,
        g.dismiss_date,
        u.main_department AS dept_id
        FROM
        tb_wx_customer_group g
        LEFT JOIN
        tb_wx_user u ON u.userid = g.`owner`
        WHERE
        (g.dismiss_status = 0 and date(g.create_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d'))
        OR date(g.dismiss_date) = str_to_date(#{statDate}, '%Y-%m-%d')
        ) AS gb
        LEFT JOIN
        (
        SELECT
        w.room_id,
        COUNT(DISTINCT w.msg_id) AS group_chat_num,
        COUNT(DISTINCT CASE WHEN tbwu.userid IS NULL THEN w.from_id END) AS active_customer_num
        FROM
        wk_chat_archive_info w
        LEFT JOIN
        tb_wx_user tbwu ON w.from_id = tbwu.userid
        WHERE
        w.chat_type = 2
        AND w.msg_day = str_to_date(#{statDate}, '%Y-%m-%d')
        GROUP BY
        w.room_id
        ) AS gcs ON gcs.room_id = gb.group_id
        LEFT JOIN
        (
        SELECT
        group_id,
        COUNT(DISTINCT user_id ) AS member_num,
        COUNT(DISTINCT CASE WHEN type = '1' THEN user_id END) AS staff_num,
        COUNT(CASE WHEN type = '2' AND date(join_time) = str_to_date(#{statDate}, '%Y-%m-%d') THEN user_id END) AS member_in_num,
        COUNT(CASE WHEN type = '2' AND date(departure_time) = str_to_date(#{statDate}, '%Y-%m-%d') THEN user_id END) AS member_out_num
        FROM
        tb_wx_customer_group_member
        where date(join_time) &lt;= str_to_date(#{statDate}, '%Y-%m-%d')
        and (departure_time is null or departure_time &gt;= str_to_date(#{statDate}, '%Y-%m-%d'))
        GROUP BY
        group_id
        ) AS gms ON gms.group_id = gb.group_id;
    </insert>


    <select id="summary" resultType="com.cenker.scrm.pojo.vo.statistic.StatisticCustomerGroupSummaryVo">
        SELECT
            COUNT(DISTINCT CASE WHEN dismiss_date is null THEN group_id END) AS groupTotal,
            COUNT(DISTINCT CASE WHEN date(create_time) BETWEEN str_to_date(#{beginTime},'%Y-%m-%d') AND str_to_date(#{endTime},'%Y-%m-%d')  THEN group_id END) AS newTotal,
            COUNT(DISTINCT CASE WHEN date(dismiss_date) BETWEEN str_to_date(#{beginTime},'%Y-%m-%d') AND str_to_date(#{endTime},'%Y-%m-%d')  THEN group_id END) AS dissTotal,
            COUNT(DISTINCT CASE WHEN group_chat_num > 0 THEN group_id END) AS groupActiveTotal,
            IFNULL(SUM(group_chat_num), 0) AS chatTotal,
            IFNULL(SUM(member_num), 0) AS groupMemberTotal,
            IFNULL(SUM(staff_num), 0) AS groupStaffTotal,
            IFNULL(SUM(member_in_num), 0) AS memberInTotal,
            IFNULL(SUM(member_out_num), 0) AS memberOutTotal,
            IFNULL(SUM(active_customer_num), 0) AS customerActiveTotal,
            IFNULL(SUM(net_new_num), 0) AS netNewMemberTotal
        FROM
            tb_statistic_customer_group
        WHERE statistic_date BETWEEN #{beginTime} AND #{endTime}
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and dept_id in
                <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and owner = #{wxUserId} and dept_id = #{deptId}
            </if>
        </if>
    </select>

    <select id="graph" resultType="com.cenker.scrm.pojo.vo.statistic.StatisticCustomerGroupSummaryVo">
        SELECT
            statistic_date AS statisticDate,
            COUNT(DISTINCT CASE WHEN dismiss_date is null THEN group_id END) AS groupTotal,
            COUNT(DISTINCT CASE WHEN date(create_time) = statistic_date  THEN group_id END) AS newTotal,
            COUNT(DISTINCT CASE WHEN date(dismiss_date) = statistic_date  THEN group_id END) AS dissTotal,
            COUNT(DISTINCT CASE WHEN group_chat_num > 0 THEN group_id END) AS groupActiveTotal,
            IFNULL(SUM(group_chat_num), 0) AS chatTotal,
            IFNULL(SUM(member_num), 0) AS groupMemberTotal,
            IFNULL(SUM(staff_num), 0) AS groupStaffTotal,
            IFNULL(SUM(member_in_num), 0) AS memberInTotal,
            IFNULL(SUM(member_out_num), 0) AS memberOutTotal,
            IFNULL(SUM(active_customer_num), 0) AS customerActiveTotal,
            IFNULL(SUM(net_new_num), 0) AS netNewMemberTotal
        FROM
            tb_statistic_customer_group
        WHERE
            statistic_date BETWEEN #{beginTime} AND #{endTime}
        <!-- 权限控制 -->
        <if test="dataScope == null or dataScope != '1'.toString()">
            <if test="permissionDeptIds != null and permissionDeptIds.size() > 0">
                and dept_id in
                <foreach item="item" collection="permissionDeptIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="permissionDeptIds == null or permissionDeptIds.size() == 0">
                and owner = #{wxUserId} and dept_id = #{deptId}
            </if>
        </if>
            GROUP BY statistic_date
		    ORDER BY statistic_date
    </select>
</mapper>
