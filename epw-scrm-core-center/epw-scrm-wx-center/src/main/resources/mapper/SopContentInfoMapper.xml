<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.sop.SopContentInfoMapper">


    <select id="getTodaySignCount" resultType="java.lang.Integer">
        select count(1)
        from ck_sop_content_info c
        where c.sop_id = #{sopId}
          and c.content_sign like concat(#{todaySign}, '%')
    </select>


    <resultMap id="taskExecuteListMap" type="com.cenker.scrm.pojo.vo.sop.ConditionSopDataVO">
        <id column="id" property="contentId"/>
        <result column="content_version" property="contentVersion"/>
        <result column="content_text" property="contentText"/>
        <result column="content_attachment" property="attachments"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result column="user_name" property="userName"/>
        <result column="preSendCustomerCnt" property="preSendCustomerCnt"/>
        <result column="taskStartTime" property="taskStartTime"/>
        <result column="taskEndTime" property="taskEndTime"/>
        <result column="taskExecuteTime" property="taskExecuteTime"/>
        <result column="executeStatus" property="executeStatus"/>
        <result column="avatar" property="customerAvatar"/>
        <result column="name" property="customerName"/>
        <result column="type" property="customerType"/>
        <result column="corp_name" property="customerCorpName"/>
        <result column="msgId" property="msgId"/>
        <result column="user_id" property="userId"/>
        <result column="recentReachTime" property="recentReachTime"/>
        <result column="sendCustomerCnt" property="sendCustomerCnt"/>
        <result column="del_time" property="recentDelTime"/>
    </resultMap>

    <select id="taskExecuteList" resultMap="taskExecuteListMap">
        select *
        from (
                 select
                        mr.id msgId,
                        sci.id,
                        sci.content_version,
                        sci.content_text,
                        sci.content_attachment,
                        mr.user_id,
                        (select name from tb_wx_user where userid = mr.user_id limit 1) user_name,
                        count(mr.external_user_id)                                preSendCustomerCnt,
                        mi.create_time                                            taskStartTime,
                        date_add(mi.create_time, interval mi.stop_task_hour hour) taskEndTime,
                        max(mr.send_time)                                    taskExecuteTime,
                        if(min(mr.send_status) != 0,1,if(mi.is_expired,3,2))executeStatus
                 from tb_wx_mass_message_sender_record mr
                          join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                          join ck_sop_content_info sci on mi.scene_id = sci.id
                 where sci.sop_id = #{sopId}
                 and sci.is_alive_version = 1
                 <if test="stageId != null">
                     and sci.stage_id = #{stageId}
                 </if>
                 <if test="contentVersion != null and contentVersion != ''">
                     and sci.content_version  like concat('%',#{contentVersion},'%')
                 </if>
                 <if test="beginTime != null">
                     and date_format(mi.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
                 </if>
                 <if test="endTime != null">
                     and date_format(mi.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
                 </if>
                 <if test="corpUserId != null  and corpUserId.size > 0">
                     and mr.user_id in
                     <foreach item="userId" collection="corpUserId" open="(" separator="," close=")">
                         #{userId}
                     </foreach>
                 </if>
                 group by mr.message_info_id,mr.user_id
             ) t
          <where>
              <if test="executeStatus != null">
                    and t.executeStatus = #{executeStatus}
              </if>
          </where>
          <if test="orderColumn != null and orderColumn != ''">
              order by ${orderColumn} ${sort}
          </if>
          <if test="orderColumn == null or orderColumn == ''">
              order by t.executeStatus,t.taskStartTime,t.id
          </if>
    </select>

    <select id="taskReachMessageList" resultMap="taskExecuteListMap">
        select
            mr.id msgId,
            sci.content_version,
            sci.content_text,
            sci.content_attachment,
            (select name from tb_wx_user where userid = mr.user_id limit 1) user_name,
            mi.create_time taskStartTime,
            mr.send_time taskExecuteTime,
            c.name,
            c.avatar,
            c.type,
            c.corp_name
        from tb_wx_mass_message_sender_record mr
                 join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                 join ck_sop_content_info sci on mi.scene_id = sci.id
                 join tb_wx_ext_customer c on c.external_user_id = mr.external_user_id
        where sci.sop_id = #{sopId}
          and sci.is_alive_version = 1
          and mr.send_status = 1
        <if test="beginTime != null">
            and date_format(mi.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null">
            and date_format(mi.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        <if test="msgId != null">
            and mr.id like concat('%',#{msgId},'%')
        </if>
        <if test="customerName != null and customerName != ''">
            and c.name like concat('%',#{customerName},'%')
        </if>
        <if test="corpUserId != null  and corpUserId.size > 0">
            and mr.user_id in
            <foreach item="userId" collection="corpUserId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="stageId != null">
            and sci.stage_id = #{stageId}
        </if>
        <if test="orderColumn != null and orderColumn != ''">
            order by mr.send_time ${sort}
        </if>
    </select>

    <select id="taskReachCustomerList" resultMap="taskExecuteListMap">
        select * from (
        select
        max(mr.send_time)recentReachTime,
        c.name,
        c.avatar,
        c.type,
        c.corp_name,
        count(mr.send_time) sendCustomerCnt
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        join tb_wx_ext_customer c on c.external_user_id = mr.external_user_id
        where sci.sop_id = #{sopId}
        and sci.is_alive_version = 1
        and mr.send_time is not null
        <if test="customerName != null and customerName != ''">
            and c.name like concat('%',#{customerName},'%')
        </if>
        <if test="stageId != null">
            and sci.stage_id = #{stageId}
        </if>
        group by c.external_user_id
       )t
        <if test="orderColumn != null and orderColumn != ''">
            order by ${orderColumn} ${sort}
        </if>
    </select>

    <select id="churnCustomerList" resultMap="taskExecuteListMap">
        select
            t.id msgId,
            c.name,
            c.avatar,
            c.type,
            c.corp_name,
            t.del_time,
            (select name from tb_wx_user where userid = t.user_id) user_name
        from (
            select
                mr.id,
                f.del_time,
                mr.user_id,
                mr.send_time,
                mr.external_user_id
            from
                tb_wx_mass_message_sender_record mr
            join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
            join ck_sop_content_info sci on mi.scene_id = sci.id
            join tb_wx_ext_follow_user f on f.external_user_id = mr.external_user_id and f.user_id = mr.user_id
            where
                sci.is_alive_version = 1
                and mr.send_status = 1
                and f.`status` = 2
                and f.del_time &gt;= mr.send_time
                -- 这段时间内（发送-截止时间有没有流失记录）
                and f.del_time &lt;= date_add(mr.send_time,interval sci.del_attribute_sop_minute minute)
                and sci.sop_id =  #{sopId}
                <if test="stageId != null">
                    and sci.stage_id = #{stageId}
                </if>
                <if test="corpUserId != null  and corpUserId.size > 0">
                    and mr.user_id in
                    <foreach item="userId" collection="corpUserId" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                </if>
            group by mr.message_info_id,mr.user_id,mr.external_user_id
            order by f.del_time desc
        )t join tb_wx_ext_customer c on c.external_user_id = t.external_user_id
        where t.del_time is not null
        <if test="customerName != null and customerName != ''">
            and c.name like concat('%',#{customerName},'%')
        </if>
        <if test="beginTime != null">
            and date_format(t.del_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null">
            and date_format(t.del_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        <if test="orderColumn != null and orderColumn != ''">
            order by t.del_time ${sort}
        </if>
    </select>


    <resultMap id="queryChildrenVersionMap" type="com.cenker.scrm.pojo.vo.sop.ConditionSopTaskDataVO">
        <id column="id" property="contentId"/>
        <result column="content_text" property="contentText"/>
        <result column="content_attachment" property="attachments"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result column="taskExecuteDayTime" property="taskExecuteDayTime"/>
        <result column="taskStartTime" property="taskStartTime"/>
        <result column="taskEndTime" property="taskEndTime"/>
    </resultMap>

    <select id="queryChildrenVersion" resultMap="queryChildrenVersionMap">
        select
            c.id,
            c.content_text,
            c.content_attachment,
            c.content_version,
            if (#{calculateDay},concat('第',c.content_sort,'天 ',date_format(c.start_time,'%H:%i')),date_format(c.start_time,'%Y-%m-%d %H:%i:%S'))taskExecuteDayTime,
            c.create_time taskStartTime,
            c.update_time taskEndTime,
            ifnull(concat(round(((
                                     select
                                         count(distinct mr.message_info_id ,mr.user_id)
                                     from tb_wx_mass_message_sender_record mr
                                              join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                              join ck_sop_content_info sci on mi.scene_id = sci.id
                                     where sci.id = c.id
                                       and mr.send_status = 1
                                       and sci.is_alive_version = 1
                                 )/(
                                     select
                                         count(distinct mr.message_info_id ,mr.user_id)
                                     from tb_wx_mass_message_sender_record mr
                                              join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                              join ck_sop_content_info sci on mi.scene_id = sci.id
                                     where sci.id = c.id
                                       and sci.is_alive_version = 1
                                 ))*100,2),'%'),'0.00%')completeTaskRate,

            ifnull(concat(round(((
                                     select
                                         count(distinct mr.message_info_id,mr.user_id,external_user_id)
                                     from tb_wx_mass_message_sender_record mr
                                              join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                              join ck_sop_content_info sci on mi.scene_id = sci.id
                                     where sci.id = c.id
                                       and sci.is_alive_version = 1
                                       and mr.send_status = 1
                                       and (select f.del_time from tb_wx_ext_follow_user f
                                            where f.external_user_id = mr.external_user_id
                                              and f.user_id = mr.user_id and f.`status` = 2
                                              and f.del_time >= mr.send_time
                                              and f.del_time &lt;= date_add(mr.send_time,interval sci.del_attribute_sop_minute minute)
                                            order by f.del_time desc limit 1
                                     ) is not null
                                 )/
                                 (select
                                      count(distinct mr.external_user_id)
                                  from tb_wx_mass_message_sender_record mr
                                           join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                           join ck_sop_content_info sci on mi.scene_id = sci.id
                                  where sci.id = c.id
                                    and mr.send_status = 1
                                    and sci.is_alive_version = 1))*100,2),'%'),'0.00%')customerReachChurnRate,

            ifnull(concat(round(((
                                     select
                                         count(distinct mr.message_info_id,mr.user_id,external_user_id)
                                     from tb_wx_mass_message_sender_record mr
                                              join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                              join ck_sop_content_info sci on mi.scene_id = sci.id
                                     where sci.id = c.id
                                       and sci.is_alive_version = 1
                                       and mr.send_status = 1
                                       and (select f.del_time from tb_wx_ext_follow_user f
                                            where f.external_user_id = mr.external_user_id
                                              and f.user_id = mr.user_id and f.`status` = 2
                                              and f.del_time >= mr.send_time
                                              and f.del_time &lt;= date_add(mr.send_time,interval sci.del_attribute_sop_minute minute)
                                              order by f.del_time desc limit 1
                                     ) is not null
                                 )/
                                 (select
                                      count(1)
                                  from tb_wx_mass_message_sender_record mr
                                           join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                           join ck_sop_content_info sci on mi.scene_id = sci.id
                                  where sci.id = c.id
                                    and sci.is_alive_version = 1
                                   <if test="stageId != null">
                                       and sci.stage_id = #{stageId}
                                   </if>
                                    and mr.send_status = 1))*100,2),'%'),'0.00%')messageReachChurnRate
        from ck_sop_content_info c
        where c.sop_id = #{sopId}
        and c.id &lt; #{contentId}
        and c.content_sign = #{contentSign}
        and c.is_alive_version = 1
        <if test="stageId != null">
            and c.stage_id = #{stageId}
        </if>
        order by c.id desc
    </select>

    <select id="queryTotalTaskCntByContentId" resultType="java.lang.Integer">
        (select count(1) from (
                                  select
                                      count(1)
                                  from tb_wx_mass_message_sender_record mr
                                           join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                           join ck_sop_content_info sci on mi.scene_id = sci.id
                                  where sci.id = #{contentId}
                                    and sci.is_alive_version = 1
                                  group by mr.message_info_id,mr.user_id
                              )t1)
    </select>

    <select id="queryCompleteTaskCntBySopId" resultType="java.lang.Integer">
        (select count(1) from (
                                  select
                                      count(1)
                                  from tb_wx_mass_message_sender_record mr
                                           join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
                                           join ck_sop_content_info sci on mi.scene_id = sci.id
                                  where sci.id = #{contentId}
                                    and mr.send_status = 1
                                    and sci.is_alive_version = 1
                                  group by mr.message_info_id,mr.user_id
                              )t1)
    </select>

    <resultMap id="sopTaskListMap" type="com.cenker.scrm.pojo.vo.sop.ConditionSopTaskDataVO">
        <id column="id" property="contentId"/>
        <result column="content_text" property="contentText"/>
        <result column="content_version" property="contentVersion"/>
        <result column="taskStartTime" property="taskStartTime"/>
        <result column="taskEndTime" property="taskEndTime"/>
        <result column="customerReachChurnRate" property="customerReachChurnRate"/>
        <result column="messageReachChurnRate" property="messageReachChurnRate"/>
        <result column="completeTaskRate" property="completeTaskRate"/>
        <result column="content_attachment" property="attachments"
                typeHandler="com.cenker.scrm.handler.JacksonTypeHandler"/>
        <result column="taskExecuteDayTime" property="taskExecuteDayTime"/>
        <collection property="childrenContent" column="{sopId=sop_id,contentId=id,contentSign=content_sign,stageId=stage_id,calculateDay=calculateDay}"
                    ofType="com.cenker.scrm.pojo.vo.sop.ConditionSopDataVO"
                    select="queryChildrenVersion"/>
        <collection ofType="java.lang.Integer" property="totalTaskCnt" select="queryTotalTaskCntByContentId"
                    column="{contentId=id}"/>
        <collection ofType="java.lang.Integer" property="completeTaskCnt" select="queryCompleteTaskCntBySopId"
                    column="{contentId=id}"/>
    </resultMap>

    <select id="sopTaskList" resultMap="sopTaskListMap">
        select * from (
        select
        c.id id,
        c.content_version content_version,
        c.content_sign,
        c.sop_id,
        if (#{calculateDay},concat('第',c.content_sort,'天 ',date_format(c.start_time,'%H:%i')),date_format(c.start_time,'%Y-%m-%d %H:%i:%S'))taskExecuteDayTime,
        #{calculateDay} calculateDay,
        c.content_text,
        c.content_attachment,
        c.create_time taskStartTime,
        c.stage_id,
        if(c.is_deleted,c.update_time,null) taskEndTime,

        ifnull(concat(round(((
        select
        count(distinct mr.message_info_id ,mr.user_id)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.id = c.id
        <if test="stageId != null">
            and sci.stage_id = #{stageId}
        </if>
        and (mr.send_status = 1 or mr.send_status = 3)
        and sci.is_alive_version = 1
        )/(
        select
        count(distinct mr.message_info_id ,mr.user_id)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.id = c.id
        and sci.is_alive_version = 1
        <if test="stageId != null">
            and sci.stage_id = #{stageId}
        </if>
        ))*100,2),'%'),'0.00%')completeTaskRate,

        ifnull(concat(round(((
        select
        count(distinct mr.message_info_id,mr.user_id,external_user_id)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.id = c.id
        and sci.is_alive_version = 1
        <if test="stageId != null">
            and sci.stage_id = #{stageId}
        </if>
        and mr.send_status = 1
        and (select f.del_time from tb_wx_ext_follow_user f
        where f.external_user_id = mr.external_user_id
        and f.user_id = mr.user_id and f.`status` = 2
        and f.del_time >= mr.send_time
        and f.del_time &lt;= date_add(mr.send_time,interval sci.del_attribute_sop_minute minute)
        order by f.del_time desc limit 1
        ) is not null
        )/
        (select
        count(distinct mr.external_user_id)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.id = c.id
        <if test="stageId != null">
            and sci.stage_id = #{stageId}
        </if>
        and mr.send_status = 1
        and sci.is_alive_version = 1))*100,2),'%'),'0.00%')customerReachChurnRate,

        ifnull(concat(round(((
        select
        count(distinct mr.message_info_id,mr.user_id,external_user_id)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.id = c.id
        and sci.is_alive_version = 1
        <if test="stageId != null">
            and sci.stage_id = #{stageId}
        </if>
        and mr.send_status = 1
        and (select f.del_time from tb_wx_ext_follow_user f
        where f.external_user_id = mr.external_user_id
        and f.user_id = mr.user_id and f.`status` = 2
        and f.del_time >= mr.send_time
        and f.del_time &lt;= date_add(mr.send_time,interval sci.del_attribute_sop_minute minute)
        order by f.del_time desc limit 1
        ) is not null
        )/
        (select
        count(1)
        from tb_wx_mass_message_sender_record mr
        join tb_wx_mass_message_info mi on mr.message_info_id = mi.id
        join ck_sop_content_info sci on mi.scene_id = sci.id
        where sci.id = c.id
        and sci.is_alive_version = 1
        <if test="stageId != null">
            and sci.stage_id = #{stageId}
        </if>
        and mr.send_status = 1))*100,2),'%'),'0.00%')messageReachChurnRate
        from ck_sop_content_info c where c.id in (
        select
        max(c.id)
        from ck_sop_content_info c
        where c.sop_id = #{sopId}
        and c.is_alive_version = 1
        <if test="contentVersion != null and contentVersion != ''">
            and c.content_version like concat('%',#{contentVersion},'%')
        </if>
        <if test="stageId != null">
            and c.stage_id = #{stageId}
        </if>
        <if test="taskExecuteDayTime != null and taskExecuteDayTime != ''">
            and c.start_time > #{taskExecuteDayTime}
        </if>
        group by c.content_sign
        ))t
    </select>

    <select id="selectValidMassMessageList" resultType="com.cenker.scrm.pojo.entity.wechat.TbWxMassMessageInfo">
        select
            distinct mi.id
        from tb_wx_mass_message_info mi
                 join ck_sop_content_info sc on mi.scene_id = sc.id
        where sc.sop_id = #{sopId}
          and mi.is_expired = 0
          and sc.is_deleted = 0
    </select>
</mapper>