<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cenker.scrm.mapper.sitecontact.ContactAreaMapper">

    <select id="selectStoreList" resultType="ContactStoreListVO">
        select
        a.id storeId,
        (case when b.parent_id = -1 then '默认'
        when b.parent_id &lt; 35 then (select area_name from contact_area where id = b.parent_id)
        when b.parent_id &lt; 1000 then concat((select area_name from contact_area where id = (select parent_id from contact_area where id = b.parent_id)),(select area_name from contact_area where id = b.parent_id))
        when b.parent_id &lt; 10000 then concat(
        (select area_name from contact_area where id = (select parent_id from contact_area where id = (select parent_id from contact_area where id = b.parent_id)))
        ,(select area_name from contact_area where id = (select parent_id from contact_area where id = b.parent_id)),(select area_name from contact_area where id = b.parent_id))
        else '默认' end)area_name,
        a.area_name store_name,
        b.area_name site_name,
        a.area_status,
        a.remark,
        a.create_time
        from contact_area a
        join contact_area b on a.parent_id = b.id
        where a.area_level = 5
        and a.del_flag = 0
        <if test="areaStatus != null">
            and a.area_status = #{areaStatus}
        </if>
        <if test="storeName != null and storeName != ''">
            and a.area_name like concat('%',#{storeName},'%')
        </if>
        <if test="siteName != null and siteName != ''">
            and b.area_name like concat('%',#{siteName},'%')
        </if>
        <if test="areaId != null and areaId != 0">
            <choose>
                <when test="areaId == -1">
                    and b.parent_id = #{areaId}
                </when>
                <when test="areaId &lt;= 34">
                    and (b.parent_id = #{areaId}
                    or  b.parent_id in (select city.id from contact_area city where city.parent_id = #{areaId})
                    or b.parent_id in (select id from contact_area where parent_id in (select id from contact_area where parent_id = #{areaId})))
                </when>
                <when test="areaId &lt;= 1000">
                    and (b.parent_id = #{areaId}
                    or b.parent_id in (select id from contact_area where parent_id = #{areaId}))
                </when>
                <otherwise>
                    and b.parent_id = #{areaId}
                </otherwise>
            </choose>
        </if>
        order by a.create_time desc
    </select>

    <select id="selectNormalAreaStoreList" resultType="ContactArea">
        select a.area_name,
               a.parent_id,
               a.id,
               a.area_level,
               if (a.area_level = 1 or a.area_level = 2 or a.area_level = 3,1,a.area_status)area_status,
               (case
                    when a.area_level = 5 then (select count(1)
                                                from contact_delivery_user du
                                                where du.store_id = a.id and du.del_flag = 0)
                    when a.area_level = 4 then
                        (select count(1)
                         from contact_delivery_user du
                         where du.store_id in (select id from contact_area where parent_id = a.id and del_flag = 0)
                           and du.del_flag = 0)
                    else 0 end
                   ) deliveryCnt
        from contact_area a
        where a.del_flag = 0
        order by a.order_num
    </select>

    <select id="selectStoreByTree" resultType="ContactStoreVO">
        select
            a.id storeId,
            concat(
                   (select area_name from contact_area where id = a.parent_id),'>',
                   a.area_name
                )areaName
        from contact_area a
        where a.area_level = 5
          and a.del_flag = 0
          <if test="areaName != null and areaName !=''">
              and a.area_name like concat('%',#{areaName},'%')
          </if>
        order by a.create_time desc
    </select>

    <select id="selectAllSiteList" resultType="ContactSiteTreeVO">
        select
            '0' site_id,
            '全部站点' site_name,
            '' area_name
        union all
        select
            a.id site_id,
            a.area_name site_name,
        (case when a.parent_id = -1 then '默认'
        when a.parent_id &lt; 35 then (select area_name from contact_area where id = a.parent_id)
        when a.parent_id &lt; 1000 then concat((select area_name from contact_area where id = (select parent_id from contact_area where id = a.parent_id)),(select area_name from contact_area where id = a.parent_id))
        when a.parent_id &lt; 10000 then concat(
        (select area_name from contact_area where id = (select parent_id from contact_area where id = (select parent_id from contact_area where id = a.parent_id)))
        ,(select area_name from contact_area where id = (select parent_id from contact_area where id = a.parent_id)),(select area_name from contact_area where id = a.parent_id))
        else '默认' end)area_name
        from contact_area a
        where a.area_level = 4
          and a.del_flag = 0
    </select>

    <select id="selectSiteByTree" resultType="ContactSiteTreeVO">
        select
        a.id siteId,
        if (a.parent_id = '-1',concat('默认>',a.area_name),
        concat((select area_name from contact_area where id = a.parent_id),'>',a.area_name))siteName
        from contact_area a
        where a.area_level = 4
        and a.del_flag = 0
        <if test="siteName != null and siteName !=''">
            and a.area_name like concat('%',#{siteName},'%')
        </if>
        order by a.create_time desc
    </select>
</mapper>