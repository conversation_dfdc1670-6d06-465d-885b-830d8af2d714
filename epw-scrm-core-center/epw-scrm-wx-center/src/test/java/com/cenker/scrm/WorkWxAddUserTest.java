package com.cenker.scrm;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import me.chanjar.weixin.cp.bean.Gender;
import me.chanjar.weixin.cp.bean.WxCpUser;

/**
 * <AUTHOR>
 * @Date 2023/8/28
 * @Description
 */
public class WorkWxAddUserTest {

    public static void main(String[] args) {

        for (int i = 5; i < 200; i++) {
            String userId = "user" + i;
            WxCpUser wxCpUser = new WxCpUser();
            wxCpUser.setUserId(userId);
            wxCpUser.setName("工具人" + i + "号");
            wxCpUser.setDepartIds(new Long[]{1L});
            wxCpUser.setGender(Gender.MALE);
            wxCpUser.setEmail(userId + "@163.com");
            wxCpUser.setEnable(1);
            wxCpUser.setToInvite(false);
            HttpRequest post = HttpUtil.createPost("https://qyapi.weixin.qq.com/cgi-bin/user/create?access_token=" +
                    "RC4HawlAYDpdEerMNGl4TUPszO4NT0ikqrH60S4HR0N0KRkBgjr6h1iPqRpOun1dlnjV9XlXR7-f0Ydb6h4VwBil65XcQw6oHoLfLcebaP5G2idkJsJ5FJeA6W-TBXLKhhTKR_NGjhYxIRGQfn_z157iLMnm0xQhjFqbKDgF0GgZd99Wugbhy-biXc8UiAvY3z6GXm_1d4MFvIknSBbfoA");
            post.body(wxCpUser.toJson());
            HttpResponse execute = post.execute();
            System.out.println(execute.body());
        }


    }
}
