<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>epw-scrm-core-center</artifactId>
        <groupId>com.cenker.scrm</groupId>
        <version>2.6.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>epw-scrm-wx-center</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-common-xxl-job</artifactId>
        </dependency>

        <!--服务调用-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!-- 第三方平台 -->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-open</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
        </dependency>

        <!-- 特殊二维码生成 -->
        <dependency>
            <groupId>com.github.liuyueyi.media</groupId>
            <artifactId>qrcode-plugin</artifactId>
        </dependency>

        <!-- 解析HTML文本 -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.11.3</version>
        </dependency>

        <!--TTL-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>

        <!-- 定时任务 -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.mchange</groupId>
                    <artifactId>c3p0</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 解析客户端操作系统、浏览器等 -->
        <dependency>
            <groupId>eu.bitwalker</groupId>
            <artifactId>UserAgentUtils</artifactId>
        </dependency>

        <!-- pdf转化图片-->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox-tools</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cky.common.sdk</groupId>
            <artifactId>cky-storage</artifactId>
        </dependency>

        <!-- 内部自研/代自建注入依赖 -->
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-work-cp</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 服务商/代自建模板注入依赖 -->
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-work-tp</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cenker.scrm</groupId>
            <artifactId>epw-scrm-system-center</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>3.2.6</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

    </dependencies>
</project>
