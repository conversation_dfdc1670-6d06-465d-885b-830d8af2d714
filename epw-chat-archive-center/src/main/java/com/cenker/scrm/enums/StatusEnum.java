package com.cenker.scrm.enums;


/**
 * 状态 枚举
 *
 * @ClassName ResultCodeEnum
 * <AUTHOR>
 * @Date 2021/6/19 16:39
 **/
public enum StatusEnum {

    NO_START( -1, "未开始"),
    START(1, "进行中"),
    COMPLETE(0, "完成"),
    NO_COMPLETE(2, "未完成"),
    RESTART(3, "需要重新执行"),
    END(4, "结束");

    /**
     * 状态
     */
    private Integer status;
    /**
     * 描述
     */
    private String descrition;

    StatusEnum(Integer status, String descrition) {
        this.status = status;
        this.descrition = descrition;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDescrition() {
        return descrition;
    }
}
