package com.cenker.scrm.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cenker.scrm.config.ChatArchiveConfig;
import com.cenker.scrm.constants.FileTypeConstant;
import com.cenker.scrm.enums.CacheKeyEnum;
import com.cenker.scrm.enums.ChatArchiveMsgTypeEnum;
import com.cenker.scrm.enums.OssStoragePathEnum;
import com.cenker.scrm.enums.SeparatorEnum;
import com.cenker.scrm.mapper.chatarchive.WkChatArchiveContentMapper;
import com.cenker.scrm.mapper.chatarchive.WkChatArchiveInfoMapper;
import com.cenker.scrm.pojo.dto.chatarchive.WxChatArchiveMsgInfo;
import com.cenker.scrm.pojo.entity.chatarchive.WkChatArchiveContentItem;
import com.cenker.scrm.pojo.entity.chatarchive.WkChatArchiveInfo;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpAgentConfig;
import com.cenker.scrm.util.EmojiUtils;
import com.cky.common.storage.cloud.OssStorageFactory;
import com.tencent.wework.Finance;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 会话消息处理
 *
 * <AUTHOR>
 * @className ChatMessageService
 * @date 2021/9/1 14:02
 **/
@Slf4j
@Service
public class ChatMessageService {
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private WkChatArchiveContentMapper wkChatArchiveContentMapper;
    @Resource
    private WkChatArchiveInfoMapper wkChatArchiveInfoMapper;

    @Resource
    private OssStorageFactory ossStorageFactory;
    @Resource
    private ChatArchiveConfig chatArchiveConfig;

    private static final long TIME_OUT = 60;

    /**
     * 保存文本消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveText(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        // 文本消息
        JSONObject contentBody = msgBody.getJSONObject("text");
        String content = emojiConverter(contentBody.getString("content"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.TEXT.getCode());
        wxChatArchive.setMsgContent(content);
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
    }

    /**
     * 保存图片消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveImage(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        TbWxCorpAgentConfig wxCorpConfig = wxChatArchiveMsgInfo.getWxCorpAgentConfig();
        // 表情消息
        JSONObject contentBody = msgBody.getJSONObject("image");

        String sdkFileId = contentBody.getString("sdkfileid");

        String fileName = System.currentTimeMillis() + FileTypeConstant.IMAGE_JPG_DOT;
        String md5sum = contentBody.getString("md5sum");
        Long filesize = contentBody.getLong("filesize");
        String filePath = mediaFileUpload(wxCorpConfig, fileName, sdkFileId, md5sum, filesize);

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.IMAGE.getCode());
        wxChatArchive.setFileUrl(filePath);
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        
    }

    /**
     * 保存小程序消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */
    public void saveWeapp(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();

        // 小程序消息
        JSONObject contentBody = msgBody.getJSONObject("weapp");
        String title = emojiConverter(contentBody.getString("title"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.WEAPP.getCode());
        wxChatArchive.setMsgContent(title);
        wxChatArchive.setContentData(contentBody.toJSONString());
    }

    /**
     * 保存链接消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveLink(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();

        // 链接消息
        JSONObject contentBody = msgBody.getJSONObject("link");
        String title = emojiConverter(contentBody.getString("title"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.LINK.getCode());
        wxChatArchive.setMsgContent(title);
        wxChatArchive.setContentData(contentBody.toJSONString());
    }

    /**
     * 保存红包消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveRedpacket(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();

        // 红包消息
        JSONObject contentBody = msgBody.getJSONObject("redpacket");

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.REDPACKET.getCode());
        wxChatArchive.setMsgContent("红包");
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        
    }

    /**
     * 保存互通红包消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveExternalRedpacket(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();

        // 互通红包消息
        JSONObject contentBody = msgBody.getJSONObject("external_redpacket");
        if (Objects.isNull(contentBody)) {
            log.info("红包信息为空 {}", msgBody);
            return;
        }
        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.EXTERNAL_REDPACKET.getCode());
        wxChatArchive.setMsgContent("互通红包");
        wxChatArchive.setContentData(contentBody.toJSONString());

    }

    /**
     * 保存图文消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveNews(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        // 图文消息
        JSONObject contentBody = msgBody.getJSONObject("news");
        JSONObject info = contentBody.getJSONObject("info");
        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.NEWS.getCode());
        wxChatArchive.setContentData(info.getJSONArray("item").toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
    }

    /**
     * 保存视频号消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveSphfeed(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        // 视频号消息
        JSONObject contentBody = msgBody.getJSONObject("sphfeed");

        String title = emojiConverter(contentBody.getString("feed_desc"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.SPHREED.getCode());
        wxChatArchive.setMsgContent(title);
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        
    }

    /**
     * 保存表情消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveEmotion(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        TbWxCorpAgentConfig wxCorpConfig = wxChatArchiveMsgInfo.getWxCorpAgentConfig();
        // 表情消息
        JSONObject contentBody = msgBody.getJSONObject("emotion");

        int imageType = contentBody.getInteger("type");
        String sdkFileId = contentBody.getString("sdkfileid");
        String md5sum = contentBody.getString("md5sum");

        String fileName = System.currentTimeMillis() + (imageType == 1 ? FileTypeConstant.IMAGE_GIF_DOT : FileTypeConstant.IMAGE_PNG_DOT);
        String filePath = mediaFileUpload(wxCorpConfig, fileName, sdkFileId, md5sum, contentBody.getLong("imagesize"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.EMOTION.getCode());
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        wxChatArchive.setFileUrl(filePath);
        
    }

    /**
     * 保存视频号消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveFile(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        TbWxCorpAgentConfig wxCorpConfig = wxChatArchiveMsgInfo.getWxCorpAgentConfig();
        // 文件消息
        JSONObject contentBody = msgBody.getJSONObject("file");
        String fileExt = contentBody.getString("fileext");
        String sdkFileId = contentBody.getString("sdkfileid");
        String md5sum = contentBody.getString("md5sum");
        // 上传文件
        String fileName = System.currentTimeMillis() + SeparatorEnum.DOT.getSeparator() + fileExt;
        String filePath = mediaFileUpload(wxCorpConfig, fileName, sdkFileId, md5sum, contentBody.getLong("filesize"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.FILE.getCode());
        wxChatArchive.setMsgContent(fileName);
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        wxChatArchive.setFileUrl(filePath);
        
    }

    /**
     * 保存撤回消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */
    public void saveRevoke(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        // 撤回消息
        JSONObject contentBody = msgBody.getJSONObject("revoke");
        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.REVOKE.getCode());
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        wxChatArchive.setPreMsgId(contentBody.getString("pre_msgid"));
        
    }

    /**
     * 保存不同意会话聊天内容
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveDisagree(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        // 不同意会话聊天内容
        JSONObject contentBody = msgBody.getJSONObject("disagree");

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.DISAGREE.getCode());
        wxChatArchive.setMsgContent("不同意会话存档");
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        
    }

    /**
     * 保存同意会话聊天内容
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveAgree(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        // 同意会话聊天内容
        JSONObject contentBody = msgBody.getJSONObject("agree");
        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.AGREE.getCode());
        wxChatArchive.setMsgContent("同意会话存档");

        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        
    }

    /**
     * 保存语音消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveVoice(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        TbWxCorpAgentConfig wxCorpConfig = wxChatArchiveMsgInfo.getWxCorpAgentConfig();
        // 语音消息
        JSONObject contentBody = msgBody.getJSONObject("voice");
        String sdkFileId = contentBody.getString("sdkfileid");
        String md5sum = contentBody.getString("md5sum");

        String fileName = System.currentTimeMillis() + FileTypeConstant.FILE_AMR_DOT;
        String filePath = mediaFileUpload(wxCorpConfig, fileName, sdkFileId, md5sum, contentBody.getLong("voice_size"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.VOICE.getCode());
        wxChatArchive.setFileUrl(filePath);
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        
    }

    /**
     * 保存视频消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveVideo(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        TbWxCorpAgentConfig wxCorpConfig = wxChatArchiveMsgInfo.getWxCorpAgentConfig();
        // 视频消息
        JSONObject contentBody = msgBody.getJSONObject("video");
        String sdkFileId = contentBody.getString("sdkfileid");
        String md5sum = contentBody.getString("md5sum");
        String fileName = System.currentTimeMillis() + FileTypeConstant.FILE_MP4_DOT;
        String filePath = mediaFileUpload(wxCorpConfig, fileName, sdkFileId, md5sum, contentBody.getLong("filesize"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.VIDEO.getCode());
        wxChatArchive.setFileUrl(filePath);
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        
    }

    /**
     * 保存会话记录消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */

    public void saveChatrecord(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        TbWxCorpAgentConfig wxCorpConfig = wxChatArchiveMsgInfo.getWxCorpAgentConfig();
        // 会话记录消息
        JSONObject contentBody = msgBody.getJSONObject("chatrecord");
        JSONArray itemArray = contentBody.getJSONArray("item");
        saveChatArchiveContentItem(wxCorpConfig, wxChatArchive, itemArray);

        String title = emojiConverter(contentBody.getString("title"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.CHATRECORD.getCode());
        wxChatArchive.setMsgContent(title);
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        
    }

    /**
     * 保存名片消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */
    public void saveCard(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        // 名片消息
        JSONObject contentBody = msgBody.getJSONObject("card");

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.CARD.getCode());
        wxChatArchive.setMsgContent("名片");
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
    }

    /**
     * 没有消息类型的消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */
    public void saveDefault(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {

    }


    /**
     * 保存位置消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */
    public void saveLocation(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        // 位置消息
        JSONObject contentBody = msgBody.getJSONObject("location");
        String title = emojiConverter(contentBody.getString("title"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.LOCATION.getCode());
        wxChatArchive.setMsgContent(title);
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        
    }

    /**
     * 保存混合消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */
    public void saveMixed(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        TbWxCorpAgentConfig wxCorpConfig = wxChatArchiveMsgInfo.getWxCorpAgentConfig();
        List<WkChatArchiveContentItem> contentItemList = new ArrayList<>();
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        JSONArray mixed = msgBody.getJSONObject("mixed").getJSONArray("item");
        mixed.forEach(item -> {
            JSONObject itemBody = JSONObject.parseObject(item.toString());
            String type = itemBody.getString("type");
            WkChatArchiveContentItem chatArchiveContentItem = new WkChatArchiveContentItem();
            if (StringUtils.equals(type, "text")) {
                JSONObject content = JSONObject.parseObject(itemBody.getString("content"));

                chatArchiveContentItem.setMsgContent(emojiConverter(content.getString("content")));
                chatArchiveContentItem.setContentData(content.toJSONString());
                chatArchiveContentItem.setArchiveId(wxChatArchive.getId());
                chatArchiveContentItem.setMsgId(wxChatArchive.getMsgId());
                contentItemList.add(chatArchiveContentItem);
                return;
            }
            if (StringUtils.equals(type, "image")) {
                JSONObject imageContent = JSONObject.parseObject(itemBody.getString("content"));
                // 图片消息
                String sdkFileId = imageContent.getString("sdkfileid");
                String md5sum = imageContent.getString("md5sum");

                String fileName = System.currentTimeMillis() + FileTypeConstant.IMAGE_JPG_DOT;
                String filePath = mediaFileUpload(wxCorpConfig, fileName, sdkFileId, md5sum, imageContent.getLong("filesize"));

                chatArchiveContentItem.setMsgType(ChatArchiveMsgTypeEnum.MIXED.getCode());
                chatArchiveContentItem.setFileUrl(filePath);
                chatArchiveContentItem.setContentData(imageContent.toJSONString());
                chatArchiveContentItem.setArchiveId(wxChatArchive.getId());
                chatArchiveContentItem.setMsgId(wxChatArchive.getMsgId());
                contentItemList.add(chatArchiveContentItem);
            }
        });

        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        wxChatArchive.setContentData(msgBody.getJSONObject("mixed").toJSONString());
        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.MIXED.getCode());
        
    }

    /**
     * 保存投票消息
     *
     * @param wxChatArchiveMsgInfo 消息信息
     * @param wxChatArchive        消息
     */
    public void saveVote(WxChatArchiveMsgInfo wxChatArchiveMsgInfo, WkChatArchiveInfo wxChatArchive) {
        JSONObject msgBody = wxChatArchiveMsgInfo.getJsonObject();
        // 投票消息
        JSONObject contentBody = msgBody.getJSONObject("vote");
        String title = emojiConverter(contentBody.getString("votetitle"));

        wxChatArchive.setMsgType(ChatArchiveMsgTypeEnum.VOTE.getCode());
        wxChatArchive.setMsgContent(title);
        wxChatArchive.setContentData(contentBody.toJSONString());
        wxChatArchive.setMsgId(wxChatArchive.getMsgId());
        
    }


    /**
     * 保存会话消息的消息内容
     *
     * @param wxCorpConfig  配置
     * @param content       内容Id
     * @param itemBodyArray 消息数组
     */
    public void saveChatArchiveContentItem(TbWxCorpAgentConfig wxCorpConfig, WkChatArchiveInfo content, JSONArray itemBodyArray) {
        List<WkChatArchiveContentItem> contentItemList = new ArrayList<>();
        for (Object jsonElement : itemBodyArray) {
            JSONObject itemBody = JSONObject.parseObject(jsonElement.toString());

            WkChatArchiveContentItem contentItem = new WkChatArchiveContentItem();

            String sdkFileId;
            String fileName;
            String filePath;

            JSONObject contentBody = itemBody.getJSONObject("content");
            String msgType = itemBody.getString("type");
            String md5sum = contentBody.getString("md5sum");

            switch (msgType) {
                case "ChatRecordText":
                    // 文本消息
                    contentItem.setMsgType(ChatArchiveMsgTypeEnum.TEXT.getCode());
                    contentItem.setMsgContent(emojiConverter(contentBody.getString("content")));
                    break;
                case "ChatRecordFile":
                    // 文件消息
                    String fileExt = contentBody.getString("fileext");
                    sdkFileId = contentBody.getString("sdkfileid");

                    fileName = System.currentTimeMillis() + SeparatorEnum.DOT.getSeparator() + fileExt;
                    filePath = mediaFileUpload(wxCorpConfig, fileName, sdkFileId, md5sum, contentBody.getLong("filesize"));

                    contentItem.setMsgType(ChatArchiveMsgTypeEnum.FILE.getCode());
                    contentItem.setFileUrl(filePath);
                    contentItem.setMsgContent(emojiConverter(contentBody.getString("filename")));

                    break;
                case "ChatRecordImage":
                    // 图片消息
                    sdkFileId = contentBody.getString("sdkfileid");

                    fileName = System.currentTimeMillis() + FileTypeConstant.IMAGE_JPG_DOT;
                    filePath = mediaFileUpload(wxCorpConfig, fileName, sdkFileId, md5sum, contentBody.getLong("filesize"));

                    contentItem.setMsgType(ChatArchiveMsgTypeEnum.IMAGE.getCode());
                    contentItem.setFileUrl(filePath);
                    contentItem.setMsgContent(emojiConverter(contentBody.getString("filename")));
                    break;
                case "ChatRecordVoice":
                    // 语音消息
                    sdkFileId = contentBody.getString("sdkfileid");

                    fileName = System.currentTimeMillis() + FileTypeConstant.FILE_AMR_DOT;
                    filePath = mediaFileUpload(wxCorpConfig, fileName, sdkFileId, md5sum, contentBody.getLong("voice_size"));

                    contentItem.setMsgType(ChatArchiveMsgTypeEnum.VOICE.getCode());
                    contentItem.setFileUrl(filePath);
                    contentItem.setMsgContent(emojiConverter(contentBody.getString("filename")));
                    break;
                case "ChatRecordVideo":
                    // 视频消息
                    sdkFileId = contentBody.getString("sdkfileid");

                    fileName = System.currentTimeMillis() + FileTypeConstant.FILE_MP4_DOT;
                    filePath = mediaFileUpload(wxCorpConfig, fileName, sdkFileId, md5sum, contentBody.getLong("filesize"));

                    contentItem.setMsgType(ChatArchiveMsgTypeEnum.VIDEO.getCode());
                    contentItem.setFileUrl(filePath);
                    contentItem.setMsgContent(emojiConverter(contentBody.getString("filename")));
                    break;
                default:
                    break;
            }
            contentItem.setContentData(contentBody.toJSONString());
            contentItem.setMsgId(content.getMsgId());
            contentItemList.add(contentItem);
        }

        if(!contentItemList.isEmpty()){

        }
        // TODO 缺保存
    }

    /**
     * 上传素材文件到OSS
     *
     * @param fileName  文件名称
     * @param sdkFileId sdkFieldId
     * @return 地址
     */
    public String mediaFileUpload(TbWxCorpAgentConfig wxCorpConfig, String fileName, String sdkFileId,
                                  String md5, Long filesize) {
        long startTime = System.currentTimeMillis();
        String cacheKey = CacheKeyEnum.CHECK_ARCHIVE_FILE_KEY.getKey() + SeparatorEnum.COLON.getSeparator() + md5;
        if (redisTemplate.hasKey(cacheKey)) {
            return Objects.requireNonNull(redisTemplate.opsForValue().get(cacheKey)).toString();
        }
        String indexBuf = "";
        long sdk = Finance.NewSdk();
        long ret = Finance.Init(sdk, wxCorpConfig.getCorpId(), wxCorpConfig.getAgentSecret());
        if (ret != 0) {
            Finance.DestroySdk(sdk);
            log.info("init sdk err ret " + ret);
            return null;
        }

        File mediaFile = new File(chatArchiveConfig.getUploadPath() + SeparatorEnum.SLASH.getSeparator() + fileName);
        while (true) {
            if (System.currentTimeMillis() - startTime > 1000 * 60 * 60) {
                log.warn("upload time out md5: {} fileSize: {} ", md5, filesize);
                return null;
            }

            //每次使用GetMediaData拉取存档前需要调用NewMediaData获取一个media_data，在使用完media_data中数据后，还需要调用FreeMediaData释放。
            long media_data = Finance.NewMediaData();
            ret = Finance.GetMediaData(sdk, indexBuf, sdkFileId, null, null, TIME_OUT, media_data);
            if (ret != 0) {
                Finance.FreeMediaData(media_data);
                return null;
            }
            try {
                //大于512k的文件会分片拉取，此处需要使用追加写，避免后面的分片覆盖之前的数据。
                FileOutputStream outputStream = new FileOutputStream(mediaFile, true);
                outputStream.write(Finance.GetData(media_data));
                outputStream.close();
            } catch (Exception e) {
                log.info("parse failed:读取文件失败，异常：", e);
            }

            if (Finance.IsMediaDataFinish(media_data) == 1) {
                //已经拉取完成最后一个分片
                Finance.FreeMediaData(media_data);
                break;
            } else {
                //获取下次拉取需要使用的indexbuf
                indexBuf = Finance.GetOutIndexBuf(media_data);
                Finance.FreeMediaData(media_data);
            }
        }
        Finance.DestroySdk(sdk);
        String corpId = wxCorpConfig.getCorpId();

        try {
            String datePath = buildDatePath();
            FileInputStream fileInputStream = new FileInputStream(mediaFile);
            String url = ossStorageFactory.build().upload(fileInputStream, OssStoragePathEnum.CHATARCHIVE_PATH.getPath() + datePath + fileName);
            log.info("文件OSS地址：{}, spend time: {}", url, System.currentTimeMillis() - startTime);

            fileInputStream.close();
            boolean isDelete = mediaFile.delete();
            log.info("临时文件删除结果：{}", isDelete);
            if (StringUtils.isNotBlank(url)) {
                redisTemplate.opsForValue().set(cacheKey, url, 4 * 60 * 60, TimeUnit.SECONDS);
            }
            if (System.currentTimeMillis() - startTime > 1000 * 60 * 10) {
                log.warn("upload time out all md5: {} fileSize: {} time: {}", md5, filesize, System.currentTimeMillis() - startTime);
            }
            return url;
        }catch (Exception ex){
            log.error("上传文件失败，fileName: {}, 失败原因：{}", fileName, ex.getMessage());
            log.error("上传文件失败，失败原因：", ex);
        }
        return null;
    }

    private String emojiConverter(String content) {
        if (EmojiUtils.containsEmoji(content)) {
            try {
                return EmojiUtils.emojiConvert1(content);
            } catch (UnsupportedEncodingException e) {
                log.info("parse error: convert emoji error: {}", e.getMessage());
            }
        }
        return content;
    }

    private String buildDatePath() {
        DateTime dateTime = DateUtil.date();
        String dayOfMethod = DateUtil.format(dateTime, "MMdd");

        return dateTime.year() + "/" + dayOfMethod + "/";
    }

}
