package com.cenker.scrm.client.knowledgebaseai.feign;

import com.cenker.scrm.constants.ServiceNameConstants;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.knowledgebaseai.CkAiRepository;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = ServiceNameConstants.KNOWLEDGEBASEAI_CENTER_SERVICE,path = "/knowledgebaseai/repository")
public interface CkAiRepositoryApiFeign {
    @RequestMapping("/list")
    TableDataInfo list(CkAiRepository ckAiRepository);
    @RequestMapping("/add")
    AjaxResult add(@RequestBody CkAiRepository ckAiRepository);
    @RequestMapping("/edit")
    AjaxResult edit(@RequestBody CkAiRepository ckAiRepository);
    @RequestMapping("/get/{id}")
    AjaxResult getCkAiRepositoryById(@RequestParam("id") Long id);
}
