package com.cenker.scrm.controller.corp;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.annotation.RepeatSubmit;
import com.cenker.scrm.client.wechat.feign.TbWxAutoTagFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.tag.TagRuleListDto;
import com.cenker.scrm.pojo.entity.wechat.TbWxAutoTag;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 群发controller
 */
@AllArgsConstructor
@RestController
@RequestMapping("/tp/autoTag")
public class TbWxAutoTagWebController {
    @Autowired
    private TbWxAutoTagFeign tbWxAutoTagFeign;

    @PostMapping("/addOrUpdate")
    @Log(module = ModuleEnum.AUTO_MARK_TAG, businessType = BusinessType.INSERT)
    @RepeatSubmit
    public AjaxResult addOrUpdate(@RequestBody @Valid TbWxAutoTag tbWxAutoTag) {
        return tbWxAutoTagFeign.addOrUpdate(tbWxAutoTag);
    }

    @PostMapping("/setStatus")
    @Log(module = ModuleEnum.AUTO_MARK_TAG, businessType = BusinessType.UPDATE)
    public AjaxResult setStatus(@RequestBody @Valid TbWxAutoTag tbWxAutoTag) {
        return tbWxAutoTagFeign.setStatus(tbWxAutoTag);
    }

    @DeleteMapping("/delete")
    //@PostMapping("/delete")
    @Log(module = ModuleEnum.AUTO_MARK_TAG, businessType = BusinessType.UPDATE)
    @RepeatSubmit
    public AjaxResult delete(@RequestBody @Valid TbWxAutoTag tbWxAutoTag) {
        return tbWxAutoTagFeign.delete(tbWxAutoTag);
    }

    @GetMapping("/list")
    public TableDataInfo list(TagRuleListDto tagRuleListDto) {
        return tbWxAutoTagFeign.list(tagRuleListDto);
    }

    @GetMapping(value = "/{autoTageId}")
    public AjaxResult getInfo(@PathVariable(value = "autoTageId") Long autoTageId) {
        return tbWxAutoTagFeign.getInfo(autoTageId);
    }

}
