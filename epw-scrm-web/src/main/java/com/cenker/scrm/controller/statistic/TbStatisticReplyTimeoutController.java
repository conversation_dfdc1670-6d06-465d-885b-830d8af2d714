package com.cenker.scrm.controller.statistic;

import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.wechat.feign.TbStatisticReplyTimeoutFeign;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.model.base.BaseController;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.entity.statistic.TbStatisticReplyTimeout;
import com.cenker.scrm.pojo.request.statistic.StatisticGraphQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticReplyTimeoutListQuery;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.file.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * 数据统计-回复超时 前端控制器
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/statistic/replyTimeout")
public class TbStatisticReplyTimeoutController extends BaseController {

    private final TbStatisticReplyTimeoutFeign tbStatisticReplyTimeoutFeign;

    /**
     * 统计
     */
    @GetMapping("/summary")
    public AjaxResult summary(StatisticSummaryQuery query) {
        return tbStatisticReplyTimeoutFeign.summary(query);
    }

    /**
     * 图表
     */
    @GetMapping("/graph")
    public AjaxResult graph(StatisticGraphQuery query) {
        return tbStatisticReplyTimeoutFeign.graph(query);
    }

    /**
     * 明细
     */
    @GetMapping("/list")
    public TableDataInfo list(StatisticReplyTimeoutListQuery query) {
        return tbStatisticReplyTimeoutFeign.list(query);
    }

    /**
     * 导出
     */
    @GetMapping("/export")
    @Log(module = ModuleEnum.REPLY_TIMEOUT_STATISTICS, businessType = BusinessType.EXPORT)
    public void export(StatisticReplyTimeoutListQuery query, HttpServletResponse response) throws UnsupportedEncodingException {
        ExcelUtil<TbStatisticReplyTimeout> util = new ExcelUtil<>(TbStatisticReplyTimeout.class);
        util.exportExcel(tbStatisticReplyTimeoutFeign.export(query), "回复超时", response);
        response.setContentType("application/octet-stream;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("回复超时-数据明细-"+ DateUtils.dateTimeNow(), "utf-8"));
    }

    /**
     * 更新数据
     */
    @PostMapping("/synData")
    @Log(module = ModuleEnum.REPLY_TIMEOUT_STATISTICS, businessType = BusinessType.UPDATE_DATA)
    public Result synData(@RequestBody StatisticSummaryQuery query) {
        Result<Object> PARAM_ERROR = validateDate(query);
        if (PARAM_ERROR != null) return PARAM_ERROR;
        return tbStatisticReplyTimeoutFeign.synData(query);
    }
}
