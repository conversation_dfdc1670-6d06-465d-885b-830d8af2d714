package com.cenker.scrm.controller.marketing;

import cn.hutool.core.collection.CollectionUtil;
import com.cenker.scrm.annotation.Log;
import com.cenker.scrm.client.wechat.feign.TbWxExtJourneyFeign;
import com.cenker.scrm.enums.ModuleEnum;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.BusinessType;
import com.cenker.scrm.handler.security.TokenService;
import com.cenker.scrm.model.base.TableDataInfo;
import com.cenker.scrm.pojo.dto.journey.ExternalJourneyDTO;
import com.cenker.scrm.pojo.dto.journey.ExternalJourneyStageDTO;
import com.cenker.scrm.pojo.dto.journey.ExternalJourneyTransferDTO;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.exception.ParameterException;
import com.cenker.scrm.pojo.valid.*;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.util.LogUtil;
import com.cenker.scrm.util.ServletUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2022/6/20
 * @Description
 */
@RestController
@RequestMapping("/external/journey")
public class TbWxExtJourneyController {
    @Autowired
    private TokenService tokenService;
    @Autowired
    private TbWxExtJourneyFeign tbWxExtJourneyFeign;

    @PostMapping
    @Log(module = ModuleEnum.JOURNEY, businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody @Validated(InsertGroup.class) ExternalJourneyDTO externalJourneyDTO) {
        LogUtil.logOperDesc(externalJourneyDTO.getJourneyName());

        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        externalJourneyDTO.setCorpId(user.getCorpConfigId());
        externalJourneyDTO.setCreateBy(Long.valueOf(user.getUserId()));
        return tbWxExtJourneyFeign.add(externalJourneyDTO);
    }

    @PutMapping
    @Log(module = ModuleEnum.JOURNEY, businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody @Validated(UpdateGroup.class) ExternalJourneyDTO externalJourneyDTO) {
        LogUtil.logOperDesc(externalJourneyDTO.getJourneyName());

        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        externalJourneyDTO.setCorpId(user.getCorpConfigId());
        externalJourneyDTO.setUpdateBy(Long.valueOf(user.getUserId()));
        return tbWxExtJourneyFeign.edit(externalJourneyDTO);
    }

    @DeleteMapping
    @Log(module = ModuleEnum.JOURNEY, businessType = BusinessType.DELETE)
    public AjaxResult remove(@RequestBody @Validated(DeleteGroup.class) ExternalJourneyDTO externalJourneyDTO) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        externalJourneyDTO.setCorpId(user.getCorpConfigId());
        externalJourneyDTO.setUpdateBy(Long.valueOf(user.getUserId()));
        return tbWxExtJourneyFeign.remove(externalJourneyDTO);
    }

    @GetMapping
    public TableDataInfo list(ExternalJourneyDTO externalJourneyDTO) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        externalJourneyDTO.setCorpId(user.getCorpConfigId());
        return tbWxExtJourneyFeign.list(externalJourneyDTO);
    }

    @GetMapping("/{journeyId}")
    public AjaxResult getInfo(@PathVariable("journeyId") Long journeyId) {
        ExternalJourneyDTO externalJourneyDTO = new ExternalJourneyDTO();
        externalJourneyDTO.setJourneyId(journeyId);
        externalJourneyDTO.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpConfigId());
        return tbWxExtJourneyFeign.getInfo(externalJourneyDTO);
    }


    @PostMapping("/sort")
    public AjaxResult sort(@RequestBody ExternalJourneyDTO externalJourneyDTO) {
        if (CollectionUtil.isEmpty(externalJourneyDTO.getOrderList())) {
            throw new ParameterException("参数错误");
        }
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        externalJourneyDTO.setCorpId(user.getCorpConfigId());
        return tbWxExtJourneyFeign.sort(externalJourneyDTO);
    }

    @GetMapping("/journeyDetail")
    public AjaxResult detail(@Validated(SelectGroup.class) ExternalJourneyDTO externalJourneyDTO) {
        externalJourneyDTO.setPageNum(ServletUtils.getParameterToInt("pageNum"));
        externalJourneyDTO.setPageSize(ServletUtils.getParameterToInt("pageSize"));
        externalJourneyDTO.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpConfigId());
        return tbWxExtJourneyFeign.journeyDetail(externalJourneyDTO);
    }

    @GetMapping("/stageDetail")
    public AjaxResult detail(@Validated(SelectGroup.class) ExternalJourneyStageDTO externalJourneyDTO) {
        externalJourneyDTO.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpConfigId());
        return tbWxExtJourneyFeign.stageDetail(externalJourneyDTO);
    }

    /**
     * 未设置阶段客户
     */
    @GetMapping("/neverStageCustomer")
    public TableDataInfo neverStageCustomer(ExternalJourneyDTO externalJourneyDTO) {
        externalJourneyDTO.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpConfigId());
        return tbWxExtJourneyFeign.neverStageCustomer(externalJourneyDTO);
    }

    /**
     * 设置客户到阶段 有初始阶段
     */
    @PostMapping("/setStageSingle")
    public AjaxResult setStageSingle(@Validated(SingleGroup.class) @RequestBody ExternalJourneyTransferDTO journeyTransferDTO){
        journeyTransferDTO.setDragTransfer(true);
        return setStage(journeyTransferDTO);
    }

    /**
     * 移除客户阶段
     */
    @PostMapping("/setStageOut")
    public AjaxResult setStageOut(@Validated(DeleteGroup.class) @RequestBody ExternalJourneyTransferDTO journeyTransferDTO){
        journeyTransferDTO.setDragTransfer(true);
        return setStage(journeyTransferDTO);
    }

    /**
     * 批量设置客户到阶段 无初始阶段
     */
    @PostMapping("/setStageBatch")
    public AjaxResult setStageBatch(@Validated(BatchGroup.class) @RequestBody ExternalJourneyTransferDTO journeyTransferDTO){
        journeyTransferDTO.setDragTransfer(false);
        return setStage(journeyTransferDTO);
    }

    @GetMapping("/getJourneySource")
    public TableDataInfo getJourneySource(ExternalJourneyDTO externalJourneyDTO){
        externalJourneyDTO.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpConfigId());
        return tbWxExtJourneyFeign.getJourneySource(externalJourneyDTO);
    }

    private AjaxResult setStage(ExternalJourneyTransferDTO journeyTransferDTO) {
        journeyTransferDTO.setCorpId(tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getCorpConfigId());
        journeyTransferDTO.setSource(TypeConstants.CREATE_SOURCE_SINCE_WEB);
        return tbWxExtJourneyFeign.setStage(journeyTransferDTO);
    }

}
