package com.cenker.scrm.efunds;

import com.cenker.scrm.efunds.model.AuthCenterUserRoleRequest;
import com.cenker.scrm.efunds.model.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 权限认证中心
 * <AUTHOR>
 */
@Component
public class EfundAuthCenterFeignCallback implements EfundAuthCenterFeignClient{

    @Override
    public Result synAuthCenter(@RequestBody AuthCenterUserRoleRequest authCenterRequest) {
        return null;
    }
}
