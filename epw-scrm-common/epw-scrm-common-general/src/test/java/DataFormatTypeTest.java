import com.cenker.scrm.pojo.entity.enums.DataFormatType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class DataFormatTypeTest {

    @Test
    public void testFormatDate() {
        String value = "20220101";
        String type = "DATE";
        String expected = "2022-01-01";

        String result = DataFormatType.format(value, type);
        Assertions.assertEquals(expected, result);
    }

    @Test
    public void testFormatMultiSelect() {
        String value = "option1,option2";
        String type = "MULTI_SELECT";
        String expected = "option1,option2";

        String result = DataFormatType.format(value, type);
        Assertions.assertEquals(expected, result);
    }

    @Test
    public void testFormatNumber() {
        String value = "123.456343242";
        String type = "NUMBER";
        String expected = "123.46";

        String result = DataFormatType.format(value, type);
        Assertions.assertEquals(expected, result);
    }

    @Test
    public void testFormatInteger() {
        String value = "123.0012";
        String type = "INTEGER";
        String expected = "123";

        String result = DataFormatType.format(value, type);
        Assertions.assertEquals(expected, result);
    }

    @Test
    public void testFormatRatio() {
        String value = "0.75";
        String type = "RATIO";
        String expected = "75%";

        String result = DataFormatType.format(value, type);
        Assertions.assertEquals(expected, result);
    }

    @Test
    public void testFormatCurrency() {
        String value = "12324230.75342";
        String type = "CURRENCY";
        String expected = "12,324,230.75";

        String result = DataFormatType.format(value, type);
        Assertions.assertEquals(expected, result);
    }

    @Test
    public void testFormatDefault() {
        String value = "test";
        String type = "UNKNOWN";
        String expected = "test";

        String result = DataFormatType.format(value, type);
        Assertions.assertEquals(expected, result);
    }
}

