package com.cenker.scrm.pojo.exception;

import com.cenker.scrm.enums.ErrCodeEnum;

/**
 * <AUTHOR>
 * @Date 2022/5/10
 * @Description
 */
public class DataNotExistOrDeletedException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    protected String message;

    private Integer code;

    public DataNotExistOrDeletedException(){
    }

    public DataNotExistOrDeletedException(ErrCodeEnum errCodeEnum) {
        this.message = errCodeEnum.getMessage();
        this.code = errCodeEnum.getCode();
    }

    public DataNotExistOrDeletedException(String message)
    {
        this.message = message;
    }

    public DataNotExistOrDeletedException(Integer code, String message)
    {
        this.code=code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
