package com.cenker.scrm.constants;

/**
 * 状态常量
 */
public interface StatusConstants {

    /**
     * 通用状态
     */
    // 是否删除 0否1是
    String DEL_FLAG_FALSE = "0";
    String DEL_FLAG_TRUE = "1";
    // 是否删除 0否1是
    int DEL_FLAG_FALSE_INT = 0;
    int DEL_FLAG_TRUE_INT = 1;
    // 是否展示 0否1是
    Integer SHOW_FLAG_FALSE = 0;
    Integer SHOW_FLAG_TRUE = 1;
    // 是否开启 0否 1是
    Integer CLOSE_FLAG = 0;
    Integer OPEN_FLAG = 1;

    /**
     * 客户群成员状态
     */
    String groupNormalStatus = "0";

    /**
     * 客户跟进状态 0正常 1 禁用 2删除 3 企业成员删除
     */
    String CUSTOMER_NORMAL_STATUS = "0";
    String CUSTOMER_DEL_STATUS = "2";
    String CUSTOMER_USER_DEL_STATUS = "3";

    /**
     * 待办状态 0 未完成 1 已完成 2 删除
     */
    String WAIT_TO_DO_DYNAMIC_NOT_FINISH = "0";
    String WAIT_TO_DO_DYNAMIC_FINISH = "1";
    String WAIT_TO_DO_DYNAMIC_DEL = "2";

    /**
     * 裂变活动状态 0 裂变不存在 1 进行中 2 已结束 3 未开始 4 无资格
     */
    Integer FISSION_ACTIVITY_INEXISTENCE = 0;
    Integer FISSION_ACTIVITY_NORMAL = 1;
    Integer FISSION_ACTIVITY_END = 2;
    Integer FISSION_ACTIVITY_NOT_START = 3;
    Integer FISSION_ACTIVITY_NOT_AUTHORITY = 4;

    /**
     * 裂变完成状态 0 有效 1无效
     */
    Integer FISSION_COMPLETE_RECORD_VALID = 0;
    Integer FISSION_COMPLETE_RECORD_INVALID = 1;

    /**
     * 裂变客户新老状态 1 新客户 2 老客户
     */
    int FISSION_NEW_CUSTOMER = 1;
    int FISSION_OLD_CUSTOMER = 2;

    /**
     * 智能物料标签状态  0 未开启 1 开启
     */
    Integer RADAR_CUSTOMER_TAG_TRUE = 1;

    /**
     * 智能物料成员名片  0 未开启 1 开启
     */
    Integer RADAR_CONTACT_STATUS_TRUE = 1;

    /**
     * 发朋友圈 0 立即发送 1 定时发送
     */
    int TIMED_TASK_SEND_ONCE = 0;
    int TIMED_TASK_SEND_TIMING = 1;

    // 发送状态 0 等待发送 1 成功发送 2 开始创建 3 创建中 4 创建失败
    int CHECK_STATUS_WAIT = 0;
    int CHECK_STATUS_SUCCESS = 1;
    int CHECK_STATUS_START = 2;
    int CHECK_STATUS_ING = 3;
    int CHECK_STATUS_FAIL = 4;

    // 是否发送朋友圈
    int PUBLISH_MOMENT_TRUE = 1;
    int PUBLISH_MOMENT_FALSE = 0;

    // 是否可见客户
    int VISIBLE_CUSTOMER = 1;
    int INVISIBLE_CUSTOMER = 0;

    /**
     * 话术库 1 全部 2 选择
     */
    int QUICK_REPLY_SEND_SCOPE_ALL = 1;
    int QUICK_REPLY_SEND_SCOPE_SELECT = 2;

    /**
     * 0 组合话术 1 文本 2 图片 3 链接 4 视频 5 小程序 6 智能物料
     */
    int QUICK_REPLY_MULTI_ATTACHMENT = 0;
    int QUICK_REPLY_TEXT = 1;
    int QUICK_REPLY_IMAGE = 2;
    int QUICK_REPLY_LINK = 3;
    int QUICK_REPLY_VIDEO = 4;
    int QUICK_REPLY_MINIPROGRAM = 5;
    int QUICK_REPLY_RADAR = 6;

    /**
     * 1 表示企业标签、为2 表示个人标签
     */
    int CORP_TAG=1;
    int INDIVIDUAL_TAG=2;

    /**
     * 订阅菜单发布状态  1 已发布 0 未发布
     */
    int SUBSCRIBE_MENU_PUBLISH_TRUE = 1;
    int SUBSCRIBE_MENU_PUBLISH_FALSE = 0;

    /**
     * 客户标签同步状态  1 已同步 0 待同步
     */
    String CUSTOMER_TAG_SYNC_TRUE = "1";
    String CUSTOMER_TAG_SYNC_FALSE = "0";
}
