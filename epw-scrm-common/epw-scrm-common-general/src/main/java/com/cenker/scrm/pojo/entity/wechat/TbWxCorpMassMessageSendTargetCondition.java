package com.cenker.scrm.pojo.entity.wechat;

import com.cenker.scrm.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 群发消息-发送对象信息-条件对象 tb_wx_corp_mass_message_send_target_condition
 *
 * <AUTHOR>
 * @date 2021-02-05
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCorpMassMessageSendTargetCondition {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 条件id
     */
    @Excel(name = "条件id")
    private String conditionId;

    /**
     * 条件类型 1 标识 企业成员id，2 标识 企业部门id， 3 标识 标签id ，4 标识 排查标签id
     */
    @Excel(name = "条件类型 1 标识 企业成员id，2 标识 企业部门id， 3 标识 标签id ，4 标识 排查标签id")
    private String type;

    /**
     * 0 标识有效 1标识删除
     */
    private Integer delFlag;

    /**
     * 公司id
     */
    @Excel(name = "公司id")
    private String corpId;

    /**
     * message info 主键
     */
    @Excel(name = "message info 主键")
    private Long messageInfoId;


}