package com.cenker.scrm.pojo.entity.knowledgebaseai;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName(value = "ck_ai_app_repository_mapping",autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CkAiAppRepositoryMapping {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long appId;

    private Long repositoryId;

    private String remark;
    /**
     * 逻辑删除 0 表示未删除，1 表示删除
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
}
