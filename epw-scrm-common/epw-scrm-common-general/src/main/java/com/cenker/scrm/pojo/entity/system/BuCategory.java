package com.cenker.scrm.pojo.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 业务分组表
 * @TableName bu_category
 */
@TableName(value ="bu_category")
@Data
public class BuCategory implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long categoryId;

    /**
     * 分组名称
     */
    private String categoryName;

    /**
     * 分组类型
     */
    private String categoryType;

    /**
     * 父级分组id
     */
    private Long parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 所属部门id
     */
    private Long deptId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 0 标识未删除 1 标识删除 
     */
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}