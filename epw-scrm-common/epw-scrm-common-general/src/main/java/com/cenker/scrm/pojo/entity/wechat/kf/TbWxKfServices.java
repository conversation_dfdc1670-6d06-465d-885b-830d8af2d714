package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 微信客服接待员对象 tb_wx_kf_services
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxKfServices extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 客户账号ID，tb_wx_kf_account的id
     */
    private Long kfId;

    /**
     * 员工ID
     */
    private String userId;

    /**
     * 部门ID
     */
    private String departmentId;
    @TableLogic
    private Integer delFlag;

}
