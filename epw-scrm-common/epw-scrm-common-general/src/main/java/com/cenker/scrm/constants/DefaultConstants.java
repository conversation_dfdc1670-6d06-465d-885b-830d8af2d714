package com.cenker.scrm.constants;

/**
 * 默认常量
 */
public interface DefaultConstants {

    /**
     * 进入工作台二维码
     */
//    String ENTER_WORKBENCH_CODE_URL_TEST = "https://static.chengkeyun.com/wwd00f0c769c162149/scrm/corp/avatar/124d6282-c3e7-4536-bd8b-7935c914da02.png";
//    String ENTER_WORKBENCH_CODE_URL_PROD = "https://static.chengkeyun.com/wwd00f0c769c162149/scrm/corp/avatar/124d6282-c3e7-4536-bd8b-7935c914da02.png";

    /**
     * 企业微信默认前缀 ww 明文 wp 密文
     */
    String CORP_ID_PREFIX = "ww";
    String OPEN_CORP_ID_PREFIX = "wp";

    String SUCCESS = "success";
    String ILLEGALITY_REQUEST = "非法请求";

    /**
     * 智能物料图文默认图
     */
    //String RADAR_CONTENT_COVER = "https://wework.qpic.cn/wwpic/322470_lvURBHFxQtSiy9H_1633775089/0";
    //String RADAR_CONTENT_COVER = "https://wework.qpic.cn/wwpic/909989_VfYY6ag6QJmwCTi_1638963565/0";

    String RADAR_CONTENT_COVER = CacheKeyConstants.SYS_CONFIG_KEY + "radar.content.cover";

    String DEFAULT_USER_AVATAR = CacheKeyConstants.SYS_CONFIG_KEY + "default.user.avatar";

    // 文章默认提示
    String DEFAULT_RADAR_TIP = CacheKeyConstants.SYS_CONFIG_KEY + "article_tip";

    /**
     * 默认消息图片
     */
    String DEFAULT_MSG_PIC = CacheKeyConstants.SYS_CONFIG_KEY + "default.msg.pic";


    /**
     * 外部客户默认头像
     */
//    String EXTERNAL_CUSTOMER_IMG = "https://rescdn.qqmail.com/node/wwmng/wwmng/style/images/independent/DefaultAvatar$73ba92b5.png";

    /**
     * 员工默认头像
     */
    String STAFF_DEFAULT_AVATAR = "https://rescdn.qqmail.com/node/wwmng/wwmng/style/images/independent/DefaultAvatar$73ba92b5.png";
    /**
     * 企业微信默认头像
     */
//    String EPW_DEFAULT_AVATAR = "https://rescdn.qqmail.com/node/wwmng/wwmng/style/images/independent/DefaultAvatar$73ba92b5.png";

    /**
     * 成客logo
     */
    String CKY_LOGO = "https://static.chengkeyun.com/wwd00f0c769c162149/scrm/corp/avatar/124d6282-c3e7-4536-bd8b-7935c914da02.png";

    /**
     * 企微logo
     */
    String QR_LOGO = CacheKeyConstants.SYS_CONFIG_KEY + "qr.logo";

    // 客服
    String CKY_CUSTOMER_SERVICE_STAFF_AVATAR = "https://wework.qpic.cn/wwpic/686812_AZXJwBOQT2ekEni_1652433272/0";

    /**
     * 引流短链 默认测试链接名
     */
    String DRAINAGE_SHORT_LINK_TEST_LINK_NAME = "默认测试短链";
    /**
     * 短链值默认前缀
     */
    String DRAINAGE_SHORT_LINK_TEST_PREFIX = "test";
    /**
     * 小程序测试页面默认短链值
     */
    String DRAINAGE_SHORT_LINK_TEST_SHORT_VALUE = "cky_Qo5qcqgw";

    /**
     * 工作台配置（站点活码）默认图
     */
    String CITY_CONTACT_DEFAULT_BACK_IMG = "";

    /**
     * 工卡背景图
     */
    String CARD_BG = CacheKeyConstants.SYS_CONFIG_KEY + "card.bg";

    /**
     * 引导语
     */
    String GUIDE_CONTENT = "扫码添加我的企业微信为您定制更多专属服务";
    /**
     * 页面标题
     */
    String PAGE_TITLE = "添加客服";
    /**
     * 昵称
     */
    String NICK_NAME = "成客客服";
    /**
     * 企业名称
     */
    String CORP_NAME = "成客数科";

    /**
     * 专属客服二维码、专属客服描述  csRemark
     */
    String CS_QR_CODE_URL = "https://static.chengkeyun.com/scrm/post/a1e727a5-dd62-49f2-87a2-07e2d10657e5.png";
    String CS_REMARK = "添加专属客服，在使用过程中遇到任何问题我都会帮您解决";
    /**
     * 企业头像
     */
//    String CORP_AVATAR = "https://static.chengkeyun.com/cky/epw_saas/icon/7399f7f0-71e7-41a0-b177-fa03255fdc72.png";

    /**
     * 文章链接开头  article link
     */
    String ARTICLE_LINK_BEGIN = "https://mp.weixin.qq.com/s";

    /**
     * 默认公司名长度
     */
    Integer CORP_NAME_LENGTH = 15;
    /**
     * 默认职位名长度
     */
    Integer POSITION_NAME_LENGTH = 10;
    /**
     * 推送全部客户
     */
    String PUSH_ALL_CUSTOMER = "0";
    /**
     * 推送指定客户
     */
    String PUSH_RANGE_CUSTOMER = "1";

    /**
     * 私有化企业id
     */
    long PRIVATE_CORP_CONFIG_ID = 1L;

    /**
     * sop 扫码人群包默认cron表达式
     */
    String CONDITION_SOP_SCAN_CRON = "0 0/10 * * * ?";
    String JOURNEY_SOP_SCAN_CRON = "0 0/15 * * * ?";
}
