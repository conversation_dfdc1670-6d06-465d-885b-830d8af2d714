package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;

/**
 * 微信客服 聊天记录 channels_shop_product 文件 对象 tb_wx_kf_msg
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wk_wx_kf_msg_channels_shop_product")
public class WkWxKfMsgChannelsShopProduct extends WkWxKfMsgItemCommonResp {

    private String productId;
    private String headImg;
    @TableField("`title`")
    private String title;
    private String salesPrice;
    private String shopNickname;
    private String shopHeadImg;

    @Override
    public WkWxKfMsgChannelsShopProduct init(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        super.init(msgItem);

        this.productId = msgItem.getChannelsShopProduct().getProductId();
        this.headImg = msgItem.getChannelsShopProduct().getHeadImg();
        this.title = msgItem.getChannelsShopProduct().getTitle();
        this.salesPrice = msgItem.getChannelsShopProduct().getSalesPrice();
        this.shopNickname = msgItem.getChannelsShopProduct().getShopNickname();
        this.shopHeadImg = msgItem.getChannelsShopProduct().getShopHeadImg();

        return this;
    }
}
