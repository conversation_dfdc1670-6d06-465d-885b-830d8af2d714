package com.cenker.scrm.constants;

import java.util.Arrays;
import java.util.Comparator;

/**
 * 文件类型常量
 */
public interface FileTypeConstant {

     String IMAGE_PNG ="png";
     String IMAGE_PNG_UPPER_CASE ="PNG";
     String IMAGE_PNG_DOT =".png";

     String IMAGE_JPG ="jpg";
     String IMAGE_JPG_UPPER_CASE ="JPG";
     String IMAGE_JPG_DOT =".jpg";

     String IMAGE_WEBP ="webp";
     String IMAGE_WEBP_UPPER_CASE ="WEBP";
     String IMAGE_WEBP_DOT =".webp";

     String IMAGE_JPEG ="jpeg";
     String IMAGE_JPEG_UPPER_CASE ="JPEG";
     String IMAGE_JPEG_DOT =".jpeg";

     String IMAGE_GIF ="gif";
     String IMAGE_GIF_UPPER_CASE ="GIF";
     String IMAGE_GIF_DOT =".gif";

     String IMAGE_BMP ="bmp";
     String IMAGE_BMP_UPPER_CASE ="BMP";
     String IMAGE_BMP_DOT =".bmp";

     String FILE_TXT = "txt";
     String FILE_TXT_DOT = ".txt";

     String FILE_AMR = "amr";
     String FILE_AMR_DOT = ".amr";

     String FILE_MP4 = "mp4";
     String FILE_MP4_DOT = ".mp4";

     String FILE_HTML = "html";
     String FILE_HTML_DOT = ".html";

     String FILE_HTM = "htm";
     String FILE_HTM_DOT = ".htm";

     String FILE_XHTML = "xhtml";
     String FILE_XHTML_DOT = ".xhtml";

     String FILE_PDF = "pdf";
     String FILE_PDF_DOT = ".pdf";

     String FILE_PPT = "ppt";
     String FILE_PPT_DOT = ".ppt";

     String FILE_PPTX = "pptx";
     String FILE_PPTX_DOT = ".pptx";

     String FILE_DOC = "doc";
     String FILE_DOC_DOT = ".doc";

     String FILE_DOCX = "docx";
     String FILE_DOCX_DOT = ".docx";

     String FILE_XLS = "xls";
     String FILE_XLS_DOT = ".xls";

     String FILE_XLSX = "xlsx";
     String FILE_XLSX_DOT = ".xlsx";
}
