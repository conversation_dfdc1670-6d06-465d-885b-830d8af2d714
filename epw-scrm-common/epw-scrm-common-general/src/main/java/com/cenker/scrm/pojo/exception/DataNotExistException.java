package com.cenker.scrm.pojo.exception;

/**
 * <AUTHOR>
 * @Date 2022/5/10
 * @Description
 */
public class DataNotExistException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    protected String message;

    private Integer code;

    public DataNotExistException(){
    }

    public DataNotExistException(String message)
    {
        this.message = message;
    }

    public DataNotExistException(Integer code, String message)
    {
        this.code=code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
