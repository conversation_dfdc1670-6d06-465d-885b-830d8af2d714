package com.cenker.scrm.pojo.request;

import com.cenker.scrm.pojo.valid.BatchGroup;
import com.cenker.scrm.pojo.valid.DeleteGroup;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import com.cenker.scrm.pojo.vo.UserVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/24
 * @Description
 */
@Data
public class ContactDeliveryUserRequest{
    @NotBlank(message = "请选择修改配送员", groups = {UpdateGroup.class, BatchGroup.class, DeleteGroup.class})
    private String id;
    /**
     * 启用状态 0 未启用 1 启用
     */
    @NotNull(message = "请选择启用状态", groups = {InsertGroup.class, UpdateGroup.class})
    @DecimalMax(value = "1", message = "非法请求")
    @DecimalMin(value = "0", message = "非法请求")
    private Integer userStatus;

    /**
     * 选择成员
     */
    @NotNull(message = "请选择配送员", groups = {InsertGroup.class})
    @Size(min = 1, message = "请至少选择一位配送员", groups = {InsertGroup.class})
    @Valid
    private List<UserVO> userList;

    /**
     * 门店id
     */
    @NotBlank(message = "请选择门店", groups = {InsertGroup.class, UpdateGroup.class, BatchGroup.class})
    private String storeId;

    /**
     * 企业ID
     */
    private Long corpConfigId = 1L;
    private Long createBy;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private Long updateBy;

    private String mobile;
    private String userName;
    private String[] storeIds;
}
