package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 素材分类信息对象 tb_wx_category
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_wx_category")
public class TbWxCategory extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 0 图片（image）、1 语音（voice）、2 视频（video），3 普通文件(file) 5(post)海报
     */
    private Integer mediaType;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父分类的id
     */
    private String parentId;

    /**
     * 0  未删除 1 已删除
     */
    private Integer delFlag;

    /**
     * $column.columnComment
     */
    private String corpId;

    private Integer orderNum;

    /**
     * 子部门
     */
    @TableField(exist = false)
    private List<TbWxCategory> children = new ArrayList<>();

    /**
     * 组排序
     */
    @TableField(exist = false)
    private List<TbWxCategory> orderList;
}
