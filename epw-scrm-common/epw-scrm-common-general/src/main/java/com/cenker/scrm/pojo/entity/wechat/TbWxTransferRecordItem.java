package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.pojo.entity.enums.EnumTransferType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户转移记录项实体类，用于记录具体的客户或客户群转移细节。
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxTransferRecordItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业ID。
     */
    private String corpId;

    /**
     * 记录项ID。
     */
    @TableId
    private Long id;

    /**
     * 关联的转移记录ID。
     */
    private String transferId;

    /**
     * 交接人用户ID。
     */
    private String handoverUserId;

    /**
     * 接管人用户ID。
     */
    private String takeoverUserId;

    /**
     * 资源类型，0代表客户，1代表客户群。
     */
    private int resourceType;

    /**
     * 资源ID，根据resourceType决定是客户ID还是客户群ID。
     */
    private String resourceId;

    /**
     * 接替状态：resourceType为0代表客户转移状态：1-接替完毕，2-等待接替，3-客户拒绝，4-接替成员客户达到上限。，5-继承失败 6 继承过于频繁
     * resourceType为0代表群转移状态：1-接替完毕 2-接替失败 3-继承过于频繁
     */
    private String status;

    /**
     * 记录的创建者。
     */
    private String createBy;

    /**
     * 记录的创建时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 记录的更新者。
     */
    private String updateBy;

    /**
     * 记录的更新时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 转移失败错误码
     */
    private String errCode;

    /**
     *转移失败错误信息
     */
    private String errorMsg;

    /**
     * 转移类型 ON_JOB(在职转移)，OFF_JOB(离职转移)
     */
    private EnumTransferType transferType;

    public static List<TbWxTransferRecordItem> filterItemsByTypeAndStatus(List<TbWxTransferRecordItem> items, int type, String status) {
        return items.stream()
                .filter(item -> item.getResourceType() == type && (status == null || item.getStatus().equals(status)))
                .collect(Collectors.toList());
    }
}
