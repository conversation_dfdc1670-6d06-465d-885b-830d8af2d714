package com.cenker.scrm.pojo.entity.session;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

/**
 * 会话超时时长设置对象 ck_session_timeout_set
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class CkSessionTimeoutSet extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 设置ID */
    private Long setId;

    /** 超时时长单位分钟 */
    private Long timeNum;
    private String startTime;
    private String endTime;

}
