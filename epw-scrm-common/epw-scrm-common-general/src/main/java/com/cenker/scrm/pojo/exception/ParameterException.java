package com.cenker.scrm.pojo.exception;

import com.cenker.scrm.enums.ErrCodeEnum;

/**
 * <AUTHOR>
 * @Date 2023/5/6
 * @Description
 */
public class ParameterException extends RuntimeException {
    protected String message;

    private Integer code;

    public ParameterException(String message) {
        this.message = message;
    }

    public ParameterException(ErrCodeEnum errCodeEnum) {
        this.message = errCodeEnum.getMessage();
        this.code = errCodeEnum.getCode();
    }

    public ParameterException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public ParameterException(String message, Throwable e) {
        super(message, e);
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }
}
