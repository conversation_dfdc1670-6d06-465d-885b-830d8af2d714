package com.cenker.scrm.pojo.entity.enums;
/****
 * 标签功能类型枚举
 *
 */
public enum GroupTagTypeEnum {
    UNIQUE("unique", "唯一型"),
    PROGRESSIVE("progressive", "递进型"),
    OVERLAPPING("overlapping", "重叠型");

    private final String code;
    private final String name;

    // 增加构造方法
    GroupTagTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    // 增加 getCode 方法
    public String getCode() {
        return code;
    }

    // 增加 getName 方法
    public String getName() {
        return name;
    }
}