package com.cenker.scrm.pojo.entity.statistic;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 数据统计-客户群数据
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Data
@TableName("tb_statistic_customer_group")
public class TbStatisticCustomerGroup {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", dateFormat = "yyyy-MM-dd", sort = 1)
    private Date statisticDate;

    /**
     * 群ID
     */
    @Excel(name = "群ID", sort = 3)
    private String groupId;

    /**
     * 群聊名称
     */
    @Excel(name = "群聊名称", sort = 2)
    private String groupName;

    /**
     * 群主ID
     */
    private String owner;

    /**
     * 群主名称
     */
    @Excel(name = "群主", sort = 4)
    private String ownerName;

    /**
     * 群聊消息总数
     */
    @Excel(name = "群聊消息总数", sort = 5)
    private Integer groupChatNum;

    /**
     * 群成员总数
     */
    @Excel(name = "群成员总数", sort = 6)
    private Integer memberNum;

    /**
     * 群员工总数
     */
    @Excel(name = "群中员工总数", sort = 7)
    private Integer staffNum;

    /**
     * 入群人数
     */
    @Excel(name = "入群客户数", sort = 8)
    private Integer memberInNum;

    /**
     * 退群人数
     */
    @Excel(name = "退群客户数", sort = 9)
    private Integer memberOutNum;

    /**
     * 活跃客户数
     */
    @Excel(name = "活跃客户数", sort = 10)
    private Integer activeCustomerNum;

    /**
     * 净增长人数
     */
    @Excel(name = "净增人数", sort = 11)
    private Integer netNewNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 群解散时间
     */
    private Date dismissDate;

    /**
     * 部门id
     */
    private Integer deptId;

}
