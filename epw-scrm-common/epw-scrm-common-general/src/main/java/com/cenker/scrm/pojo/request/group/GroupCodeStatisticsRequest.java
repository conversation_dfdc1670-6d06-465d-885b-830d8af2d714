package com.cenker.scrm.pojo.request.group;

import com.cenker.scrm.pojo.valid.DetailGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/8/28
 * @Description
 */
@Data
public class GroupCodeStatisticsRequest extends GroupCodeRequest {

    /**
     * 查询类型 1 按群聊查看 2 按日期查看
     */
    @NotNull(message = "缺失查询类型", groups = {DetailGroup.class})
    private Integer statisticsType;

    /**
     * 群名
     */
    private String chatGroupName;

    /**
     * 群主名
     */
    private String chatOwnerName;

    /**
     * 查询开始时间
     */
    private String beginTime;
    /**
     * 查询结束时间
     */
    private String endTime;

    /**
     * 查询开始时间
     */
    private Date queryBeginTime;
    /**
     * 查询结束时间
     */
    private Date queryEndTime;

    /**
     * 1 进群 2 退群
     */
    private Integer changeType;
}
