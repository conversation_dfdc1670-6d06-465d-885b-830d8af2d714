package com.cenker.scrm.pojo.entity.enums;

public enum CkSessionEnum {

    ACT_TYPE_SENN_MOBILE("1","发送手机号码"),
    ACT_TYPE_SENN_EMAIL("2","发送邮箱地址"),
    ACT_TYPE_SENN_ACCP_REDPAGE("3","发送和接收红包"),
    ACT_TYPE_SENN_CARD("4","发送名片"),
    ACT_TYPE_SENN_FILE("5","发送文件");


    private final String value;
    private final String desc;

    CkSessionEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getValueByDesc(String desc) {
        for (CkSessionEnum e : CkSessionEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e.getValue();
            }
        }
        //throw new IllegalArgumentException("Invalid key: " + key);
        return "";
    }
    public static String getDescByValue(String value) {
        for (CkSessionEnum e : CkSessionEnum.values()) {
            if (e.getValue().equals(value)) {
                return e.getDesc();
            }
        }
        //throw new IllegalArgumentException("Invalid key: " + key);
        return "";
    }
}
