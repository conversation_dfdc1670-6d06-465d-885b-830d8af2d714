package com.cenker.scrm.pojo.entity.content.material;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 欢迎语模板对象 bu_welcome_contact_template
 * 
 * <AUTHOR>
 * @date 2025-05-24
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("bu_welcome_contact_template")
public class BuWelcomeContactTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 企业ID */
    private String corpId;

    /** 欢迎语模板名称 */
    private String name;

    /** 欢迎语 */
    private String content;

    /** 欢迎语附件 */
    private String attachments;

    /** 是否删除：1：删除，0:正常 */
    private Integer delFlag;

    /** 状态：1:启用，0:停用 */
    private Integer status;

    /** 部门id */
    private Integer deptId;

}
