package com.cenker.scrm.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 企业成员对象 tb_wx_user
 *
 * <AUTHOR>
 * @date 2021-01-22
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WechatUser {
    private static final long serialVersionUID = 1L;
    private String id;
    /**
     * 成员UserID。对应管理端的帐号
     */
    // @Excel(name = "成员UserID。对应管理端的帐号")
    private String userid;

    /**
     * 成员名称
     */
    // @Excel(name = "成员名称")
    private String name;

    /**
     * 手机号码
     */
    // @Excel(name = "手机号码")
    private String mobile;

    /**
     * 成员所属部门id列表，仅返回该应用有查看权限的部门id
     */
    // @Excel(name = "成员所属部门id列表，仅返回该应用有查看权限的部门id")
    private String department;

    /**
     * 部门内的排序值
     */
    // @Excel(name = "部门内的排序值")
    @TableField("`order`")
    private String order;

    /**
     * 职务信息
     */
    // @Excel(name = "职务信息")
    private String position;

    /**
     * 0表示未定义，1表示男性，2表示女性
     */
    // @Excel(name = "0表示未定义，1表示男性，2表示女性")
    private String gender;

    /**
     * 邮箱
     */
    // @Excel(name = "邮箱")
    private String email;

    /**
     * 表示在所在的部门内是否为上级。0-否；1-是。
     */
    // @Excel(name = "表示在所在的部门内是否为上级。0-否；1-是。")
    private String isLeaderInDept;

    /**
     * 头像url
     */
    // @Excel(name = "头像url")
    private String avatar;

    /**
     * 头像缩略图url
     */
    // @Excel(name = "头像缩略图url")
    private String thumbAvatar;

    /**
     * 座机
     */
    // @Excel(name = "座机")
    private String telephone;

    /**
     * 别名
     */
    // @Excel(name = "别名")
    private String alias;

    /**
     * 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业
     */
    // @Excel(name = "激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业")
    private Integer status;

    /**
     * 员工个人二维码
     */
    // @Excel(name = "员工个人二维码")
    private String qrCode;

    /**
     * 对外职务
     */
    // @Excel(name = "对外职务")
    private String externalPosition;

    /**
     * 地址
     */
    // @Excel(name = "地址")
    private String address;

    /**
     * 全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节
     */
    // @Excel(name = "全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节")
    private String openUserid;

    /**
     * 主部门
     */
    // @Excel(name = "主部门")
    private String mainDepartment;

    /**
     * 主部门名称
     */
    @TableField(exist = false)
    private String mainDepartmentName;

    /**
     * 企业ID
     */
    // @Excel(name = "企业ID")
    private String corpId;

    /**
     * 离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    // @Excel(name = "离职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dimissionTime;

    /**
     * 离职是否分配（0=未分配，1=已分配）
     */
    // @Excel(name = "离职是否分配", readConverterExp = "0==未分配，1=已分配")
    private Integer isAllocate;

    /**
     * 客户标签,字符串使用逗号隔开
     */
    // @Excel(name = "客户标签,字符串使用逗号隔开")
    private String customerTags;

    private String delFlag;
}