package com.cenker.scrm.pojo.dto;


import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class WxLeaveUserDto implements Serializable {
    /**
     *
     */
    private Map<String, String> params;
    /**
     * 企业成员
     */
    private List<String> userIds;

    private String corpId;

    /**
     * 离职开始时间
     */
    private String beginTime;
    /**
     * 离职结束时间
     */
    private String endTime;

    private Integer allocate;

    private String handOverUserId;

    private String corpUserName;
}
