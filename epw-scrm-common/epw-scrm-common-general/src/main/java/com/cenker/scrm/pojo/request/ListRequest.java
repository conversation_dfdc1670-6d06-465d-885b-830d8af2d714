package com.cenker.scrm.pojo.request;

import com.cenker.scrm.model.base.BaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/9/5
 * @Description 列表查询
 */
@Data
public class ListRequest extends BaseRequest {

    private Long id;
    /**
     * 一般是任务名
     */
    private String name;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 查询开始时间
     */
    private String beginTime;

    /**
     * 查询结束时间
     */
    private String endTime;

    /**
     * 排序类型 1...
     */
    private Integer orderType;

    /**
     * 自定义查询类型
     */
    private Integer type;

    private Long corpConfigId;
    private String corpId;

    /**
     * 查询类型 single，表示发送给客户，group表示发送给客户群
     */
    private String chatType;

    private String checkStatus;

    private String approvalUser;
}
