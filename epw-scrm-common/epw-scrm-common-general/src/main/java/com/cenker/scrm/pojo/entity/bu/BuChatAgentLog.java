package com.cenker.scrm.pojo.entity.bu;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ChatAgent会话记录表 bu_chat_agent_log
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
@TableName(value = "bu_chat_agent_log",autoResultMap = true)
public class BuChatAgentLog implements Serializable {

    private static final long serialVersionUID = -6985861864076960449L;

    /** 主键 */
    private Long id;

    /** agentSn */
    @Excel(name = "agentSn")
    private String agentSn;

    /** sessionSn */
    @Excel(name = "sessionSn")
    private String sessionSn;

    /** 提问内容 */
    @Excel(name = "提问内容")
    private String question;

    /** 提问语气类型 */
    @Excel(name = "提问语气类型")
    private String type;

    /** Agent平台回答内容 */
    @Excel(name = "Agent平台回答内容")
    private String agentAnswer;

    /** 用户发送内容 */
    @Excel(name = "用户发送内容")
    private String sendAnswer;

    /** 消息id */
    @Excel(name = "消息id")
    private String msgSn;

    /** UserId */
    @Excel(name = "UserId")
    private String userId;

    /** 部门id */
    @Excel(name = "部门id")
    private Integer deptId;

    /** 消息时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "消息时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date msgTime;

    /** 企业ID */
    @Excel(name = "企业ID")
    private String corpId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 是否深度思考
     */
    private String thinkSwitch;
    /**
     * 停止生成
     */
    private String stopAsk;
}