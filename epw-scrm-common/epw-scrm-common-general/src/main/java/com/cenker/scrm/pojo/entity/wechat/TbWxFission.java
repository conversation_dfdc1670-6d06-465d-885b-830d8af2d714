package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

@TableName("tb_wx_fission")
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxFission {

    private static final long serialVersionUID = 6991089940616896801L;
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 活动类型，1 任务裂变 2 群裂变
     */
    @NotNull(message = "非法请求")
    private Integer fissionType;
    /**
     * 活动名称
     */
    @Size(message = "活动名称过长",max = 30)
    private String taskName;
    /**
     * 裂变引导语
     */
    private String fissInfo;
    /**
     * 裂变客户数量
     */
    @DecimalMax(message = "请输入不超过5位数的正整数",value = "99999")
    private Integer fissNum;
    /**
     * 活动开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 活动结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 客户标签id列表，当为全部时保存为all
     */
    private String customerTagId;
    /**
     * 客户标签名称列表，为all是可为空
     */
    private String customerTag;
    /**
     * 海报id
     */
    private long postersId;
    /**
     * 裂变海报路径
     */
    private String postersUrl;
    /**
     * 任务裂变目标员工/群裂变id
     */
    private String fissionTargetId;
    /**
     * 任务裂变目标员工姓名/群裂变二维码地址
     */
    private String fissionTarget;
    /**
     * 任务裂变目标二维码
     */
    private String fissQrcode;
    /**
     * 兑奖链接
     */
    private String rewardUrl;
    /**
     * 兑奖链接图片
     */
    private String rewardImageUrl;
    /**
     * 兑奖规则
     */
    private String rewardRule;
    /**
     * 裂变活动状态，1 进行中 2 已结束
     */
    private Integer fissStatus;
    /**
     * 新客欢迎语
     */
    private String welcomeMsg;
    /**
     * 0:正常;2:删除;
     */
    private Integer delFlag;

    private String corpId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 发起成员
     */
    @TableField(exist = false)
    private List<TbWxFissionStaff> tbWxFissionStaffs;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
