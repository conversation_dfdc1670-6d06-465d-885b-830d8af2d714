package com.cenker.scrm.pojo.request.statistic;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/4/16
 * @Description 数据统计-明细查询通用对象
 */
@Data
public class StatisticCommonListQuery extends StatisticSummaryQuery {

    /**
     * 查询分页页码
     */
    private Integer pageSize;
    /**
     * 查询分页大小
     */
    private Integer pageNum;

    /**
     * 排序字段
     */
    private String orderByColumn;
    /**
     * 排序方法：desc 或 asc
     */
    private String sort;
}
