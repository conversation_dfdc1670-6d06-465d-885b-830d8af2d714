package com.cenker.scrm.pojo.vo.contact;


import com.cenker.scrm.pojo.vo.tag.ExternalUserTagVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 成员信息VO
 */
@Data
public class CorpUserInfoVO implements Serializable {
    private static final long serialVersionUID = 3715204313911380179L;

    @JsonSerialize(using = ToStringSerializer.class)
    private String id;

    private String userId;
    /**
     * 企业成员名称
     */
    private String userName;
    /**
     * 企业成员头像
     */
    private String avatar;
    /**
     * 企业人员所在部门
     */
    private String deptName;
    /**
     * 添加外部联系人时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addDate;
    /**
     * 企业成员打标签
     */
    private String tag;
    /**
     * 备注
     */
    private String remark;
    /**
     * 描述
     */
    private String description;
    /**
     * 来源方式
     */
    private String addWay;
    /**
     * 客户状态
     */
    private String status;
    /**
     * 添加渠道
     */
    private String state;

    private List<String> tags;

    private String selfTag;

    private List<String> selfTags;

    /**
     * 标签信息
     */
    private List<ExternalUserTagVO> userTagVoList;

}
