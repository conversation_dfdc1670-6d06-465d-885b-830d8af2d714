package com.cenker.scrm.model.login;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/11/2
 * @Description 工作台登录对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class H5LoginUser extends BaseLoginInfo {
    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * h5工作台用户信息
     */
    private MobileUser mobileUser;

    /**
     * 微信端用户信息
     */
    private MpWxUser mpWxUser;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @Override
    public String getPassword() {
        return null;
    }

    @Override
    public String getUsername() {
        return null;
    }

    @Override
    public boolean isAccountNonExpired() {
        return false;
    }

    @Override
    public boolean isAccountNonLocked() {
        return false;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return false;
    }

    @Override
    public boolean isEnabled() {
        return false;
    }
}
