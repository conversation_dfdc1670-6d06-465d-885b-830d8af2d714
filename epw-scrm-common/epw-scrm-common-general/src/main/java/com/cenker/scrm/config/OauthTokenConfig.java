package com.cenker.scrm.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023/6/14
 * @Description 授权登录token
 */
@Component
@ConfigurationProperties(prefix = "token")
public class OauthTokenConfig {
    /**
     * 令牌自定义标识 后台
     */
    private static String header;
    /**
     * 令牌自定义标识 活动端
     */
    private static String wxHeader;

    /**
     * 令牌自定义标识 域账号登录方式
     */
    private static String domainHeader;

    /**
     * 令牌密钥
     */
    private static String secret;
    /**
     * 令牌有效期（默认30分钟）
     */
    private static Integer expireTime;
    /**
     * 工作台/聊天侧边栏token 单位：天
     */
    private static Integer workbenchExpireTime;

    public void setHeader(String header) {
        OauthTokenConfig.header = header;
    }

    public void setWxHeader(String wxHeader) {
        OauthTokenConfig.wxHeader = wxHeader;
    }

    public void setSecret(String secret) {
        OauthTokenConfig.secret = secret;
    }

    public void setExpireTime(Integer expireTime) {
        OauthTokenConfig.expireTime = expireTime;
    }

    public void setWorkbenchExpireTime(Integer workbenchExpireTime) {
        OauthTokenConfig.workbenchExpireTime = workbenchExpireTime;
    }

    public static String getDomainHeader() {
        return domainHeader;
    }

    public static void setDomainHeader(String domainHeader) {
        OauthTokenConfig.domainHeader = domainHeader;
    }

    public static String getHeader() {
        return header;
    }

    public static String getWxHeader() {
        return wxHeader;
    }

    public static String getSecret() {
        return secret;
    }

    public static Integer getExpireTime() {
        return expireTime;
    }

    public static Integer getWorkbenchExpireTime() {
        return workbenchExpireTime;
    }
}
