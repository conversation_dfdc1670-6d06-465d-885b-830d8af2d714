package com.cenker.scrm.pojo.request;

import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.pojo.vo.UserVO;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/22
 * @Description 站点参数
 */
@Data
public class SiteRequest{
    private String id;

    @Size(max = 10, message = "站点名字数受限")
    @NotBlank(message = "站点名不能为空", groups = {InsertGroup.class, UpdateGroup.class})
    private String siteName;

    /**
     * 所在地区
     */
    @NotNull(message = "所在地区不能为空", groups = {InsertGroup.class, UpdateGroup.class})
    private Long areaId;

    /**
     * 企业ID
     */
    private String corpId;
    private Long createBy;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private Long updateBy;
    private Date updateTime;

    /**
     * 活码参数
     */
    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 联系方式类型,1-单人, 2-多人
     */
    private Integer type = 2;

    /**
     * 执行者用户列表 最多配置100个使用成员（包含部门展开后的成员）
     */
    @NotNull
    private List<UserVO> userList;

    /**
     * 联系我添加标签 添加客户时自动添加
     */
    private List<TagVO> tagList;

    /**
     * 场景，1-在小程序中联系，2-通过二维码联系（当前默认二维码）
     */
    private String scene = "2";

    /**
     * 外部客户添加时是否无需验证，默认为true(1)
     */
    @NotNull
    private Boolean skipVerify;

    /**
     * 欢迎语
     */
    @Size(max = 2000, message = "欢迎语字数限制")
    private String welContent;

    /**
     * 附件
     */
    private List<WelcomeAttachmentVo> attachments;

    /**
     * 使用成员查询
     */
    private List<String> corpUserId;

    /**
     * 站点活码id
     */
    @NotNull(message = "id不能为空", groups = {UpdateGroup.class})
    private Long siteContactId;

    private Boolean showAll;
}
