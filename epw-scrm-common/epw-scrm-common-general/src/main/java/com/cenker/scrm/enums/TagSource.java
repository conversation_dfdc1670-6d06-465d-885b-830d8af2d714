package com.cenker.scrm.enums;

import com.cenker.scrm.util.StringUtils;
import lombok.Getter;

/**
 * 添加标签来源枚举类
 *
 * <AUTHOR>
 */
@Getter
public enum TagSource {

    /**
     * 渠道活码
     */
    CHANNEL_CODE("渠道活码"),

    /**
     * 社群活码
     */
    GROUP_CODE("社群活码"),

    /**
     * 获客链接
     */
    ACQUISITION_LINK("获客链接"),

    /**
     * 表单
     */
    SMART_FORM("智能表单"),

    /**
     * 条件打标/自动打标
     */
    CONDITION("条件打标签"),

    /**
     * 智能物料
     */
    MATERIAL("智能物料"),

    /**
     * 客户列表
     */
    CUSTOMER_LIST("客户列表"),

    /**
     * 客户画像
     */
    CUSTOMER_PROFILE("客户画像"),

    /**
     * 其他
     */
    OTHER("其他");

    private String desc;

    TagSource(String desc) {
        this.desc = desc;
    }

    public static String buildTagSource(String tagSource, String userName) {
        if (StringUtils.isBlank(tagSource)) {
            tagSource = OTHER.name();
        }

        TagSource source = TagSource.valueOf(tagSource);
        switch (source) {
            case CHANNEL_CODE:
            case GROUP_CODE:
            case ACQUISITION_LINK:
            case SMART_FORM:
            case CONDITION:
            case MATERIAL:
                return String.format("通过【%s】添加", source.getDesc());
            default:
                return String.format("由【%s】添加", userName);
        }
    }
}
