package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.model.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxMomentCustomer {

    /**
     * 记录项ID。
     */
    @TableId
    private Long id;

    /**
     * 朋友圈id
     */
    private String momentId;

    /**
     * 外部客户Id
     */
    private String externalUserId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public TbWxMomentCustomer(String momentId, String externalUserId) {
        this.momentId = momentId;
        this.externalUserId = externalUserId;
        this.createTime=new Date();
        this.updateTime=new Date();
    }
}
