package com.cenker.scrm.pojo.entity.wechat;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TbWxMassMessageInfo {

  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * 群发任务名
   */
  private String massName;
  /**
   * 群发消息场景 1 企业群发 2 个人群发 3 sop企业群发
   */
  private Integer massScene;
  /**
   * 是否定时任务 0 立即发送 1 定时发送
   */
  private Integer timedTask;
  /**
   * 消息范围 0 全部客户  1 指定客户
   */
  @TableField(exist = false)
  private Integer pushRange;
  /**
   * 状态: PENDING_EXEC 未开始  EXECUTING 执行中
   * CANCELED 已取消 EXEC_EXCEPTION 创建失败
   * FINISHED 已完成 PENDING_APPROVAL 待审核
   * REJECTED 已退回 REVOKED 已撤回
   */
  private String checkStatus;
  /**
   * 预计发送客户数
   */
  private Integer expectSendCnt;
  /**
   * 是否员工去重 是-单条走企微去重 否-多条指定员工
   */
  private Boolean userDistinct;
  /**
   * 发送时间
   */
  @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
  private Date settingTime;
  /**
   * 群发任务的类型，默认为single，表示发送给客户，group表示发送给客户群
   */
  private String chatType;
  /**
   * 企业主键id
   */
  private Long corpConfigId;
  /**
   * 创建来源1 web 2 侧边栏 3工作台
   */
  private Integer since;
  private String remark;
  private Integer delFlag;
  /**
   * 筛选发送条件json
   */
  private String sendCondition;
  /**
   * 群发消息文本
   */
  private String massContent;
  /**
   * 群发消息附件json
   */
  private String massAttachment;

  private String massJson;
  /**
   * 场景对应的id引用
   */
  private Long sceneId;
  private Long createBy;
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;
  private Long updateBy;
  @TableField(fill = FieldFill.UPDATE)
  private Date updateTime;

  /**
   * 重构新增 标签、客户阶段、添加时间是否筛选 0 否 1 是
   */
  private Integer tagSelectType;
  private Integer stageSelectType;
  private Integer addSelectType;

  /**
   * 任务停止时间 单位小时
   */
  private Integer stopTaskHour;

  /**
   * 是否过期 0否 1是
   */
  @TableField(value = "is_expired")
  private Boolean expired;

  /**
   * 部门id
   */
  private Integer deptId;
  /**
   * 启用了审批
   * true:启用审批
   * false:未启用审批
   */
  private boolean enableApproval;
  /**
   * 审批人账号
   */
  private String approvalUser;
  /**
   * 意见
   */
  private String approvalRemark;
  /**
   * EXEC_EXCEPTION 执行异常时记录异常信息
   */
  private String errorMsg;
}
