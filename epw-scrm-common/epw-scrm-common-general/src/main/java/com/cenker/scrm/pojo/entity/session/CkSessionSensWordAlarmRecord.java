package com.cenker.scrm.pojo.entity.session;

import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 敏感词告警记录信息对象 ck_session_sens_word_alarm_record
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class CkSessionSensWordAlarmRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    private String sensitiveWord;
    private Long id;
    private String contentValue;

    private String msgId;
    private Integer chatType;
    private String roomId;
    private String roomConsumeId;

    private Long ruleId;
    private String sendUserId;
    private String acceptUserId;
    private String  sendUserType;
    private String  acceptUserType;
    private Date createTime;
    private Date triggerTime;

    private String  ruleName;
    private String  sendUserName;
    private String  acceptUserName;
    private String  sendCorpName;
    private String  acceptCorpName;

}
