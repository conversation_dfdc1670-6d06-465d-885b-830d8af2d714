package com.cenker.scrm.pojo.entity.cachecontent;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 内容缓存
 * @TableName tb_wx_cache_content
 */
@TableName(value ="tb_wx_cache_content")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCacheContent implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 0 标识未删除 1 标识删除
     */
    private Integer delFlag;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * link-链接，video-视频，photo-图片，miniprogram-小程序
     */
    private String type;

    /**
     * 详细信息
     */
    private String value;

    /**
     * 名称-简称
     */
    private String name;


}