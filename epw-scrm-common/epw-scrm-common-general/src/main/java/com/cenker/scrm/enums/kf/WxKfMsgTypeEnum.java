package com.cenker.scrm.enums.kf;

public enum WxKfMsgTypeEnum {

    TEXT("text"),
    MEDIA_IMAGE("image"),
    MEDIA_VOICE("voice"),
    MEDIA_VIDEO("video"),
    MEDIA_FILE("file"),
    BUSINESS_CARD_LOCATION("location"),
    LINK("link"),
    BUSINESS_CARD("business_card"),
    MINIPROGRAM("miniprogram"),
    CHANNELS_SHOP_PRODUCT("channels_shop_product"),
    CHANNELS_SHOP_ORDER("channels_shop_order"),
    EVENT("event"),
    ;

    private final String msgType;

    WxKfMsgTypeEnum(String msgType) {
        this.msgType = msgType;
    }

    public String getMsgType() {
        return this.msgType;
    }

    @Override
    public String toString() {
        return String.valueOf(msgType);
    }

    public static WxKfMsgTypeEnum fromValue(String msgType) {
        for (WxKfMsgTypeEnum value : WxKfMsgTypeEnum.values()) {
            if (value.getMsgType().equals(msgType)) {
                return value;
            }
        }
        return null;
    }
}
