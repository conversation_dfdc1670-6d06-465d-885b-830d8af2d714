package com.cenker.scrm.enums;

/**
 * 符号枚举
 *
 * @ClassName MQDestinationEnum
 * <AUTHOR>
 * @Date 2021/6/19 11:45
 **/
public enum SeparatorEnum {

    SLASH("/", "反斜杠"),
    COLON(":", "冒号"),
    UNDERLINE("_", "下划线"),
    COMMA(",", "逗号"),
    DOT(".", "点");

    private String separator;
    private String remark;

    SeparatorEnum(String separator, String remark) {
        this.separator = separator;
        this.remark = remark;
    }

    public String getSeparator() {
        return separator;
    }

    public String getRemark() {
        return remark;
    }}
