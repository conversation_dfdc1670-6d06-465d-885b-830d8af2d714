package com.cenker.scrm.pojo.entity.session;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

/**
 * 热词信息对象 ck_session_hot_word_info
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
@TableName("ck_session_hot_word_info")
public class CkSessionHotWordInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 热词ID */
    @TableId(value = "hot_id", type = IdType.AUTO)
    private Long hotId;

    /** 热词名称 */
    private String hotWord;

    /** 近似词，多个逗号分隔 */
    private String synonWords;

    @TableField(exist = false)
    private String nickName;

    @TableField(exist = false)
    private Long notEqHotId;

    /**
     * 部门id
     */
    private Integer deptId;

}
