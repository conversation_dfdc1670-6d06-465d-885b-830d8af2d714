package com.cenker.scrm.pojo.entity.system;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 物料制作定时任务明细表(bu_material_job_detail)实体类
 *
 * <AUTHOR>
 * @since 2025-04-06 21:04:13
 * @description 由 Mybatisplus Code Generator 创建
 */
@Data
@TableName("bu_material_job_detail")
public class BuMaterialJobDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
	private Long id;
    /**
     * 任务ID
     */
    private Long jobId;
    /**
     * 来源物料的唯一标识
     */
    private String sourceId;
    /**
     * 标题
     */
    private String title;
    /**
     * 封面
     */
    private String cover;
    /**
     * 摘要
     */
    private String digest;
    /**
     * 作者
     */
    private String author;
    /**
     * 内容
     */
    private String content;
    /**
     * 生成的智能物料ID
     */
    private String materialId;
    /**
     * 创建时间
     */
    private Date createTime;

}