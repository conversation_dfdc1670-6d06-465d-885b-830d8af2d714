package com.cenker.scrm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户添加方式枚举类
 */
@AllArgsConstructor
@Getter
public enum CustomerAddWay {

    UNKNOW("0", "未知来源"),
    SCAN_QR_CODE("1", "扫描二维码"),
    SEARCH_PHONE_NUMBER("2", "搜索手机号"),
    CARD_SHARE("3", "名片分享"),
    GROUP_CHAT("4", "群聊"),
    PHONE_CONTACT_BOOK("5", "手机通讯录"),
    WECHAT_CONTACT("6", "微信联系人"),
    AUTO_ADD_BY_THIRD_APP("8", "安装第三方应用时自动添加的客服人员"),
    SEARCH_EMAIL("9", "搜索邮箱"),
    VIDEO_ADD("10", "视频号添加"),
    ADD_BY_SCHEDULE_PARTICIPANT("11", "通过日程参与人添加"),
    ADD_BY_MEETING_PARTICIPANT("12", "通过会议参与人添加"),
    ADD_BY_WECHAT_FRIEND("13", "添加微信好友对应的企业微信"),
    ADD_BY_SMART_DEVICE_SERVICE("14", "通过智慧硬件专属客服添加"),
    ADD_BY_ONLINE_SERVICE("15", "通过上门服务客服添加"),
    ADD_BY_RECRUIT_LINK("16", "通过获客链接添加"),
    ADD_BY_CUSTOM_DEVELOP("17", "通过定制开发添加"),
    ADD_BY_REQUIREMENT_REPLY("18", "通过需求回复添加"),
    ADD_BY_SALES_PRE_SERVICE("21", "通过第三方售前客服添加"),
    ADD_BY_POSSIBLE_PARTNER("22", "通过可能的商务伙伴添加"),
    ADD_BY_FRIEND_APPLY("24", "通过接受微信账号收到的好友申请添加"),
    INTERNAL_MEMBER_SHARE("201", "内部成员共享"),
    ADMIN_DISTRIBUTE("202", "管理员/负责人分配");

    private String addWay;
    private String desc;

    /**
     * 根据addWay获取描述
     * @param addWay
     * @return
     */
    public static String getDesc(String addWay) {
        CustomerAddWay customerAddWay = valueOfEnum(addWay);
        return customerAddWay.getDesc();
    }

    /**
     * 根据addWay获取枚举对象
     * @param addWay
     * @return
     */
    public static CustomerAddWay valueOfEnum(String addWay) {
        for (CustomerAddWay c : CustomerAddWay.values()) {
            if (c.getAddWay().equals(addWay)) {
                return c;
            }
        }

        return CustomerAddWay.UNKNOW;
    }
}
