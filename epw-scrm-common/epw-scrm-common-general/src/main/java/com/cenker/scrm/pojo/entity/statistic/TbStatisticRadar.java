package com.cenker.scrm.pojo.entity.statistic;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 数据统计-物料统计
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Data
@TableName("tb_statistic_radar")
public class TbStatisticRadar {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", dateFormat = "yyyy-MM-dd", sort = 1)
    private Date statisticDate;

    /**
     * 物料id
     */
    private Long radarId;

    /**
     * 类型 1 图文 2 链接 3 PDF
     */
    @Excel(name = "物料类型", readConverterExp = "1=图文,2=链接,3=PDF", sort = 4)
    private Integer radarType;

    /**
     * 封面
     */
    private String cover;

    /**
     * 摘要
     */
    private String digest;

    /**
     * 物料名称
     */
    @Excel(name = "物料名称", sort = 3)
    private String radarName;

    /**
     * 标题
     */
    @Excel(name = "物料内容", sort = 2)
    private String title;

    /**
     * 一级分类
     */
    private String firstCategory;

    /**
     * 二级分类
     */
    private String secondCategory;

    /**
     * 发送次数
     */
    @Excel(name = "发送数", sort = 5)
    private Integer sendTimes;

    /**
     * 点击次数
     */
    @Excel(name = "点击数", sort = 6)
    private Integer clickTimes;

    /**
     * 转发次数
     */
    @Excel(name = "转发数", sort = 7)
    private Integer forwardTimes;

    /**
     * 平均浏览时长(S)
     */
    @Excel(name = "平均浏览时长(S)", sort = 8)
    private Integer averageReadTime;

    /**
     * 新增标记：0-否，1-是
     */
    private Integer newFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 部门id
     */
    private Integer deptId;

}
