package com.cenker.scrm.pojo.entity.wechat.kf;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;

import static com.cenker.scrm.constants.WkWxKfConstants.LIMIT;
import static com.cenker.scrm.constants.WkWxKfConstants.VOICE_FORMAT;

@Data
@AllArgsConstructor
public class WkWxKfSyncMsgResp {
    @JSONField(ordinal = 0)
    private String corpId;  // 企业 id
    @JSONField(ordinal = 1)
    private String openKfId; // 指定拉取某个客服帐号的消息，否则默认返回有权限的客服帐号的消息。
    @JSONField(ordinal = 2)
    @TableField("`token`")
    private String token; // 回调事件返回的 token字段，10分钟内有效；可不填，如果不填接口有严格的频率限制。
    @JSONField(ordinal = 3)
    @TableField("`cursor`")
    private String cursor; // 上一次返回的 nextCursor,若不传, 从3天内最早的消息开始返回。
    @TableField("`limit`")
    private Integer limit;
    private Integer voiceFormat;

    public WkWxKfSyncMsgResp(String corpId, String openKfId, String token) {
        this.corpId = corpId;
        this.openKfId = openKfId;
        this.token = token;
    }

    public WkWxKfSyncMsgResp baseInit(String cursor) {
        this.cursor = cursor;
        this.limit = LIMIT;
        this.voiceFormat = VOICE_FORMAT;
        return this;
    }


}
