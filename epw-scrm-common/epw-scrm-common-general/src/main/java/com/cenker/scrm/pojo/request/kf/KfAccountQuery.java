package com.cenker.scrm.pojo.request.kf;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KfAccountQuery implements Serializable {

    private static final long serialVersionUID = -8962211171112727800L;
    /**
     * 微信客服名称
     */
    private String name;

    private String corpId;

    /**
     * 状态（为空则查询全部，0标识正常，1标识停用）
     */
    private Integer status;

}
