package com.cenker.scrm.pojo.entity.statistic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天会话表（以最近一次员工回复闭环）
 * @TableName wk_chat_conversation
 */
@TableName(value ="wk_chat_conversation")
@Data
public class WkChatConversation implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 统计时间
     */
    private Date statisticDate;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 员工ID
     */
    private String staffId;

    /**
     * 客户消息Id
     */
    private String originMsgId;

    /**
     * 客户消息时间
     */
    private Date originMsgTime;

    /**
     * 客户消息内容
     */
    private String originMsgContent;

    /**
     * 客户消息Id
     */
    private String replyMsgId;

    /**
     * 客户消息时间
     */
    private Date replyMsgTime;

    /**
     * 客户消息内容
     */
    private String replyMsgContent;

    /**
     * 员工回复时长，单位：分钟
     */
    private Integer duration;

    /**
     * 是否超时回复，1是，0否
     */
    private Integer timeout;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}