package com.cenker.scrm.model.base;


import com.cenker.scrm.model.enummodel.ResponseCodeEnum;
import com.cenker.scrm.util.DateTimeUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2021/6/29 12:01
 */
@Data
public class BaseResponse<T> {
    /**
     * 响应码
     */
    private String respCode;
    /**
     * 响应信息
     */
    private String respMessage;
    /**
     * 响应时间
     */
    private String respTime;
    /**
     * 响应结果体
     */
    private T respBody;

    /**
     * 设置成功结果
     */
    public void setSuccessResponse(T respBody) {
        setRespCode(ResponseCodeEnum.ResponseCode_0000.getCode());
        setRespMessage(ResponseCodeEnum.getDescription(ResponseCodeEnum.ResponseCode_0000.getCode()));
        setRespTime(DateTimeUtils.getCurrDateTimeStr());
        setRespBody(respBody);
    }

    /**
     * 设置成功结果
     *
     * @param respCode 结果Code值
     * @param respBody 结果体
     */
    public void setSuccessResponse(ResponseCodeEnum respCode, T respBody) {
        setRespCode(respCode.getCode());
        setRespMessage(ResponseCodeEnum.getDescription(respCode.getCode()));
        setRespTime(DateTimeUtils.getCurrDateTimeStr());
        setRespBody(respBody);
    }

    /**
     * 设置失败结果
     *
     * @param respCode
     * @param message
     */
    public void setErrorResponse(ResponseCodeEnum respCode, String message) {
        setRespCode(respCode.getCode());
        if (StringUtils.isEmpty(message)) {
            setRespMessage(ResponseCodeEnum.getDescription(respCode.getCode()));
        } else {
            setRespMessage(message);
        }
        setRespTime(DateTimeUtils.getCurrDateTimeStr());
    }

    /**
     * 设置失败结果
     *
     * @param respCode
     * @param message
     */
    public void setErrorResponse(String respCode, String message) {
        setRespCode(respCode);
        setRespMessage(message);
        setRespTime(DateTimeUtils.getCurrDateTimeStr());
    }

    /**
     * 设置失败结果
     *
     * @param resp
     */
    public void setErrorResponse(ResponseCodeEnum resp) {
        setRespCode(resp.getCode());
        setRespMessage(resp.getDescription());
        setRespTime(DateTimeUtils.getCurrDateTimeStr());
    }


    //成功返回
    private static BaseResponse builder() {
        return new BaseResponse<>();
    }

    public static BaseResponse success() {
        BaseResponse response = BaseResponse.builder();
        response.setSuccessResponse(null);
        return response;
    }

    public static BaseResponse success(Object respBody) {
        BaseResponse response = BaseResponse.builder();
        response.setSuccessResponse(respBody);
        return response;
    }

    //失败返回
    public static BaseResponse fail(ResponseCodeEnum resp) {
        BaseResponse response = BaseResponse.builder();
        response.setErrorResponse(resp);
        return response;
    }

    public static BaseResponse fail(String msg) {
        BaseResponse response = BaseResponse.builder();
        response.setErrorResponse(ResponseCodeEnum.ResponseCode_1001, msg);
        return response;
    }

    public static BaseResponse fail(ResponseCodeEnum respCode, String msg) {
        BaseResponse response = BaseResponse.builder();
        response.setErrorResponse(respCode, msg);
        return response;
    }

    public static BaseResponse fail(String respCode, String msg) {
        BaseResponse response = BaseResponse.builder();
        response.setErrorResponse(respCode, msg);
        return response;
    }
}