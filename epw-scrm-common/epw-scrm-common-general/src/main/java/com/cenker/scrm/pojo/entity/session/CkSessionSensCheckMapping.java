package com.cenker.scrm.pojo.entity.session;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

/**
 * 规则审计对象 ck_session_sens_check_mapping
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class CkSessionSensCheckMapping extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    private Long id;

    /** 规则ID */
    private Long ruleId;

    /** 审计人ID */
    private String checkUserId;

    private String name;
    private String avatar;

}
