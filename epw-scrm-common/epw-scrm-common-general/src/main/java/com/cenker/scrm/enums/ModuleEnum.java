package com.cenker.scrm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 菜单枚举
 */
@Getter
@AllArgsConstructor
public enum ModuleEnum {

    WORKBENCH("workbench", "工作台"),

    /** 引流获客 */
    CHANNEL("channel", "渠道活码"),
    COMMUNITY_CODE("community-code", "社群活码"),
    CUSTOMER_ACQUISITION_LINK("customer-acquisition-link", "获客链接"),

    /** 客户触达 */
    CONDITION_SOP("condition-sop", "条件SOP"),
    JOURNEY_SOP("journey-sop", "旅程SOP"),
    ONE_TO_ONE_SOP("one-to-one-sop", "1V1SOP"),
    MASS_CUSTOMER("mass-customer", "1V1群发"),
    SENDMOMENTS("sendmoments", "发朋友圈"),
    MASS_GROUP("mass-group", "社群群发"),


    /** 内容中心 */
    ENTERPRISESCRIPT("enterprisescript", "服务话术"),
    INTELLIGENT_MATERIAL("intelligent-material", "智能物料"),
    NORMAL_MATERIAL("normal-material", "营销素材"),
    NORMAL_MATERIAL_POSTER("normal-material-poster", "营销素材-海报设置"),
    SMART_FORM("smartForm", "智能表单"),
    WELCOME_CONTACT_TEMPLATE("welcomeContact", "欢迎语"),

    /** 客户管理 */
    CUSTOMER_LIST("customerList", "客户列表"),
    CUSTOMER_TAG("customerTag", "客户标签"),
    CHURN("churn", "流失提醒"),
    COMMUNITY("community", "社群管理"),
    JOURNEY("journey", "客户旅程"),
    BUSINESS_TAG("businesstag", "业务标签"),
    AUTO_MARK_TAG("automaticTagging", "自动打标签"),
    CUSTOMER_CUS_SEARCH("customerCusSearch", "客户列表自定义筛选"),

    /** 员工管理 */
    INCUMBENCY_SUCCESSION("incumbency-succession", "在职继承"),
    DIMISSION("dimission", "离职继承"),
    DEPARTMENT("department", "通讯录"),
    PERMISSION_MANAGEMENT("permission-management", "权限管理"),


    /** 会话存档 */
    CHAT_ARCHIVE("chatArchive", "聊天存档"),
    SENSITIVE_RULES("sensitive-rules", "敏感规则"),
    SENSITIVE_ALERT_LOG("sensitive-alert-log", "敏感词警告"),
    SENSITIVE_BEHAVIOR_WARNINGLOG("sensitive-behavior-warningLog", "敏感行为"),
    REPLY_TIMEOUT_SETTING("reply-timeout-setting", "回复超时"),
    HOT_WORK_SETTING("hot-work-setting", "热词设置"),

    /** 数据统计 */
    STAFF_STATISTICS("staff-statistics", "员工数据"),
    CUSTOMER_STATISTICS("customer-statistics", "客户数据"),
    CUSTOMER_GROUP_STATISTICS("customer-group-statistics", "客户群数据"),
    RADAR_STATISTICS("radar-statistics", "物料统计"),
    HOT_WORD_STATISTICS("hot-word-statistics", "热词统计"),
    SENSITIVE_WORD_STATISTICS("sensitive-word-statistics", "敏感词统计"),
    SENSITIVE_BEHAVIOR_STATISTICS("sensitive-behavior-statistics", "敏感行为"),
    REPLY_TIMEOUT_STATISTICS("reply-timeout-statistics", "回复超时"),

    /** 设置中心 */
    ENTERPRISE_CONFIGURATION("enterpriseConfiguration", "企业设置"),
    SYSTEM_APP_CONFIG("system-app-config", "应用配置"),
    MENU("menu", "菜单管理"),
    USER("user", "用户管理"),
    ROLE("role", "角色管理"),
    CONFIG("config", "参数管理"),
    DICT("dict", "字典管理"),
    OPERATION_LOG("operationLog", "操作日志"),
    ONLINE("online", "在线用户"),
    LOGIN_INFO("login_info", "登录日志"),
    NAMING_RULE("naming-rule", "命名规则"),
    SYSTEM_NOTICE("system-notice", "系统公告"),

    /** 订阅管理 */
    SUBSCRIBE_MENU("subscribeMenu", "订阅菜单"),
    SUBSCRIBE_SECTION("subscribeSection", "订阅栏目"),

    /** 其他 */
    OTHER("other", "其他");

    private String code;

    private String name;
}
