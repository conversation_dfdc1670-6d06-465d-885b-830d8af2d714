package com.cenker.scrm.enums;

import lombok.Getter;

/**
 * 操作事件类型枚举
 */
@Getter
public enum TrackEventTypeEnum {
    // 编辑客户信息
    CUSTOMER_INFO_EDIT("编辑客户信息", "CUSTOMER_INFO_EDIT", "CUSTOMER_INFO_EDIT", "编辑客户信息", "更新客户信息【{0}】为【{1}】", "编辑"),
    // 编辑客户企业标签
    CORPORATE_TAG_EDIT_ADD("编辑客户企业标签", "CORPORATE_TAG_EDIT", "CORPORATE_TAG_EDIT_ADD", "【编辑客户企业标签】添加标签", "添加客户企业标签{0}", "编辑"),
    CORPORATE_TAG_EDIT_REMOVE("编辑客户企业标签", "CORPORATE_TAG_EDIT", "CORPORATE_TAG_EDIT_REMOVE", "【编辑客户企业标签】移除标签", "移除客户企业标签{0}", "编辑"),
    // 同步客户信息
    CORPORATE_TAG_EDIT_SYNC_ADD("同步客户信息", "CORPORATE_TAG_EDIT_SYNC", "CORPORATE_TAG_EDIT_SYNC_ADD", "【同步客户信息】添加标签", "添加客户企业标签{0}", "编辑"),
    CORPORATE_TAG_EDIT_SYNC_REMOVE("同步客户信息", "CORPORATE_TAG_EDIT_SYNC", "CORPORATE_TAG_EDIT_SYNC_REMOVE", "【同步客户信息】移除标签", "移除客户企业标签{0}", "编辑"),
    // 编辑个人标签
    PERSONAL_TAG_EDIT_ADD("编辑个人标签", "PERSONAL_TAG_EDIT", "PERSONAL_TAG_EDIT_ADD", "【编辑个人标签】新增标签", "新增个人标签{0}", "编辑"),
    PERSONAL_TAG_EDIT_REMOVE("编辑个人标签", "PERSONAL_TAG_EDIT", "PERSONAL_TAG_EDIT_REMOVE", "【编辑个人标签】移除标签", "移除个人标签{0}", "编辑"),
    // 批量编辑标签
    BULK_EDIT_TAG_ADD("批量编辑标签", "BULK_EDIT_TAG", "BULK_EDIT_TAG_ADD", "【批量编辑标签】批量添加", "批量添加企业标签{0}", "编辑"),
    BULK_EDIT_TAG_REMOVE("批量编辑标签", "BULK_EDIT_TAG", "BULK_EDIT_TAG_REMOVE", "【批量编辑标签】批量移除", "批量移除企业标签{0}", "编辑"),
    // 客户流失
    CUSTOMER_CHURN_CUST("客户流失", "CUSTOMER_CHURN", "CUSTOMER_CHURN_CUST", "【客户流失】员工删除客户", "企业员工删除客户", "被删除"),
    CUSTOMER_CHURN_EMPE("客户流失", "CUSTOMER_CHURN", "CUSTOMER_CHURN_EMPE", "【客户流失】客户删除员工", "客户删除企业员工", "被删除"),
    // 加入群聊
    GROUP_JOIN_INVITE("加入群聊", "GROUP_JOIN", "GROUP_JOIN_INVITE", "【加入群聊】邀请客户入群", "企业员工邀请客户加入群聊", "进群"),
    GROUP_JOIN_SCAN("加入群聊", "GROUP_JOIN", "GROUP_JOIN_SCAN", "【加入群聊】客户扫码入群", "扫描二维码{0}加入群聊", ""),
    // 退出群聊
    GROUP_LEAVE_REMOVED("退出群聊", "GROUP_LEAVE", "GROUP_LEAVE_REMOVED", "【退出群聊】客户被移出群聊", "群主/群管理员将客户移出群聊", "被移出"),
    GROUP_LEAVE_VOLUNTARY("退出群聊", "GROUP_LEAVE", "GROUP_LEAVE_VOLUNTARY", "【退出群聊】客户主动退群", "主动退出群聊", ""),
    // 自动打标签
    AUTO_TAGGING_CONDI_ADD("自动打标签", "AUTO_TAGGING", "AUTO_TAGGING_CONDI_ADD", "【自动打标签】条件打标签", "通过条件打标签规则【{0}】自动添加标签{1}", ""),
    AUTO_TAGGING_CONDI_REMOVE("自动打标签", "AUTO_TAGGING", "AUTO_TAGGING_CONDI_REMOVE", "【自动打标签】删除规则自动移除标签", "删除条件打标签规则【{0}】自动移除标签{1}", ""),
    AUTO_TAGGING_RADAR("自动打标签", "AUTO_TAGGING", "AUTO_TAGGING_RADAR", "【自动打标签】渠道活码打标签", "通过渠道活码 【{0}】添加企业好友 自动添加标签{1}", "企业好友"),
    AUTO_TAGGING_GROUP("自动打标签", "AUTO_TAGGING", "AUTO_TAGGING_GROUP", "【自动打标签】社群活码打标签", "通过社群活码【{0}】自动添加标签{1}", ""),
    AUTO_TAGGING_SMART_MATERIAL("自动打标签", "AUTO_TAGGING", "AUTO_TAGGING_SMART_MATERIAL", "【自动打标签】智能表单打标签", "提交表单【{0}】自动添加标签{1}", ""),
    AUTO_TAGGING_LINK("自动打标签", "AUTO_TAGGING", "AUTO_TAGGING_LINK", "【自动打标签】获客链接打标签", "通过获客链接 【{0}】添加企业好友 自动添加标签{1}", "企业好友"),
    // 添加客户
    CUSTOMER_ADD_RADAR("添加客户", "CUSTOMER_ADD", "CUSTOMER_ADD_RADAR", "【添加客户】渠道活码添加", "通过渠道活码【{0}】添加企业好友", "企业好友"),
    CUSTOMER_ADD_LINK("添加客户", "CUSTOMER_ADD", "CUSTOMER_ADD_LINK", "【添加客户】获客链接添加", "点击获客链接【{0}】添加企业好友", "企业好友"),
    CUSTOMER_ADD_OTHER("添加客户", "CUSTOMER_ADD", "CUSTOMER_ADD_OTHER", "【添加客户】其他方式添加", "通过【{0}】添加企业好友", "企业好友"),
    // 智能物料互动
    SMART_MATERIAL_ACTION_VIEW("智能物料互动", "SMART_MATERIAL_ACTION", "SMART_MATERIAL_ACTION_VIEW", "【智能物料互动】客户浏览", "第{0}次浏览智能物料，浏览了{1}, 阅读率{2}", ""),
    SMART_MATERIAL_ACTION_SHARE("智能物料互动", "SMART_MATERIAL_ACTION", "SMART_MATERIAL_ACTION_SHARE", "【智能物料互动】客户转发", "转发智能物料", ""),
    SMART_MATERIAL_AUTO_TAG("智能物料互动", "SMART_MATERIAL_ACTION", "SMART_MATERIAL_AUTO_TAG", "【智能物料互动】自动打标签", "浏览智能物料，自动添加标签{0}", "侧边栏发送"),
    // 客户认证
    CUSTOMER_AUTH("客户认证", "CUSTOMER_AUTH", "CUSTOMER_AUTH", "客户认证", "客户已认证", ""),
    // 编辑客户订阅
    CUSTOMER_SUBSCR_EDIT_REMOVE("订阅更新", "CUSTOMER_SUBSCR_EDIT", "CUSTOMER_SUBSCR_EDIT_REMOVE", "【订阅更新】取消订阅", "取消订阅栏目{0}", "编辑"),
    CUSTOMER_SUBSCR_EDIT_ADD("订阅更新", "CUSTOMER_SUBSCR_EDIT", "CUSTOMER_SUBSCR_EDIT_ADD", "【订阅更新】新增订阅", "新增订阅栏目{0}", "编辑"),
    // 朋友圈互动
    MOMENT_INTERACTION_COMMENT("朋友圈互动", "MOMENT_INTERACTION", "MOMENT_INTERACTION_COMMENT", "【朋友圈互动】客户评论", "评论顾问的朋友圈", "的朋友圈"),
    MOMENT_INTERACTION_LIKE("朋友圈互动", "MOMENT_INTERACTION", "MOMENT_INTERACTION_LIKE", "【朋友圈互动】客户点赞", "点赞顾问的朋友圈", "的朋友圈");

    private final String name;
    private final String eventType;
    private final String subEventType;
    private final String title;
    private final String content;
    private final String subTitle;

    TrackEventTypeEnum(String name, String eventType, String subEventType, String title, String content, String subTitle) {
        this.name = name;
        this.eventType = eventType;
        this.subEventType = subEventType;
        this.title = title;
        this.content = content;
        this.subTitle = subTitle;
    }

    /**
     * 根据subEventType获取对应的动态枚举值
     * @param subEventType
     * @return
     */
    public static TrackEventTypeEnum getTrackEventTypeBySubEventType(String subEventType) {
        // 根据 subEventType 获取对应的枚举值
        for (TrackEventTypeEnum trackEventTypeEnum : TrackEventTypeEnum.values()) {
            if (trackEventTypeEnum.getSubEventType().equals(subEventType)) {
                return trackEventTypeEnum;
            }
        }
        return null;
    }
}