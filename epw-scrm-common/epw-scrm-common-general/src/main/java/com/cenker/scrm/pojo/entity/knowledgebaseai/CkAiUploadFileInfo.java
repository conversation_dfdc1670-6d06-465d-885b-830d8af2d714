package com.cenker.scrm.pojo.entity.knowledgebaseai;

import com.baomidou.mybatisplus.annotation.*;
import com.cenker.scrm.annotation.Excel;
import com.cenker.scrm.handler.ArrayLongTypeHandler;
import com.cenker.scrm.handler.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;

@Data
@TableName(value = "ck_ai_upload_file_info",autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CkAiUploadFileInfo {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String sourceUrl;
    @Excel(name = "问题名称")
    private String name;
    private Integer segmentNum;
    @Excel(name = "回答内容")
    private String description;
    private Integer uploadType;
    private Long repositoryId;
    private String status;
    private String remark;
    /**
     * 逻辑删除 0 表示未删除，1 表示删除
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    @TableField(exist = false)
    private Integer uploadTypeCount;

    @TableField(exist = false)
    private String userName;

}
