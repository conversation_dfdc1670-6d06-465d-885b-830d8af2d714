package com.cenker.scrm.constants;

/**
 * <AUTHOR>
 * 微服务调用服务名
 * 这里说明一下为什么有几个服务是一样的名字epw-scrm-api-center：
 * 原先wx-cenker等工程为单独的启动项，对于目前的业务规模而言，尽量的减少服务启动项利大于弊
 * 但为了之后的扩展，还是预留了他们单独作为启动项的可能，所以才有了这里这样的用法
 */
public interface ServiceNameConstants {

    String SYSTEM_CENTER_SERVICE = "epw-scrm-api-center";
    String WX_CENTER_SERVICE = "epw-scrm-api-center";
    String ACTIVITY_CENTER_SERVICE = "epw-scrm-api-center";
    String CUSTOMER_SERVICE = "epw-scrm-api-center";
    String KNOWLEDGEBASEAI_CENTER_SERVICE = "epw-scrm-api-center";
    /**
     * 授权服务
     */
    String OAUTH_SERVICE = "epw-scrm-oauth-center";
    /**
     * 智能表单
     */
    String QUESTIONNAIRE_SERVICE = "epw-scrm-questionnaire";
    /**
     * 企微服务
     */
    String WORK_API_SERVICE = "epw-scrm-work-api";

    /**
     * 会话服务
     */
    String CHAT_ARCHIVE_SERVICE = "epw-chat-archive-center";

    String API_CENTER_SERVICE = "epw-scrm-api-center";
}
