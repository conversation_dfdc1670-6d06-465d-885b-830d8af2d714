package com.cenker.scrm.pojo.request.sop;

import com.cenker.scrm.pojo.valid.DataGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2023/7/28
 * @Description
 */
@Data
public class JourneySopQueryRequest extends BaseSopRequest {
    /**
     * 阶段id
     */
    @NotNull(message = "缺失阶段id", groups = {DataGroup.class})
    private Long stageId;

    /**
     * 执行状态 1 已完成 2 未完成 3 已过期
     */
    private Integer executeStatus;

    /**
     * 任务版本号
     */
    private String contentVersion;

    /**
     * 消息id
     */
    private Long msgId;

    /**
     * 客户名
     */
    private String customerName;

    /**
     * 员工id
     */
    private String userId;
}
