package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.pojo.entity.enums.EnumTransferType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.io.Serializable;
import java.util.Date;

/**
 * 客户转移记录实体类，用于记录员工之间的客户转移信息。
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxTransferRecord implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 企业ID。
     */
    private String corpId;

    /**
     * 转移记录ID。
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String transferId;

    /**
     * 交接人用户ID。
     */
    private String handoverUserId;

    /**
     * 接管人用户ID。
     */
    private String takeoverUserId;

    /**
     * 外部用户ID，可能指的是被转移的客户的ID。
     */
    private String externalUserid;

    /**
     * 聊天ID列表，可能涉及到的会话列表。
     */
    private String chatIdList;

    /**
     * 转移成功消息。
     */
    private String transferSuccessMsg;

    /**
     * 记录的创建者。
     */
    private String createBy;

    /**
     * 记录的创建时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 记录的更新者。
     */
    private String updateBy;

    /**
     * 记录的更新时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 转移类型。
     */
    private EnumTransferType transferType;
}
