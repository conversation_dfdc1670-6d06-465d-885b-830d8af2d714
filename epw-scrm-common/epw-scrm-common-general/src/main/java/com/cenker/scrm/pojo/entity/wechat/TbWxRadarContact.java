package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@TableName("tb_wx_radar_contact")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TbWxRadarContact {

  @TableId(type = IdType.ASSIGN_ID)
  private String id;
  /**
   * 企业id
   */
  private String corpId;
  /**
   * 内容id
   */
  private String contentId;
  /**
   * 员工id
   */
  private String staffId;
  /**
   * 活码id
   */
  private String contactId;
  /**
   * 活码地址
   */
  private String contactCode;
  /**
   * 备注
   */
  private String remark;
  /**
   * 0 未删除 1已删除
   */
  private Integer delFlag;


  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date createTime;
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date updateTime;
  private String createBy;
  private String updateBy;


  @TableField(exist = false)
  private String staffImg;
  @TableField(exist = false)
  private String staffName;
}
