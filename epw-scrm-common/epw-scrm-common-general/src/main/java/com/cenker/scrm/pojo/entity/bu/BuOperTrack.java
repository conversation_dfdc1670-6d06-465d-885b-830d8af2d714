package com.cenker.scrm.pojo.entity.bu;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 客户动态记录表
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("bu_oper_track")
public class BuOperTrack extends BaseEntity {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 操作id，记录一次操作的id，同一次操作，运营动态和客户动态同一id
     */
    private String operId;

    /**
     * 分类；customer 客户动态 business 运营动态
     */
    private String trackType;

    /**
     * 操作事件类型（如：批量编辑标签、客户流失）
     */
    private String eventType;

    /**
     * 操作事件子类型（如：【编辑客户企业标签】添加标签、【编辑客户企业标签】移除标签）
     */
    private String subEventType;

    /**
     * 操作标题（如：【批量编辑标签】批量移除）
     */
    private String title;

    /**
     * 操作内容详情
     */
    private String content;

    /**
     * json字符串，保存不同类型事件额外的信息
     */
    private String relatedResource;

    /**
     * 外部联系人id
     */
    private String externalUserId;

    /**
     * 员工id
     */
    private String userId;

    /**
     * 企业ID
     */
    private String corpId;
}