package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;

/**
 * 微信客服 聊天记录 business_card + location 文件 对象 tb_wx_kf_msg
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wk_wx_kf_msg_business_card")
public class WkWxKfMsgBusinessCard extends WkWxKfMsgItemCommonResp {

    private String userId; // 名片 userid

    // location消息
    private String name;
    private String address;
    private Float latitude;
    private Float longitude;

    @Override
    public WkWxKfMsgBusinessCard init(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        super.init(msgItem);

        this.userId = msgItem.getBusinessCard().getUserId();

        this.name = msgItem.getLocation().getName();
        this.address = msgItem.getLocation().getAddress();
        this.latitude = msgItem.getLocation().getLatitude();
        this.longitude = msgItem.getLocation().getLongitude();

        return this;
    }
}
