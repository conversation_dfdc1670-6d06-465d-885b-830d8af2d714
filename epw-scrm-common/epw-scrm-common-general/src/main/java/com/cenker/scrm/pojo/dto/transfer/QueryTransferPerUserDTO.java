package com.cenker.scrm.pojo.dto.transfer;

import lombok.Data;

/**
 * 查询每个客户的转移情况
 */
@Data
public class QueryTransferPerUserDTO implements java.io.Serializable{


    /**
     * 记录Id
     */
    private String recordId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 原所属员工
     */
    private String originalEmployee;

    /**
     *接替状态： 1-接替完毕 2-等待接替 3-客户拒绝 4-接替成员客户达到上限 5-继承失败 6-继承过于频繁
     */
    private Integer takeoverStatus =null;


}
