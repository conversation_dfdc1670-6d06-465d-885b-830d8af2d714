package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 内部人员名单
 * @TableName t_staff
 */
@TableName(value ="t_staff")
@Data
public class TStaff implements Serializable {
    /**
     * 客户号
     */
    @TableId
    private String custno;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}