package com.cenker.scrm.pojo.request.reply;

import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/4/13
 * @Description 话术查询
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuickReplyQuery {
    private Long id;
    /**
     * 话术内容
     */
    private List<WelcomeAttachmentVo> attachments;
    /**
     * 话术标题
     */
    private String title;
    /**
     * 分组名
     */
    private String categoryName;
    private Long categoryId;
    private Integer orderNum;
    /**
     * 发送次数
     */
    private Integer sendCnt;
    /**
     * 类型
     */
    private String type;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 企业微信素材id，用于前端回显
     */
    private String mediaId;
}
