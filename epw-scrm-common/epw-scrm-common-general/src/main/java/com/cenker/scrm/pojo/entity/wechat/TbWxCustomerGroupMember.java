package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 企业微信客户群-成员对象 tb_wx_customer_group_member
 *
 * <AUTHOR>
 * @date 2021-01-26
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCustomerGroupMember {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 客户群id
     */
     // @Excel(name = "客户群id")
    private String groupId;

    /**
     * 成员类型
     */
     // @Excel(name = "成员类型")
    private String type;

    /**
     * 群成员id
     */
     // @Excel(name = "群成员id")
    private String userId;

    /**
     * 进群时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
     // @Excel(name = "进群时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date joinTime;

    /**
     * 入群方式  1=由成员邀请入群（直接邀请入群）, 2=由成员邀请入群（通过邀请链接入群）, 3=通过扫描群二维码入群)
     */
     // @Excel(name = "入群方式")
    private String joinScene;

    /**
     * 外部联系人在微信开放平台的唯一身份标识（微信unionid）
     */
     // @Excel(name = "外部联系人在微信开放平台的唯一身份标识", readConverterExp = "微信unionid")
    private String unionId;

    /**
     * 企业id
     */
     // @Excel(name = "企业id")
    private String corpId;

    /**
     * （0正常 1 禁用 2删除）
     */
     // @Excel(name = "status", readConverterExp = "0=正常,1=,禁=用,2=删除")
    private Integer status;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date departureTime;

    /**
     * 邀请者的userid(目前仅当是由本企业内部成员邀请入群时会返回该值)
     */
    private String invitorUserId;

    /**
     * 群昵称
     */
    @TableField(value = "group_nickname")
    private String groupNickname;

    /**
     * 名字。仅当 need_name = 1 时返回
     * 如果是微信用户，则返回其在微信中设置的名字
     * 如果是企业微信联系人，则返回其设置对外展示的别名或实名
     */
    private String name;

    /**
     * 企业自定义的state参数，用于区分不同的入群渠道
     */
    private String state;

    /**
     * 当是成员退群时有值。表示成员的退群方式
     * 0 - 自己退群
     * 1 - 群主/群管理员移出
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String QuitScene;

    /**
     * 记录创建时间
     */
    private Date createTime;
}