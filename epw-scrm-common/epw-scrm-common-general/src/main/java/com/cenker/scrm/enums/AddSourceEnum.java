package com.cenker.scrm.enums;

/**
 * <AUTHOR>
 * @Date 2023/6/7
 * @Description 企微添加渠道
 */
public enum AddSourceEnum {
    /**
     * 企微添加渠道
     */
    aad_source_0(0,"未知来源"),
    aad_source_1(1,"扫描二维码"),
    aad_source_2(2,"搜索手机号"),
    aad_source_3(3,"名片分享"),
    aad_source_4(3,"名片分享"),
    aad_source_5(3,"名片分享"),
    aad_source_6(3,"名片分享"),
    aad_source_7(3,"名片分享"),
    aad_source_8(3,"名片分享"),
    aad_source_9(3,"名片分享"),
    aad_source_10(3,"名片分享"),



    ;
    private Integer code;
    private String source;

    AddSourceEnum(Integer code, String source) {
        this.code = code;
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }}
