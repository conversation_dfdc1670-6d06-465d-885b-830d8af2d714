package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.*;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @author:znlian
 * @time:2024/4/5
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
// @ApiModel("获客链接实体")
public class TbWxCustlink  implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    private String linkName;
    private String linkUrl;
    private String tagIds;
    private String welContent;
    private String attachments;
    private Long totalAddNum;
    private Long totalTallNum;
    private Long totalGrowNum;
    private Long totalLossNum;
    private String status;
    private String skipVerify;
    private String showFlag;
    private String tagListJson;

    /**
     * 创建者
     */
    private String createBy;

    @TableField(exist = false)
    private String createByName;

    private String updateBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private Date updateTime;

    /**
     * 企业ID
     */
    // @ApiModelProperty("企业ID")
    @Excel(name = "企业ID")
    private String corpId;

    private String wxLinkId;
    private Long  urlCreateTime;

    /**
     * 部门id
     */
    private Integer deptId;

    /** 欢迎语模板 */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String welTplId;


}
