package com.cenker.scrm.pojo.entity.subscr;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 订阅菜单从表实体类
 * 用于存储订阅菜单从表的相关信息
 */
@Data
@TableName("bu_subscription_menu_sub")
public class BuSubscriptionMenuSub extends BaseEntity {
    /**
     * 订阅菜单从表主键ID
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 主菜单ID，关联订阅菜单主表的id
     */
    private String menuId;

    /**
     * 子菜单名称
     */
    private String subMenuName;

    /**
     * 上级ID
     */
    private String parentId;

    /**
     * 排序
     */
    private Integer subMenuSort;

    /**
     * 简介
     */
    private String intro;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    private Integer delFlag;

    /**
     * 栏目ID
     */
    private String sectionId;

    /**
     * 删除时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date delTime;

    @TableField(exist = false)
    private boolean isNew;
}