package com.cenker.scrm.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/8/16
 * @Description 读取系统配置
 */
@Component
@ConfigurationProperties(prefix = "epw-scrm")
public class CkyConfig {
    /**
     * 项目名称
     */
    private String name;

    /**
     * 版本
     */
    private String version;

    /**
     * 版权年份
     */
    private String copyrightYear;

    /**
     * 实例演示开关
     */
    private boolean demoEnabled;

    /**
     * 上传路径
     */
    private static String profile;

    /**
     * 获取地址开关
     */
    private static boolean addressEnabled;

    /**
     * 批量提交数量
     */
    private static Integer submitCount;

    /**
     * 下载路径
     */
    private static String downPath;

    /**
     * 设置可信域名的微信文件路径
     */
    private static String WxMpFilePath;


    private static String env;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCopyrightYear() {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear) {
        this.copyrightYear = copyrightYear;
    }

    public boolean isDemoEnabled() {
        return demoEnabled;
    }

    public void setDemoEnabled(boolean demoEnabled) {
        this.demoEnabled = demoEnabled;
    }

    public static String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        CkyConfig.profile = profile;
    }

    public static boolean isAddressEnabled() {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        CkyConfig.addressEnabled = addressEnabled;
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath() {
        return getProfile() + "/avatar";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath() {
        // 直接获取静态变量会为空 这样获取就可以？
        return getProfile() + "/download/";
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath() {
        return getProfile() + "/upload";
    }

    public static Integer getSubmitCount() {
        return submitCount;
    }

    public static void setSubmitCount(Integer submitCount) {
        CkyConfig.submitCount = submitCount;
    }

    public static String getDownPath() {
        return downPath;
    }

    public static void setDownPath(String downPath) {
        CkyConfig.downPath = downPath;
    }

    public static String getWxMpFilePath() {
        return WxMpFilePath;
    }

    public  void setWxMpFilePath(String wxMpFilePath) {
        WxMpFilePath = wxMpFilePath;
    }

    public static String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        CkyConfig.env = env;
    }
}
