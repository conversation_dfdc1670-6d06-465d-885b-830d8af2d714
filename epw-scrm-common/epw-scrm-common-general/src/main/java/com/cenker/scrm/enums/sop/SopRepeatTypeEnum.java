package com.cenker.scrm.enums.sop;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/8/3
 * @Description 重复类型枚举
 */
@Getter
public enum SopRepeatTypeEnum {
    /**
     *  重复类型 0 永不 1 每天 2 每周 3 每两周 4 每月 5 每年
     *  重复单位 0 永不 1 日 2 周 3 月 4 年
     */
    NEVER_REPEAT(0,0,"永不"),
    EVERY_DAY(1,1,"每天"),
    EVERY_WEEK(2,2,"每周"),
    EVERY_TWO_WEEK(3,2,"每两周"),
    EVERY_MONTH(4,3,"每月"),
    EVERY_YEAR(5,4,"每年"),
    WORK_DAY(6,1,"工作日");

    /**
     * 重复类型值
     */
    private Integer repeatType;
    /**
     * 重复单位
     */
    private Integer repeatUnit;
    /**
     * 重复类型描述
     */
    private String repeatTypeDesc;

    SopRepeatTypeEnum(Integer repeatType, Integer repeatUnit, String repeatTypeDesc) {
        this.repeatType = repeatType;
        this.repeatUnit = repeatUnit;
        this.repeatTypeDesc = repeatTypeDesc;
    }

    /**
     * 根据类型获取对应的枚举类
     * @param repeatType 重复类型值
     * @return 枚举类
     */
    public static SopRepeatTypeEnum getSopRepeatTypeEnum(Integer repeatType){
        for (SopRepeatTypeEnum value : SopRepeatTypeEnum.values()) {
            if (value.getRepeatType().equals(repeatType)) {
                return value;
            }
        }
        return SopRepeatTypeEnum.NEVER_REPEAT;
    }

}
