package com.cenker.scrm.model.base;

import cn.hutool.core.util.ObjectUtil;
import com.cenker.scrm.constants.HttpStatus;
import com.cenker.scrm.enums.ErrCodeEnum;
import com.cenker.scrm.model.login.BaseLoginInfo;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxCorpConfig;
import com.cenker.scrm.pojo.request.statistic.StatisticSummaryQuery;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.base.Result;
import com.cenker.scrm.util.DateUtils;
import com.cenker.scrm.util.SqlUtil;
import com.cenker.scrm.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
@Slf4j
public class BaseController {

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataInfo getDataTable(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 页面跳转
     */
    public String redirect(String url) {
        return StringUtils.format("redirect:{}", url);
    }


    /**
     * 过滤条件
     * @param loginInfo
     * @param request
     */
    protected void filterCondition(BaseLoginInfo loginInfo, BaseRequest request) {
        if (Objects.isNull(loginInfo)) {
            return;
        }

        request.setCorpId(loginInfo.getTenantId());
        request.setUserId(loginInfo.getUserId());
        request.setWxUserId(loginInfo.getWxUserId());
        request.setDeptId(loginInfo.getDeptId());
        request.setDataScope(loginInfo.getDataScope());
        request.setPermissionDeptIds(loginInfo.getPermissionDeptIds());
    }


    /**
     * 过滤创建人
     * @param sysUser
     * @param request
     */
    protected void filterCreateByPermission(SysUser sysUser, BaseRequest request) {
        if (ObjectUtil.isNotNull(sysUser) && !sysUser.isAdmin()) {
            request.setSysUserIds(sysUser.getSysUserIds());
        }
    }

    /**
     * 过滤员工
     * @param sysUser
     * @param request
     */
    protected void filterWkUserIdPermission(SysUser sysUser, BaseRequest request) {
        if (ObjectUtil.isNotNull(sysUser) && !sysUser.isAdmin()) {
            request.setWkUserIds(sysUser.getWkUserIds());
        }
    }
    /**
     * 校验日期参数
     * @param query
     * @return
     */
    protected Result<Object> validateDate(StatisticSummaryQuery query) {
        // 如果开始时间和结束时间都为空，则默认查询最近一天的数据
        if (query.getBeginTime() == null && query.getEndTime() == null) {
            query.setBeginTime(DateUtils.getNow());
            query.setEndTime(DateUtils.getNow());
        } else if (query.getBeginTime() != null && query.getEndTime() != null) {
            try {
                // 解析日期并缓存结果，避免重复解析
                Date beginTime = DateUtils.parseDate(query.getBeginTime());
                Date endTime = DateUtils.parseDate(query.getEndTime());
                // 如果开始时间大于结束时间，则提示错误
                if (beginTime.after(endTime)) {
                    return Result.error(ErrCodeEnum.PARAM_ERROR.getCode(), "开始时间不能大于结束时间");
                }

                // 检查时间范围是否超过30天（包含等于30天的情况）
                long diffDays = DateUtils.diffTimeDay(endTime, beginTime);
                if (diffDays > 30) {
                    return Result.error(ErrCodeEnum.PARAM_ERROR.getCode(), "查询时间范围不能超过30天");
                }
            } catch (Exception e) {
                // 捕获日期解析异常并返回错误信息
                return Result.error(ErrCodeEnum.PARAM_ERROR.getCode(), "日期格式错误，请检查输入的时间参数");
            }
        }
        return null;
    }

    /**
     * 判断部门是否在审批部门列表中
     * @param deptId
     * @param data
     * @return
     */
    protected static Boolean getEnableApproval(String deptId, TbWxCorpConfig data) {
        if (data == null) {
            return false;
        }
        String contentAuditDepart = data.getContentAuditDepart();
        Boolean enableApproval = false;
        if (StringUtils.isNotEmpty(contentAuditDepart)) {
            String[] departIdArr = contentAuditDepart.split(",");
            enableApproval = Stream.of(departIdArr).anyMatch(departId -> departId.equals(deptId));
        }
        return enableApproval;
    }
}
