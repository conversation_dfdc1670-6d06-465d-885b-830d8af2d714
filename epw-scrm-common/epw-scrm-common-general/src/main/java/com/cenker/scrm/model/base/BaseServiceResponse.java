package com.cenker.scrm.model.base;

/**
 * <AUTHOR>
 * @create 2021/6/29 12:07
 */
public class BaseServiceResponse<T> {
    /**
     * 是否执行成功
     */
    private boolean isSuccess;

    /**
     * 结果说明
     */
    private String message;

    /**
     * 获取结果说明
     * @return
     */
    public String getMessage() {
        return message;
    }

    /**
     * 设置结果说明
     * @param message
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 请求实体
     */
    private T requestModel;

    /**
     * 获取请求实体是否设置成功
     * @return
     */
    public boolean isSuccess() {
        return isSuccess;
    }

    /**
     * 设置请求实体是否成功
     * @param success
     */
    public void setSuccess(boolean success) {
        isSuccess = success;
    }

    /**
     * 获取请求实体信息
     * @return
     */
    public T getRequestModel() {
        return requestModel;
    }

    /**
     * 设置请求实体信息
     * @param requestModel
     */
    public void setRequestModel(T requestModel) {
        this.requestModel = requestModel;
    }
}
