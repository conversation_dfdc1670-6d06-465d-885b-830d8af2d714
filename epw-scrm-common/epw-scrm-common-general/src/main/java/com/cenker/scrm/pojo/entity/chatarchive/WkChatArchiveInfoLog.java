package com.cenker.scrm.pojo.entity.chatarchive;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 会话记录基础记录对象 wk_chat_archive_info
 *
 * <AUTHOR>
 * @date 2024-10-31
 */
@Data
@TableName(value = "wk_chat_archive_info_log",autoResultMap = true)
public class WkChatArchiveInfoLog implements Serializable {

    private static final long serialVersionUID = -6985861864076960449L;

    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 消息id */
    @Excel(name = "消息id")
    private String msgId;

    /** 员工id */
    @Excel(name = "员工id")
    private String userId;

    /** 成员名称 */
    @Excel(name = "成员名称")
    private String userName;

    /** 部门id */
    @Excel(name = "部门id")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 群ID */
    @Excel(name = "群ID")
    private String roomId;

    /** 群名称 */
    @Excel(name = "群名称")
    private String roomName;

    /** 消息时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date msgTime;

    /** 企业ID */
    @Excel(name = "企业ID")
    private String corpId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
