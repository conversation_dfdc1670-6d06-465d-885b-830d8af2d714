package com.cenker.scrm.handler.oper;

import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;

/**
 * 客户流失
 */
public class CustomerChurnHandler extends DefaultBuOperTrackHandler {
    public CustomerChurnHandler(TrackEventTypeEnum typeEnum) {
        this.eventType = typeEnum;
    }

    @Override
    protected String getContent(OperTrackParams operTrackParams) {
        return eventType.getContent();

    }
}
