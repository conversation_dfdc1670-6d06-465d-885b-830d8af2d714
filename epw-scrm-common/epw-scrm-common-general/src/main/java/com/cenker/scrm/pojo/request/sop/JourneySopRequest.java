package com.cenker.scrm.pojo.request.sop;

import com.cenker.scrm.pojo.dto.sop.JourneySopStageDTO;
import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/25
 * @Description 旅程sop请求实体
 */
@Data
public class JourneySopRequest extends BaseSopRequest {

    /**
     * 旅程id
     */
    @NotNull(message = "旅程id不能为空",groups = {UpdateGroup.class})
    private Long journeyId;

    /**
     * 阶段id
     */
    private Long stageId;

    /**
     * 阶段条件与内容
     */
    @NotNull(message = "请填写阶段",groups = {InsertGroup.class, UpdateGroup.class})
    @Size(min = 1, message = "请至少填写一个阶段",groups = {InsertGroup.class, UpdateGroup.class})
    @Size(max = 10,message = "阶段数受限",groups = {InsertGroup.class,UpdateGroup.class})
    @Valid
    private List<JourneySopStageDTO> sopStageList;
}
