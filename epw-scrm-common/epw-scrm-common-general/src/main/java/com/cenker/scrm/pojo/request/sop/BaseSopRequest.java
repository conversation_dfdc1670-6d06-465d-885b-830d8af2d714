package com.cenker.scrm.pojo.request.sop;

import com.cenker.scrm.model.base.BaseWebRequest;
import com.cenker.scrm.pojo.valid.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/7/7
 * @Description sop请求通用
 */
@Data
public class BaseSopRequest extends BaseWebRequest {

    /**
     * sopId
     */
    @Null(message = "参数错误，不应存在此id", groups = {InsertGroup.class})
    @NotNull(message = "缺失sopId", groups = {UpdateGroup.class, DeleteGroup.class, DetailGroup.class, ChangeStatusGroup.class,DataGroup.class,BatchGroup.class})
    private Long sopId;

    /**
     * sop名称
     */
    @NotBlank(message = "sop名称不能为空", groups = {InsertGroup.class, UpdateGroup.class})
    @Size(max = 10, message = "sop名称字数受限", groups = {InsertGroup.class, UpdateGroup.class})
    private String sopName;

    /**
     * sop备注
     */
    @Size(max = 100, message = "sop备注字数受限", groups = {InsertGroup.class, UpdateGroup.class})
    private String remark;

    /**
     * sop类型 1 条件sop 2 旅程sop 3 1V1sop 4 社群sop
     */
    private Integer sopType;

    /**
     * 状态： EXEC_INTERRUPTED 已停用 EXECUTING 运行中 PENDING_APPROVAL 待审核 REJECTED 已退回  REVOKED 已撤回
     */
    private String alive;

    /**
     * 定时任务id
     */
    private Long jobId;

    /**
     * 查询时间（创建时间）
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    /**
     * 是否启用审批流程
     * 0 否 1 是
     */
    private boolean enableApproval;

    private String approvalUser;
    /**
     * 是否启用sop
     * true 启用 false 停用
     */
    @NotNull(message = "缺失操作状态", groups = {ChangeStatusGroup.class})
    private boolean enableSop;
}
