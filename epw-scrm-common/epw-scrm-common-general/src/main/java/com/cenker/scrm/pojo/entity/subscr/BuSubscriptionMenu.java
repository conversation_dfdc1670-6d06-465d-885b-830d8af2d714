package com.cenker.scrm.pojo.entity.subscr;

import com.baomidou.mybatisplus.annotation.*;
import com.cenker.scrm.model.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 订阅菜单实体类
 * 用于存储订阅菜单的相关信息
 */
@Data
@TableName("bu_subscription_menu")
public class BuSubscriptionMenu extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 发布状态（0：未发布，1：已发布）
     */
    private Integer releaseStatus;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    private Integer delFlag;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 主标题
     */
    private String mainTitle;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 服务协议
     */
    private String serviceAgreement;
    /**
     * 订阅升级时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date subscrUpgradeTime;
    /**
     * 部门id
     */
    private Integer deptId;
}