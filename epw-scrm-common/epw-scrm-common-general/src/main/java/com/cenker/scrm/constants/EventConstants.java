package com.cenker.scrm.constants;

/**
 * 事件常量
 *
 * @ClassName EventConstants
 * <AUTHOR>
 * @Date 2021/4/8 18:12
 **/
public class EventConstants {

    public static final String CHANGE_EXTERNAL_CHAT = "change_external_chat";

    public static final String EIDT_EXTERNAL_CONTACT = "edit_external_contact";

    public static final String EXTERNAL_CHAT_CREATE = "create";

    public static final String EXTERNAL_CHAT_UPDATE = "update";

    public static final String EXTERNAL_CHAT_DISMISS = "dismiss";

    public static final String EXTERNAL_CHAT_DELETE = "delete";

    // 重排事件
    public static final String EXTERNAL_CHAT_SHUFFLE = "shuffle";

    public static final String TRANSFER_FAIL = "transfer_fail";

    public static final String CHANGE_CONTACT_CREATE_USER = "create_user";
    public static final String CHANGE_CONTACT_UPDATE_USER = "update_user";
    public static final String CHANGE_CONTACT_DELETE_USER = "delete_user";

    public static final String CHANGE_CONTACT_CREATE_PARTY = "create_party";
    public static final String CHANGE_CONTACT_UPDATE_PARTY = "update_party";
    public static final String CHANGE_CONTACT_DELETE_PARTY = "delete_party";

    /**
     * 企业标签事件
     */
    public static final String CHANGE_EXTERNAL_TAG = "change_external_tag";

    /**
     * 标签
     */
    public static final String TAG_TYPE_TAG = "tag";

    /**
     * 标签组
     */
    public static final String TAG_TYPE_TAG_GROUP= "tag_group";

    /**
     * 代自建应用重置secret
     */
    public static final String APP_RESET_PERMANENT_CODE= "reset_permanent_code";


    /**
     * 事件回调的参数key
     */
    public static final String WX_CP_SERVICE = "wxCpService";
    public static final String WX_SESSION_MANAGER = "wxSessionManager";
    public static final String WELCOME_ATTACHMENT_VO = "welcomeAttachmentVo";
    public static final String USER_ID= "user_id";

}
