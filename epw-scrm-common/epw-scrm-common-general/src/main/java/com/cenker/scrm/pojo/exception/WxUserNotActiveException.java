package com.cenker.scrm.pojo.exception;

/**
 * <AUTHOR>
 * @Date 2022/3/28
 * @Description 未激活异常
 */
public class WxUserNotActiveException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private Integer code;

    private String message;

    public WxUserNotActiveException(String message) {
        this.message = message;
    }

    public WxUserNotActiveException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public WxUserNotActiveException(String message, Throwable e) {
        super(message, e);
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }
}
