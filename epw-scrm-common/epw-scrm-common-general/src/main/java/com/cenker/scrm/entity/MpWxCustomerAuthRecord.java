package com.cenker.scrm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 微信客户认证记录实体类
 * 用于记录微信客户的认证状态和相关信息，易服务通过 ETL 同步到 SCRM 系统中
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Data
public class MpWxCustomerAuthRecord {
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 微信用户唯一标识
     */
    private String unionId;
    
    /**
     * 客户编号
     */
    private String custNo;
    
    /**
     * 应用编码
     */
    private String appCode;
    
    /**
     * 是否认证
     * 1: 已认证
     * 0: 未认证
     */
    private String isAuth;

    /**
     * 客户名称
     */
    private String realName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 同步时间
     */
    private Date syncTime;

    /**
     * customer信息是否发生变化，比较realName、custNo 2个字段
     */
    @TableField(exist = false)
    private boolean hasChanged;
}