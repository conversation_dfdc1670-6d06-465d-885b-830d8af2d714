package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 素材信息对象 tb_wx_material
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxMaterial {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 分类id
     */
    // @Excel(name = "分类id")
    private String categoryId;

    /**
     * 本地资源文件地址/海报时是背景图片
     */
    // @Excel(name = "本地资源文件地址")
    private String materialUrl;
    /**
     * 图片名称
     */
    // @Excel(name = "图片名称")
    private String materialName;
    /**
     * 摘要
     */
    // @Excel(name = "摘要")
    private String digest;
    /**
     * 0 未删除 1 已删除
     */
    private Integer delFlag;
    /**
     * 音频时长
     */
    // @Excel(name = "音频时长")
    private String audioTime;
    /**
     * 企业id
     */
    // @Excel(name = "企业id")
    private String corpId;
    /**
     * 媒体ID
     */
    // @Excel(name = "媒体id")
    private String mediaId;
    /**
     * 有效类型，0临时，1永久有效
     */
    private Integer validityType;
    /**
     * 视频截帧地址
     */
    private String frameUrl;

    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 素材类型
     */
    private Integer mediaType;

    private Long size;
    private Integer width;
    private Integer height;

    /**
     * 标题命名规则
     */
    private String namingRule;

    /**
     * 分类名称
     */
    @TableField(exist = false)
    private String categoryName;
    /**
     * 海报示例图
     */
    @TableField(exist = false)
    private String sampleUrl;

    /**
     * 聊天侧标栏是否抓取
     */
    @TableField(exist = false)
    private boolean isGrab;

    /**
     * 侧边栏id 用来获取已抓取的素材
     */
    @TableField(exist = false)
    private String sideId;
    /**
     * 侧边栏id 用来排除已抓取的素材
     */
    @TableField(exist = false)
    private String sideIds;
    @TableField(exist = false)
    private List<String> queryCategoryIds;

    /**
     * 部门ID
     */
    private Integer deptId;

}
