package com.cenker.scrm.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023/7/4
 * @Description 智能物料配置
 */
@Component
@ConfigurationProperties(prefix = "wechat.radar")
public class RadarConfig {

    /**
     * 智能物料页面路径
     */
    private static String contentPage;

    /**
     * 转换图片的dpi 越高越清晰
     */
    private static Integer pdfImageDpi;

    public static String getContentPage() {
        return contentPage;
    }

    public static Integer getPdfImageDpi() {
        return pdfImageDpi;
    }

    public void setContentPage(String contentPage) {
        RadarConfig.contentPage = contentPage;
    }

    public void setPdfImageDpi(Integer pdfImageDpi) {
        RadarConfig.pdfImageDpi = pdfImageDpi;
    }
}
