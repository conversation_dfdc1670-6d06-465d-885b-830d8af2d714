package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;

/**
 * 微信客服 聊天记录 channels_shop_order 文件 对象 tb_wx_kf_msg
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wk_wx_kf_msg_channels_shop_order")
public class WkWxKfMsgChannelsShopOrder extends WkWxKfMsgItemCommonResp {

    private String orderId;
    private String productTitles;
    private String priceWording;
    @TableField("`state`")
    private String state;
    private String imageUrl;
    private String shopNickname;

    @Override
    public WkWxKfMsgChannelsShopOrder init(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        super.init(msgItem);

        this.orderId = msgItem.getChannelsShopOrder().getOrderId();
        this.productTitles = msgItem.getChannelsShopOrder().getProductTitles();
        this.priceWording = msgItem.getChannelsShopOrder().getPriceWording();
        this.state = msgItem.getChannelsShopOrder().getState();
        this.imageUrl = msgItem.getChannelsShopOrder().getImageUrl();
        this.shopNickname = msgItem.getChannelsShopOrder().getShopNickname();

        return this;
    }
}
