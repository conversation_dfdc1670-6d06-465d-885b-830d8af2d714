package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.*;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 员工活码对象 tb_wx_contact
 *
 * <AUTHOR>
 * @date 2021-01-25
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
// @ApiModel("员工活码实体")
public class TbWxContact implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 企业ID
     */
    // @ApiModelProperty("企业ID")
    @Excel(name = "企业ID")
    private String corpId;

    /**
     * 使用该联系方式的用户userID列表，在type为1时为必填，且只能有一个
     */
    // @ApiModelProperty("使用该联系方式的用户userID列表，在type为1时为必填，且只能有一个")
    @Excel(name = "使用该联系方式的用户userID列表，在type为1时为必填，且只能有一个")
    private String userId;

    /**
     * 使用该联系方式的部门id列表，只在type为2时有效
     */
    // @ApiModelProperty("使用该联系方式的部门id列表，只在type为2时有效")
    @Excel(name = "使用该联系方式的部门id")
    private String party;

    /**
     * 联系方式类型,1-单人, 2-多人
     */
    // @ApiModelProperty("联系方式类型,1-单人, 2-多人")
    @Excel(name = "联系方式类型,1-单人, 2-多人")
    private Integer type;

    /**
     * 场景，1-在小程序中联系，2-通过二维码联系
     */
    // @ApiModelProperty("2-通过二维码联系")
    @Excel(name = "场景，1-在小程序中联系，2-通过二维码联系")
    private String scene;

    /**
     * 外部客户添加时是否无需验证，默认为true(1)
     */
    // @ApiModelProperty("外部客户添加时是否无需验证")
    @Excel(name = "外部客户添加时是否无需验证，默认为true(1)")
    private Integer skipVerify;

    /**
     * 企业自定义的state参数，用于区分不同的添加渠道，在调用“获取外部联系人详请
     */
    @Excel(name = "企业自定义的state参数，用于区分不同的添加渠道，在调用“获取外部联系人详请")
    private String state;

    /**
     * 新增联系方式的配置id
     */
    @Excel(name = "新增联系方式的配置id")
    private String configId;

    /**
     * 使用状态，1：使用中，0：已废弃
     */
    private Integer useStatus;

    /**
     * 联系我二维码链接，仅在scene为2时返回
     */
    // @ApiModelProperty("联系我二维码链接，仅在scene为2时返回")
    @Excel(name = "联系我二维码链接，仅在scene为2时返回")
    private String qrCode;

    /**
     * 是否删除1：删除
     */
    private String delFlag;

    /**
     * 是否前端展示 0否 1是
     */
    @TableField("`show`")
    private Integer show;

    /**
     * 欢迎语模板ID
     */
    // @ApiModelProperty("欢迎语模板ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED) // 强制更新 null 值
    @Excel(name = "欢迎语模板ID")
    private String welTplId;

    /**
     * 欢迎语
     */
    // @ApiModelProperty("欢迎语")
    @Excel(name = "欢迎语")
    private String welContent;

    /**
     * 素材中心Id
     */
    // @ApiModelProperty("素材中心Id")
    @Excel(name = "素材中心Id")
    private String mediaId;

    /**
     * 图片Url
     */
    // @ApiModelProperty("图片Url")
    @Excel(name = "图片Url")
    private String imgUri;

    /**
     * 二维码样式 1 二维码 2 工卡
     */
    private Integer qrStyle;
    /**
     * 二维码样式地址
     */
    private String qrStyleCode;
    /**
     * 欢迎语附件
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String welcomeAttachment;


    /**
     * 员工活码标签信息
     */
    // @ApiModelProperty("员工活码标签信息")
    private String tagIds;

    // 欢迎语模板内容
    // @ApiModelProperty("欢迎语模板内容")
    @TableField(exist = false)
    private String welTplMsg;

    // 欢迎语模板图片地址
    // @ApiModelProperty("欢迎语模板图片地址")
    @TableField(exist = false)
    private String welTplUrl;

    // 用户名称
    // @ApiModelProperty("用户名称")
    @TableField(exist = false)
    private String userName;

    // 用户手机
    // @ApiModelProperty("用户手机")
    @TableField(exist = false)
    private String userPhone;

    // 部门名称
    // @ApiModelProperty("部门名称")
    @TableField(exist = false)
    private String partyName;

    // 标签名称
    // @ApiModelProperty("标签名称")
    @TableField(exist = false)
    private String tagName;

    // @ApiModelProperty("开始时间")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    // @ApiModelProperty("结束时间")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    private String remark;

    /**
     * 前段字段
     */
    @TableField(value = "weEmpleCodeTags",updateStrategy = FieldStrategy.IGNORED)
    private String weEmpleCodeTags;

    /**
     * 前段字段
     */
    @TableField("weEmpleCodeUseScops")
    private String weEmpleCodeUseScops;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 2022-05-31 新增渠道活码创建来源标识 1 web  2 侧边栏 3 工作台
     */
    private Integer since;

    /**
     * 站点id
     */
    @TableField(exist = false)
    private Long cityId;

    /**
     * 部门id
     */
    private Integer deptId;
}
