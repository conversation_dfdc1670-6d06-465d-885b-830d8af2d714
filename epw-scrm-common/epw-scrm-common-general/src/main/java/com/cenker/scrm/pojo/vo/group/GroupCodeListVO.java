package com.cenker.scrm.pojo.vo.group;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/8/23
 * @Description
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroupCodeListVO extends GroupCodeInfoVO {

    /**
     * 扫码次数
     */
    private Integer scanCodeCnt;

    /**
     * 入群总客户数
     */
    private Integer totalAddGroupCnt;

    /**
     * 今日入群客户数量
     */
    private Integer todayAddGroupCnt;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 活码地址
     */
    private String groupCodeUrl;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
