package com.cenker.scrm.pojo.request.kf;

import com.cenker.scrm.enums.kf.WxKfMsgTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.sql.Date;
import java.util.List;

@Data
@AllArgsConstructor
public class WkWxKfChatRecordQuery {
    private String servicerUserId;
    private String externalUserId;
    private List<String> msgTypeList;
    private Date startTime;
    private Date endTime;
    private String queryText;
    private String action;

    public void setMsgTypeList(List<WxKfMsgTypeEnum> msgTypeList) {
        for (WxKfMsgTypeEnum msgType : msgTypeList) {
            this.msgTypeList.add(msgType.getMsgType());
        }
    }

    public WxKfMsgTypeEnum getMsgTypeEnum() {
        for (String msgType: msgTypeList) {
            return WxKfMsgTypeEnum.fromValue(msgType);
        }
        return null;
    }
}
