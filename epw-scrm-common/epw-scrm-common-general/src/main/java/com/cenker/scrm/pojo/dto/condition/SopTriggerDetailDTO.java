package com.cenker.scrm.pojo.dto.condition;

import com.cenker.scrm.pojo.valid.InsertGroup;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2023/7/7
 * @Description 触发条件详情
 */
@Data
public class SopTriggerDetailDTO {

    /**
     * 触发类型 1 加好友 2 加群 3 浏览智能物料 4 累计消费 5 购买商品
     */
    @NotNull(message = "请选择触发类型",groups = {InsertGroup.class})
    @Min(value = 1,message = "触发类型非法参数",groups = {InsertGroup.class})
    private Integer triggerType;

    /**
     * 触发条件类型
     * 1 指定好友/指定群/浏览指定智能物料 2 添加好友数/添加群数/转发指定智能物料的
     * 3 浏览全部智能物料总次数  4 浏览全部智能物料总时长（单位：秒） 5 浏览全部智能物料的转发数
     */
    @NotNull(message = "请选择触发条件类型",groups = {InsertGroup.class})
    @Min(value = 1,message = "触发条件类型非法参数",groups = {InsertGroup.class})
    private Integer triggerConditionType;

    /**
     * 触发关系类型 1 等于 2 不等于 3 包含 4 不包含 5 大于 6 小于 7 大于等于 8 小于等于
     */
    @Min(value = 0,message = "触发关系类型非法参数",groups = {InsertGroup.class})
    @NotNull(message = "请选择触发关系类型",groups = {InsertGroup.class})
    private Integer triggerRelationType;

    /**
     * 触发条件值
     */
    @NotNull(message = "请填写或选择触发条件值",groups = {InsertGroup.class})
    private SopTriggerConditionValueDTO triggerConditionValue;
}
