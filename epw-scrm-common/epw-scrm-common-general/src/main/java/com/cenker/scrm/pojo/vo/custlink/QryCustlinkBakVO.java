package com.cenker.scrm.pojo.vo.custlink;


import com.cenker.scrm.model.base.BaseRequest;
import com.cenker.scrm.pojo.dto.condition.UserConditionDTO;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/9
 * @Description 联系我封装对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QryCustlinkBakVO {
    private String linkName;
    private String linkUrl;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 欢迎语
     */
    private String welContent;
    @NotNull
    private List<UserConditionDTO> userConditionList;
    /**
     * 联系我添加标签 添加客户时自动添加
     */
    private List<TagVO> tagList;
    /**
     * 附件
     */
    private List<WelcomeAttachmentVo> attachments;
    @NotNull
    private boolean skipVerify;
    private String customerNum;
    private String createByName;
    private String createTime;
    private String status;

    /** 欢迎语模板 */
    private String welTplId;

    /** 欢迎语模板名称 */
    private String welTplName;
}
