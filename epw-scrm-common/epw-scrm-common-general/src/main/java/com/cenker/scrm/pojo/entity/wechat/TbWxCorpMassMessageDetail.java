package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 群发信息-发送信息详情对象 tb_wx_corp_mass_message_detail
 *
 * <AUTHOR>
 * @date 2021-02-03
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCorpMassMessageDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 图片素材id
     */
    @Excel(name = "文本素材id")
    private String textMessageId;

    /**
     * 消息文本内容，最多4000个字节
     */
    @Excel(name = "消息文本内容，最多4000个字节")
    private String content;

    /**
     * 图片消息：图片的media_id，可以通过 <a href="https://work.weixin.qq.com/api/doc/90000/90135/90253">素材管理接口</a>获得
     */
    private String mediaId;

    /**
     * 图片消息：图片的链接，仅可使用<a href="https://work.weixin.qq.com/api/doc/90000/90135/90256">上传图片接口</a>得到的链接
     */
    private String picUrl;

    /**
     * 链接消息：图文消息标题
     */
    @Excel(name = "链接消息：图文消息标题")
    private String linkTitle;

    /**
     * 链接消息：图文消息封面的url
     */
    @Excel(name = "链接消息：图文消息封面的url")
    private String linkPicUrl;

    /**
     * 链接消息：图文消息的描述，最多512个字节
     */
    @Excel(name = "链接消息：图文消息的描述，最多512个字节")
    private String linDesc;

    /**
     * 链接消息：图文消息的链接
     */
    @Excel(name = "链接消息：图文消息的链接")
    private String linkUrl;

    /**
     * 小程序消息标题，最多64个字节
     */
    @Excel(name = "小程序消息标题，最多64个字节")
    private String miniProgramTitle;

    /**
     * 小程序消息封面的mediaid，封面图建议尺寸为520*416
     */
    @Excel(name = "小程序消息封面的mediaid，封面图建议尺寸为520*416")
    private String miniProgramMediaId;

    /**
     * 小程序appid，必须是关联到企业的小程序应用
     */
    @Excel(name = "小程序appid，必须是关联到企业的小程序应用")
    private String appId;

    /**
     * 小程序page路径
     */
    @Excel(name = "小程序page路径")
    private String page;

    /**
     * 删除标识
     */
    private Integer delFlag;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 公司id
     */
    @Excel(name = "公司id")
    private String corpId;

    private Long messageInfoId;
}