package com.cenker.scrm.pojo.entity.wechat;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TbWxMassMessageSenderRecord {
  
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * message_info表主键
   */
  private Long messageInfoId;
  /**
   * message_detail表主键
   */
  private Long messageDetailId;
  private String userId;
  private Long userPriId;
  private String externalUserId;
  private Long externalPriId;
  private String chatId;
  private Long chatPriId;
  /**
   * 发送状态 0-未发送 1-已发送 2-因客户不是好友导致发送失败 3-因客户已经收到其他群发消息导致发送失败
   */
  private Integer sendStatus;
  /**
   * 发送时间
   */
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
  private Date sendTime;
  private String remark;
  private Integer delFlag;
  private Long corpConfigId;
  private Long createBy;
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;
  private Long updateBy;
  private Date updateTime;

}
