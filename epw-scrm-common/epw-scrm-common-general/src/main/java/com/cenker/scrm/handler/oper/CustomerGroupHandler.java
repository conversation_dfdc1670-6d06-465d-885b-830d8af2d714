package com.cenker.scrm.handler.oper;

import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;

/**
 * 客户入群
 */
public class CustomerGroupHandler extends DefaultBuOperTrackHandler {
    public CustomerGroupHandler(TrackEventTypeEnum trackEventTypeEnum) {
        this.eventType = trackEventTypeEnum;
    }

    @Override
    protected String getContent(OperTrackParams operTrackParams) {
        String contentStr = eventType.getContent();
        if (TrackEventTypeEnum.GROUP_JOIN_SCAN.equals(eventType)) {
            contentStr = contentStr.replace("{0}", operTrackParams.getName());
        }
        return contentStr;
    }
}
