package com.cenker.scrm.pojo.entity.knowledgebaseai;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.util.Date;
import java.util.List;

@Data
@TableName(value = "ck_ai_app",autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
public class CkAiApp {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String appName;
    private String appDescription;
    private String appUrl;
    private String logoUrl;
    @DecimalMax(value = "1",message = "温度不能大于1")
    @DecimalMin(value = "0",message = "温度不能小于0")
    private Float temperature;
    private String status;
    private String remark;
    /**
     * 逻辑删除 0 表示未删除，1 表示删除
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    private String replySetting;
    private String replyValue;
    private String apiCode;
    private String apiSecret;
    @TableField(exist = false)
    private String ckAiRepositoryNum;
    @TableField(exist = false)
    private List<CkAiRepository> ckAiRepositoryList;
    @TableField(exist = false)
    private Long[] ckAiRepositoryIds;
}
