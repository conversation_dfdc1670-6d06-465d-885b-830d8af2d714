package com.cenker.scrm.pojo.request.system;

import com.cenker.scrm.model.base.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @title SystemNoticeRequest
 * @date 2024/12/3 10:20
 * @description TODO
 */
@Data
public class SysNoticeRequest extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 8226644343422639801L;

    /**
     * 消息记录Id
     */
    private Long id;
    /**
     * 消息Id
     */
    private Long noticeId;
    /**
     * 消息标题
     */
    private String noticeTitle;

    /**
     * 员工Id
     */
    private String userId;

    private List<String> userIds;

    /**
     * 状态，0-未发布，1-待发布，2-已发布，3-已撤回，全部为空
     */
    private String status;

    /**
     * 公告类型，1-公告，2-站内信
     */
    private Integer noticeType;

    /**
     * 通知方式，1-管理后台，2-企微端
     */
    private Integer noticeMode;

    /**
     * 创建开始时间
     */
    private String beginTime;

    /**
     * 创建结束时间
     */
    private String endTime;

    /**
     * 操作者Id
     */
    private String operatorId;

    private String corpId;
    /**
     * 是否已读，0-未读，1-已读
     */
    private Integer readStatus;
    /** 是否已提醒，0-未提醒，1-已提醒 */
    private String hasReminder;
}
