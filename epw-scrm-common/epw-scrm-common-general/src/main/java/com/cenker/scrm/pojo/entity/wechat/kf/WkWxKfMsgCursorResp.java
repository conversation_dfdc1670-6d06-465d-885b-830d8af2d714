package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Date;
import java.time.LocalDate;

/**
 * 微信客服 cursor 对象 tb_wx_kf_cursor
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@Data
@TableName("wk_wx_kf_msg_cursor_resp")
public class WkWxKfMsgCursorResp {
    private Long id; // 主键 ID
    private String corpId; // 企业 ID
    private String openKfId; // 客服 ID
    @TableField("`cursor`")
    private String cursor; // 消息游标, 定位上次读取消息位置
    @TableField("`status`")
    private String status; // 消息可用状态, 1:表示可用, 0:已使用
    private Date createTime; // 创建时间(java.sql.Date)
    private Date updateTime; // 更新时间(java.sql.Date)

    public WkWxKfMsgCursorResp(Long id, String corpId, String openKfId, String cursor) {
        this.id = id;
        this.corpId = corpId;
        this.openKfId = openKfId;
        this.cursor = cursor;
        this.status = "1";
        this.createTime = Date.valueOf(LocalDate.now());
        this.updateTime = Date.valueOf(LocalDate.now());
    }
}
