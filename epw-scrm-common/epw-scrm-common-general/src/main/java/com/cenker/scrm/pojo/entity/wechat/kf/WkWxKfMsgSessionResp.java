package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Date;
import java.time.LocalDate;

@Data
@TableName("wk_wx_kf_msg_session_resp")
public class WkWxKfMsgSessionResp {
    private Long id;
    private String externalUsername;
    @TableField("`status`")
    private String status;
    private String channel;
    private String servicerName;
    private String openKfId;
    private Date sessionStartTime;
    private Date sessionEndTime;
    private Date createTime;
    private Date updateTime;

    public WkWxKfMsgSessionResp(Long id, String externalUsername, String status, String channel, String servicerName, String openKfId, Date sessionStartTime, Date sessionEndTime) {
        this.id = id;
        this.externalUsername = externalUsername;
        this.status = status;
        this.channel = channel;
        this.servicerName = servicerName;
        this.openKfId = openKfId;
        this.sessionStartTime = sessionStartTime;
        this.sessionEndTime = sessionEndTime;
        this.createTime = Date.valueOf(LocalDate.now()); // 创建时间
        this.updateTime = Date.valueOf(LocalDate.now()); // 更新时间
    }
}
