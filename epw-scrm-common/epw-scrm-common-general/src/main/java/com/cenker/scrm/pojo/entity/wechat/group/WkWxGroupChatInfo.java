package com.cenker.scrm.pojo.entity.wechat.group;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseDbEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * @Description 客户群「加入群聊」表
 * <AUTHOR> @Date 2023-08-23 
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@Builder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "fieldHandler"}, ignoreUnknown = true)
@TableName("wk_wx_group_chat_info")
public class WkWxGroupChatInfo extends BaseDbEntity implements Serializable {
	private static final long serialVersionUID =  5061312223251185249L;
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 社群活码唯一标识
	 */
	private Long groupCodeId;

	/**
	 * 是否自动创建新群 0 否 1 是
	 */
	@TableField(value = "is_auto_create_room")
	private Boolean autoCreateRoom;

	/**
	 * 自动建群新群名称 当is_auto_create_room为1时有效 最长40个utf8字符
	 */
	private String roomBaseName;

	/**
	 * 自动建群的群起始序号
	 */
	private Integer roomBaseId;

	/**
	 * 企业自定义的state参数，用于区分不同的入群渠道。不超过30个UTF-8字符
	 */
	private String state;

	/**
	 * 场景1 - 群的小程序插件 2 - 群的二维码插件
	 */
	private Integer scene;

	/**
	 * 新增联系方式的配置id
	 */
	private String configId;

	/**
	 * 联系二维码的URL或小程序插件的URL
	 */
	private String qrCode;
}

