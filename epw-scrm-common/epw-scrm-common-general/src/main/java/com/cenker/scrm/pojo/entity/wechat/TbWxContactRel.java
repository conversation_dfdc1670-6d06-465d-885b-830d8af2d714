package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_wx_contact_user_rel")
public class TbWxContactRel implements Serializable {

    private String userId;

    private String corpId;

    private String contactId;

    /**
     * 1 员工活码 2 站点活码
     */
    private Integer type;
}
