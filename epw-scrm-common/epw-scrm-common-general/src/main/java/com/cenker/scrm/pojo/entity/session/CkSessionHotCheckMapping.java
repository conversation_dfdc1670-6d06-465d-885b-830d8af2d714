package com.cenker.scrm.pojo.entity.session;
import com.baomidou.mybatisplus.annotation.TableField;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

/**
 * 热词审计人关联对象 ck_session_hot_check_mapping
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class CkSessionHotCheckMapping extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表iD */
    private Long id;

    /** 热词ID */
    private Long hotId;

    /** 审计人ID */
    private String checkUserId;

    @TableField(exist = false)
    private String name;
    @TableField(exist = false)
    private String avatar;


}
