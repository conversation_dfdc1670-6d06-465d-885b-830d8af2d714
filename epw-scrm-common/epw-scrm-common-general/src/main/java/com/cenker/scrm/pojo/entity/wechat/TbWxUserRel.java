package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_wx_user_rel")
public class TbWxUserRel {

    private String oldUserId;

    private String newUserId;

    private String corpId;

    private Date createTime;
}
