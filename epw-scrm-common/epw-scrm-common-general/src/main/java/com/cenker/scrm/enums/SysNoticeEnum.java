package com.cenker.scrm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @title SysNoticeEnum
 * @date 2024/12/4 14:07
 * @description TODO
 */
@Getter
@AllArgsConstructor
public enum SysNoticeEnum {

    NOTICE_MODE_ADMIN(1,"管理后台"),
    NOTICE_MODE_QW_WORK(2,"企业端"),
    NOTICE_TYPE_NOTICE(1,"系统公告"),
    NOTICE_TYPE_MSG(2,"消息通知"),
    // 0-未发布，1-待发布，2-已发布，3-已撤回
    NOTICE_STATUS_UN_PUBLISH(0,"未发布"),
    NOTICE_STATUS_WAIT_PUBLISH(1,"待发布"),
    NOTICE_STATUS_PUBLISH(2,"已发布"),
    NOTICE_STATUS_REVOCATION(3,"已撤回"),
    // 是否已读，0-未读，1-已读
    READ_STATUS_UNREAD(0,"未读"),
    READ_STATUS_READ(1,"已读"),

    REMINDER_STATUS_REMIND(1,"已提醒"),
    REMINDER_STATUS_UNREMIND(0,"未提醒");

    private Integer code;

    private String remark;

}
