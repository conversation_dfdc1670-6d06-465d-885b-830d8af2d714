package com.cenker.scrm.pojo.entity.wechat.sop;

import com.baomidou.mybatisplus.annotation.*;
import com.cenker.scrm.handler.JacksonTypeHandler;
import com.cenker.scrm.pojo.vo.welcome.WelcomeAttachmentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description sop内容信息表
 * <AUTHOR> @Date 2023-07-07 
 */
@Data
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "fieldHandler"}, ignoreUnknown = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ck_sop_content_info",autoResultMap = true)
public class SopContentInfo implements Serializable {
	private static final long serialVersionUID =  7167388322350123826L;
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * sop唯一标识
	 */
	private Long sopId;

	/**
	 * 旅程id
	 */
	private Long journeyId;

	/**
	 * 旅程阶段id
	 */
	private Long stageId;

	/**
	 * 内容标识
	 */
	private String contentSign;

	/**
	 * 内容版本号 格式：RWYYYYMMDD0001-V001
	 */
	private String contentVersion;

	/**
	 * 是否未删除的版本 用于标识需要统计数据的版本 0 否 1 是
	 */
	@TableField(value = "is_alive_version")
	private Boolean aliveVersion;

	/**
	 * 内容排序 从0开始 条件sop则为执行天数
	 */
	private Integer contentSort;

	/**
	 * 重复类型 0 永不 1 每天 2 每周 3 每两周 4 每月 5 每年
	 */
	private Integer repeatType;

	/**
	 * 重复单位 0 永不 1 日 2 周 3 月 4 年
	 */
	private Integer repeatUnit;

	/**
	 * 结束重复 0 永不 1 指定日期
	 */
	private Integer repeatExpire;

	/**
	 * 执行单位的值 1-31 逗号分隔 如单位为周1,2代表周一、周二
	 */
	private String executeValue;

	/**
	 * xxl-job任务id
	 */
	private Long jobId;

	/**
	 * 开始时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startTime;

	/**
	 * 结束时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endTime;

	/**
	 * 流失归因时间 单位分钟
	 */
	private Integer delAttributeSopMinute;

	/**
	 * 任务停止时间 单位小时
	 */
	private Integer stopTaskHour;

	/**
	 * 消息文本
	 */
	private String contentText;

	/**
	 * 消息附件
	 */
	@TableField(typeHandler = JacksonTypeHandler.class)
	private List<WelcomeAttachmentVo> contentAttachment;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 企业id
	 */
	private String corpId;

	/**
	 * 逻辑删除 0 表示未删除，1 表示删除
	 */
	@TableField(value = "is_deleted")
	@TableLogic
	private Boolean deleted;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}

