package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbWxDrainageShortLink {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 链接名称
     */
    private String linkName;
    /**
     * 1 加人 2 加群
     */
    private Integer type;
    /**
     * 1 默认 2 自定义海报
     */
    private Integer style;
    /**
     * 关联活码id
     */
    private Long codeId;
    /**
     * 活码/群码地址
     */
    private String codeUrl;
    /**
     * 0 永久 1 到期失效
     */
    private Integer expire;
    /**
     * 到期时间
     */
    private Date dueDate;
    /**
     * 1 上传 2 群活码导入
     */
    private Integer way;
    /**
     * 引导语
     */
    private String guideContent;
    /**
     * 页面标题
     */
    private String pageTitle;
    /**
     * 显示头像昵称 0 不开启 1 开启
     */
    private Integer showBaseInfo;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 显示企业信息 0 不开启 1 开启
     */
    private Integer showCorpInfo;
    /**
     * 企业名称
     */
    private String corpName;
    /**
     * 企业logo
     */
    private String corpAvatar;
    /**
     * 创建人
     */
    private Long createBy;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private Long updateBy;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    private Integer delFlag;
    /**
     * 企业id
     */
    private String corpId;
    /**
     * 短链映射参数
     */
    private String shortValue;
}
