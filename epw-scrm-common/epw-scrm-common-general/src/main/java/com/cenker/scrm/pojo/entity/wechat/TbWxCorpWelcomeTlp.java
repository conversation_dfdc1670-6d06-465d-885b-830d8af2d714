package com.cenker.scrm.pojo.entity.wechat;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 欢迎语模板对象 tb_wx_corp_welcome_tlp
 *
 * <AUTHOR>
 * @date 2021-01-24
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
 // @ApiModel("欢迎语实体")
public class TbWxCorpWelcomeTlp {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
     // @ApiModelProperty("模板ID")
    private Long id;

    /**
     * 欢迎语
     */
     // @ApiModelProperty("欢迎语")
     // @Excel(name = "欢迎语")
    @NotBlank(message = "欢迎语不能为空")
    private String welcomeMsg;

    /**
     * 素材的id
     */
     // @ApiModelProperty("素材Id")
     // @Excel(name = "素材的id")
    private String mediaId;

    /**
     * 0:正常;2:删除;
     */
     // @ApiModelProperty("删除标志")
    private Integer delFlag;

    /**
     * 欢迎语模板类型:1:员工欢迎语;2:部门员工欢迎语;3:客户群欢迎语
     */
     // @ApiModelProperty("欢迎语模板类型")
     // @Excel(name = "欢迎语模板类型:1:员工欢迎语;2:部门员工欢迎语;3:客户群欢迎语")
    @NotBlank(message = "请选择模板类型")
    private String welcomeMsgTplType;

    /**
     * $column.columnComment
     */
     // @ApiModelProperty("创建人")
     // @Excel(name = "欢迎语模板类型:1:员工欢迎语;2:部门员工欢迎语;3:客户群欢迎语")
    private String creator;

    /**
     * 企业id
     */
     // @ApiModelProperty("企业id")
     // @Excel(name = "企业id")
    private String corpId;

    /**
     * 图片url地址
     */
     // @ApiModelProperty("图片url地址")
     // @Excel(name = "图片url地址")
    private String imgUri;

     // @ApiModelProperty("客户群模板ID（微信返回）")
    private String templateId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
