package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TbWxCustomerAuthRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 微信用户唯一标识
     */
    private String unionId;

    /**
     * ESB系统中的用户编号
     */
    private String custno;

    /**
     * 绑定时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bindTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 逻辑删除 0 表示未删除，1 表示删除
     */
    @TableField(value = "del_flag")
    @TableLogic
    private Boolean delFlag;

    public TbWxCustomerAuthRecord(String unionId, String consNo) {
        this.unionId = unionId;
        this.custno = consNo;
        this.bindTime=new Date();
        this.setCreateTime(new Date());
        this.delFlag=false;
    }
}
