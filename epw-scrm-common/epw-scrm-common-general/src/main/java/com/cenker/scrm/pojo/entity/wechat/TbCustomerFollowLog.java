package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

/**
 * 客户跟踪日志对象 tb_customer_follow_log
 *
 * <AUTHOR>
 * @date 2021-04-07
 */
@Data
public class TbCustomerFollowLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 企业ID
     */
    // @Excel(name = "企业ID")
    private String corpId;

    /**
     * 企业成员userid
     */
    // @Excel(name = "企业成员userid")
    private String userId;

    /**
     * 客户id
     */
    // @Excel(name = "客户id")
    private String externalUserId;

    /**
     * 跟踪内容
     */
    // @Excel(name = "跟踪内容")
    private String content;

    /**
     * 跟踪类型 1表示动态 2表示轨迹
     */
    // @Excel(name = "跟踪类型 1表示动态 2表示轨迹")
    private String type;

    /**
     * 跟踪来源 1表示来源企业微信 2 表示智能物料
     */
    // @Excel(name = "跟踪来源 1表示来源企业微信 2 表示智能物料")
    private String source;

    @TableLogic
    private Integer delFlag;
}
