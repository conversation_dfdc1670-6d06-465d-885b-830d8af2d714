package com.cenker.scrm.pojo.entity.wechat;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbDcUserTagsOperrelMapping implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键。
     */
    @TableId(type = IdType.AUTO)
    private int id;

    /**
     * 运算式字典：1:等于(=) 2：大于(>)  3：小于(<)  4：大于等于(>=)   5：小于等于(<=)  6.包含(in)  7.不包含(not in) 8.模糊查询（like）
     */
    private String operRel;

    /**
     * 运算名称
     */
    private String operRelName;

    /**
     * 标签名称。
     */
    private String tagTypeId;


    /**
     * 记录的创建者。
     */
    private String createBy;
    /**
     * 排序
     */
    private int displayOrder;

    /**
     * 记录的创建时间。
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}