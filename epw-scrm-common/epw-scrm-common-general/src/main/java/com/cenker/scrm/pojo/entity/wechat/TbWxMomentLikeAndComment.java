package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.model.base.BaseEntity;
import com.cenker.scrm.pojo.entity.enums.EnumMomentCommentType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxMomentLikeAndComment {
    /**
     * 记录项ID。
     */
    @TableId
    private Long id;
    /**
     * 朋友圈id
     */
    private String momentId;

    /**
     * 外部客户Id
     */
    private String externalUserId;

    /**
     * 内部用户Id
     */
    private String userId;

    /**
     *评论或点赞
     */
    private EnumMomentCommentType type;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date interactTime;

    /**
     * 企业Id
     */
    private String corpId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    public TbWxMomentLikeAndComment(String momentId, String externalUserId, String userId, EnumMomentCommentType type, Date interactTime, String corpId) {
        this.momentId = momentId;
        this.externalUserId = externalUserId;
        this.userId = userId;
        this.type = type;
        this.interactTime = interactTime;
        this.corpId = corpId;
        this.createTime=new Date();
        this.updateTime=new Date();
    }
}
