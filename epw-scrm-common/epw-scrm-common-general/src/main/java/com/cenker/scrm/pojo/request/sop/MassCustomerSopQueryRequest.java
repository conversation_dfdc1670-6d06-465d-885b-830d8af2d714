package com.cenker.scrm.pojo.request.sop;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/8/4
 * @Description
 */
@Data
public class MassCustomerSopQueryRequest extends BaseSopRequest {
    /**
     * 执行状态 1 已完成 2 未完成 3 已过期
     */
    private Integer executeStatus;

    /**
     * 任务版本号
     */
    private String contentVersion;

    /**
     * 消息id
     */
    private Long msgId;

    /**
     * 客户名
     */
    private String customerName;

    /**
     * 员工id
     */
    private String userId;

    /**
     * 是否需要计算
     */
    private Boolean calculateDay = true;
}
