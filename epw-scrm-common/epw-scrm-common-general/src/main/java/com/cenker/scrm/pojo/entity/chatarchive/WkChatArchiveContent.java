package com.cenker.scrm.pojo.entity.chatarchive;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 会话记录详细内容对象 wk_chat_archive_content
 * 
 * <AUTHOR>
 * @date 2024-01-24
 */
@Deprecated
@Data
@TableName(value = "wk_chat_archive_content",autoResultMap = true)
public class WkChatArchiveContent implements Serializable {

    private static final long serialVersionUID = 3937214356976470988L;
    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 消息Id，消息的唯一标识 */
    private String msgId;

    /** 消息类型，text：文本，image：图片，类型较多不一一介绍 */
    private Integer msgType;

    /** 消息内容 */
    private String msgContent;

    /** 资源文件链接 */
    private String fileUrl;

    /** 撤回消息Id，消息的唯一标识 */
    private String preMsgId;

    /** 消息内容JSON */
    private String contentData;

    /** 企业ID */
    private String corpId;

    /** 消息的seq值，标识消息的序号 */
    private Long seq;

    /** 群聊类型，0表示未知，1表示单聊，2表示群聊 */
    private Integer chatType;
    /** 消息动作，目前有send(发送消息)/recall(撤回消息)/switch(切换企业日志)三种类型 */
    private String action;

    /** 发送方Id，同一企业内容为userid，非相同企业为external_userid。消息如果是机器人发出，也为external_userid */
    private String fromId;

    /** 群聊消息，则是群ID */
    private String roomId;

    /** 消息接受者ID */
    private String consumeId;

    /** 群聊消息接受者ID */
    private String roomConsumeId;

    /** 消息时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date msgTime;

    /** 消息时间-年月日，根据msg_time提取 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date msgDay;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
