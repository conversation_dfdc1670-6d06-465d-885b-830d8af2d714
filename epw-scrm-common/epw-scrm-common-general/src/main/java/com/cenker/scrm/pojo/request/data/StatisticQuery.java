package com.cenker.scrm.pojo.request.data;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/3/29
 * @Description 数据查询通用对象
 */
@Data
public class StatisticQuery {
    /**
     * 对应数据id
     */
    private String id;
    /**
     * 查询最近7天数据
     */
    private Boolean seven;
    /**
     * 查询最近30天数据
     */
    private Boolean thirty;
    /**
     * 查询开始时间
     */
    private String beginTime;
    /**
     * 查询结束时间
     */
    private String endTime;
    /**
     * 企业ID
     */
    private String corpId;
    /**
     * 查询数据类型（根据业务自定义）
     * 客户趋势：1 客户总数 2 新增客户数 3 流失客户数 4 活跃客户数
     * 社群趋势：1 群成员总数  2 入群客户数  3 退群客户数  4 群聊活跃客户数 5 活跃群聊数
     */
    private Integer type;
    /**
     * 首页数据类型 1 客户趋势 2 社群趋势
     */
    private Integer trendType;
}
