package com.cenker.scrm.pojo.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 菜单权限表 sys_menu
 *
 * <AUTHOR>
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class SysMenu extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableLogic
    private Integer delFlag;

    /**
     * 菜单ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String menuId;

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Size(min = 0, max = 50, message = "菜单名称长度不能超过50个字符")
    private String menuName;

    /**
     * 父菜单名称
     */
    private String parentName;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 显示顺序
     */
    @NotBlank(message = "显示顺序不能为空")
    private String orderNum;

    /**
     * 路由地址
     */
    @Size(min = 0, max = 200, message = "路由地址不能超过200个字符")
    private String path;

    /**
     * 组件路径
     */
    @Size(min = 0, max = 200, message = "组件路径不能超过255个字符")
    private String component;

    /**
     * 是否为外链（0是 1否）
     */
    private String isFrame;

    /**
     * 是否缓存（0缓存 1不缓存）
     */
    private String isCache;

    /**
     * 类型（M目录 C菜单 F按钮）
     */
    @NotBlank(message = "菜单类型不能为空")
    private String menuType;

    /**
     * 显示状态（0显示 1隐藏）
     */
    private String visible;

    /**
     * 菜单状态（0显示 1隐藏）
     */
    private String status;

    /**
     * 权限字符串
     */
    @Size(min = 0, max = 100, message = "权限标识长度不能超过100个字符")
    private String perms;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 子菜单
     */
    private List<SysMenu> children = new ArrayList<SysMenu>();

    /**
     * 案例链接
     */
    private String caseLink;
    /**
     * 使用手册链接
     */
    private String helpLink;
    /**
     * 简介
     */
    private String remark;
    /**
     * 简介说明标题
     */
    private String helpTitle;
    /**
     * 简介说明图标
     */
    private String helpIcon;
    /**
     * 角标
     */
    private String cornerMark;
    private String cornerMarkBackGround;
    private String cornerMarkColor;

    private String icon1;
    private String icon2;
    /**
     * 所属终端，01:PC，02：企微
     */
    private String belongTerminal;
    /**
     * 是否只返回权限属性的集合
     */
    private boolean onlyPermsProp;
    /**
     * 是否只返回正常的菜单；true,表示进行过滤；父级停用，子集也停用
     */
    private boolean filterStatus = false;
}
