package com.cenker.scrm.pojo.request;

import com.cenker.scrm.pojo.valid.DataGroup;
import com.cenker.scrm.pojo.valid.SelectGroup;
import com.cenker.scrm.pojo.valid.SingleGroup;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @Date 2022/11/2
 * @Description 拉新数据(排行榜)
 */
@Data
public class DeliveryUserContactDataRequest {

    /**
     * 配送员id
     */
    private Long deliveryUserId;

    /**
     * 配送员企微id
     */
    private String userId;

    /**
     * 配送员通讯录id
     */
    private String userPriId;

    /**
     * 查询榜单 1 今日榜 2 上周榜 3 上月榜
     * 查询范围 1 近30 2 近7  3 自定义
     */
    @NotNull(message = "查询榜单类型为空",groups = {SelectGroup.class, SingleGroup.class})
    @DecimalMax(value = "3",message = "非法请求")
    @DecimalMin(value = "1",message = "非法请求")
    private Integer rankTimeType;

    private Long areaId;
    /**
     * 查询站点id
     */
    @NotNull(message = "查询站点id为空",groups = SelectGroup.class)
    @DecimalMin(value = "0",message = "非法请求")
    private Long siteId;
    @Pattern(groups = DataGroup.class,message = "日期格式错误",regexp = "((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))")
    private String beginTime;
    @Pattern(groups = DataGroup.class,message = "日期格式错误",regexp = "((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))")
    private String startTime;
    @Pattern(groups = DataGroup.class,message = "日期格式错误",regexp = "((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))")
    private String endTime;

    /**
     * 清除缓存类型 1 城市拉新榜 2 配送员拉新榜（后台） 3 首页折线图
     */
    private Integer cleanType;

    private String deliveryUserMobile;
    private String deliveryUserName;
    private String storeId;

    /**
     * -1 删除 0 停用 1 正常
     */
    private Integer storeStatus;

    private String siteName;

    /**
     * 系统账号id
     */
    private String sysUserId;
}
