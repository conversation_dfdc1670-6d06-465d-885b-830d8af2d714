package com.cenker.scrm.pojo.entity.statistic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 数据统计-客户数据
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Data
@TableName("tb_statistic_customer")
public class TbStatisticCustomer {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", dateFormat = "yyyy-MM-dd", sort = 1)
    private Date statisticDate;

    /**
     * 客户编号(客户号)
     */
    private String custno;

    /**
     * 外部联系人的userid
     */
    @Excel(name = "客户ID", sort = 2)
    private String externalUserId;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称", sort = 3)
    private String customerName;

    /**
     * 客户头像
     */
    private String customerAvatar;

    /**
     * 性别 0-未知 1-男性 2-女性
     */
    private Integer customerGender;

    /**
     * 状态（0正常 1 禁用 2删除）
     */
    private String status;

    /**
     * 客户认证标记：0-未认证，1-新认证，2-已认证
     */
//    @Excel(name = "客户认证状态", readConverterExp = "0=未认证,1=已认证")
    private Integer isAuth;

    /**
     * 首次添加时间
     */
    private Date addTime;

    /**
     * 认证时间
     */
    private Date authTime;

    /**
     * 客户流失时间，即最后一次删除员工好友时间
     */
    private Date lossTime;

    /**
     * 1v1发消息数量
     */
    @Excel(name = "1v1发消息数量", sort = 5)
    private Integer aloneChatNum;

    /**
     * 群发消息数量
     */
    @Excel(name = "群聊发消息数", sort = 6)
    private Integer groupChatNum;

    /**
     * 客户所在群数
     */
    @Excel(name = "所在群数", sort = 7)
    private Integer customerGroupNum;

    /**
     * 物料接收次数
     */
    @Excel(name = "物料接收数", sort = 8)
    private Integer radarReceiveTimes;

    /**
     * 物料点击数
     */
    @Excel(name = "物料点击数", sort = 9)
    private Integer radarClickTimes;

    /**
     * 物料转发数
     */
    @Excel(name = "物料转发数", sort = 10)
    private Integer radarForwardTimes;

    /**
     * 平均浏览时长(S)
     */
    @Excel(name = "平均浏览时长(S)", sort = 11)
    private Integer averageReadTime;

    /**
     * 服务员工列表
     */
    @TableField(exist = false)
    private List<TbStatisticCustomerDetail> serverStaffList;

    /**
     * 服务员工名称集合，多个用逗号分隔
     */
    @Excel(name = "服务员工", sort = 4)
    @TableField(exist = false)
    private String serverStaffNames;
}
