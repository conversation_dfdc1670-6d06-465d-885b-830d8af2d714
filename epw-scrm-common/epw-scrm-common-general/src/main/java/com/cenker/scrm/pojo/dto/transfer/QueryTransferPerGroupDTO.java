package com.cenker.scrm.pojo.dto.transfer;

import lombok.Data;

/**
 * 查询每个客户群的转移情况
 */
@Data
public class QueryTransferPerGroupDTO implements java.io.Serializable{

    /**
     * 记录Id。
     */
    private String recordId;

    /**
     * 客户群名称
     */
    private String groupName;

    /**
     * 原所属员工
     */
    private String originalEmployee;

    /**
     *接替状态： 1-接替完毕 2-接替失败 3-继承过于频繁
     */
    private Integer takeoverStatus = null;
}
