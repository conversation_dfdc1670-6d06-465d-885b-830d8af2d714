package com.cenker.scrm.pojo.dto.knowledgebaseai;

import com.alibaba.fastjson.annotation.JSONField;
import com.cenker.scrm.constants.HttpStatus;
import lombok.Data;

import java.util.Map;

@Data
public class KnowledgeBaseAiDTO {
    private Map<String,String> keys;

    private String message;

    @J<PERSON>NField(name = "message_code")
    private String messageCode;

    public boolean isSuccess() {
        return this.messageCode.equals(String.valueOf(HttpStatus.SUCCESS));
    }

    public boolean isError() {
        return this.messageCode.equals(String.valueOf(HttpStatus.ERROR));
    }
}
