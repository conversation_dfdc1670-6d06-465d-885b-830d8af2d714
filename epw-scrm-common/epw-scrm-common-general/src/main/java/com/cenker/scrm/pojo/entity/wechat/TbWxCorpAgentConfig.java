package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 企业微信-应用相关配置对象 tb_wx_corp_agent_config
 *
 * <AUTHOR>
 * @date 2023-02-27
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCorpAgentConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 企业id（登录企业微信管理后台 —— 我的企业 —— 企业ID）
     */
    // @ApiModelProperty("企业id")
    // @Excel(name = "企业id", readConverterExp = "登=录企业微信管理后台,—=—,我=的企业,—=—,企=业ID")
    private String corpId;

    /**
     * 应用名称
     */
    // @ApiModelProperty("企业名称")
    // @Excel(name = "企业名称")
    private String agentName;

    /**
     * 应用类型，0默认自建应用，1表示通讯录应用，2表示客户联系，3表示微信客服，4表示企业红包
     */
    private Integer agentType;

    /**
     * 应用标识，对外提供
     */
    private String agentKey;

    /**
     * 应用Id
     */
    private Integer agentId;

    /**
     * 应用私钥（登录企业微信管理后台 —— 管理工具 —— 通讯录 —— 密钥）
     */
    // @ApiModelProperty("通讯私钥")
    // @Excel(name = "通讯私钥", readConverterExp = "登=录企业微信管理后台,—=—,管=理工具,—=—,通=讯录,—=—,密=钥")
    private String agentSecret;

    /**
     * 回调事件Token
     */
    private String agentCallBackToken;

    /**
     * 回调事件加密密钥
     */
    private String agentCallBackSecret;

    /**
     * 加密私钥
     */
    private String pkcsKey;

    /**
     * 状态（0正常 1停用)
     */
    // @ApiModelProperty("状态")
    // @Excel(name = "状态", readConverterExp = "状态（0正常 1停用)")
    private Integer status;

    @TableLogic
    private Integer delFlag;

}
