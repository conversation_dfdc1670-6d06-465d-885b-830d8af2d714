package com.cenker.scrm.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @Date 2023/9/5
 * @Description Cipher加解密工具
 */
public class CipherUtil {

    /**
     * 解密数据
     *
     * @param data 加密Base64字符串
     * @return 解密数据
     */
    public static String decryptData(String key, String data) throws Exception {
        SecretKeySpec keySpecs = new SecretKeySpec(key.getBytes(), "AES");
        Cipher ciphers = Cipher.getInstance("AES/ECB/PKCS5Padding");
        ciphers.init(Cipher.DECRYPT_MODE, keySpecs);
        return new String(ciphers.doFinal(java.util.Base64.getDecoder().decode(data)));
    }

    /**
     * 加密数据
     *
     * @param data 原文字符串
     * @return 加密数据
     */
    public static String encryptData(String key, String data) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        return new String(Base64.getEncoder().encode(cipher.doFinal(data.getBytes()))).trim();
    }


    public static void main(String[] args) throws Exception {
        String key = "C4572FDE09OKJ78B77Q0B3PL7624E23C";
        String s = encryptData(key, "ba0ce1e40520e7cfb6681a22043f8db7");
        System.out.println(s);

    }
}
