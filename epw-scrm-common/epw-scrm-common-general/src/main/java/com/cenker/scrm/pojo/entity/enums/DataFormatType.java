package com.cenker.scrm.pojo.entity.enums;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;

import java.util.Date;

/**
 * 数据格式类型
 */
public enum DataFormatType {

    DATE("DATE", "yyyyMMdd","yyyy-MM-dd","日期"),
    NUMBER("NUMBER", "", "0.00", "浮点数"),
    INTEGER("INTEGER", "", "0", "整数"),
    RATIO("RATIO", "", "0%", "比例"),

    TEXT("TEXT", "", "", "文本"),
    CURRENCY("CURRENCY", "", "#,###.##", "货币"),
    MULTI_SELECT("MULTI_SELECT", "", "", "多选");

    private String code;

    private String originalFormat;

    private String targetFormat;

    private String desc;

    DataFormatType(String code, String originalFormat, String targetFormat, String desc) {
        this.code = code;
        this.originalFormat = originalFormat;
        this.targetFormat = targetFormat;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getOriginalFormat() {
        return originalFormat;
    }

    public String getTargetFormat() {
        return targetFormat;
    }

    public static String format(String value, String type) {
        try {
            DataFormatType formatType = DataFormatType.valueOf(type.toUpperCase());
            switch (formatType) {
                case DATE:
                    Date date = DateUtil.parse(value, formatType.getOriginalFormat());
                    return DateUtil.format(date, formatType.getTargetFormat());
                case NUMBER:
                case INTEGER:
                case CURRENCY:
                case RATIO:
                    return NumberUtil.decimalFormat(formatType.getTargetFormat(), NumberUtil.toBigDecimal(value));
                default:
                    return value;
            }
        } catch (Exception e) {
            return value;
        }
    }
}
