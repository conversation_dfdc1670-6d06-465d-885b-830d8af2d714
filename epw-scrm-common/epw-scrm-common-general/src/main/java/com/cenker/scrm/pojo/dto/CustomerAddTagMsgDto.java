package com.cenker.scrm.pojo.dto;

import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.pojo.entity.wechat.TbWxAutoTagRecord;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 为客户添加企业标签的MQ消息体内容
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CustomerAddTagMsgDto extends AbstractCustomerTagMsgDto{

    private String corpId;

    /**
     * 外部联系人ID集合
     */
    private List<String> externalUserIds;
    /**
     * 是否来自回调，默认false
     * 为true时，表示来自回调，员工使用userId
     */
    private boolean fromCallback;

    /**
     * 标签集合(映射对象：TagVO）
     */
    private List<TagVO> tagList;

    /**
     * 标签来源类型
     */
    private String tagSource;

    /**
     * 是否重试，默认false
     * 非重试场景，如果失败，会插入一条重试任务
     */
    private boolean isRetry;

    /**
     * 操作id
     */
    private String operId;
    /**
     * 操作人
     */
    private String userId;
    private String nickName;
    /**
     * 如果是条件打标签，则存在该对象，需要将该对象入库
     */
    private TbWxAutoTagRecord autoTagRecord;
    /**
     * 企业运营动态：批量编辑客户标签，是否进行消息通知
     */
    private boolean isMsgNotify;

    private TrackEventTypeEnum eventTypeEnum;

    private String name;

    private Integer radarType;

    private String radarRuleNum;
    /**
     * 批量操作标签需要的参数
     */
    private OperTrackParams operTrackParams;
    // 实现父类的方法
    @Override
    public TrackEventTypeEnum getEventTypeEnum() {
        return this.eventTypeEnum;
    }
    @Override
    public Integer getRadarType() {
        return this.radarType;
    }
    @Override
    public String getRadarRuleNum() {
        return this.radarRuleNum;
    }
    @Override
    public OperTrackParams getOperTrackParams() {
        return this.operTrackParams;
    }
    @Override
    public String getUserId() {
        return this.userId;
    }
    @Override
    public String getCorpId() {
        return this.corpId;
    }
    @Override
    public String getOperId() {
        return this.operId;
    }
    @Override
    public String getName() {
        return this.name;
    }
}
