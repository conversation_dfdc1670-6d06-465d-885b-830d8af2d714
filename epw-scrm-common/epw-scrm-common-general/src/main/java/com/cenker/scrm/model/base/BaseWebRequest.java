package com.cenker.scrm.model.base;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/10
 * @Description 后台请求基类
 */
@Data
public class BaseWebRequest extends BaseRequest{

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 更新人id
     */
    private Long updateBy;

    /**
     * 自定义排序 与分页插件orderByColumn区分开 用于默认排序是两个字段的时候
     */
    private String orderColumn;

    /**
     * 排序的方向desc（降序）或者asc（升序） 默认传desc
     */
    private String sort;

    /**
     * 使用成员查询 统一当前执行员工组件查询
     */
    private List<String> corpUserId;
}
