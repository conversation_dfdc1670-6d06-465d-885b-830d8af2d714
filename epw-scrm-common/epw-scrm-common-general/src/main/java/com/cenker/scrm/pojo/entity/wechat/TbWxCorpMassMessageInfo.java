package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 群发消息-微信消息对象 tb_wx_corp_mass_message_info
 *
 * <AUTHOR>
 * @date 2021-02-03
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TbWxCorpMassMessageInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 群发任务的类型，默认为single，表示发送给客户，group表示发送给客户群
     */
    @Excel(name = "群发任务的类型，默认为single，表示发送给客户，group表示发送给客户群")
    private String chatType;

    /**
     * 消息发送状态 0 未发送  1 已发送
     */
    @Excel(name = "消息发送状态 0 未发送  1 已发送")
    private String checkStatus;

    /**
     * 1 标识删除 0 标识未删除
     */
    private Integer delFlag;

    /**
     * 发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date settingTime;

    /**
     * 预计发送消息数（客户对应多少人 客户群对应多个群）
     */
    @Excel(name = "预计发送消息数", readConverterExp = "客=户对应多少人,客=户群对应多个群")
    private Long expectSend;

    /**
     * 实际发送消息数（客户对应多少人 客户群对应多个群）
     */
    @Excel(name = "实际发送消息数", readConverterExp = "客=户对应多少人,客=户群对应多个群")
    private Long actualSend;

    /**
     * 是否定时任务 0 常规 1 定时发送
     */
    @Excel(name = "是否定时任务 0 常规 1 定时发送")
    private Integer timedTask;

    /**
     * 企业id
     */
    @Excel(name = "企业id")
    private String corpId;


    /**
     * 消息范围 0 全部客户  1 指定客户
     */
    private String pushRange;

    /**
     * 群发类型 1 企业群发  2 个人群发
     */
    private String massType;
    /**
     * 微信id--通过微信群发列表获取的，本地发送为空
     */
    private String msgId;

    private String targetSelectedStr;

    /**
     * 裂变id
     */
    private String taskFissionId;
}