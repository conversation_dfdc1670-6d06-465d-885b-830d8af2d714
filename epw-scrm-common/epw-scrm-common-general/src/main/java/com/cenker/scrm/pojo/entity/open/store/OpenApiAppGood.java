package com.cenker.scrm.pojo.entity.open.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("open_api_app_good")
public class OpenApiAppGood {

  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * 企业id
   */
  private Long corpId;
  /**
   * 接入凭证Id
   */
  private Long appInfoId;
  /**
   * 商城Id
   */
  private Long storeId;
  /**
   * 商品id
   */
  private String goodId;
  /**
   * 商品名称
   */
  private String goodName;
  /**
   * 商品分类名称
   */
  private String categoryName;
  /**
   * 商品分类id
   */
  private String categoryId;
  /**
   * 商品封面
   */
  private String cover;
  /**
   * 商品描述
   */
  private String goodDesc;
  /**
   * 商品当前价
   */
  private String goodPrice;
  /**
   * 商品原价
   */
  private String goodOriginPrice;
  /**
   * 商品库存
   */
  private String goodStock;
  /**
   * 商品详情页链接地址
   */
  private String detailUrl;
  /**
   * 商品排序
   */
  private Integer orderNum;
  /**
   * 商品状态 0：待上架；1：上架中；2：已售罄；3：已下架
   */
  private Integer goodStatus;
  /**
   * 商品过期时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date expireTime;
  /**
   * 备注
   */
  private String remark;
  @TableLogic
  private Integer delFlag;
  private Date createTime;
  private Date updateTime;
  private Long createBy;
  private Long updateBy;
}
