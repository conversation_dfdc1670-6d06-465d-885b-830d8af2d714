package com.cenker.scrm.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.HashUtil;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: wz
 * @Date: 2021/5/26 下午3:47
 * @Description: 雪花id工具类
 */
@Slf4j
@Configuration
public class SnowflakeIdUtil {

    private static long workerId = 1;

    private static long dataCenterId = 1;

    private static long max = (2 << 4) - 1;

    static {
        try {
          /*  Yaml yaml = new Yaml();
            String hostAddress = InetAddress.getLocalHost().getHostAddress();
            workerId = HashUtil.mixHash(hostAddress) & max;
            URL url = SnowflakeIdUtil.class.getClassLoader().getResource("application.yml");
            if (url != null) {
                //获取application.yaml文件中的配置数据，然后转换为obj，
                Map map = yaml.load(new FileInputStream(url.getFile()));
                Map spring = (Map) map.get("spring");
                Map profiles = (Map) spring.get("application");
                String applicationName = (String) profiles.get("name");
                if (ObjectUtil.isNotEmpty(applicationName)) {
                    dataCenterId = HashUtil.mixHash(applicationName) & max;
                }
            }*/
            dataCenterId = HashUtil.mixHash("epw-scrm") & max;
        } catch (Exception e) {
            log.error("SnowflakeIdUtil init error!");
        }
    }

    static Snowflake snowflake = IdUtil.createSnowflake(workerId, dataCenterId);

    public static long getSnowId() {
        return snowflake.nextId();
    }

    public static String getSnowIdStr() {
        return snowflake.nextId()+"";
    }

    public static String getSnowIdToString() {
        return String.valueOf(snowflake.nextId());
    }

    public static void main(String[] args) {
        System.out.println(getSnowId());
    }
}
