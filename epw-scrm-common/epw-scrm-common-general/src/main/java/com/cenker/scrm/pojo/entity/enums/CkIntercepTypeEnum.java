package com.cenker.scrm.pojo.entity.enums;

/**
 * @description:
 * @author:znlian
 * @time:2024/3/29
 */
public enum CkIntercepTypeEnum {

    //1.事前警告并拦截, 3.事后审计
    INTERCEP_TTYPE_BEFORE("1", "事前警告并拦截"),
    INTERCEP_TTYPE_AND("2", "警告且事后审计"),
    INTERCEP_TTYPE_AFTER("3", "事后审计");


    private final String value;
    private final String desc;

    CkIntercepTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getValueByDesc(String desc) {
        for (CkIntercepTypeEnum e : CkIntercepTypeEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e.getValue();
            }
        }
        //throw new IllegalArgumentException("Invalid key: " + key);
        return "";
    }
}
