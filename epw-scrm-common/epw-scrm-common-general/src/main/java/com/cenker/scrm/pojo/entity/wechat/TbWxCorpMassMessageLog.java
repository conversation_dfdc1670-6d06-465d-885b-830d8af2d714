package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 群发消息-微信消息发送日志对象 tb_wx_corp_mass_message_log
 *
 * <AUTHOR>
 * @date 2021-02-03
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCorpMassMessageLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * id
     */

    @TableId
    private Long id;

    /**
     * 微信消息表id
     */
    @Excel(name = "微信消息表id")
    private String messageId;

    /**
     * 外部联系人userid或者群id
     */
    @Excel(name = "发送人id")
    private String senderId;

    /**
     * 发送状态 0-未发送 1-已发送 2-因客户不是好友导致发送失败 3-因客户已经收到其他群发消息导致发送失败
     */
    @Excel(name = "发送状态 0-未发送 1-已发送 2-因客户不是好友导致发送失败 3-因客户已经收到其他群发消息导致发送失败")
    private String status;

    /**
     * sendTime
     */
    @Excel(name = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 0 发给客户 1 发给客户群 2 定时发送
     */
    @Excel(name = "0 发给客户 1 发给客户群 2 定时发送")
    private String sendType;

    /**
     * delFlag
     */
    private Integer delFlag;

    private Long messageInfoId;

    private String corpId;

    private String userId;
}