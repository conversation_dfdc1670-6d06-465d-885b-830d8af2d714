package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业微信标签对象 tb_wx_corp_tag
 *
 * <AUTHOR>
 * @date 2021-01-26
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
// @ApiModel("企业微信标签")
public class TbWxCorpTag implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 创建者
     */
    // @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
    // @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    // @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    // @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    // @ApiModelProperty("备注")
    private String remark;
    /**
     * 标签组id
     */
    // @ApiModelProperty("标签组id")
    // @Excel(name = "标签组id")
    private String groupId;

    /**
     * 标签名
     */
    // @ApiModelProperty("标签名")
    // @Excel(name = "标签名")
    private String name;

    /**
     * 状态（0正常 1 禁用 2删除）
     */
    // @ApiModelProperty("状态（0正常 1 禁用 2删除）")
    // @Excel(name = "状态", readConverterExp = "0=正常,1=,禁=用,2=删除")
    private String status;

    /**
     * 微信端返回的id
     */
    @TableId
    // @ApiModelProperty("微信端返回的id")
    private String tagId;

    /**
     * 顺序
     */
    // @ApiModelProperty("顺序")
    // @Excel(name = "顺序")
    @TableField(value = "`order`")
    private Integer order;

    /**
     * 企业id
     */
    // @ApiModelProperty("企业id")
    // @Excel(name = "企业id")
    private String corpId;


}