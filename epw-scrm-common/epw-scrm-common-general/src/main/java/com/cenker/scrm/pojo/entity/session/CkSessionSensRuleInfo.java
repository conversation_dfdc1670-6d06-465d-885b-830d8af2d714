package com.cenker.scrm.pojo.entity.session;

import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

/**
 * 敏感规则信息对象 ck_session_sens_rule_info
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class CkSessionSensRuleInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则ID */
    private Long ruleId;

    /** 规则名称 */
    private String ruleName;

    /** 拦截方式 字典：1.警告并拦截 2.警告且事后审计 3.仅事后审计 */
    private String interceptType;

    /** 敏感行为多选，逗号分隔,字典：1.发送邮箱地址 2.发送手机号 3.发送和接收红包 */
    private String actTypes;

    /** 敏感词，多个逗号分开 */
    private String sensitiveWords;

    /** 敏感词个数 */
    private Long sensitiveWordNum;

    /** 状态 */
    private String status;

    private String nickName;

    private String wxRuleId;
    private String wxRuleSyncFlag;

    private Long notEpRuleId;

}
