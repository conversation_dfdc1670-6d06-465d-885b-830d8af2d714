package com.cenker.scrm.util;

import lombok.extern.slf4j.Slf4j;
import org.quartz.CronExpression;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * cron表达式工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class CronUtils {
    /**
     * 每年时间format格式
     */
    public static final String DATEFORMAT_YEAR = "ss mm HH dd MM ? *";

    /**
     * 每月时间format格式
     */
    public static final String DATEFORMAT_MONTH = "ss mm HH dd * ?";

    /**
     * 每天时间format格式
     */
    public static final String DATE_FORMAT_EVERYDAY = "ss mm HH * * ?";

    /**
     * 指定时间format格式
     */
    public static final String DATE_FORMAT_ASSIGN_DAY = "ss mm HH dd MM ? yyyy";

    /**
     * 每周时间format格式
     */
    public static final String DATE_FORMAT_WEEK = "ss mm HH ? * %s";

    /**
     * 工作日format格式
     */
    public static final String DATE_FORMAT_WORK = "ss mm HH ? * %%";

    private static final String SUNDAY = "ss mm HH ? * 1";
    private static final String MONDAY = "ss mm HH ? * 2";
    private static final String TUESDAY = "ss mm HH ? * 3";
    private static final String WEDNESDAY = "ss mm HH ? * 4";
    private static final String THURSDAY = "ss mm HH ? * 5";
    private static final String FRIDAY = "ss mm HH ? * 6";
    private static final String SATURADY = "ss mm HH ? * 7";


    /**
     * 返回一个布尔值代表一个给定的Cron表达式的有效性
     *
     * @param cronExpression Cron表达式
     * @return boolean 表达式是否有效
     */
    public static boolean isValid(String cronExpression) {
        return CronExpression.isValidExpression(cronExpression);
    }

    /**
     * 返回一个字符串值,表示该消息无效Cron表达式给出有效性
     *
     * @param cronExpression Cron表达式
     * @return String 无效时返回表达式错误描述,如果有效返回null
     */
    public static String getInvalidMessage(String cronExpression) {
        try {
            new CronExpression(cronExpression);
            return null;
        } catch (ParseException pe) {
            log.error("返回有效cron方法有误,Cron表达式:{}",cronExpression);
            log.error("错误信息：{}",pe);
            return pe.getMessage();
        }
    }

    /**
     * 返回下一个执行时间根据给定的Cron表达式
     *
     * @param cronExpression Cron表达式
     * @return Date 下次Cron表达式执行时间
     */
    public static Date getNextExecution(String cronExpression) {
        try {
            CronExpression cron = new CronExpression(cronExpression);
            return cron.getNextValidTimeAfter(new Date(System.currentTimeMillis()));
        } catch (ParseException e) {
            log.error("返回下一个执行时间根据给定的Cron表达式方法有误,Cron表达式:{}",cronExpression);
            log.error("错误信息:{}",e);
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    private static String formatDateByPattern(Date date, String dateFormat) {
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        String formatTimeStr = null;
        if (date != null) {
            formatTimeStr = sdf.format(date);
        }
        return formatTimeStr;
    }

    /**
     * 时间转换时间表达式
     */
    public static String getCron(Date date, String dateFormat) {
        return formatDateByPattern(date, dateFormat);
    }
}
