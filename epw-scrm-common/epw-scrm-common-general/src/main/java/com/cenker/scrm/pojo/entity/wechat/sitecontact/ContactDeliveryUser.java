package com.cenker.scrm.pojo.entity.wechat.sitecontact;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("contact_delivery_user")
public class ContactDeliveryUser {
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  private Long storeId;
  private Long userPriId;
  private String userId;
  private Integer userStatus;

  /**
   * 唯一标识
   */
  private String signId;
  @TableLogic
  private Integer delFlag;
  /**
   * 企业ID
   */
  private Long corpId;
  private Long createBy;
  @TableField(fill = FieldFill.INSERT)
  @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  private Long updateBy;
  private Date updateTime;
  private String remark;

}
