package com.cenker.scrm.pojo.entity.knowledgebaseai;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName(value = "ck_ai_chat_record",autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
public class CkAiChatRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Integer sourceType;
    private Long appId;
    private Long repositoryId;
    @JSONField(name ="question")
    private String recordQuestion;
    @JSONField(name ="response")
    private String recordResponse;
    @JSONField(name ="context")
    private String recordContext;
    @JSONField(name ="sources")
    private String recordSources;
    @TableField(value = "is_hit")
    private String hit;
    @TableField(value = "is_learn")
    private String learn;
    private String remark;
    /**
     * 逻辑删除 0 表示未删除，1 表示删除
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
    @TableField(exist = false)
    private String appName;
}
