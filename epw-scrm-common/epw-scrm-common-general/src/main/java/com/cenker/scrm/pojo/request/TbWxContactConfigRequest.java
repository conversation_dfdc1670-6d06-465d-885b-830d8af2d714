package com.cenker.scrm.pojo.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/10/27
 * @Description
 */
@Data
public class TbWxContactConfigRequest {
    @NotNull(message = "参数错误")
    private Long id;
    /**
     * 所属活码id
     */
    private Long contactId;
    private Long refreshRate;
    private Long deliveryUserId;
    @NotBlank(message = "请选择上传背景图")
    private String backImg;
    @NotNull(message = "请选择设置")
    private Boolean viewTop;
    private String remark;
    private Long createBy;
    private Date createTime;
    private Long updateBy;
    private Date updateTime;
}
