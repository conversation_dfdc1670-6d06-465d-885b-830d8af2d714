package com.cenker.scrm.handler.oper;

import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.bu.RelatedResource;
import com.cenker.scrm.util.BuOperTrackUtils;
import com.cenker.scrm.util.DateUtils;

import java.util.List;
/**
 * 智能物料互动事件处理器
 */
public class SmartMaterialActionHandler extends DefaultBuOperTrackHandler {
    public SmartMaterialActionHandler(TrackEventTypeEnum trackEventTypeEnum) {
        this.eventType = trackEventTypeEnum;
    }

    @Override
    protected String getContent(OperTrackParams operTrackParams) {
        RelatedResource relatedResource = operTrackParams.getRelatedResource();
        List<String> addTagList = relatedResource.getAddTagList();
        if (addTagList == null || addTagList.isEmpty()) {
            addTagList = relatedResource.getRemoveTagList();
        }
        RelatedResource.LinkVo linkVo = relatedResource.getLinkVo();
        String name = operTrackParams.getName();

        String contentStr = eventType.getContent();
        switch (eventType) {

            case SMART_MATERIAL_ACTION_VIEW:
                contentStr = contentStr.replace("{0}", linkVo.getClickNum()+"");
                contentStr = contentStr.replace("{1}", DateUtils.getSecondToStr(linkVo.getReadNum()));
                contentStr = contentStr.replace("{2}", linkVo.getReadPer());
                break;
            case SMART_MATERIAL_ACTION_SHARE:
//                contentStr = contentStr.replace("{0}", value);
                break;
            case SMART_MATERIAL_AUTO_TAG:
                /**
                 *      * 1、打开物料【**】次数达到【*】次，自动添加【**】标签
                 *      * 2、浏览物料【**】时长超过【*】秒，自动添加【**】标签
                 *      * 3、转发物料【**】次数超过【*】次，自动添加【**】标签"
                 */
                if (TypeConstants.RADAR_ADD_TAG_RULE_TYPE_READ == relatedResource.getRadarType()) {
                    contentStr = "浏览物料【"+name+"】时长超过【"+relatedResource.getRadarRuleNum()+"】秒，自动添加标签"+BuOperTrackUtils.buildHtml(addTagList, "tag",null);
                } else if (TypeConstants.RADAR_ADD_TAG_RULE_TYPE_CLICK == relatedResource.getRadarType()) {
                    contentStr = "打开物料【"+name+"】次数达到【"+relatedResource.getRadarRuleNum()+"】次，自动添加标签"+BuOperTrackUtils.buildHtml(addTagList, "tag",null);
                } else {
                    contentStr = "转发物料【"+name+"】次数超过【"+relatedResource.getRadarRuleNum()+"】次，自动添加标签"+BuOperTrackUtils.buildHtml(addTagList, "tag",null);
                }
                break;
            default:
                break;
        }
        return contentStr;
    }
}
