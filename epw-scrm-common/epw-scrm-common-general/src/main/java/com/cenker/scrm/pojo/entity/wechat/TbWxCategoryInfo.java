package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCategoryInfo {
  private static final long serialVersionUID = 8829524353129501200L;
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * 4 话术
   */
  private Long mediaType;
  /**
   * 分组名
   */
  private String name;
  /**
   * 父级分组id 一级分组 0
   */
  private Long parentId;
  /**
   * 创建人
   */
  private Long createBy;
  /**
   * 创建时间
   */
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;
  private Long updateBy;
  private Date updateTime;
  private Boolean delFlag;
  /**
   * 企业配置id
   */
  private Long corpConfigId;

  /**
   * 排序号
   */
  private Integer orderNum;

  /**
   * 子分类
   */
  @TableField(exist = false)
  private List<TbWxCategoryInfo> children = new ArrayList<>();

  /**
   * 组排序
   */
  @TableField(exist = false)
  private List<TbWxCategoryInfo> orderList;
}
