package com.cenker.scrm.pojo.dto.transfer;

import com.cenker.scrm.model.base.BaseRequest;
import com.cenker.scrm.pojo.entity.enums.EnumTransferType;
import lombok.Data;
import java.io.Serializable;

/**
 * 用于查询客户转移记录的数据传输对象
 */
@Data
public class QueryTransferRecordsDTO  extends BaseRequest {
    /**
     * 查询开始时间
     */
    private String beginTime;

    /**
     * 查询结束时间
     */
    private String endTime;

    /**
     * 移交人ID
     */
    private String handoverUserId;

    /**
     * 接手人ID
     */
    private String takeoverUserId;

    /**
     * 移交人姓名
     */
    private String handoverUserName;

    /**
     * 接手人姓名
     */
    private String takeoverUserName;

    /**
     * 移交类型
     */
    private EnumTransferType transferType = EnumTransferType.ON_JOB;

    public void setTransferType(EnumTransferType transferType) {
        if(transferType==null)
            transferType = EnumTransferType.ON_JOB;
        this.transferType = transferType;
    }
}

