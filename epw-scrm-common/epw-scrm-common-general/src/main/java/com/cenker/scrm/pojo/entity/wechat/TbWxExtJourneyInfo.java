package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TbWxExtJourneyInfo {
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * 旅程名
   */
  private String journeyName;
  /**
   * 创建来源
   */
  private Integer since;
  /**
   * 排序
   */
  private Integer orderNum;

  /**
   * 是否自动化 0 否 1是
   */
  @TableField(value = "is_automation")
  private Boolean automation;

  /**
   * 备注
   */
  private String remark;
  /**
   * 企业id
   */
  private Long corpId;
  @TableLogic
  private Integer delFlag;
  private Long createBy;
  private Long updateBy;


  /**
   * 创建时间
   */
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @TableField(value = "create_time", fill = FieldFill.INSERT)
  private Date createTime;

  /**
   * 更新时间
   */
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
  private Date updateTime;
}
