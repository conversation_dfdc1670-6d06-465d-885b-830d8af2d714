package com.cenker.scrm.handler.oper;

import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
/**
 * 客户添加
 */
public class CustomerAddHandler extends DefaultBuOperTrackHandler {
    public CustomerAddHandler(TrackEventTypeEnum trackEventTypeEnum) {
        this.eventType = trackEventTypeEnum;
    }

    @Override
    protected String getContent(OperTrackParams operTrackParams) {
        String contentStr = eventType.getContent();
        contentStr = contentStr.replace("{0}", operTrackParams.getName());
        return contentStr;
    }
}
