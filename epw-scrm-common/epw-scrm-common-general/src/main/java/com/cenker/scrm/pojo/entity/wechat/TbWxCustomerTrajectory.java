package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.pojo.vo.external.CustomerTrajectoryContentVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Time;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCustomerTrajectory {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    // 轨迹类型(1:信息动态;2:社交动态;3:活动规则;4:待办动态)
    private Integer trajectoryType;
    // 外部联系人id
    private String externalUserId;
    // 文案内容
    private String content;
    // 处理日期
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createDate;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;
    // 处理开始时间
    private Time startTime;
    // 处理结束时间
    private Time endTime;
    // 0:正常;1:完成;2:删除
    @TableField("`status`")
    private String status;
    // 当前员工的id
    private String userId;
    // 当前应用的id
    private String agentId;
    private String corpId;

    // 员工头像
    @TableField(exist = false)
    private String userHeadImg;
    // 员工名
    @TableField(exist = false)
    private String userName;

    /**
     * 动态类型 1、更新信息 2、更新标签 3、浏览转发 4、更新好友关系 5、跟进 6、社群关系 7、朋友圈
     */
    private Integer behaviorType;

    // 前端标识符
    @TableField(exist = false)
    private boolean show = false;
    @TableField(exist = false)
    private boolean showPopover = false;

    // 行为json对象
    @TableField(exist = false)
    private CustomerTrajectoryContentVO customerTrajectoryContentVo;

    // 跟进是否能删除
    @TableField(exist = false)
    private boolean deleted;

    /**
     * 1 员工 2 客户
     */
    @TableField(exist = false)
    private Integer actionType;

    /**
     * 1 微信 2 企业微信
     */
    @TableField(exist = false)
    private Integer type;

    @TableField(exist = false)
    private String externalUserName;
    @TableField(exist = false)
    private String externalUserAvatar;
    /**
     * 1 微信 2 企微
     */
    @TableField(exist = false)
    private Integer externalUserType;
    @TableField(exist = false)
    private String externalUserCorpName;
}
