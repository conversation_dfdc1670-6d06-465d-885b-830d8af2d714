package com.cenker.scrm.pojo.entity.statistic;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户企微业务标签表
 * @TableName tb_wx_business_tag
 */
@TableName(value ="tb_wx_business_tag")
@Data
public class TbWxBusinessTag implements Serializable {
    /**
     * 外部联系人的userid
     */
    @TableId
    private String externalUserId;

    /**
     * 统计日期
     */
    private Date statisticDate;

    /**
     * 加入企微后发送消息数
     */
    private Integer messageSentCnt;

    /**
     * 近7天发送企微消息数
     */
    private Integer l7dMessageSentCnt;

    /**
     * 近30天发送企微消息数
     */
    private Integer l30dMessageSentCnt;

    /**
     * 近7天企微活跃天数
     */
    private Integer l7dActiveDays;

    /**
     * 近30天企微活跃天数
     */
    private Integer l30dActiveDays;

    /**
     * 近7天物料阅读次数
     */
    private Integer l7dMaterialReadCnt;

    /**
     * 近30天物料阅读次数
     */
    private Integer l30dMaterialReadCnt;


    /**
     * 是否内部员工，0-客户，1-内部员工
     */
    private String internalEmployee;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}