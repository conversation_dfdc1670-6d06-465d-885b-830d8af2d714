package com.cenker.scrm.model.login;

import lombok.Data;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.List;

@Data
public abstract class BaseLoginInfo implements UserDetails {

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 登录类型
     */
    private String loginType;

    /**
     * 企业微信用户id（员工企微账号）
     */
    private String wxUserId;

    /**
     * 主部门ID
     */
    private Integer deptId;

    /**
     * 主部门名称
     */
    private String deptName;

    /**
     * 用户唯一标识
     * 注意：这个不是实际的token，而是用户的唯一标识，用于区分不同的用户，因为之前已经用了这个名字，所以暂时不改
     */
    private String token;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 角色ID列表
     */
    private List<Long> roleIds;

    /**
     * 角色权限字符列表
     */
    private List<String> roleKeys;

    /**
     * 菜单权限列表
     */
    private List<String> menuPermissions;

    /**
     * 数据权限类型（1全部数据权限 2自定义数据权限 3本部门数据权限 4本部门及以下数据权限 5仅本人数据权限）
     * 当用户拥有全部数据权限或仅本人数据权限时，该字段有值，其他情况该字段为空
     */
    private String dataScope;

    /**
     * 数据权限列表（部门ID集合）
     * 仅当数据权限类型为自定义数据权限、本部门数据权限、本部门及以下数据权限时，该字段有值，其他情况该字段为空
     */
    private List<Integer> permissionDeptIds;

    /**
     * 标签分组权限范围（ALL全部分组、CUSTOM指定分组、NONE无分组）
     */
    private String tagCategoryScope;

    /**
     * 标签分组ID列表
     */
    private List<Long> permissionTagCategoryIds;

}
