package com.cenker.scrm.model.login;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/11/2
 * @Description h5 登录对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MobileUser implements Serializable {
    private static final long serialVersionUID = -5249580756957201601L;
    /**
     * 用户所属企业的corpid
     */
    //@SerializedName("CorpId")
    private String corpId;

    /**
     * 用户在企业内的UserID，如果该企业与第三方应用有授权关系时，返回明文UserId，否则返回密文UserId
     */
    //@SerializedName("UserId")
    private String userId;

    /**
     * 手机设备号(由企业微信在安装时随机生成，删除重装会改变，升级不受影响)
     */
    //@SerializedName("DeviceId")
    private String deviceId;

    /**
     * 成员票据，最大为512字节。
     * scope为snsapi_userinfo或snsapi_privateinfo，且用户在应用可见范围之内时返回此参数。
     * 后续利用该参数可以获取用户信息或敏感信息，参见:https://work.weixin.qq.com/api/doc/90001/90143/91122
     */
    //@SerializedName("user_ticket")
    private String userTicket;

    /**
     * user_ticket的有效时间（秒），随user_ticket一起返回
     */
    //@SerializedName("expires_in")
    private String expiresIn;

    /**
     * 全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节。仅第三方应用可获取
     */
    //@SerializedName("open_userid")
    private String openUserId;

    /**
     * 第三方应用在授权企业中的应用id
     */
    //@SerializedName("agent_id")
    private String agentId;

    /**
     * 是否企业管理员
     */
    private Boolean administrator;

    /**
     * 用户所属企业的密文corpid
     */
    private String openCorpId;

    /**
     * 后台账号id
     */
    private String sysUserId;

    /**
     * 区分 1 工作台 2 客户画像 3 素材
     */
    private Integer loginType;

    /**
     * 企业配置id
     */
    private Long corpConfigId;

    /**
     * 配送员id
     */
    private Long deliveryUserId;
}
