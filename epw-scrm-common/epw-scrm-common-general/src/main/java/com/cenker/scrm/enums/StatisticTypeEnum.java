package com.cenker.scrm.enums;

import lombok.Getter;
/**
 * 统计类型枚举类
 * <AUTHOR>
 */
@Getter
public enum StatisticTypeEnum {
    // 员工 客户 客户群 物料统计 热词统计 敏感词统计 敏感行为 回复超时
    STAFF("staff", "员工数据"),
    CUSTOMER("customer", "客户数据"),
    CUSTOMER_GROUP("customerGroup", "客户群数据"),
    RADAR("radar", "物料统计"),
    HOT_WORD("hotWord", "热词统计"),
    SENS_WORD("sensWord", "敏感词统计"),
    SENS_ACT("sensAct", "敏感行为"),
    REPLY_TIMEOUT("replyTimeout", "回复超时"),
    CHAT_CONVERSATION("chatConversation", "聊天会话数据"),
    CHAT_SENS("sensAlarm", "敏感规则功能统计敏感词及行为"),
    CHAT_HOT_WORD("HotWord", "热词触发记录")
    ;

    private String code;
    private String name;

    StatisticTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
