package com.cenker.scrm.pojo.exception;

/**
 * <AUTHOR>
 * @Date 2022/3/28
 * @Description 无客户联系权限
 */
public class WxUserNotExtCustomerPermissionException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private Integer code;

    private String message;

    public WxUserNotExtCustomerPermissionException(String message) {
        this.message = message;
    }

    public WxUserNotExtCustomerPermissionException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public WxUserNotExtCustomerPermissionException(String message, Throwable e) {
        super(message, e);
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }
}
