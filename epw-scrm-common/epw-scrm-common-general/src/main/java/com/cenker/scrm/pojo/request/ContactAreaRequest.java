package com.cenker.scrm.pojo.request;

import com.cenker.scrm.pojo.valid.InsertGroup;
import com.cenker.scrm.pojo.valid.UpdateGroup;
import com.cenker.scrm.pojo.valid.UpdateStoreGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/10/20
 * @Description 查询城市区域
 */
@Data
public class ContactAreaRequest {

    private String id;

    /**
     * 所在地区id
     */
    private Long areaId;

    /**
     * 站点id
     */
    @NotNull(message = "站点不能为空",groups = {InsertGroup.class, UpdateStoreGroup.class})
    private Long siteId;
    /**
     * 站点名
     */
    private String siteName;

    /**
     * 省份名/城市/地区名
     */
    private String areaName;
    /**
     * 地区父节点
     */
    private Long parentId;
    /**
     * 启用状态 0 未启用 1 启用
     */
    @NotNull(message = "请选择启用状态",groups = {InsertGroup.class, UpdateGroup.class, UpdateStoreGroup.class})
    @DecimalMax(value = "1",message = "非法请求")
    @DecimalMin(value = "0",message = "非法请求")
    private Integer areaStatus;
    /**
     * 是否加入城市列表
     */
    private Boolean selectStatus;
    /**
     * 排序号
     */
    private Integer orderNum;
    @Size(max = 200,message = "字数受限")
    private String remark;
    private Integer delFlag;
    /**
     * 企业ID
     */
    private Long corpId;
    private Long createBy;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private Long updateBy;
    private Date updateTime;
    private Long corpConfigId = 1L;

    /**
     * 门店名
     */
    @Size(max = 10,message = "字数受限")
    @NotBlank(message = "门店名不能为空",groups = {InsertGroup.class, UpdateStoreGroup.class})
    private String storeName;

    @NotNull(message = "请选择指定门店",groups = {UpdateStoreGroup.class})
    private Long storeId;

    /**
     * 是否暂时默认区域 默认false
     */
    private Boolean showDefault = false;
}
