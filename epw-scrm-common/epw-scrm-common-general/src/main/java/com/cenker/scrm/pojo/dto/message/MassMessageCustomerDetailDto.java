package com.cenker.scrm.pojo.dto.message;

import com.cenker.scrm.model.base.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 群发消息送达客户详情查询接口参数对象
 * <AUTHOR>
 */
@Data
public class MassMessageCustomerDetailDto extends BaseRequest {

    /**
     * 群发任务ID
     */
    @NotNull
    private Long id;

    /**
     * 发送状态 0-未发送 1-已送达 2-送达失败
     */
    private String type;

    /**
     * 发送者名称
     */
    private String name;
    
}
