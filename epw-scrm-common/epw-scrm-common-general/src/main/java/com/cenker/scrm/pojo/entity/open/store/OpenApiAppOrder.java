package com.cenker.scrm.pojo.entity.open.store;

import com.baomidou.mybatisplus.annotation.*;
import com.cenker.scrm.handler.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("open_api_app_order")
public class OpenApiAppOrder {
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * 企业id
   */
  private Long corpId;
  /**
   * 接入凭证Id
   */
  private Long appInfoId;
  /**
   * 商城Id
   */
  private Long storeId;
  /**
   * 微信开放平台唯一标识
   */
  private String unionId;
  /**
   * 用户id
   */
  private String userId;
  /**
   * 商品id
   */
  private String goodId;
  /**
   * 订单id
   */
  private String orderId;
  /**
   * 购买数量
   */
  private Integer goodNum;
  /**
   * 商户订单号
   */
  private String outTradeNo;
  /**
   * 交易id
   */
  private String transactionId;
  /**
   * 订单状态 0：待支付；1：已支付；2：已发货；3：已完成；4：已取消；5：售后中；6：已关闭；7：已退款
   */
  private Integer orderStatus;
  /**
   * 订单金额
   */
  private String orderPrice;
  /**
   * 订单时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date orderTime;
  /**
   * 订单备注
   */
  private String orderRemark;
  /**
   * 备注
   */
  private String remark;
  @TableLogic
  private Integer delFlag;
  private Date createTime;
  private Date updateTime;
  private Long createBy;
  private Long updateBy;
  /**
   * 商品
   */
  @TableField(typeHandler = JacksonTypeHandler.class)
  private List<AppOrderGoods> goods;
}
