package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 智能物料表
 */
@TableName("tb_wx_radar_interact")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TbWxRadarInteract {

  @TableId(type = IdType.ASSIGN_ID)
  private String id;
  /**
   * 企业id
   */
  private String corpId;
  /**
   * 智能物料分类 1 企业雷达 2 个人雷达
   */
  private Integer scope;
  /**
   * 智能物料类型 1 图文 2 链接 3 PDF
   */
  private Integer type;
  /**
   * 智能物料标题
   */
  private String title;
  /**
   * 匹配方式 1 昵称匹配 2 精准匹配
   */
  private Integer matchType;
  /**
   * 成员名片 0 未开启 1 已开启
   */
  private Integer contactStatus;
  /**
   * 行为通知 0 未开启 1 已开启
   */
  private Integer behaviorInform;
  /**
   * 动态通知 0 未开启 1 已开启
   */
  private Integer dynamicInform;
  /**
   * 客户标签 0 未开启 1 已开启
   */
  private Integer customerTag;


  private String remark;
  private String delFlag;
  
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date updateTime;
  private String createBy;
  private String updateBy;

  /**
   * 2022-05-31 新增渠道活码创建来源标识 1 web  2 侧边栏 3 工作台
   */
  private Integer since;

  /**
   * 分组ID
   */
  private String categoryId;

  /**
   * 标题命名规则
   */
  private String namingRule;

  /**
   * 是否展示在侧边栏 0 否 1 是
   */
  private Integer showStatus;

  /**
   * 部门ID
   */
  private Integer deptId;

  /**
   * 查看权限 onlyWeComFri:仅企微好友; onlyWeComAuthFri:仅企微认证好友; none:不限制
   */
  private String viewPerm;
}
