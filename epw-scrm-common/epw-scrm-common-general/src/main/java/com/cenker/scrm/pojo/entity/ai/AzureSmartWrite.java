package com.cenker.scrm.pojo.entity.ai;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description
 * <AUTHOR>
 * @date 2023/6/8 11:46
 */
@Accessors(chain = true)
@TableName(value = "azure_smart_write",autoResultMap = true)
@Data
public class AzureSmartWrite {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 0 标识未删除 1 标识删除
     */
    @TableLogic
    private Integer delFlag;
    /**
     * 企业Id
     */
    private Long corpConfigId;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 1群发客户 2群发社群 3群发朋友圈
     */
    private Integer type;
    /**
     * 发送总条数
     */
    private Integer countAll;
    /**
     * 成功总条数
     */
    private Integer countSuccess;
    /**
     * 完成总时间
     */
    private Integer completeTime;

}

