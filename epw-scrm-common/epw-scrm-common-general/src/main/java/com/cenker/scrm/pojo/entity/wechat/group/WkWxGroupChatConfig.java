package com.cenker.scrm.pojo.entity.wechat.group;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseDbEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.io.Serializable;

/**
 * @Description 客户群「加入群聊」群配置表
 * <AUTHOR> @Date 2023-08-23 
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "fieldHandler"}, ignoreUnknown = true)
@TableName("wk_wx_group_chat_config")
public class WkWxGroupChatConfig extends BaseDbEntity implements Serializable {
	private static final long serialVersionUID =  8741677116875281273L;
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 「加入群聊」唯一标识
	 */
	private Long groupChatId;

	/**
	 * 新增联系方式的配置id
	 */
	private String configId;

	/**
	 * 企微群聊id
	 */
	private String chatId;
}

