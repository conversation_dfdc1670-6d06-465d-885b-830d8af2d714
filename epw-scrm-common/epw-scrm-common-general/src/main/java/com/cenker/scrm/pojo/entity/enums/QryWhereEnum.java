package com.cenker.scrm.pojo.entity.enums;

public enum QryWhereEnum {
    QRY_USER_TAG("USER_TAG", "客户标签"),
    QRY_JOURNERY("JOURNERY", "客户旅程"),
    QRY_USER_ID("USER_ID", "添加成员"),
    QRY_ADD_DATE("ADD_DATE", "添加时间"),
    QRY_BUSINESS_DATA("BUSINESS_DATA", "业务标签"),
    QRY_SUBSCR_SECTION("SUBSCR_SECTION", "订阅栏目"),
    QRY_AUTH_STATUS("AUTH_STATUS", "认证状态"),
    QRY_USER_DEFINED("USER_DEFINED", "自定义"),

    CONDITION_KEY_ADD_DATE("SEL_ADD_DATE", "添加时间"),
    CONDITION_KEY_FIRST_ADD_DATE("SEL_FIRST_ADD_DATE", "添加时间");


    private final String value;
    private final String desc;

    QryWhereEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getValueByDesc(String desc) {
        for (QryWhereEnum e : QryWhereEnum.values()) {
            if (e.getDesc().equals(desc)) {
                return e.getValue();
            }
        }
        return "";
    }

}
