package com.cenker.scrm.pojo.entity.open.store;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("open_api_app_category")
public class OpenApiAppCategory {
  @TableId(type = IdType.ASSIGN_ID)
  private Integer id;
  private Long corpId;
  private Long appInfoId;
  private Long storeId;
  private Integer parentId;
  private String categoryName;
  private String remark;
  @TableLogic
  private Integer delFlag;
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;
  private Date updateTime;
  private Long createBy;
  private Long updateBy;
}
