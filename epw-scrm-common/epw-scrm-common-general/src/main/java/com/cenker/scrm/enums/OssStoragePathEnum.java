package com.cenker.scrm.enums;

/**
 * <AUTHOR>
 * @Date 2023/8/25
 * @Description 对象存储路径
 */
public enum  OssStoragePathEnum {

    /**
     * 对象存储路径常量
     */
    SCRM_PATH("scrm/", "素材存储根路径"),
    GROUP_CODE("scrm/groupCode/","社群活码"),
    CHATARCHIVE_PATH("scrm/chatarchive/","会话存档文件存储路径"),
    MOMENT_PATH("scrm/moment/","话术库的素材存储路径"),
    GROUP_FISSION_PATH("scrm/groupFission/","任务列表的素材存储路径"),
    KNOWLEDGE_BASE_AI_PATH("scrm/knowledgebaseai/","AI知识库的素材存储路径"),
    AVATAR_PATH("scrm/avatar/","头像的素材存储路径"),
    WORK_CARD_PATH("scrm/workCard/","工牌的素材存储路径"),
    QR_CODE_AVATAR_PATH("scrm/QrCodeAvatar/","渠道二维码头像的素材存储路径"),
    CORP_AVATAR_PATH("scrm/corp/avatar/","企业头像的素材存储路径"),
    DRAINAGE_QR_AVATAR_PATH("scrm/drainage_short_link/QrCodeAvatar/","短链的企业头像的素材存储路径"),
    RADAR_CONTACT_PATH("scrm/radarContact/","智能物料的素材存储路径"),
    PDF_IMAGE_PATH("scrm/pdfImages/","智能物料PDF的素材存储路径"),
    KF_MEDIA_PATH("scrm/kf/mediaCard/","微信客服的素材存储路径"),
    COMMON_PATH("scrm/common/","同用的素材存储路径"),
    IMAGE_PATH("scrm/image/","图片的素材存储路径"),
    VOICE_PATH("scrm/voice/","语音的素材存储路径"),
    VIDEO_PATH("scrm/video/","视频的素材存储路径"),
    FILE_PATH("scrm/file/","文件的素材存储路径"),
    POST_PATH("scrm/post/","帖子的素材存储路径"),
    PDF_PATH("scrm/pdf/","PDF的素材存储路径"),
    CONTACT_MOMENT_PATH("scrm/contact/moment/","客户朋友圈的素材存储路径");
    private String path;
    private String remark;

    OssStoragePathEnum(String path, String remark) {
        this.path = path;
        this.remark = remark;
    }

    public String getPath() {
        return path;
    }

    public String getRemark() {
        return remark;
    }
}
