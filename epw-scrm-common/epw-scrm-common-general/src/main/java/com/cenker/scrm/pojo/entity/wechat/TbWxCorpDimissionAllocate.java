package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业成员离职分配记录对象 tb_wx_corp_dimission_allocate
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCorpDimissionAllocate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * $column.columnComment
     */
    @TableId
    private Long id;

    /**
     * 接替成员的userid
     */
     // @Excel(name = "接替成员的userid")
    private String takeOverUserId;

    /**
     * 被分配的客户id或者群id
     */
     // @Excel(name = "被分配的客户id或者群id")
    private String allocateId;

    /**
     * 分配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
     // @Excel(name = "分配时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date allocateTime;

    /**
     * 原跟进成员的userid
     */
     // @Excel(name = "原跟进成员的userid")
    private String handoverUserid;

    /**
     * 类型:1表示 外部联系人的userid,2 表示 客户群id
     */
     // @Excel(name = "类型:1表示 外部联系人的userid,2 表示 客户群id")
    private String type;

    /**
     * 企业id
     */
     // @Excel(name = "企业id")
    private String corpId;

    /**
     * 状态 0 表示成功、1 表示待分配 2 分配中
     */
     // @Excel(name = "状态 0 表示成功、1 表示待分配 2 分配中")
    private String status;

}
