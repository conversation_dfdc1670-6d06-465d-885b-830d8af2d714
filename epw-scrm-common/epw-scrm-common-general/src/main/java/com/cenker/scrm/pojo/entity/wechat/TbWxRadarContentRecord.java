package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@TableName("tb_wx_radar_content_record")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TbWxRadarContentRecord {

  @TableId(type = IdType.ASSIGN_ID)
  private String id;
  /**
   * 图文id
   */
  @NotEmpty(message = "图文id为空")
  private String contentId;
  /**
   * 员工id
   */
//  @NotEmpty(message = "员工id为空")
  private String staffId;
  /**
   * 客户id
   */
  private String customerId;
  /**
   * 当前会话转发次数
   */
  private Integer forwardNum = 0;
  /**
   * 是否转发 0 否 1 是
   */
  private Integer forwardTo = 0;
  /**
   * 点击渠道 1 侧边栏 2 朋友圈 3 工作台 4 渠道活码 5 企业话术
   */
  private Integer clickSource;
  /**
   * 阅读完成率
   */
  private String readRate = "1%";
  /**
   * 阅读时长(默认3秒)
   */
  private Integer readTime = 3;
  /**
   * 备注
   */
  private String remark;
  /**
   * 0 未删除 1已删除
   */
  private Integer delFlag;
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  /**
   * 总查看数
   */
  @TableField(exist = false)
  private Integer totalReadNum;

  /**
   * 总阅读秒数
   */
  @TableField(exist = false)
  private Integer totalReadTime;

  /**
   * 转发人
   */
  @TableField(exist = false)
  private String forwardUser;
}
