package com.cenker.scrm.handler.oper;

import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.enums.TrackTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.bu.RelatedResource;
import com.cenker.scrm.util.BuOperTrackUtils;
import com.cenker.scrm.util.StringUtils;

import java.util.List;
/**
 * 客户标签变更事件处理器
 */
public class CustomerTagChangeHandler extends DefaultBuOperTrackHandler {
    public CustomerTagChangeHandler(TrackEventTypeEnum event) {
        this.eventType = event;
    }

    @Override
    protected String getContent(OperTrackParams operTrackParams) {
        RelatedResource relatedResource = operTrackParams.getRelatedResource();

        List<String> addTagList = relatedResource.getAddTagList();
        if (addTagList == null || addTagList.isEmpty()) {
            addTagList = relatedResource.getRemoveTagList();
        }

        String contentStr = eventType.getContent();
        TrackTypeEnum trackType = BuOperTrackUtils.getTrackTypeEnum(eventType.getEventType(), false);
        String value;
        switch (eventType) {
            case CORPORATE_TAG_EDIT_ADD:
            case CORPORATE_TAG_EDIT_REMOVE:
            case CORPORATE_TAG_EDIT_SYNC_ADD:
            case CORPORATE_TAG_EDIT_SYNC_REMOVE:
            case PERSONAL_TAG_EDIT_ADD:
            case PERSONAL_TAG_EDIT_REMOVE:
                value = BuOperTrackUtils.buildHtml(addTagList, "tag", null);
                // 标签
                contentStr = contentStr.replace("{0}", value);
                break;
            case BULK_EDIT_TAG_ADD:
            case BULK_EDIT_TAG_REMOVE:
                if (trackType == TrackTypeEnum.BUSINESS) {
                    contentStr = contentStr.replace("{0}", "");
                    StringBuilder sb = new StringBuilder();
                    List<String> customerList = relatedResource.getCustomerList();
                    if (relatedResource.isAllSelected()) {
                        BuOperTrackUtils.buildSpanHtml("全选数据后，为"+customerList.size()+"位客户" +contentStr, null, sb);
                        BuOperTrackUtils.buildHtml(addTagList, "tag", sb);
                        contentStr = sb.toString();
                    } else if (StringUtils.isNotEmpty(relatedResource.getCondition())) {
                        BuOperTrackUtils.buildSpanHtml("为通过【" + relatedResource.getCondition() + "】条件筛选的客户：", null, sb);
                        BuOperTrackUtils.buildHtml(customerList, "name", sb);
                        BuOperTrackUtils.buildSpanHtml("共" + customerList.size() + "人" + contentStr, null, sb);
                        BuOperTrackUtils.buildHtml(addTagList, "tag", sb);
                        contentStr = sb.toString();
                    } else {
                        BuOperTrackUtils.buildSpanHtml("为", null, sb);
                        // 如何 customerList 大于3 个，则只显示前三个，后面的用省略号代替
                        /*if (customerList.size() > 3) {
                            BuOperTrackUtils.buildHtml(customerList.subList(0, 3), "name", sb);
                            BuOperTrackUtils.buildSpanHtml("……", "name", sb);
                            BuOperTrackUtils.buildSpanHtml(customerList.size() + "人" + contentStr, null, sb);
                        } else {*/
                        BuOperTrackUtils.buildHtml(customerList, "name", sb);
                        BuOperTrackUtils.buildSpanHtml(" 共" + customerList.size() + "人" + contentStr, null, sb);
                        /*}*/
                        BuOperTrackUtils.buildHtml(addTagList, "tag", sb);
                        contentStr = sb.toString();
                    }
                } else {
                    value = BuOperTrackUtils.buildHtml(addTagList, "tag", null);
                    contentStr = contentStr.replace("{0}", value);
                }
                break;
        }
        return contentStr;
    }

}
