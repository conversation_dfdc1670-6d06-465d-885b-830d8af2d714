package com.cenker.scrm.constants;

/**
 * 缓存Key常量类
 * <AUTHOR>
 * @Date 2021/5/5 12:35
 **/
public class CacheKeyConstants {

    /**
     * 应用统一前缀
     */
    private static final String APP_PREFIX = "scrm:";

    /**
     * 微信公众号前缀
     */
    public static final String WX_MP_PREFIX = APP_PREFIX + "wx_mp:";

    /**
     * 企业微信配置Redis Key
     */
    public static final String CORP_CONFIG = APP_PREFIX + "corp_config:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = APP_PREFIX + "captcha_codes:";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = APP_PREFIX + "login_tokens:";

    /**
     * 域账号token
     */
    public static final String DOMAIN_LOGIN_TOKEN_KEY = APP_PREFIX + "domain_login_tokens:";

    /**
     * 登录失败重试次数
     */
    public static final String LOGIN_RETRY_TIMES = APP_PREFIX + "login_retry_times:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = APP_PREFIX + "repeat_submit:";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = APP_PREFIX + "login_user_key";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = APP_PREFIX + "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = APP_PREFIX + "sys_dict:";

    /**
     * 微信code前綴
     */
    public static final String WECHAT_CODE_USER_INFO = APP_PREFIX + "wx_code_user_info:";

    /**
     * 企业基本信息 Redis Key
     */
    public static final String CORP_BASE_INFO = APP_PREFIX + "corp_base_info:";

    /**
     * 企业更换头像
     */
    public static final String CORP_AVATAR_REMIND = APP_PREFIX + "corp_avatar_remind:";

    /**
     * 话术发送次数统计
     */
    public static final String QUICK_REPLY_COUNT = APP_PREFIX + "quick_reply_count:";

    /**
     * 工作台令牌前缀
     */
    public static final String WORKBENCH_LOGIN_USER_KEY = APP_PREFIX + "workbench_login_user_key";

    /**
     * 工作台登录用户 redis key
     */
    public static final String WORKBENCH_LOGIN_TOKEN_KEY = APP_PREFIX + "workbench_login_tokens:";

    /**
     * 配送员活码缓存
     */
    public static final String DELIVERY_CONTACT_CACHE_KEY = APP_PREFIX + "delivery_contact_cache_key:";

    /**
     * wxapp缓存
     */
    public static final String WXAPP_APP_ID_CACHE = APP_PREFIX + "wx:app_id";

    /**
     * wxapp缓存
     */
    public static final String WXAPP_WEIXIN_SIGN_CACHE = APP_PREFIX + "wx:weixin_Sign";

    /**
     * 工作台排行榜缓存
     */
    public static final String WORKBENCH_CONTACT_ACQUISITION_DELIVERY_RANK = APP_PREFIX + "workbench_contact_acquisition_delivery_rank:";
    /**
     * 微信短链
     */
    public static final String WXOPEN_COMPONENT_WXMA_URL_LINK = APP_PREFIX + "wxopen:component:wxma_url_link:";
    /**
     * 后台城市排行榜缓存
     */
    public static final String ADMIN_CONTACT_ACQUISITION_CITY_RANK = APP_PREFIX + "admin_contact_acquisition_city_rank:";
    /**
     * 后台配送员排行榜缓存
     */
    public static final String ADMIN_CONTACT_ACQUISITION_DELIVERY_RANK = APP_PREFIX + "admin_contact_acquisition_delivery_rank:";
    /**
     * 客户拉新趋势
     */
    public static final String ADMIN_CONTACT_ACQUISITION_COUNT_TREND = APP_PREFIX + "admin_contact_acquisition_count_trend:";
    /**
     * 工作台配置缓存
     */
    public static final String WORKBENCH_CONTACT_CONFIG = APP_PREFIX + "workbench_contact_config:";

    /**
     * 客户画像标签
     */
    public static final String CUSTOMER_PORTRAIT_TAG = APP_PREFIX + "customer-porTrait-tag:";
    /**
     * 智能物料活码生成
     */
    public static final String RADAR_CONTACT = APP_PREFIX + "radar-contact:";
    /**
     * 智能物料活码生成
     */
    public static final String SYNC_ADD_CORP_TAG_GROUP = APP_PREFIX + "add-corp-tag-group:";
    /**
     * 编辑客户群
     */
    public static final String CUSTOMER_GROUP_UPDATE = APP_PREFIX + "customer-group-update:";
    /**
     * 客户事件
     */
    public static final String CUSTOMER__ADD_CALLBACK = APP_PREFIX + "callback-CorpExternalContact";
    /**
     * 客户群事件
     */
    public static final String CUSTOMER_CUSTOMER_UPDATE_CALLBACK = APP_PREFIX + "callback-CorpExternalCustomerGroup";
    /**
     * 任务裂变数据缓存
     */
    public static final String FISSION_SEND_CUSTOMER_CNT = APP_PREFIX + "fission_send_customer_cnt:";
    /**
     * RedisLockKey
     * 编辑客户资料
     */
    public static final String CUSTOMER_PORTRAIT_INFO = APP_PREFIX + "customer-porTrait-info:";

    /**
     * 智能物料文章缓存
     */
    public static final String RADAR_CONTENT_H5 = APP_PREFIX + "radar_content_h5:";

    /**
     * jsapiTicket
     */
    public static final String JSAPI_TICKET = APP_PREFIX + "jsapi_ticket:";

    /**
     * 企微登录回调
     */
    public static final String OAUTH2_CALLBACK = APP_PREFIX + "oauth2_callback:";

    /**
     * 条件sop更新
     */
    public static final String SOP_CONDITION_UPDATE = APP_PREFIX + "sop_condition_update:";
    public static final String SOP_JOURNEY_UPDATE = APP_PREFIX + "sop_journey_update:";
    public static final String SOP_MASS_CUSTOMER_UPDATE = APP_PREFIX + "sop_mass_customer_update:";
    /**
     * 条件sop内容执行
     */
    public static final String SOP_CONDITION_RUN = APP_PREFIX + "sop_condition_run:";
    /**
     * 社群活码生成
     */
    public static final String GROUP_CODE_SCAN_LOCK = APP_PREFIX + "group_code_scan_lock:";
    /**
     * license证书到期时间key、坐席限制数
     */
    public static final String LICENSE_CERT_EXPIRE_TIME = APP_PREFIX + "licenseCertExpireTime:";
    public static final String LICENSE_SEATS_LIMIT_COUNT = APP_PREFIX + "licenseSeatsLimitCnt:";
    public static final String LICENSE_EXTRA_VERIFY = APP_PREFIX + "licenseSeatsLimitCnt:";

    /**
     * 素材前缀
     */
    public static final String MATERIAL_PREFIX = APP_PREFIX + "material:";

    public static final String CK_SESSION_STATS_SENS_ALARM_END_TIME = APP_PREFIX+"startSensAlarmEndTime:";

    /**
     * 记录上次触发热词的时间
     */
    public static final String RECORD_HOT_WORD_TRIGGER_LAST_END_TIME = APP_PREFIX+"recordHotWordTriggerLastEndTime:";

    /**
     * 日志缓存
     */
    public static final String LOG_CACHE_KEY = APP_PREFIX + "log_cache_key:";

    /**
     * 最后一次同步朋友圈数据时间
     */
    public static final String MOMENT_LAST_SYNC_TIME_KEY = APP_PREFIX + "moment_last_sync_time_key:";

    /**
     * 同步客户时，客户上锁key
     */
    public static final String SYNC_CUSTOMER_LOCK = APP_PREFIX + "sync_customer:";
    /**
     * 同步客户标签时，客户上锁key
     */
    public static final String SYNC_CUSTOMER_LOCK_TAG = APP_PREFIX + "sync_customer_tag:";
    /**
     * 智能物料互动记录动态，上锁key
     */
    public static final String MOMENT_INTERACTION_TRACK = APP_PREFIX + "moment_interaction_track:";
    /**
     * 统计更新key
     * 用于统计更新时，防止同一时刻重复更新
     */
    public static final String STATISTIC_UPDATE_KEY = APP_PREFIX + "statistic_update_key:";
    /**
     * 审批key
     * 用于审核时，防止同一时刻重复审核
     */
    public static final String APPROVAL_KEY = APP_PREFIX + "approval_key:";
    /**
     * 定时同步客户认证状态key
     * 用于定时同步客户认证状态时，防止上一次任务未完成时重复执行
     */
    public static final String CUSTOMER_AUTH_SYNC = APP_PREFIX + "customer_auth_sync:";



}
