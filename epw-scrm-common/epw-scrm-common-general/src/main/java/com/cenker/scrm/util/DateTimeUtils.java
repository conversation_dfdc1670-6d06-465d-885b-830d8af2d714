package com.cenker.scrm.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 * @create 2021/6/28 14:11
 * 时间工具类
 */
@Slf4j
public class DateTimeUtils {
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_FORMAT_CN = "yyyy年MM月dd日";
    public static final String DATE_FORMAT_CN1 = "yyyy年MM月dd日";
    public static final String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String TIME_FORMAT2 = "yyyy-MM-dd HH:mm";
    public static final String TIME_FORMAT3 = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String TIME_FORMAT4 = "HH:mm";
    public static final String TIME_FORMAT_CN = "yyyy年MM月dd日 HH:mm:ss";
    public static final String MONTH_FORMAT = "yyyy-MM";
    public static final String DAY_FORMAT = "yyyyMMdd";
    public static final String DATE_TIME_FORMAT_CN = "MM月dd日 HH:mm";

    static long ONE_DAY_MILLSECOND = 86400000L;

    private DateTimeUtils() {
    }

    public static String getDateTimeAsString(LocalDateTime localDateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIME_FORMAT);
        return localDateTime.format(formatter);
    }


    public static Date getCurrDate() {
        return new Date();
    }

    public static Timestamp getCurrTimestamp() {
        return new Timestamp(System.currentTimeMillis());
    }

    /**
     * 日期格式字符串转换成时间戳
     *
     * @param date_str 字符串日期
     * @param format   如：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String date2TimeStamp(String date_str, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return String.valueOf(sdf.parse(date_str).getTime() / 1000);
        } catch (Exception e) {
            log.error("日期格式字符串转换成时间戳异常,对应的时间:{},对应的格式:{}",date_str,format);
            log.error("错误信息:{}",e);
        }
        return "";
    }

    public static String getFormatDate(Date currDate) {
        return getFormatDate(currDate, "yyyy-MM-dd");
    }

    public static Date getFormatDateToDate(Date currDate) {
        return getFormatDate(getFormatDate(currDate));
    }

    public static String getFormatDate_CN(Date currDate) {
        return getFormatDate(currDate, "yyyy年MM月dd日");
    }

    public static String getFormatDate_MMdd(Date currDate) {
        return getFormatDate(currDate, "MM月dd日");
    }

    public static Date getFormatDateToDate_CN(Date currDate) {
        return getFormatDate_CN(getFormatDate_CN(currDate));
    }

    public static Date getFormatDate(String currDate) {
        return getFormatDate(currDate, "yyyy-MM-dd");
    }

    public static Date getFormatDate_CN(String currDate) {
        return getFormatDate(currDate, "yyyy年MM月dd日");
    }

    public static String getFormatMonthTimeCn(Date currDate) {
        return getFormatDate(currDate, DATE_TIME_FORMAT_CN);
    }

    public static String getFormatTimeMillis(Date currDate) {
        return getFormatDate(currDate, TIME_FORMAT3);
    }

    public static String getFormatTimeHHmm(Date currDate) {
        return getFormatDate(currDate, TIME_FORMAT4);
    }

    public static String getFormatDate(Date currDate, String format) {
        SimpleDateFormat dtFormatdB = null;

        try {
            dtFormatdB = new SimpleDateFormat(format);
            return dtFormatdB.format(currDate);
        } catch (Exception var4) {
            log.error("输入的格式有误:{},已使用默认的格式:yyyy-MM-dd",format);
            log.error("错误信息:{}",var4);
            dtFormatdB = new SimpleDateFormat("yyyy-MM-dd");
            return dtFormatdB.format(currDate);
        }
    }

    public static String getFormatDateTime(Date currDate) {
        return getFormatDateTime(currDate, "yyyy-MM-dd HH:mm:ss");
    }

    public static String getFormatDateTime2(Date currDate) {
        return getFormatDateTime(currDate, "yyyy-MM-dd HH:mm");
    }

    public static Date getFormatDateTimeToTime(Date currDate) {
        return getFormatDateTime(getFormatDateTime(currDate));
    }

    public static Date getFormatDateTime(String currDate) {
        return getFormatDateTime(currDate, "yyyy-MM-dd HH:mm:ss");
    }

    public static String getFormatDateTime_CN(Date currDate) {
        return getFormatDateTime(currDate, "yyyy年MM月dd日 HH:mm:ss");
    }

    public static Date getFormatDateTimeToTime_CN(Date currDate) {
        return getFormatDateTime_CN(getFormatDateTime_CN(currDate));
    }

    public static Date getFormatDateTime_CN(String currDate) {
        return getFormatDateTime(currDate, "yyyy年MM月dd日 HH:mm:ss");
    }

    public static String getFormatDateTime(Date currDate, String format) {
        SimpleDateFormat dtFormatdB = null;

        try {
            dtFormatdB = new SimpleDateFormat(format);
            return dtFormatdB.format(currDate);
        } catch (Exception var4) {
            log.error("输入的格式有误:{},已使用默认的格式:yyyy-MM-dd",format);
            log.error("错误信息:{}",var4);
            dtFormatdB = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return dtFormatdB.format(currDate);
        }
    }

    public static Date getFormatDate(String currDate, String format) {
        SimpleDateFormat dtFormatdB = null;

        try {
            dtFormatdB = new SimpleDateFormat(format);
            return dtFormatdB.parse(currDate);
        } catch (Exception var6) {
            dtFormatdB = new SimpleDateFormat("yyyy-MM-dd");

            try {
                return dtFormatdB.parse(currDate);
            } catch (Exception var5) {
                log.error("输入的时间有误:{}",currDate);
                log.error("错误信息:{}",var5);
                return null;
            }
        }
    }

    public static Date getFormatDateTime(String currDate, String format) {
        SimpleDateFormat dtFormatdB = null;

        try {
            dtFormatdB = new SimpleDateFormat(format);
            return dtFormatdB.parse(currDate);
        } catch (Exception var6) {
            dtFormatdB = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            try {
                return dtFormatdB.parse(currDate);
            } catch (Exception var5) {
                log.error("输入的时间有误:{}",currDate);
                log.error("错误信息:{}",var5);
                return null;
            }
        }
    }

    public static String getCurrDateStr() {
        return getFormatDate(getCurrDate());
    }

    public static String getCurrDateTimeStr() {
        return getFormatDateTime(getCurrDate());
    }

    public static String getCurrDateStr_CN() {
        return getFormatDate(getCurrDate(), "yyyy年MM月dd日");
    }

    public static String getCurrDateTimeStr_CN() {
        return getFormatDateTime(getCurrDate(), "yyyy年MM月dd日 HH:mm:ss");
    }

    public static long getDateTimeDiff(Date startDate, Date endDate) {
        return endDate.getTime() - startDate.getTime();
    }

    public static Date getDateBeforeOrAfter(int iDate) {
        Calendar cal = Calendar.getInstance();
        cal.add(5, iDate);
        return cal.getTime();
    }

    public static Date getDateBeforeOrAfter(Date curDate, int iDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(curDate);
        cal.add(5, iDate);
        return cal.getTime();
    }

    public static String getFormatMonth(Date currDate) {
        return getFormatDate(currDate, "yyyy-MM");
    }

    public static String getFormatDay(Date currDate) {
        return getFormatDate(currDate, "yyyyMMdd");
    }

    public static String getFirstDayOfMonth() {
        Calendar cal = Calendar.getInstance();
        int firstDay = cal.getMinimum(5);
        cal.set(5, firstDay);
        return getFormatDate(cal.getTime(), "yyyy-MM-dd");
    }

    public static String getFirstDayOfMonth(Date currDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(currDate);
        int firstDay = cal.getMinimum(5);
        cal.set(5, firstDay);
        return getFormatDate(cal.getTime(), "yyyy-MM-dd");
    }

    public static Date getDateBeforeOrAfterHours(Date curDate, int iHour) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(curDate);
        cal.add(11, iHour);
        return cal.getTime();
    }

    public static Date getDateBeforeOrAfterMinutes(Date curDate, int minutes) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(curDate);
        cal.add(12, minutes);
        return cal.getTime();
    }

    public static Integer untilNexDayTimeGap(Date currentDate) {
        return currentDate == null ? null : (new Long(ONE_DAY_MILLSECOND - currentDate.getTime() % ONE_DAY_MILLSECOND / 1000L)).intValue();
    }

    public static int getWeekOfDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int week = cal.get(7) - 1;
        if (week < 0) {
            week = 0;
        }

        return week;
    }

    /**
     * LocalDateTime转换为Date
     *
     * @param localDateTime
     */
    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }

        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * Date转换为LocalDate
     *
     * @param date
     */
    public static LocalDateTime date2LocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static LocalDateTime string2LocalDateTime(String date) {
        return null;
    }

    public static Date addDay(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, amount);
        return cal.getTime();
    }

    public static Date addHour(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR_OF_DAY, amount);
        return cal.getTime();
    }

    public static Date addMinute(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE, amount);
        return cal.getTime();
    }

    /**
     * 计算两个日期相差的天数
     * @param dateEnd 结束时间
     * @param dateBegin 开始时间
     * @return
     * @throws ParseException
     */
    public static int getDiffDays(String dateEnd,String dateBegin) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            cal.setTime(sdf.parse(dateBegin));
            long time1 = cal.getTimeInMillis();
            cal.setTime(sdf.parse(dateEnd));
            long time2 = cal.getTimeInMillis();
            long between_days = (time2 - time1) / (1000 * 3600 * 24);
            return Integer.parseInt(String.valueOf(between_days));
        } catch (ParseException ex) {
            log.error("计算两个日期相差的天数方法异常,结束时间:{}，开始时间:{}",dateEnd,dateBegin);
            log.error("错误信息:{}",ex);
            return 0;
        }
    }

//    public static void main(String[] args) {
//        try {
//            System.out.println(getDiffDays("2021-09-10 11:04:21", "2021-09-10 10:39:11"));
//            System.out.println(getDiffDays("2021-09-10 11:04:21", "2021-09-08 10:39:11"));
//            System.out.println(getDiffDays("2021-09-10 11:04:21", "2021-09-07 10:39:11"));
//            System.out.println(getDiffDays("2021-09-10 11:04:21", "2021-09-06 10:39:11"));
//        } catch (ParseException ex) {
//
//        }
//    }

    /**
     * 获取时间小时数差
     *
     * @param dateEnd   结束时间
     * @param dateBegin 开始时间
     * @return
     */
    public static double getDiffHour(Date dateEnd, Date dateBegin) {
        double diff = ((double) (dateEnd.getTime() - dateBegin.getTime()) / (1000 * 60 * 60));
        double hours = new BigDecimal(diff).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        return hours;
    }

    /**
     * 获取时间分钟数差
     *
     * @param dateEnd   结束时间
     * @param dateBegin 开始时间
     * @return
     */
    public static int getDiffMin(Date dateEnd, Date dateBegin) {
        Long diff = ((long) (dateEnd.getTime() - dateBegin.getTime()) / (1000 * 60));
        return Integer.parseInt(diff.toString());
    }

    /**
     * 获取当前小时数
     *
     * @param date
     * @return
     */
    public static int getHours(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 时间比较 前者大于后者 返回大于0 反之小于0。
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int compareDate(Date date1, Date date2) {
        int result = date1.compareTo(date2);
        return result;
    }

    public static String getWeek(Date date) {
        String[] weeks = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int week_index = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (week_index < 0) {
            week_index = 0;
        }
        return weeks[week_index];
    }

    public static Date string2Date(String dateString, String pattern, Locale locale) {
        SimpleDateFormat format = new SimpleDateFormat(pattern, locale);
        ParsePosition pos = new ParsePosition(0);
        return format.parse(dateString, pos);
    }

    public static LocalTime getLocalTimeMax(){
        return LocalTime.of(23, 59, 59, 99);
    }

    public static Date getDateByLocalDateTime(LocalDateTime time) {
        Date date = Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
        return date;
    }

    public static String localDateTimeToString(LocalDateTime date,String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return date.format(formatter);
    }

}
