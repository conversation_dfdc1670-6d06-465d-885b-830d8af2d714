package com.cenker.scrm.pojo.entity.wechat;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AllocateDetailInfoVO implements Serializable {
    private String name;
    private String allocateId;
    private String type;
    private String corpUserName;
    private String departmentName;

    private int cnt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date allocateTime;
}
