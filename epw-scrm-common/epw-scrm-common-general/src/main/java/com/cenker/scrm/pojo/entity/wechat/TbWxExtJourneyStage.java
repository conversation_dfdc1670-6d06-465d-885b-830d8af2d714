package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TbWxExtJourneyStage {

  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * 阶段名
   */
  private String stageName;
  /**
   * 旅程id
   */
  private Long journeyInfoId;
  /**
   * 排序
   */
  private Integer orderNum;
  /**
   * 阶段描述
   */
  private String stageDesc;
  /**
   * 备注
   */
  private String remark;
  /**
   * 企业id
   */
  private Long corpId;
  @TableLogic
  private Integer delFlag;
  private Long createBy;
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;
  private Long updateBy;
  @TableField(fill = FieldFill.UPDATE)
  private Date updateTime;
}
