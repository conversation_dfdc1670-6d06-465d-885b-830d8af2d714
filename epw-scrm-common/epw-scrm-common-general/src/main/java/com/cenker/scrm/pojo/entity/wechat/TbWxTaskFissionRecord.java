package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/10/18
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbWxTaskFissionRecord {
    private static final long serialVersionUID = 8770538385789110599L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务裂变表id
     */
    @Excel(name = "任务裂变表id")
    // @ApiModelProper("任务裂变表id")
    private String taskFissionId;

    /**
     * 裂变任务客户id
     */
    @Excel(name = "裂变任务客户id")
    // @ApiModelProper("裂变任务客户id")
    private String customerId;

    /**
     * 裂变任务客户姓名
     */
    @Excel(name = "裂变任务客户姓名")
    // @ApiModelProper("裂变任务客户姓名")
    private String customerName;

    /**
     * 裂变客户数量
     */
    @Excel(name = "裂变客户数量")
    // @ApiModelProper("裂变客户数量")
    private Long fissNum;

    // @ApiModelProper("二维码链接")
    private String qrCode;

    // @ApiModelProper("海报链接")
    private String poster;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    // @ApiModelProper(value = "创建时间")
    private Date createTime = new Date();

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    // @ApiModelProper(value = "完成时间")
    private Date completeTime;

    /**
     * 活动状态
     */
    @TableField(exist = false)
    private Integer status;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy年MM月dd日")
    @TableField(exist = false)
    private Date startTime;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy年MM月dd日")
    @TableField(exist = false)
    private Date endTime;

    // 活动名称
    @TableField(exist = false)
    private String taskName;
}
