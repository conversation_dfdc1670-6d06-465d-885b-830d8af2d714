package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxMomentSendCustomer {

  @TableId(type = IdType.ASSIGN_ID)
  private String id;
  /**
   * 发朋友圈表id
   */
  private String momentTaskId;
  /**
   * 执行者id
   */
  private String userId;
  /**
   * 外部客户联系id
   */
  private String externalUserId;
  /**
   * 是否可见客户
   */
  private Integer visible;
  /**
   * 评论时间
   */
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date commentTime;
  /**
   * 点赞时间
   */
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date likeTime;
  private String corpId;
}
