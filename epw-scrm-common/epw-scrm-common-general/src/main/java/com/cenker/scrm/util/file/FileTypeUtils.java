package com.cenker.scrm.util.file;

import com.cenker.scrm.constants.FileTypeConstant;
import org.apache.commons.lang3.StringUtils;

import java.io.File;

/**
 * 文件类型工具类
 *
 * <AUTHOR>
 */
public class FileTypeUtils {
    /**
     * 获取文件类型
     * <p>
     * 例如: aimi.txt, 返回: txt
     *
     * @param file 文件名
     * @return 后缀（不含".")
     */
    public static String getFileType(File file) {
        if (null == file) {
            return StringUtils.EMPTY;
        }
        return getFileType(file.getName());
    }

    /**
     * 获取文件类型
     * <p>
     * 例如: aimi.txt, 返回: txt
     *
     * @param fileName 文件名
     * @return 后缀（不含".")
     */
    public static String getFileType(String fileName) {
        int separatorIndex = fileName.lastIndexOf(".");
        if (separatorIndex < 0) {
            return "";
        }
        return fileName.substring(separatorIndex + 1).toLowerCase();
    }

    /**
     * 获取文件类型
     *
     * @param photoByte 文件字节码
     * @return 后缀（不含".")
     */
    public static String getFileExtendName(byte[] photoByte) {
        String strFileExtendName = "";
        if ((photoByte[0] == 71) && (photoByte[1] == 73) && (photoByte[2] == 70) && (photoByte[3] == 56)
                && ((photoByte[4] == 55) || (photoByte[4] == 57)) && (photoByte[5] == 97)) {
            strFileExtendName = FileTypeConstant.IMAGE_GIF_UPPER_CASE;
        } else if ((photoByte[6] == 74) && (photoByte[7] == 70) && (photoByte[8] == 73) && (photoByte[9] == 70)) {
            strFileExtendName = FileTypeConstant.IMAGE_JPG_UPPER_CASE;
        } else if ((photoByte[0] == 66) && (photoByte[1] == 77)) {
            strFileExtendName = FileTypeConstant.IMAGE_BMP_UPPER_CASE;
        } else if ((photoByte[1] == 80) && (photoByte[2] == 78) && (photoByte[3] == 71)) {
            strFileExtendName = FileTypeConstant.IMAGE_PNG_UPPER_CASE;
        }else if (photoByte[0] == 82 && photoByte[1] == 73 && photoByte[2] == 70 && photoByte[3] == 70) {
            strFileExtendName = FileTypeConstant.IMAGE_WEBP_UPPER_CASE;
        }else if (photoByte[0] == -1 && photoByte[1] == -40 && photoByte[2] == -1 && photoByte[3] == -32 && photoByte[4] == 0) {
            strFileExtendName = FileTypeConstant.IMAGE_JPEG_UPPER_CASE;
        }
        return strFileExtendName;
    }
}