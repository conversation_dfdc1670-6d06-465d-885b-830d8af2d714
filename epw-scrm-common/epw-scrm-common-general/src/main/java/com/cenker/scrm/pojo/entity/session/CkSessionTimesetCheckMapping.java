package com.cenker.scrm.pojo.entity.session;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

/**
 * 规则审计对象 ck_session_sens_check_mapping
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class CkSessionTimesetCheckMapping extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    private Long id;

    /** 时长设置ID */
    private Long setId;

    /** 审计人ID */
    private String checkUserId;
    /** 审计人名称 */
    private String name;
    private String avatar;

}
