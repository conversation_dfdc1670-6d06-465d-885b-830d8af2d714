package com.cenker.scrm.pojo.export;

import com.cenker.scrm.annotation.Excel;
import lombok.Data;

/**
 * @Title: ChurnCustomerExport
 * @Package: com.examination.bean
 * @Description: 描述
 * @author: db.xie
 * @date: 2021/2/7-14:58
 * @version: V1.0
 */
@Data
public class ChurnCustomerExport {
    @Excel(name = "客户名称")
    private String custName;

    @Excel(name = "客户标签")
    private String tag;

    @Excel(name = "添加人")
    private String corpUserName;

    @Excel(name = "添加时间")
    private String createTime;

    @Excel(name = "流失时间")
    private String churnDate;

    @Excel(name = "流失类型")
    private String churnStatus;
}
