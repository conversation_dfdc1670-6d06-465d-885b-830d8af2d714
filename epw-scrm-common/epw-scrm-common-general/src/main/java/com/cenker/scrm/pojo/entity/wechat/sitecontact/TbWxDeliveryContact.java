package com.cenker.scrm.pojo.entity.wechat.sitecontact;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_wx_delivery_contact")
public class TbWxDeliveryContact {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    private String state;
    private Integer type;
    private Long contactId;
    private Long storeId;
    private Long deliveryUserId;
    private String configId;
    private String qrCode;
    private Integer qrStyle;
    private String qrStyleCode;
    private Integer scene;
    private Boolean skipVerify;
    private String remark;
    /**
     * 活码生成时间
     */
    private Long generateTime;

    @TableLogic
    private Integer delFlag;
    private Long createBy;
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private Long updateBy;
    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
