package com.cenker.scrm.pojo.entity.subscr;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

/**
 * 订阅栏目实体类
 * 用于存储订阅栏目的相关信息
 */
@Data
@TableName("bu_subscription_section")
public class BuSubscriptionSection extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 栏目名称
     */
    private String sectionName;

    /**
     * 栏目简介
     */
    private String sectionIntro;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    private Integer delFlag;
    /**
     * 部门id
     */
    private Integer deptId;
}