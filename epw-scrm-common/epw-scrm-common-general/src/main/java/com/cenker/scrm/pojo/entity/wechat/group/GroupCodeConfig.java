package com.cenker.scrm.pojo.entity.wechat.group;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseDbEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.io.Serializable;

/**
 * @Description 社群活码群配置表
 * <AUTHOR> @Date 2023-08-23 
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "fieldHandler"}, ignoreUnknown = true)
@TableName("ck_group_code_config")
public class GroupCodeConfig extends BaseDbEntity implements Serializable {
	private static final long serialVersionUID =  5680354708692371302L;
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 社群活码唯一标识
	 */
	private Long groupCodeId;

	/**
	 * 企微群聊id
	 */
	private String chatId;

	/**
	 * 社群列表排序 从0开始
	 */
	private Integer listSort;
}

