package com.cenker.scrm.pojo.entity.wechat;

import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 企业微信成员对外属性对象 tb_wx_user_external_profile
 *
 * <AUTHOR>
 * @date 2021-01-22
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxUserExternalProfile extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * tb_wx_user表中的user_id
     */
    private String userId;

    /**
     * 全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节
     */
    private String openUserid;

    /**
     * 属性类型: 0-本文 1-网页 2-小程序.
     */
    private Integer type;

    /**
     * 属性名称
     */
    private String name;

    /**
     * 文本内容
     */
    private String textValue;

    /**
     * 网页地址
     */
    private String webUrl;

    /**
     * 网页Title
     */
    private String webTitle;

    /**
     * 小程序appid，必须是有在本企业安装授权的小程序，否则会被忽略.
     */
    private String appid;

    /**
     * 小程序的页面路径.
     */
    private String pagepath;
}
