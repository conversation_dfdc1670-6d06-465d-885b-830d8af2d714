package com.cenker.scrm.pojo.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 角色和分组关联表
 * @TableName sys_role_category
 */
@TableName(value ="sys_role_category")
@Data
public class SysRoleCategory implements Serializable {
    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 分组ID
     */
    private Long categoryId;

    /**
     * 分组类型
     */
    private String categoryType;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 0 标识未删除 1 标识删除 
     */
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}