package com.cenker.scrm.pojo.entity.wechat.sop;

import com.baomidou.mybatisplus.annotation.*;
import com.cenker.scrm.handler.JacksonTypeHandler;
import com.cenker.scrm.pojo.dto.condition.SopConditionDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description sop客户人群包
 * <AUTHOR> @Date 2023-07-17 
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@EqualsAndHashCode
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "fieldHandler"}, ignoreUnknown = true)
@TableName("ck_sop_customer_info")
public class SopCustomerInfo implements Serializable {
	private static final long serialVersionUID =  7100880188165898730L;
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * sop唯一标识
	 */
	private Long sopId;

	/**
	 * 筛选发送条件
	 */
//	@TableField(typeHandler = JacksonTypeHandler.class)
//	private SopConditionDTO sendCondition;
	private String sendCondition;

	/**
	 * 添加了此外部联系人的企业成员userid
	 */
	private String userId;

	/**
	 * 客户id
	 */
	private String externalUserId;

	/**
	 * 是否满足条件当前条件 0 否 1 是
	 */
	@TableField(value = "is_accord")
	private Boolean accord;

	/**
	 * 加入时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date joinTime;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 企业id
	 */
	private String corpId;

	/**
	 * 逻辑删除 0 表示未删除，1 表示删除
	 */
	@TableField(value = "is_deleted")
	@TableLogic
	private Boolean deleted;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
}

