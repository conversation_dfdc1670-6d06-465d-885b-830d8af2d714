package com.cenker.scrm.pojo.dto.session;

import com.cenker.scrm.annotation.Excel;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.Data;

import java.util.List;

/**
 * 敏感规则信息对象 ck_session_sens_rule_info
 * 
 * <AUTHOR>
 * @date 2024-03-15
 */
@Data
public class AddSensRuleDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long ruleId;

    @Excel(name = "规则名称", cellType = Excel.ColumnType.STRING, prompt = "敏感规则名称：不能超过20个字")
    /** 规则名称 */
    private String ruleName;

    /** 字典sens_rule_cept_type：1.警告并拦截 2.警告且事后审计 3.仅事后审计 */
    @Excel(name = "拦截方式", cellType = Excel.ColumnType.STRING, prompt = "拦截方式：事前警告并拦截, 警告且事后审计, 事后审计 ")
    private String interceptType;

    /** 敏感行为，多个逗号分隔  字典：1.发送手机号 2.发送邮箱地址  3.发送和接收红包*/
    @Excel(name = "敏感行为", cellType = Excel.ColumnType.STRING, prompt = "敏感行为多个用英文逗号分隔：[拦截方式为：事前警告并拦截或警告且事后审计时填(发送邮箱地址、发送手机号码、发送和接收红包),拦截方式为：事后审计时填(发送名片、发送文件，发送和接收红包)]")
    private String actTypes;
    /**敏感词，多个逗号分隔**/
    @Excel(name = "敏感词", cellType = Excel.ColumnType.STRING, prompt = "敏感词多个用英文逗号分隔（词1,词2）")
    private String  sensitiveWords;
    //检测范围列表
    private List<UserDto> userConditionList;

    private String checkUserIds;


}
