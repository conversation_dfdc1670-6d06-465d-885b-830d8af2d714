package com.cenker.scrm.pojo.entity.open.store;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("open_api_app_info")
public class OpenApiAppInfo {
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  private Long corpId;
  private String appId;
  private String appSecret;
  private Integer appType;
  private Date expiresTime;
  private String remark;
  @TableField(value = "create_time",fill = FieldFill.INSERT)
  private Date createTime;
  private Date updateTime;
  private Long createBy;
  private Long updateBy;
}
