package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/10/18
 * @Description 裂变任务完成数据
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_wx_fission_complete_record")
public class TbWxTaskFissionCompleteRecord {
    private static final long serialVersionUID = -9170275723334248435L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务裂变表id
     */
    @Excel(name = "任务裂变表id")
    // @ApiModelProper("任务裂变表id")
    private String taskFissionId;

    /**
     * 任务裂变记录表id
     */
    @Excel(name = "任务裂变记录表id")
    // @ApiModelProper("任务裂变记录表id")
    private String fissionRecordId;

    /**
     * 裂变客户id
     */
    @Excel(name = "裂变客户id")
    // @ApiModelProper("裂变客户id")
    private String customerId;

    /**
     * 裂变客户姓名
     */
    @Excel(name = "裂变客户姓名")
    // @ApiModelProper("裂变客户姓名")
    private String customerName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    // @ApiModelProper("创建时间")
    private Date createTime = new Date();

    // @ApiModelProper("状态 0 有效 1 无效")
    private Integer status;

    // @ApiModelProper("客户头像")
    private String customerAvatar;

    /**
     * 1 新客户 2 老客户
     */
    private Integer type;
}
