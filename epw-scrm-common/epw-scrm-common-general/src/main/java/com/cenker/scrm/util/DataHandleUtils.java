package com.cenker.scrm.util;

import cn.hutool.core.collection.CollectionUtil;
import com.cenker.scrm.pojo.vo.contact.ContactStatisticsDailyVO;
import com.cenker.scrm.service.IStatistics;
import com.google.common.collect.Lists;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/8/3
 * @Description 数据处理工具
 */
public class DataHandleUtils {

    /**
     * 获取趋势图 总数数据处理
     *
     * @param startTime     查询开始时间
     * @param endTime       查询结束时间
     * @param vo            返回的外层数据
     * @param dailyDataList 返回的分日数据
     * @param list          需要处理的分日数据
     */
    public static void setTotalCountGroup(Date startTime, Date endTime, IStatistics vo, List<ContactStatisticsDailyVO> dailyDataList, List<ContactStatisticsDailyVO> list) {
        List<ContactStatisticsDailyVO> count = Lists.newArrayList();
        ContactStatisticsDailyVO countVO = new ContactStatisticsDailyVO();
        countVO.setCount(0);
        count.add(countVO);
        if (CollectionUtil.isNotEmpty(list)) {
            // 设置初始的添加数 不然可能会导致一开始的数据没有值 但考虑到可能拿到的比如是2021年的 查2022年的数据 初始值就会错误拿到2021年的 所以新增逻辑
            // 最大的时间
            ContactStatisticsDailyVO contactStatisticsDailyVo = list.get(0);
            Date nowDate = DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, contactStatisticsDailyVo.getDate()));
            if (list.size() == 1 || nowDate.after(startTime)) {
                countVO.setCount(list.get(0).getCount());
                countVO.setDate(list.get(0).getDate());
                count.set(0, countVO);
            } else {
                // 最大的时间
                contactStatisticsDailyVo = list.get(list.size() - 1);
                nowDate = DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, contactStatisticsDailyVo.getDate()));
                if (nowDate.before(startTime)) {
                    countVO.setCount(list.get(list.size() - 1).getCount());
                    countVO.setDate(list.get(list.size() - 1).getDate());
                    count.set(0, countVO);
                } else {
                    // 看是不是在查询时间内 初始时间就是两者之间的前者 如2022-01-18和2022-01-23 开始时间是2022-01-20 就拿2022-01-18
                    ContactStatisticsDailyVO lastContactStatistics = new ContactStatisticsDailyVO();
                    for (ContactStatisticsDailyVO statisticsDailyVo : list) {
                        nowDate = DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, statisticsDailyVo.getDate()));
                        if (nowDate.before(startTime)) {
                            lastContactStatistics = statisticsDailyVo;
                        } else {
                            countVO.setCount(lastContactStatistics.getCount());
                            countVO.setDate(lastContactStatistics.getDate());
                            count.set(0, lastContactStatistics);
                            break;
                        }
                    }
                }
            }
        }
        // 群总数
        Map<String, List<ContactStatisticsDailyVO>> map = list.parallelStream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(item -> DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getDate())));
        DateUtils.findDates(startTime, endTime).stream()
                .map(d -> DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, d))
                .forEach(date -> {
                            // 设置日期
                            ContactStatisticsDailyVO dailyVo = new ContactStatisticsDailyVO();
                            dailyVo.setDay(date);
                            List<ContactStatisticsDailyVO> contactStatisticsDailyVos = map.get(date);
                            if (CollectionUtil.isNotEmpty(contactStatisticsDailyVos)) {
                                // 设置累计数量
                                dailyVo.setCount(contactStatisticsDailyVos.get(0).getCount());
                            } else {
                                // 设置上一次累计值
                                if (CollectionUtil.isNotEmpty(dailyDataList)) {
                                    ContactStatisticsDailyVO contactStatisticsDailyVo = dailyDataList.get(dailyDataList.size() - 1);
                                    dailyVo.setCount(contactStatisticsDailyVo.getCount());
                                } else {
                                    // 当前的时间date必须要大于最初始的数据时间才可以用
                                    Date firstDataDate = count.get(0).getDate();
                                    Date nowDate = DateUtils.parseDate(date);
                                    if (firstDataDate != null && firstDataDate.before(nowDate)) {
                                        dailyVo.setCount(count.get(0).getCount());
                                    } else {
                                        dailyVo.setCount(0);
                                    }
                                }
                            }
                            dailyDataList.add(dailyVo);
                        }
                );
        vo.setData(dailyDataList);
    }
}
