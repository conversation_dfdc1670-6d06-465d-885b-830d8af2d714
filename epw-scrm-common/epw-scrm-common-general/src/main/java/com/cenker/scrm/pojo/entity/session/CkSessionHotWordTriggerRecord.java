package com.cenker.scrm.pojo.entity.session;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 热词触发记录表
 * @TableName ck_session_hot_word_trigger_record
 */
@TableName(value ="ck_session_hot_word_trigger_record")
@Data
public class CkSessionHotWordTriggerRecord implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 规则ID,对应hot_id
     */
    private Long ruleId;

    /**
     * 热词名称
     */
    private String hotWord;

    /**
     * 近似词，多个逗号分隔
     */
    private String synonWords;

    /**
     * 触发关键词
     */
    private String word;

    /**
     * 消息Id，消息的唯一标识
     */
    private String msgId;

    /**
     * 会话原文
     */
    private String msgContent;

    /**
     * 群聊类型，0表示未知，1表示单聊，2表示群聊
     */
    private Integer chatType;

    /**
     * 发送方Id，同一企业内容为userid，非相同企业为external_userid。消息如果是机器人发出，也为external_userid
     */
    private String fromId;

    /**
     * 群聊消息，群ID
     */
    private String roomId;

    /**
     * 单聊-消息接受者ID
     */
    private String consumeId;

    /**
     * 群聊-消息接受者ID
     */
    private String roomConsumeId;

    /**
     * 消息时间（即触发时间）
     */
    private Date msgTime;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}