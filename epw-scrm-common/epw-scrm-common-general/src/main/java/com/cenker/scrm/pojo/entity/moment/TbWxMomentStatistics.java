package com.cenker.scrm.pojo.entity.moment;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/1/8
 * @Description 朋友圈指标数据统计
 */
@Data
//@TableName(value = "tb_wx_moment_statistics",autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
public class TbWxMomentStatistics {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 朋友圈id
     */
    private String momentId;

    /**
     * 点赞客户数
     */
    private Integer likeCnt;

    /**
     * 评论客户数
     */
    private Integer commentCnt;

    /**
     * 预计送达客户
     */
    private Integer totalSendCustomerCnt;

    /**
     * 已送达客户
     */
    private Integer sendCustomerCnt;

    /**
     * 未送达客户
     */
    private Integer unSendCustomerCnt;

    /**
     * 全部发表成员
     */
    private Integer totalSendUserCnt;

    /**
     * 已发表成员
     */
    private Integer sendUserCnt;

    /**
     * 未发表成员
     */
    private Integer unSendUserCnt;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}

