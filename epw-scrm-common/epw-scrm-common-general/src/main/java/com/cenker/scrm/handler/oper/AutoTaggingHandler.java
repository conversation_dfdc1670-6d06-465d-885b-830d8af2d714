package com.cenker.scrm.handler.oper;

import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.bu.RelatedResource;
import com.cenker.scrm.util.BuOperTrackUtils;

import java.util.List;
/**
 * 自动打标签
 */
public class AutoTaggingHandler extends DefaultBuOperTrackHandler {
    public AutoTaggingHandler(TrackEventTypeEnum trackEventTypeEnum) {
        this.eventType = trackEventTypeEnum;
    }

    @Override
    protected String getContent(OperTrackParams operTrackParams) {
        RelatedResource relatedResource = operTrackParams.getRelatedResource();

        List<String> addTagList = relatedResource.getAddTagList();
        if (addTagList == null || addTagList.isEmpty()) {
            addTagList = relatedResource.getRemoveTagList();
        }
        String contentStr = eventType.getContent();
        contentStr = contentStr.replace("{0}", operTrackParams.getName());
        contentStr = contentStr.replace("{1}", BuOperTrackUtils.buildHtml(addTagList, "tag",null));

        return contentStr;
    }

}
