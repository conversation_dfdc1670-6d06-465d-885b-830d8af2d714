package com.cenker.scrm.enums;

import com.cenker.scrm.util.StringUtils;
import lombok.Getter;

/**
 * 审核类型枚举类
 */
@Getter
public enum ApprovalTypeEnum {
    /**
     * condition 条件 SOP
     * journeysop 旅程 SOP
     * oneseparateone 1V1SOP
     * masscustomer 1V1 群发
     * sendmoments 发朋友圈
     * groupToGroup 社群群发
     */
    CONDITION("condition", "条件 SOP", "modules:condition:btn:reviewTask"),
    JOURNEYSOP("journeysop", "旅程 SOP", "modules:journeysop:btn:reviewTask"),
    ONESEPARATEONE("oneseparateone", "1V1SOP", "modules:oneseparateone:btn:reviewTask"),
    MASSCUSTOMER("masscustomer", "1V1 群发", "modules:massCustomer:btn:reviewTask"),
    SENDMOMENTS("sendmoments", "发朋友圈", "modules:sendMoments:btn:reviewTask"),
    GROUPTOGROUP("groupToGroup", "社群群发", "modules:groupToGroup:btn:reviewTask");
    private String type;

    private String desc;

    private String perms;

    ApprovalTypeEnum(String type, String desc, String perms) {
        this.type = type;
        this.desc = desc;
        this.perms = perms;
    }

    public static ApprovalTypeEnum getType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        for (ApprovalTypeEnum approvalTypeEnum : ApprovalTypeEnum.values()) {
            if (approvalTypeEnum.getType().equals(type)) {
                return approvalTypeEnum;
            }
        }
        return null;
    }
}
