package com.cenker.scrm.pojo.dto.condition;

import com.cenker.scrm.pojo.dto.sop.ConditionSopCustomerDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/7
 * @Description 触发条件值
 */
@Data
public class SopTriggerConditionValueDTO {

    /**
     * 员工条件
     */
    private List<UserConditionDTO> userConditionList;

    /**
     * 群条件
     */
    private List<GroupConditionDTO> groupConditionList;

    /**
     * 数量条件
     */
    private Integer conditionCnt;

    /**
     * 条件客户
     */
    private List<ConditionSopCustomerDTO> customerList;

    /**
     * 智能物料
     */
    private List<RadarConditionDTO> radarConditionList;
}
