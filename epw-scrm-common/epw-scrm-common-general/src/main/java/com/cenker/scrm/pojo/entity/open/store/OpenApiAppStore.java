package com.cenker.scrm.pojo.entity.open.store;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("open_api_app_store")
public class OpenApiAppStore {
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * 企业id
   */
  private Long corpId;
  /**
   * 接入凭证Id
   */
  private Long appInfoId;
  /**
   * 商城类型 1表示H5，2表示小程序...
   */
  private Integer storeType;
  /**
   * 商城名
   */
  private String storeName;
  /**
   * 小程序appId type = 2
   */
  private String appId;
  /**
   * 备注
   */
  private String remark;
  /**
   * 逻辑删除标志
   */
  @TableLogic
  private Integer delFlag;
  private Date createTime;
  private Date updateTime;
  private Long createBy;
  private Long updateBy;
}
