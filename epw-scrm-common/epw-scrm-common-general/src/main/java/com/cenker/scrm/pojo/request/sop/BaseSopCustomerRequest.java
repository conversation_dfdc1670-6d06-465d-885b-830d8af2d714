package com.cenker.scrm.pojo.request.sop;

import com.cenker.scrm.pojo.valid.BatchGroup;
import com.cenker.scrm.pojo.vo.sop.ExternalSopCustomerVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/8
 * @Description sop客户请求体
 */
@Data
public class BaseSopCustomerRequest extends BaseSopRequest {

    /**
     * 外部客户列表
     */
    @NotNull(message = "请选择外部客户",groups = {BatchGroup.class})
    @Size(min = 1, message = "请至少选择一个外部客户",groups = {BatchGroup.class})
    private List<ExternalSopCustomerVO> externalSopCustomerList;
}
