package com.cenker.scrm.pojo.entity.statistic;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 数据统计-员工数据
 *
 * <AUTHOR>
 * @since 2024-04-16 17:02
 */
@Data
@TableName("tb_statistic_staff")
public class TbStatisticStaff {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", dateFormat = "yyyy-MM-dd", sort = 1)
    private Date statisticDate;

    /**
     * 员工UserID
     */
    @Excel(name = "员工编号", sort = 2)
    private String userid;

    /**
     * 员工名称
     */
    @Excel(name = "员工名称", sort = 3)
    private String userName;

    /**
     * 客户总数量
     */
    @Excel(name = "客户总数", sort = 4)
    private Integer customerTotalNum;

    /**
     * 客户新增数
     */
    @Excel(name = "新增客户数", sort = 5)
    private Integer customerAddNum;

    /**
     * 客户被删除/拉黑数量
     */
    @Excel(name = "被删除次数", sort = 6)
    private Integer customerDelNum;

    /**
     * 客户聊天数量
     */
    @Excel(name = "聊天客户总数", sort = 7)
    private Integer customerChatNum;

    /**
     * 单聊发消息数量
     */
    @Excel(name = "单聊发消息数", sort = 8)
    private Integer aloneChatNum;

    /**
     * 总会话数（客户单聊发送消息数）
     */
    private Integer totalConversationNum;

    /**
     * 及时回复聊天数
     */
    private Integer replyTimelyNum;

    /**
     * 总回复时长（单位：秒）
     */
    private Integer totalConversationDuration;

    /**
     * 及时回复聊天占比(%)
     */
    @Excel(name = "单聊及时回复聊天占比(%)", sort = 9)
    @TableField(exist = false)
    private BigDecimal chatTimelyRate;

    /**
     * 平均回复时长(S)
     */
    @Excel(name = "单聊平均回复时长(S)", sort = 10)
    @TableField(exist = false)
    private Integer averageReplyTime;

    /**
     * 员工管理群聊总数
     */
    @Excel(name = "员工管理群聊总数", sort = 11)
    private Integer staffGroupManageNum;

    /**
     * 员工加入群聊总数
     */
    @Excel(name = "员工加入的群聊总数", sort = 12)
    private Integer staffGroupJoinNum;

    /**
     * 员工群聊发消息总数
     */
    @Excel(name = "员工群聊发消息数", sort = 13)
    private Integer staffGroupChatNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 部门id
     */
    private Integer deptId;

}
