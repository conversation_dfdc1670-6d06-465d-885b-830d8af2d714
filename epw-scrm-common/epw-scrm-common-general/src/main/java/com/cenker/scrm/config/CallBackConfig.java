package com.cenker.scrm.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Title: Config
 * @Package: com.examination.bean
 * @Description: 描述
 * @author: db.xie
 * @date: 2021/1/30-10:52
 * @version: V1.0
 */
@Configuration
@ConfigurationProperties(prefix = "call-back")
public class CallBackConfig {

    public static String addBookCallBackUrl;

    public static String custMessageCallBackUrl;

    public static String appMessageCallbackUrl;

    public String getAddBookCallBackUrl() {
        return addBookCallBackUrl;
    }

    public void setAddBookCallBackUrl(String addBookCallBackUrl) {
        CallBackConfig.addBookCallBackUrl = addBookCallBackUrl;
    }

    public String getCustMessageCallBackUrl() {
        return custMessageCallBackUrl;
    }

    public void setCustMessageCallBackUrl(String custMessageCallBackUrl) {
        CallBackConfig.custMessageCallBackUrl = custMessageCallBackUrl;
    }

    public String getAppMessageCallbackUrl() {
        return appMessageCallbackUrl;
    }

    public void setAppMessageCallbackUrl(String appMessageCallbackUrl) {
        CallBackConfig.appMessageCallbackUrl = appMessageCallbackUrl;
    }
}
