package com.cenker.scrm.pojo.dto;

import com.cenker.scrm.pojo.dto.external.TagDetailList;
import com.cenker.scrm.pojo.vo.TagVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 为客户添加企业标签的MQ消息体内容
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CustomerTagMsgDto {


    /**
     * 待添加、删除的标签的客户信息
     */
    private List<CustomerTagDto> customerTagList;

    /**
     * 操作员工Id
     */
    private String operatorUserId;

    /**
     * 操作员工昵称
     */
    private String operatorNickName;

    /**
     * 标签来源类型
     */
    private String tagSource;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否重试，默认false
     * 非重试场景，如果失败，会插入一条重试任务
     */
    private boolean isRetry;

    /**
     * 需要打标签、移除标签的列表
     */
    private List<TagVO> tagDetailLists;

    /**
     * 是否进行消息通知
     */
    private boolean isMsgNotify;

    /**
     * 是否添加轨迹
     */
    private boolean isAddTrajectory;

    /**
     * 客户标签类型: 添加-BIND， 移除-UNBIND
     */
    private String tagMode;

    /**
     * 失败客户数量
     */
    private Long failedCount;

    /**
     * 客户数量
     */
    private Integer customerCount;

    @Data
    @Builder
    public static class CustomerTagDto {

        private String corpId;

        private String userId;

        private String externalUserId;
        /**
         * 标签集合(映射对象：TagVO）
         */
        private List<TagVO> addTagList;

        /**
         * 标签集合(映射对象：TagVO）
         */
        private List<TagVO> removeTagList;
    }
}
