package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TbWxExtJourneyCustomerStage {
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  /**
   * 客户旅程id
   */
  private Long journeyInfoId;
  /**
   * 原阶段id
   */
  private Long originalStageId;
  /**
   * 阶段id
   */
  private Long stageId;
  /**
   * 外部客户id
   */
  private Long extCustomerId;

  @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
  private Date joinTime;

  private String remark;

//  private String externalUserId;

  /**
   * 企业id
   */
  private Long corpId;
  @TableLogic
  private Integer delFlag;
  private Long createBy;
  @TableField(fill = FieldFill.INSERT)
  private Date createTime;
  private Long updateBy;
  @TableField(fill = FieldFill.UPDATE)
  private Date updateTime;
}
