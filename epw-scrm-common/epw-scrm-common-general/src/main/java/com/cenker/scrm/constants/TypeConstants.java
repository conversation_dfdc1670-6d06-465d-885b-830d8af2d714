package com.cenker.scrm.constants;

/**
 * 类型常量
 */
public interface TypeConstants {

    /**
     * 是否过期 0 否 1 是
     */
    int DATA_EXPIRE_TRUE = 1;
    int DATA_EXPIRE_FALSE = 0;

    /**
     * h5登录类型 1 工作台 2 素材 3 客户画像 4 商城运营
     */
    int LOGIN_FROM_WORKBENCH = 1;
    int LOGIN_FROM_MATERIAL = 2;
    int LOGIN_FROM_PORTRAIT = 3;
    int LOGIN_FROM_STORE = 4;

    /**
     * 智能物料类型 1 图文 2 链接 3 pdf
     */
    int RADAR_TYPE_CONTENT = 1;
    int RADAR_TYPE_LINK = 2;
    int RADAR_TYPE_PDF = 3;


    /**
     * 智能物料点击渠道 1 侧边栏 2 朋友圈 3 工作台 4 渠道活码 5 企业话术 6 群发客户
     */
    int RADAR_SOURCE_SIDEBAR = 1;
    int RADAR_SOURCE_MOMENT = 2;
    int RADAR_SOURCE_WORKBENCH = 3;
    int RADAR_SOURCE_CONTACT = 4;
    int RADAR_SOURCE_CORP_QUICK_REPLY = 5;
    int RADAR_SOURCE_MASS_MESSAGE = 6;

    /**
     * 轨迹类型(1:信息动态;2:社交动态;3:活动规则;4:待办动态)
     */
    Integer MESSAGE_DYNAMIC = 1;
    Integer WAIT_TO_DO_DYNAMIC = 4;

    /**
     * 员工活码 联系方式类型 联系方式类型,1-单人, 2-多人
     */
    int CONTACT_SINGLE = 1;
    int CONTACT_MULTI = 2;

    /**
     * 1二维码 2工卡
     */
    int CONTACT_IMG_CODE = 1;
    int CONTACT_IMG_CARD = 2;

    /**
     * 外部客户添加时是否无需验证 1 是 0 否
     */
    int SKIP_VERIFY_TRUE = 1;
    int SKIP_VERIFY_FALSE = 0;

    /**
     * tag
     */
    // 企业标签类型 1 企业标签 2 个人标签
    String CORP_TAG_TYPE = "1";
    String PERSON_TAG_TYPE = "2";

    // 添加标签规则类型 1 点击次数 2 阅读时长 3 转发次数
    int RADAR_ADD_TAG_RULE_TYPE_CLICK = 1;
    int RADAR_ADD_TAG_RULE_TYPE_READ = 2;
    int RADAR_ADD_TAG_RULE_TYPE_FORWARD = 3;


    /**
     * 动态类型 1、更新信息 2、更新标签 3、浏览转发 4、更新好友关系 5、跟进 6、社群关系 7、朋友圈 8、客户标签
     */
    int CUSTOMER_TRAJECTORY_INFORMATION_INFO = 1;
    int CUSTOMER_TRAJECTORY_INFORMATION_TAG = 2;
    int CUSTOMER_TRAJECTORY_INFORMATION_VIEW = 3;
    int CUSTOMER_TRAJECTORY_INFORMATION_RELATION = 4;
    int CUSTOMER_TRAJECTORY_INFORMATION_FOLLOW = 5;
    int CUSTOMER_TRAJECTORY_INFORMATION_GROUP = 6;
    int CUSTOMER_TRAJECTORY_INFORMATION_MOMENT = 7;
    int CUSTOMER_TRAJECTORY_INFORMATION_BATCH_TAG = 8;

    // 行为类型 1 员工行为 2 系统行为 3 客户行为
    int CUSTOMER_TRAJECTORY_STAFF_ACTION = 1;
    int CUSTOMER_TRAJECTORY_SYSTEM_ACTION = 2;
    int CUSTOMER_TRAJECTORY_CUSTOMER_ACTION = 3;

    /**
     * 朋友圈 1 点赞 2 评论
     */
    int MOMENT_CUSTOMER_LIKE = 1;
    int MOMENT_CUSTOMER_COMMENT = 2;

    /**
     * 引流短链 1 加人 2 加群
     */
    int DRAINAGE_SHORT_LINK_TYPE_CONTACT = 1;
    int DRAINAGE_SHORT_LINK_TYPE_GROUP = 2;

    int LOGIN_IP_FROM_WEB = 1;
    int LOGIN_IP_FROM_H5 = 2;

    /**
     * 创建来源 1 web 2 侧边栏 3 工作台
     */
    int CREATE_SOURCE_SINCE_WEB = 1;
    int CREATE_SOURCE_SINCE_SIDE = 2;
    int CREATE_SOURCE_SINCE_WORKBENCH = 3;

    /**
     * 安装代自建 1 正常 2 从未安装 3 安装后删除
     */
    int INSTALL_APP_PERMANENT_STATUS_NORMAL = 1;
    int INSTALL_APP_PERMANENT_STATUS_NEVER = 2;
    int INSTALL_APP_PERMANENT_STATUS_UNINSTALL = 3;

    /**
     * 1 代自建应用 2 标准应用
     */
    int SELF_BUILT_AGENT = 1;
    int THIRD_PARTY_AGENT = 2;

    /**
     * 群发消息 发送范围 0 全部客户/群  1 指定客户/群主
     */
    int MASS_MESSAGE_PUSHRANGE_ALL = 0;
    int MASS_MESSAGE_PUSHRANGE_ASSIGN = 1;
    /**
     * 是否筛选客户
     */
    int MASS_MESSAGE_SELECT_ALLOW = 0;
    int MASS_MESSAGE_SELECT_REFUSE = 1;

    /**
     * 群发消息 发送状态 0 未开始 1 进行中 2 取消发送 3 发送失败 4 创建中 5 任务完成
     */
    int MASS_MESSAGE_UN_SEND = 0;
    int MASS_MESSAGE_SEND = 1;
    int MASS_MESSAGE_CANCEL_SEND = 2;
    int MASS_MESSAGE_SEND_FAIL = 3;
    int MASS_MESSAGE_SEND_ING = 4;
    int MASS_MESSAGE_SEND_FINISH = 5;

    /**
     * 任务状态 0 未开始 1 进行中 2 已取消 3 创建失败 4 任务完成
     */
    int TASK_STATUS_WAIT = 0;
    int TASK_STATUS_DOING = 1;
    int TASK_STATUS_CANCEL = 2;
    int TASK_STATUS_FAIL = 3;
    int TASK_STATUS_FINISH = 4;

    /**
     * 消息状态 1 已发送 0 发送失败
     */
    int MASS_MESSAGE_STATUS_SUCCESS = 1;
    int MASS_MESSAGE_STATUS_FAIL = 0;

    /**
     * 发送状态 0-未发送 1-已发送 2-因客户不是好友导致发送失败 3-因客户已经收到其他群发消息导致发送失败
     */
    int SEND_STATUS_UNSEND = 0;
    int SEND_STATUS_SUCCESS = 1;
    int SEND_STATUS_FAIL_NOT_FRIEND = 2;
    int SEND_STATUS_FAIL_ALREADY_RECEIVE = 3;

    /**
     * 区域类型
     */
    int CONTACT_COUNTRY = 0;
    int CONTACT_PROVINCE = 1;
    int CONTACT_CITY = 2;
    int CONTACT_DISTRICT = 3;
    int CONTACT_SITE = 4;
    int CONTACT_STORE = 5;

    /**
     * 标识类型 1 城市活码 2 门店 3 配送员
     */
    int SIGN_CITY_CONTACT = 1;
    int SIGN_STORE = 2;
    int SIGN_DELIVERY = 3;

    /**
     * 用户注册平台 0未知 1 web 2 h5 3 小程序
     **/
    int REGISTER_FROM_WEB = 1;
    int REGISTER_FROM_H5 = 2;
    int REGISTER_FROM_APPLET = 3;

    /***任务裂变***/
    Integer USER_FISSION = 1;
    /***群裂变***/
    Integer GROUP_FISSION = 2;


    /**
     * sop类型 1 条件sop 2 旅程sop 3 1V1sop 4 社群sop
     */
    int SOP_TYPE_OF_CONDITION = 1;
    int SOP_TYPE_OF_JOURNEY = 2;
    int SOP_TYPE_OF_MASS_CUSTOMER = 3;

    /**
     * 触发类型 1 加好友 2 加群 3 浏览智能物料 4 累计消费 5 购买商品
     */
    int SOP_TRIGGER_CONDITION_ADD_USER_TYPE = 1;
    int SOP_TRIGGER_CONDITION_ADD_GROUP_TYPE = 2;
    int SOP_TRIGGER_CONDITION_VIEW_RADAR_TYPE = 3;

    /**
     * 触发条件类型
     * 1 指定好友/指定群/浏览指定智能物料 2 添加好友数/添加群数/转发指定智能物料的
     * 3 浏览全部智能物料总次数  4 浏览全部智能物料总时长（单位：秒） 5 浏览全部智能物料的转发数
     */
    int SOP_TRIGGER_CONDITION_ADD_USER_TYPE_1 = 1;
    int SOP_TRIGGER_CONDITION_ADD_USER_TYPE_2 = 2;
    int SOP_TRIGGER_CONDITION_ADD_USER_TYPE_3 = 3;
    int SOP_TRIGGER_CONDITION_ADD_USER_TYPE_4 = 4;
    int SOP_TRIGGER_CONDITION_ADD_USER_TYPE_5 = 5;
    int SOP_TRIGGER_CONDITION_ADD_USER_TYPE_6 = 6;
    int SOP_TRIGGER_CONDITION_ADD_USER_TYPE_7 = 7;

    /**
     * 触发关系类型 1 等于 2 不等于 3 包含 4 不包含 5 大于 6 小于 7 大于等于 8 小于等于
     */
    int SOP_TRIGGER_CONDITION_RELATION_EQ = 1;
    int SOP_TRIGGER_CONDITION_RELATION_NE = 2;
    int SOP_TRIGGER_CONDITION_RELATION_EXCLUDE = 3;
    int SOP_TRIGGER_CONDITION_RELATION_EXCLUSIVE = 4;
    int SOP_TRIGGER_CONDITION_RELATION_GT = 5;
    int SOP_TRIGGER_CONDITION_RELATION_LT = 6;
    int SOP_TRIGGER_CONDITION_RELATION_GE = 7;
    int SOP_TRIGGER_CONDITION_RELATION_LE = 8;

    /**
     * sop任务执行状态 1 已完成 2 未完成 3 已过期
     */
    int SOP_TASK_EXECUTE_STATUS_UNFINISHED = 2;

    /**
     * 群发消息场景
     */
    int MASS_MESSAGE_SCENE_CORP = 1;
    int MASS_MESSAGE_SCENE_PERSON = 2;
    int MASS_MESSAGE_SCENE_SOP_CORP = 3;

    /**
     * 结束重复 0 永不 1 指定日期
     */
    Integer SOP_REPEAT_EXPIRE_TYPE_0 = 0;
    Integer SOP_REPEAT_EXPIRE_TYPE_1 = 1;

    /**
     * 社群活码分类 1 顺序入群 2 分类入群
     */
    int GROUP_CODE_SEQUENCE_ADD_TYPE = 1;

    String DEPT_SCOPE_1 = "1";
    String DEPT_SCOPE_2 = "2";
    String DEPT_SCOPE_3 = "3";
    String DEPT_SCOPE_4 = "4";


    String SENS_INTERCEPT_TYPE_1 = "1";
    String SENS_INTERCEPT_TYPE_3 = "3";
    String SENS_INTERCEPT_TYPE_2 = "2";

    /*调新增规则接口失败**/
    String SENS_SYNC_WECHAT_FLAG_0 = "0";
    /*调接口成功**/
    String SENS_SYNC_WECHAT_FLAG_1 = "1";
    /*调修改接口失败**/
    String SENS_SYNC_WECHAT_FLAG_2 = "2";
    /*调删除接口失败**/
    String SENS_SYNC_WECHAT_FLAG_3 = "3";

    /**
     * 客户标签类型: 添加-BIND， 移除-UNBIND
     */
    String CUSTOMER_TAG_BIND = "BIND";
    String CUSTOMER_TAG_UNBIND = "UNBIND";

    /**
     * 栏目数据类型 today 今日 history 历史
     */
    String SECTION_TYPE_TODAY = "today";
    String SECTION_TYPE_HISTORY = "history";

    /**
     * 是否对客验证类型 subMenu 子菜单  section 栏目
     */
    String VERIFY_TYPE_SUBMENU = "subMenu";
    String VERIFY_TYPE_SECTION = "section";
}
