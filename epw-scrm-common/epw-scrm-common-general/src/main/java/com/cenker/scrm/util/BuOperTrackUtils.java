package com.cenker.scrm.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cenker.scrm.enums.*;
import com.cenker.scrm.pojo.dto.condition.StageConditionDTO;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.entity.enums.OperRelEnum;
import com.cenker.scrm.pojo.entity.enums.QryWhereEnum;
import com.cenker.scrm.pojo.entity.wechat.TbDcUserTags;
import com.cenker.scrm.pojo.vo.base.AjaxResult;
import com.cenker.scrm.pojo.vo.bu.BuOperTrackVO;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户动态工具类
 * <AUTHOR>
 */
public class BuOperTrackUtils {
    /**
     * 构建span标签
     * @param content
     * @param className
     * @param sb
     * @return
     */
    public static StringBuilder buildSpanHtml(String content, String className, StringBuilder sb) {
        if (sb == null) {
            sb = new StringBuilder();
        }
        // 增加判断 如果className 为空，则不添加class
        if (StringUtils.isNotBlank(className)) {
            sb.append("<span class=\"").append(className).append("\">").append(content).append("</span>");
        } else {
            sb.append("<span>").append(content).append("</span>");
        }

        return sb;
    }

    /**
     * 构建动态广场前端需要的html标签
     * @param tagList
     * @param className
     * @param sb
     * @return
     */
    public static String buildHtml(List<String> tagList, String className, StringBuilder sb) {
        if (CollectionUtils.isEmpty(tagList)) {
            return Strings.EMPTY;
        }
        if (sb == null) {
            sb = new StringBuilder();
        }
        for (String tag : tagList) {
            buildSpanHtml(tag, className, sb);
        }
        return sb.toString();
    }
    /**
     * 根据事件动态事件eventType获取其所属的TrackTypeEnum
     *
     * @param eventCode
     * @param fromWechat
     * @return
     */
    public static TrackTypeEnum getTrackTypeEnum(String eventCode, boolean fromWechat) {
        TrackTypeEnum current = TrackTypeEnum.CUSTOMER;
        if (eventCode.startsWith("CUSTOMER_INFO_EDIT")
                ||eventCode.startsWith("CORPORATE_TAG_EDIT")
                ||eventCode.startsWith("CORPORATE_TAG_EDIT_SYNC")
                || eventCode.startsWith("PERSONAL_TAG_EDIT")
                || eventCode.startsWith("BULK_EDIT_TAG")
                || eventCode.startsWith("CUSTOMER_CHURN_CUST")
                || eventCode.startsWith("GROUP_JOIN_INVITE")
                || eventCode.startsWith("GROUP_LEAVE_REMOVED")
        ) {
            current =  TrackTypeEnum.BUSINESS;
        } else if (eventCode.startsWith("CUSTOMER_SUBSCR_EDIT")) {
            // 编辑客户订阅
            if (!fromWechat) {
                // 不是来自微信端，则是员工操作，记录为企业动态
                return TrackTypeEnum.BUSINESS;
            }
        }
        return current;
    };

    /**
     * 构建批量编辑标签且全选时候的查询条件 描述
     * @param dto
     * @param list
     * @return
     */
    public static String buildCondition(CustomerChurnDTO dto, List<TbDcUserTags> list) {
        StringBuilder sb = new StringBuilder();

        if (BatchTagType.ATTR.name().equals(dto.getSearchType())) {
            appendIfNotBlank(sb, "客户名称：", dto.getCustomerName());
            appendIfNotBlank(sb, "客户unionid：", dto.getUnionId());
            appendIfNotNull(sb, "认证状态：", dto.getIsAuth(), auth -> auth ? "已认证" : "未认证");
            appendIfNotBlank(sb, "添加人：", dto.getCorpUserIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
            appendIfNotBlank(sb, "企业标签：", dto.getTag());
            appendIfNotBlank(sb, "渠道来源：", dto.getAddWay());
            appendIfNotNull(sb, "客户类型：", dto.getType(), type -> type == 1 ? "微信客户" : "企业客户");
            if (StringUtils.isNotBlank(dto.getBeginTime())) {
                sb.append("首次添加时间：").append(dto.getBeginTime()).append("-").append(dto.getEndTime()).append("，");
            }
//            appendIfNotBlank(sb, "首次添加时间：", dto.getBeginTime(), time -> time + "-" + dto.getEndTime());
            appendStageConditions(sb, dto.getStageConditionList());

            if (sb.length() > 0) {
                removeLastComma(sb);
            }
        } else {
            parseCustomCondition(sb, dto.getCondition(), list);
        }

        return sb.toString();
    }

    /**
     * 拼接查询条件，有勾选就拼接上
     * @param sb
     * @param label
     * @param value
     */
    private static void appendIfNotBlank(StringBuilder sb, String label, String value) {
        if (StrUtil.isNotBlank(value)) {
            sb.append(label).append(value).append("，");
        }
    }
    /**
     * 拼接查询条件，有勾选就拼接上
     * 带转换器
     * @param sb
     * @param label
     * @param value
     */
    private static <T> void appendIfNotNull(StringBuilder sb, String label, T value, Function<T, String> mapper) {
        if (value != null) {
            sb.append(label).append(mapper.apply(value)).append("，");
        }
    }
    /**
     * 拼接查询条件 客户旅程
     */
    private static void appendStageConditions(StringBuilder sb, List<StageConditionDTO> stageConditionList) {
        if (!CollectionUtils.isEmpty(stageConditionList)) {
            String conditions = stageConditionList.stream()
                    .map(stage -> "阶段：" + stage.getStageName() + ":" + stage.getJourneyName())
                    .collect(Collectors.joining(","));
            sb.append("客户旅程：").append(conditions).append("，");
        }
    }

    /**
     * 移除最后一个逗号
     * @param sb
     */
    private static void removeLastComma(StringBuilder sb) {
        if (sb.length() > 0 && sb.charAt(sb.length() - 1) == '，') {
            sb.deleteCharAt(sb.length() - 1);
        }
    }

    /**
     * 将自定义JSON字符串解析成查询条件
     * @param sb
     * @param conditionJson
     * @param list
     */
    private static void parseCustomCondition(StringBuilder sb, String conditionJson, List<TbDcUserTags> list) {
        if (StrUtil.isBlank(conditionJson)) {
            return;
        }

        JSONObject condition = JSONObject.parseObject(conditionJson);
        JSONArray orList = condition.getJSONArray("orList");

        if (CollectionUtil.isNotEmpty(orList)) {
            for (Object qryDtoObj : orList) {
                if (sb.length() > 0) {
                    sb.append(" 或 ");
                }

                JSONObject qryDto = (JSONObject) qryDtoObj;
                sb.append("(");

                JSONArray andList = qryDto.getJSONArray("andList");
                if (CollectionUtil.isNotEmpty(andList)) {
                    for (Object whereVoObj : andList) {
                        JSONObject whereVo = (JSONObject) whereVoObj;

                        if (StrUtil.isBlank(whereVo.getString("conditionValue")) || StrUtil.isBlank(whereVo.getString("operRelId"))) {
                            continue;
                        }

                        String qryTypeName = whereVo.getString("qryTypeName");
                        String qryType = whereVo.getString("qryType");
                        sb.append(qryTypeName);
                        if (QryWhereEnum.QRY_BUSINESS_DATA.getValue().equals(qryType)) {
                            Map<String, TbDcUserTags> columnMap = list.stream().collect(Collectors.toMap(TbDcUserTags::getReferenceColumn, Function.identity()));
                            JSONArray rawConditionValue = whereVo.getJSONArray("rawConditionValue");
                            rawConditionValue.forEach(rawValue -> {
                                TbDcUserTags dcUserTags = columnMap.get(whereVo.getString("conditionKey"));
                                sb.append("：").append(dcUserTags.getTagName());
                            });
                        }

                        if (QryWhereEnum.QRY_USER_DEFINED.getValue().equals(qryType)) {
                            Object rawConditionValue = whereVo.get("rawConditionKey");
                            sb.append(rawConditionValue);
                        }
                        parseOperRel(sb, whereVo);
                        parseQueryType(sb, whereVo, list);
                        sb.append("，");
                    }

                    if (sb.charAt(sb.length() - 1) == '，') {
                        sb.deleteCharAt(sb.length() - 1);
                    }
                }

                sb.append(")");
            }
        }
    }

    /**
     * 解析查询条件
     * @param sb
     * @param whereVo
     * @param list
     */
    private static void parseQueryType(StringBuilder sb, JSONObject whereVo, List<TbDcUserTags> list) {
        String qryType = whereVo.getString("qryType");

        if (QryWhereEnum.QRY_BUSINESS_DATA.getValue().equals(qryType)) {
            Map<String, TbDcUserTags> columnMap = list.stream()
                    .collect(Collectors.toMap(TbDcUserTags::getReferenceColumn, Function.identity()));
            JSONArray rawConditionValue = whereVo.getJSONArray("rawConditionValue");

            rawConditionValue.forEach(rawValue -> {
                TbDcUserTags dcUserTags = columnMap.get(whereVo.getString("conditionKey"));
                sb.append(dcUserTags.getTagName());
            });
        } else if (QryWhereEnum.QRY_USER_TAG.getValue().equals(qryType)) {
            JSONArray rawConditionValue = whereVo.getJSONArray("rawConditionValue");
            rawConditionValue.forEach(rawValue -> {
                JSONObject rawValueObj = (JSONObject) rawValue;
                sb.append(rawValueObj.getString("groupName"))
                        .append(":")
                        .append(rawValueObj.getString("tagName"));
            });
        } else if (QryWhereEnum.QRY_JOURNERY.getValue().equals(qryType)) {
            JSONArray rawConditionValue = whereVo.getJSONArray("rawConditionValue");
            rawConditionValue.forEach(rawValue -> {
                JSONObject rawValueObj = (JSONObject) rawValue;
                sb.append(rawValueObj.getString("stageName"))
                        .append(StringUtils.isNotNull(rawValueObj.getString("journeyName")) ? ":" + rawValueObj.getString("journeyName") : "");
            });
        } else if (QryWhereEnum.QRY_USER_ID.getValue().equals(qryType)) {
            JSONArray rawConditionValue = whereVo.getJSONArray("rawConditionValue");
            rawConditionValue.forEach(rawValue -> {
                JSONObject rawValueObj = (JSONObject) rawValue;
                sb.append(rawValueObj.getString("userName"));
            });
        } else if (QryWhereEnum.QRY_ADD_DATE.getValue().equals(qryType)) {
            String conditionKey = whereVo.getString("conditionKey");
            if (QryWhereEnum.CONDITION_KEY_ADD_DATE.getValue().equals(conditionKey)
                    || QryWhereEnum.CONDITION_KEY_FIRST_ADD_DATE.getValue().equals(conditionKey)) {
                sb.append(whereVo.getString("conditionValue"));
            }
        } else if (QryWhereEnum.QRY_USER_DEFINED.getValue().equals(qryType)) {
            sb.append(whereVo.get("rawConditionKey"));
        } else if (QryWhereEnum.QRY_AUTH_STATUS.getValue().equals(qryType)) {
            JSONArray rawConditionValue = whereVo.getJSONArray("rawConditionValue");
            rawConditionValue.forEach(rawValue -> {
                sb.append("认证状态：").append(Objects.equals(rawValue, "1") ? "已认证" : "未认证");
            });
        }
    }

    /**
     * 解析查询条件关系
     * @param sb
     * @param whereVo
     */
    private static void parseOperRel(StringBuilder sb, JSONObject whereVo) {
        String operRelId = whereVo.getString("operRelId");

        if (OperRelEnum.OPER_REL_NOTIN.getValue().equals(operRelId)) {
            sb.append(OperRelEnum.OPER_REL_NOTIN.getDesc());
        } else if (OperRelEnum.OPER_REL_IN.getValue().equals(operRelId)) {
            sb.append(OperRelEnum.OPER_REL_IN.getDesc());
        } else if (OperRelEnum.OPER_REL_LIKE.getValue().equals(operRelId)) {
            sb.append(OperRelEnum.OPER_REL_LIKE.getDesc());
        } else if (OperRelEnum.OPER_REL_NOTLIKE.getValue().equals(operRelId)) {
            sb.append(OperRelEnum.OPER_REL_NOTLIKE.getDesc());
        } else if (OperRelEnum.OPER_REL_EQ.getValue().equals(operRelId)) {
            sb.append(OperRelEnum.OPER_REL_EQ.getDesc());
        } else if (OperRelEnum.OPER_REL_GT.getValue().equals(operRelId)) {
            sb.append(OperRelEnum.OPER_REL_GT.getDesc());
        } else if (OperRelEnum.OPER_REL_LT.getValue().equals(operRelId)) {
            sb.append(OperRelEnum.OPER_REL_LT.getDesc());
        } else if (OperRelEnum.OPER_REL_GE.getValue().equals(operRelId)) {
            sb.append(OperRelEnum.OPER_REL_GE.getDesc());
        } else if (OperRelEnum.OPER_REL_LE.getValue().equals(operRelId)) {
            sb.append(OperRelEnum.OPER_REL_LE.getDesc());
        } else {
            sb.append(OperRelEnum.OPER_REL_NE.getDesc());
        }
    }
    /**
     * 根据添加方式获取事件类型
     * @param addWay
     * @return
     */
    public static TrackEventTypeEnum getTrackEventTypeEnumByAddWay(String addWay) {
        if (CustomerAddWay.ADD_BY_RECRUIT_LINK.getAddWay().equals(addWay)) {
            return TrackEventTypeEnum.CUSTOMER_ADD_LINK;
        } else if (CustomerAddWay.SCAN_QR_CODE.getAddWay().equals(addWay)) {
            return TrackEventTypeEnum.CUSTOMER_ADD_RADAR;
        }
        return TrackEventTypeEnum.CUSTOMER_ADD_OTHER;
    }

    public static void dealData(List<BuOperTrackVO> rows, String trackType, Map map) {
        // 处理标题
        dealTitle(rows, trackType);
        // 填充子标题
        fillSubTitle(rows, trackType, map);
    }

    private static void dealTitle(List<BuOperTrackVO> rows, String trackType) {
        if (CollectionUtil.isNotEmpty(rows)) {
            rows.forEach(row -> {
                TrackEventTypeEnum eventType = TrackEventTypeEnum.getTrackEventTypeBySubEventType(row.getSubEventType());
                if (eventType == null) {
                    return;
                }
                if (StringUtils.isEmpty(trackType)) {
                    // 客户画像、客户详情中的 【批量编辑标签】批量添加类型 对应的删除标签操作，显示 编辑企业标签
                    if (row.getSubEventType().equals(TrackEventTypeEnum.BULK_EDIT_TAG_ADD.getSubEventType())
                            && row.getId() != null && row.getId().endsWith(OperTypeEnum.DEL.getCode())) {
                        row.setTitle(TrackEventTypeEnum.BULK_EDIT_TAG_REMOVE.getTitle());
                    }
                }
            });
        }
    }

    /**
     * 填充子标题
     */
    private static void fillSubTitle(List<BuOperTrackVO> rows, String trackType, Map map) {
        if (CollectionUtil.isNotEmpty(rows)) {
            rows.forEach(row -> {
                TrackEventTypeEnum eventType = TrackEventTypeEnum.getTrackEventTypeBySubEventType(row.getSubEventType());
                if (eventType == null) {
                    return;
                }
                row.setSubTitle(eventType.getSubTitle());
                if (row.getEventType().equals(TrackEventTypeEnum.SMART_MATERIAL_ACTION_VIEW.getEventType())) {
                    List<LinkedHashMap> data = (List<LinkedHashMap>) map.get(AjaxResult.DATA_TAG);
                    Map<Object, Object> dictMap = data.stream().collect(Collectors.toMap(x -> x.get("dictValue"), x -> x.get("dictLabel")+"发送"));
                    if (row.getRelatedResource() != null && row.getRelatedResource().getLinkVo() != null) {
                        Integer clickSource = row.getRelatedResource().getLinkVo().getClickSource();
                        if (clickSource != null) {
                            // 处理 clickSource 的逻辑
                        row.setSubTitle(String.valueOf(dictMap.get(clickSource+"")));
                        }
                    }
                } else if (TrackTypeEnum.BUSINESS.getCode().equals(trackType)) {
                    // 运营动态中
                    // 批量编辑标签 批量添加/批量添加中，当content不包含”为通过”，将subTitle设置为空字符
                    if (row.getSubEventType().equals(TrackEventTypeEnum.BULK_EDIT_TAG_REMOVE.getSubEventType())
                            || row.getSubEventType().equals(TrackEventTypeEnum.BULK_EDIT_TAG_ADD.getSubEventType())) {
                        row.setSubTitle("");
                    } else if (row.getSubEventType().equals(TrackEventTypeEnum.CUSTOMER_SUBSCR_EDIT_REMOVE.getSubEventType())
                            || row.getSubEventType().equals(TrackEventTypeEnum.CUSTOMER_SUBSCR_EDIT_ADD.getSubEventType())) {
                        // 客户订阅事件 不显示
                        row.setSubTitle("");
                    }
                } else if (StringUtils.isEmpty(trackType)) {
                    // 客户画像、客户详情中的 员工删除客户类型
                    if (row.getSubEventType().equals(TrackEventTypeEnum.CUSTOMER_CHURN_CUST.getSubEventType())) {
                        row.setSubTitle("删除");
                    }
                }
            });
        }
    }
}
