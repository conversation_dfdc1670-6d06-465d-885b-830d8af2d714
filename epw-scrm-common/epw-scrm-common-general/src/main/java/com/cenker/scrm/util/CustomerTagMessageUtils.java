package com.cenker.scrm.util;

import com.alibaba.fastjson.JSON;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.enums.TagSource;
import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.pojo.dto.CustomerAddTagMsgDto;
import com.cenker.scrm.pojo.dto.CustomerRemoveTagMsgDto;
import com.cenker.scrm.pojo.dto.external.CustomerChurnDTO;
import com.cenker.scrm.pojo.entity.system.SysUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxAutoTagRecord;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.vo.TagVO;
import com.google.common.collect.Lists;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;

import java.util.Collections;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2025/03/21
 */
@Slf4j
public class CustomerTagMessageUtils {

    /**
     * 智能物料打标签
     */
    public static CustomerAddTagMsgDto genCustomerAddTagMsgDto(String corpId, TbWxExtCustomer customer, List<TagVO> tagVos, String userId, boolean fromCallback, TrackEventTypeEnum eventType, String name) {
        return buildCustomerAddTagMsgDto(corpId, Lists.newArrayList(customer.getExternalUserId()),
                tagVos, TagSource.MATERIAL, false, userId, fromCallback, eventType, name).build();
    }

    /**
     * 条件打标签
     */
    public static CustomerAddTagMsgDto genCustomerAddTagMsgDto(String externalUserId, String corpId, List<TagVO> filteredTagList, String operId, TbWxAutoTagRecord autoTagRecord, TrackEventTypeEnum eventType, String name, String userId) {
        return buildCustomerAddTagMsgDto(corpId, Lists.newArrayList(externalUserId), filteredTagList, TagSource.CONDITION, false, Constants.DEFAULT_USER, false, eventType, name)
                .operId(operId)
                .autoTagRecord(autoTagRecord)
                .userId(userId)
                .build();
    }

    /**
     * 智能表单打标签
     */
    public static CustomerAddTagMsgDto genCustomerAddTagMsgDto(String corpId, TbWxExtCustomer wxExtCustomer, List<TagVO> tagList, String remark, TrackEventTypeEnum eventType, String name) {
        return buildCustomerAddTagMsgDto(corpId,
                Collections.singletonList(wxExtCustomer.getExternalUserId()),
                tagList, TagSource.SMART_FORM, false, null, false, eventType, name).build();
    }

    /**
     * 社群活码打标签
     */
    public static CustomerAddTagMsgDto genCustomerAddTagMsgDto(String corpId, List<String> customerIds, List<TagVO> tagList, String remark, boolean isRetry, TrackEventTypeEnum eventType, String name) {
        return buildCustomerAddTagMsgDto(corpId, customerIds, tagList, TagSource.GROUP_CODE,
                isRetry, null, false, eventType, name).build();
    }

    /**
     * 批量编辑标签
     */
    public static CustomerAddTagMsgDto genCustomerAddTagMsgDto(CustomerChurnDTO dto, List<String> externalUserIds, List<TagVO> tagVOList, SysUser sysUser, String nickName, String operId, TrackEventTypeEnum eventType) {
        return buildCustomerAddTagMsgDto(dto.getCorpId(), externalUserIds, tagVOList, TagSource.CUSTOMER_LIST, false, sysUser.getUserId(), false, eventType, null)
                .operId(operId)
                .nickName(nickName)
                .isMsgNotify(true)
                .build();
    }

    /**
     * 批量删除标签
     */
    public static CustomerRemoveTagMsgDto genCustomerRemoveTagMsgDto(CustomerChurnDTO dto, List<String> externalUserIds, List<TagVO> tagVOList, SysUser sysUser, String nickName, String operId, TrackEventTypeEnum eventType) {
        return buildCustomerRemoveTagMsgDto(dto.getCorpId(), externalUserIds, tagVOList, TagSource.CUSTOMER_LIST, false, eventType)
                .operId(operId)
                .userId(sysUser.getUserId())
                .nickName(nickName)
                .isMsgNotify(true)
                .build();
    }

    /**
     * 回调事件
     */
    public static CustomerAddTagMsgDto genCustomerAddTagMsgDto(String corpId, String externalUserId, List<TagVO> addTags, String userId, TbWxUser user, TrackEventTypeEnum eventType) {
        return buildCustomerAddTagMsgDto(corpId, Lists.newArrayList(externalUserId), addTags, TagSource.OTHER, false, userId, true, eventType, null)
                .operId(user.getUserid())
                .nickName(user.getName())
                .build();
    }

    /**
     * 回调事件
     */
    public static CustomerRemoveTagMsgDto genCustomerRemoveTagMsgDto(String corpId, String externalUserId, List<TagVO> removeIds, String userId, TbWxUser user, TrackEventTypeEnum eventType, boolean fromCallback) {
        return buildCustomerRemoveTagMsgDto(corpId, Lists.newArrayList(externalUserId), removeIds, TagSource.OTHER, false, eventType)
                .userId(userId)
                .fromCallback(fromCallback)
                .nickName(user != null ? user.getName() : null)
                .build();
    }

    /**
     * 渠道活码、获客链接 打标签
     */
    public static CustomerAddTagMsgDto genCustomerAddTagMsgDto(WxCpXmlMessage wxMessage, String tags, String tagSource, String tagRemark, TrackEventTypeEnum eventType, String name) {
        List<TagVO> tagVos = JSON.parseArray(tags, TagVO.class);
        return buildCustomerAddTagMsgDto(wxMessage.getToUserName(), Lists.newArrayList(wxMessage.getExternalUserId()),
                tagVos, TagSource.valueOf(tagSource), false, null, false, eventType, name).build();
    }

    /**
     * 客户画像标签添加消息DTO
     * @return
     */
    public static CustomerAddTagMsgDto genCustomerAddTagMsgDto(String corpId, String externalUserId, TbWxUser user, List<TagVO> newTags, String userId, TrackEventTypeEnum eventType) {
        return buildCustomerAddTagMsgDto(corpId, Lists.newArrayList(externalUserId), newTags, TagSource.CUSTOMER_PROFILE, false, userId, true, eventType, null)
                .operId(user.getUserid())
                .nickName(user.getName())
                .build();
    }

    /**
     * 客户画像标签删除消息DTO
     * @return
     */
    public static CustomerRemoveTagMsgDto genCustomerRemoveTagMsgDto(String corpId, String externalUserId, TbWxUser user, List<TagVO> removeFollowUserTags, TrackEventTypeEnum eventType) {
        return buildCustomerRemoveTagMsgDto(corpId, Lists.newArrayList(externalUserId), removeFollowUserTags, TagSource.CUSTOMER_PROFILE, false, eventType)
                .userId(user.getUserid())
                .fromCallback(true)
                .operId(user.getUserid())
                .nickName(user.getName())
                .build();
    }

    /**
     * 构建CustomerAddTagMsgDto
     * @return
     */
    public static CustomerAddTagMsgDto.CustomerAddTagMsgDtoBuilder buildCustomerAddTagMsgDto(String corpId, List<String> externalUserIds, List<TagVO> tagList, TagSource tagSource, boolean isRetry, String userId, boolean fromCallback, TrackEventTypeEnum eventTypeEnum, String name) {
        return CustomerAddTagMsgDto.builder()
                .eventTypeEnum(eventTypeEnum)
                .corpId(corpId)
                .externalUserIds(externalUserIds)
                .userId(userId)
                .tagList(tagList)
                .tagSource(tagSource.name())
                .fromCallback(fromCallback)
                .name(name)
                .isRetry(isRetry);
    }

    /**
     * 构建CustomerRemoveTagMsgDto
     */
    public static CustomerRemoveTagMsgDto.CustomerRemoveTagMsgDtoBuilder buildCustomerRemoveTagMsgDto(String corpId, List<String> externalUserIds, List<TagVO> tagList, TagSource tagSource, boolean isRetry, TrackEventTypeEnum eventType) {
        return CustomerRemoveTagMsgDto.builder()
                .corpId(corpId)
                .externalUserIds(externalUserIds)
                .tagList(tagList)
                .tagSource(tagSource.name())
                .isRetry(isRetry)
                .eventTypeEnum(eventType);
    }
}
