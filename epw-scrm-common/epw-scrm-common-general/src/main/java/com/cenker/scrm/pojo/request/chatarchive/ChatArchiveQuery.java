package com.cenker.scrm.pojo.request.chatarchive;

import com.cenker.scrm.model.base.BaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatArchiveQuery extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 3278132453281001975L;
    /**
     * 聊天类型，1表示单聊消息，2表示群聊消息
     */
    private Integer chatType;

    /**
     * 员工名称/客户名称/群聊名称，支持模糊搜索
     */
    private String name;

    /**
     * 发送者类型，1表示员工，2表示客户
     */
    private Integer sendType;

    /**
     * 发送者ID，员工ID或客户ID
     */
    private String fromId;

    /**
     * 员工ID/客户ID/群聊ID
     * chatType=1时，传入员工ID或客户ID
     * chatType=2时，传入群聊ID
     */
    private String chatId;

    /**
     * 消息类型，全部时传空
     */
    private Integer msgType;

    /**
     * 消息类型，全部时传空
     */
    private String msgTypeValue;

    /**
     * 消息日期，格式：YYYY-MM-DD，为空时，默认为当日
     * 用在按员工检索、客户检索查询会话消息
     */
    private String msgDate;

    /**
     * 消息日期开始，格式：YYYY-MM-DD，为空时，默认为当日
     * 用在按条件检索
     */
    private String msgDateStart;

    /**
     * 消息日期结束，格式：YYYY-MM-DD，为空时，默认为当日
     * 用在按条件检索
     */
    private String msgDateEnd;

    /**
     * 消息内容，支持模糊搜索
     */
    private String content;

    /**
     * 0表示全部，1表示当前本人
     */
    private Integer isSelf;

    /**
     * 用于区分执行SQL，1标识查询员工发送，2标识查询客户发送
     */
    private Integer queryType;
    /**
     * 自定义分页查询
     */
    private Integer startNum;
    private Integer pageSize;


}
