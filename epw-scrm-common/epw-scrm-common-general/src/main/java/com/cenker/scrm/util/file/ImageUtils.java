package com.cenker.scrm.util.file;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.IoUtil;
import com.cenker.scrm.config.CkyConfig;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.util.StringUtils;
import com.cenker.scrm.util.UrlReplaceUtils;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Arrays;

/**
 * 图片处理工具类
 *
 * <AUTHOR>
 */
public class ImageUtils {
    private static final Logger log = LoggerFactory.getLogger(ImageUtils.class);

    public static byte[] getImage(String imagePath) {
        InputStream is = getFile(imagePath);
        try {
            return IOUtils.toByteArray(is);
        } catch (Exception e) {
            log.error("图片加载异常 {}", e);
            return null;
        } finally {
            IOUtils.closeQuietly(is);
        }
    }

    public static InputStream getFile(String imagePath) {
        try {
            byte[] result = readFile(imagePath);
            if (null == result) {
                return null;
            }
            result = Arrays.copyOf(result, result.length);
            return new ByteArrayInputStream(result);
        } catch (Exception e) {
            log.error("获取图片异常 {}", e);
        }
        return null;
    }

    /**
     * 读取文件为字节数据
     *
     * @param url 地址
     * @return 字节数据
     */
    public static byte[] readFile1(String url) {
        InputStream in = null;
        ByteArrayOutputStream baos = null;
        try {
            if (url.startsWith("http")) {
                // 网络地址
                URL urlObj = new URL(url);
                URLConnection urlConnection = urlObj.openConnection();
                urlConnection.setConnectTimeout(30 * 1000);
                urlConnection.setReadTimeout(60 * 1000);
                urlConnection.setDoInput(true);
                in = urlConnection.getInputStream();
            } else {
                // 本机地址
                // String localPath = AiMiConfig.getProfile();
                //String downloadPath = localPath + StringUtils.substringAfter(url, Constants.RESOURCE_PREFIX);
                String downloadPath = "";
                in = new FileInputStream(downloadPath);
            }
            return IOUtils.toByteArray(in);
        } catch (Exception e) {
            log.error("获取文件路径异常 {}", e);
            return null;
        } finally {
            IOUtils.closeQuietly(baos);
        }
    }

    // 链接url下载图片
    public static String downloadPicture(String urlList, String fileName) {
        URL url = null;
        int imageNumber = 0;
        String imageName = "";
        try {
            // 域名处理
            urlList = UrlReplaceUtils.urlReplace(urlList);

            url = new URL(urlList);
            DataInputStream dataInputStream = new DataInputStream(url.openStream());

            // 上传到指定文件夹
            imageName = CkyConfig.getUploadPath() + fileName;

            FileOutputStream fileOutputStream = new FileOutputStream(new File(imageName));
            ByteArrayOutputStream output = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int length;

            while ((length = dataInputStream.read(buffer)) > 0) {
                output.write(buffer, 0, length);
            }
            byte[] context = output.toByteArray();
            fileOutputStream.write(output.toByteArray());
            dataInputStream.close();
            fileOutputStream.close();

        } catch (MalformedURLException e) {
            log.error("下载失败，下载文件：{}", urlList);
            log.error("下载失败，失败原因：",e);
        } catch (IOException e) {
            log.error("下载失败，下载文件：{}", urlList);
            log.error("下载失败，失败原因：",e);
        }
        return imageName;
    }

    /**
     * 删除文件
     *
     * @param filePath 文件
     * @return
     */
    public static boolean deleteFile(String filePath) {
        boolean delete = false;
        File file = new File(filePath);
        // 路径为文件且不为空则进行删除
        if (file.isFile() && file.exists()) {
            delete = file.delete();
            if (delete) {
                log.info("缓存文件删除成功", filePath);
            }
        }
        return delete;
    }

    /**
     * 删除文件
     */
    public static boolean deleteFile(File file) {
        boolean flag = false;
        // 路径为文件且不为空则进行删除
        if (file.isFile() && file.exists()) {
            boolean delete = file.delete();
            if (delete) {
                log.info("缓存文件删除成功", file);
            }
            flag = true;
        }
        return flag;
    }

    /**
     * 读取文件为字节数据
     *
     * @param url 地址
     * @return 字节数据
     */
    public static byte[] readFile(String url) {
        InputStream in = null;
        ByteArrayOutputStream baos = null;
        try {
            if (url.startsWith("http")) {
                // 网络地址
                URL urlObj = new URL(url);
                URLConnection urlConnection = urlObj.openConnection();
                urlConnection.setConnectTimeout(30 * 1000);
                urlConnection.setReadTimeout(60 * 1000);
                urlConnection.setDoInput(true);
                in = urlConnection.getInputStream();
            } else {
                // 本机地址
                String localPath = CkyConfig.getProfile();
                String downloadPath = localPath + StringUtils.substringAfter(url, Constants.RESOURCE_PREFIX);
                in = new FileInputStream(downloadPath);
            }
            return IOUtils.toByteArray(in);
        } catch (Exception e) {
            log.error("获取文件路径异常 {}", e);
            return null;
        } finally {
            IOUtils.closeQuietly(baos);
        }
    }

    public static BufferedImage getBufferedImageByUrl(String url) throws IOException {
        URL urlObj = new URL(url);
        URLConnection urlConnection = urlObj.openConnection();
        urlConnection.setConnectTimeout(30 * 1000);
        urlConnection.setReadTimeout(60 * 1000);
        urlConnection.setDoInput(true);
        return ImageIO.read(urlConnection.getInputStream());
    }

    /**
     * 指定长和宽对图片进行缩放
     *
     * @param width  长
     * @param height 宽
     * @throws IOException
     */
    public static BufferedImage zoomBySize(BufferedImage bufferedImage, int width, int height) throws IOException {
        // 与按比例缩放的不同只在于,不需要获取新的长和宽,其余相同.
        Image img = bufferedImage.getScaledInstance(width, height, Image.SCALE_DEFAULT);
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics = image.createGraphics();
        graphics.drawImage(img, 0, 0, null);
        graphics.dispose();
        return image;
    }

    /**
     * 将图片等比例压缩至指定大小以内
     * @param sourceBytes
     * @param targetSize
     * @return
     */
    public static byte[] compressImage(byte[] sourceBytes, long targetSize) {
        byte[] outputBytes = sourceBytes;
        long fileSize = sourceBytes.length;
        ByteArrayOutputStream scaledOutputStream = null;

        while (fileSize > targetSize) {
            try {
                Image image = ImgUtil.read(new ByteArrayInputStream(outputBytes));

                float scale = targetSize / (float)fileSize;
                scaledOutputStream = new ByteArrayOutputStream();
                ImgUtil.scale(image, scaledOutputStream, scale);

                outputBytes = scaledOutputStream.toByteArray();
                fileSize = outputBytes.length;
            } finally {
                IoUtil.close(scaledOutputStream);
            }
        }

        return outputBytes;
    }

    /**
     * 将图片等比例压缩至指定尺寸以内
     * @param sourceBytes
     * @param maxWidth
     * @param maxHeight
     * @return
     */
    public static byte[] compressImage(byte[] sourceBytes, long maxWidth, long maxHeight) {
        byte[] outputBytes = sourceBytes;
        ByteArrayOutputStream scaledOutputStream = null;

        try {
            BufferedImage originalImage = ImgUtil.read(new ByteArrayInputStream(sourceBytes));

            int width = originalImage.getWidth();
            int height = originalImage.getHeight();

            if (width <= maxWidth && height <= maxHeight) {
                return sourceBytes;
            }

            float scale = Math.min(maxWidth / (float) width, maxHeight / (float) height);

            scaledOutputStream = new ByteArrayOutputStream();
            ImgUtil.scale(originalImage, scaledOutputStream, scale);
            outputBytes = scaledOutputStream.toByteArray();
        } finally {
            IoUtil.close(scaledOutputStream);
        }

        return outputBytes;
    }
}
