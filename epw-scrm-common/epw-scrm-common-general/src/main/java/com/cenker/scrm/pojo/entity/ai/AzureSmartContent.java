package com.cenker.scrm.pojo.entity.ai;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/***
 * @description
 * <AUTHOR>
 * @date 2023/6/8 11:46
*/
@Accessors(chain = true)
@TableName(value = "azure_smart_content",autoResultMap = true)
@Data
public class AzureSmartContent {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 0 标识未删除 1 标识删除
     */
    @TableLogic
    private Integer delFlag;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 更新人
     */
    private Long updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 完成时间 毫秒
     */
    private Integer completeTime;
    /**
     * 输入内容
     */
    private String azureRequest;
    /**
     * 输出内容
     */
    private String azureResponse;
    /**
     * 创作主键id
     */
    private Long writeId;
    /**
     * 发送状态 1发送成功，2发送失败
     */
    private Integer sendStatus;
    /**
     * 温度
     */
    private Integer temperature;

    /**
     * 1群发客户 2群发社群 3群发朋友圈
     */
    private Integer type;
}

