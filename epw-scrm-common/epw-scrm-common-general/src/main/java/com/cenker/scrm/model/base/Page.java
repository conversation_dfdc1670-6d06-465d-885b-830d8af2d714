package com.cenker.scrm.model.base;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @create 2021/6/28 14:22
 * 分页列表响应基类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Page {

    private Long total; // 总记录数

    private Integer pageSize; // 每页显示记录数

    private Integer pages; // 总页数

    private Integer pageIndex; // 当前页

    public Page(Long total, Integer pageSize, Integer pages, Integer pageIndex) {
        this.total = total;
        this.pageSize = pageSize;
        this.pages = pages;
        this.pageIndex = pageIndex;
    }

    public Page(long total, long size, long pages, long current) {
        this.total = total;
        this.pageSize = pageSize;
        this.pages = (int)pages;
        this.pageIndex = pageIndex;
    }
}
