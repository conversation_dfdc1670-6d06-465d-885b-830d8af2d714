package com.cenker.scrm.pojo.entity.chatarchive;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 会话记录接受者记录对象 wk_chat_archive_permit_user_record
 * 
 * <AUTHOR>
 * @date 2024-01-24
 */
@Data
@TableName(value = "wk_chat_archive_permit_user_record",autoResultMap = true)
public class WkChatArchivePermitUserRecord implements Serializable {

    private static final long serialVersionUID = 7493149746010268143L;
    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 企业ID */
    private String corpId;

    /** 员工ID */
    private String userId;

    /** 状态，0表示未开启会话存档，1表示开启会话存档 */
    private Integer status;

    /** 开始时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 创建时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 修改时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
