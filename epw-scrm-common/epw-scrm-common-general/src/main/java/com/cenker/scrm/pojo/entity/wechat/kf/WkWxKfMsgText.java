package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;

/**
 * 微信客服 聊天记录 text 文件 对象 tb_wx_kf_msg
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wk_wx_kf_msg_text")
public class WkWxKfMsgText extends WkWxKfMsgItemCommonResp {

    private String content;
    private String menuId;

    @Override
    public WkWxKfMsgText init(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        super.init(msgItem);

        this.content = msgItem.getText().getContent();
        this.menuId = msgItem.getText().getMenuId();

        return this;
    }
}
