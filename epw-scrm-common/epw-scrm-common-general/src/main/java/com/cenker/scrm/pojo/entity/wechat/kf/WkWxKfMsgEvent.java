package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;
import me.chanjar.weixin.cp.bean.kf.msg.WxCpKfWechatChannelsMsg;

/**
 * 微信客服 聊天记录 event 事件 对象 tb_wx_kf_msg
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wk_wx_kf_msg_event")
public class WkWxKfMsgEvent extends WkWxKfMsgItemCommonResp {

    private String eventType;
    private String openKfId;
    private String externalUserId;
    private String servicerUserId;
    private String oldServicerUserId;
    private String newServicerUserId;
    private String scene;
    private String sceneParam;
    private String welcomeCode;
    private String failMsgId;
    private Integer failType;
    @TableField("`status`")
    private Integer status;
    private Integer changeType;
    private String msgCode;
    private String recallMsgId;
    private Integer rejectSwitch;

    private String wechatChannelsNickname;
    private String wechatChannelsShopNickname;
    private Integer wechatChannelsScene;

    @Override
    public WkWxKfMsgEvent init(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        super.init(msgItem);

        this.eventType = msgItem.getEvent().getEventType();
        this.openKfId = msgItem.getEvent().getOpenKfid();
        this.externalUserId = msgItem.getEvent().getExternalUserId();
        this.servicerUserId = msgItem.getEvent().getServicerUserId();
        this.oldServicerUserId = msgItem.getEvent().getOldServicerUserId();
        this.newServicerUserId = msgItem.getEvent().getNewServicerUserId();
        this.scene = msgItem.getEvent().getScene();
        this.sceneParam = msgItem.getEvent().getSceneParam();
        this.welcomeCode = msgItem.getEvent().getWelcomeCode();
        this.failMsgId = msgItem.getEvent().getFailMsgId();
        this.failType = msgItem.getEvent().getFailType();
        this.status = msgItem.getEvent().getStatus();
        this.changeType = msgItem.getEvent().getChangeType();
        this.msgCode = msgItem.getEvent().getMsgCode();
        this.recallMsgId = msgItem.getEvent().getRecallMsgId();
        this.rejectSwitch = msgItem.getEvent().getRejectSwitch();

        return initWxCpKfWechatChannelsMsg(msgItem);
    }

    private WkWxKfMsgEvent initWxCpKfWechatChannelsMsg(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        WxCpKfWechatChannelsMsg wxCpKfWechatChannelsMsg = msgItem.getEvent().getWxCpKfWechatChannelsMsg();
        if (null != wxCpKfWechatChannelsMsg) {
            this.wechatChannelsNickname = wxCpKfWechatChannelsMsg.getNickname();
            this.wechatChannelsShopNickname = wxCpKfWechatChannelsMsg.getShopNickname();
            this.wechatChannelsScene = wxCpKfWechatChannelsMsg.getScene();
        }
        return this;
    }
}
