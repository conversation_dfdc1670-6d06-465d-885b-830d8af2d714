package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业微信外部客户信息对象 tb_wx_ext_customer
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
// @ApiModel("企业微信外部客户信息实体")
public class TbWxExtCustomer implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 创建者
     */
    // @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
    // @ApiModelProperty("创建时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    // @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    // @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    // @ApiModelProperty("备注")
    private String remark;
    /**
     * 外部联系人的userid
     */
    // @ApiModelProperty("外部客户Id")
    // @TableId
    private String externalUserId;

    /**
     * 外部联系人名称
     */
    // @ApiModelProperty("外部联系人名称")
    // @Excel(name = "外部联系人名称")
    private String name;

    /**
     * 外部联系人头像
     */
    // @ApiModelProperty("外部联系人头像")
    // @Excel(name = "外部联系人头像")
    private String avatar;

    /**
     * 外部联系人的类型，1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户
     */
    // @ApiModelProperty("外部联系人的类型")
    // @Excel(name = "外部联系人的类型，1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户")
    private Integer type;

    /**
     * 外部联系人性别 0-未知 1-男性 2-女性
     */
    // @ApiModelProperty("外部联系人性别")
    // @Excel(name = "外部联系人性别 0-未知 1-男性 2-女性")
    private Integer gender;

    /**
     * 外部联系人在微信开放平台的唯一身份标识,通过此字段企业可将外部联系人与公众号/小程序用户关联起来。
     */
    // @ApiModelProperty("外部联系人在微信开放平台的唯一身份标识,通过此字段企业可将外部联系人与公众号/小程序用户关联起来")
    // @Excel(name = "外部联系人在微信开放平台的唯一身份标识,通过此字段企业可将外部联系人与公众号/小程序用户关联起来。")
    private String unionId;

    /**
     * 生日
     */
    // @ApiModelProperty("生日")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    // @Excel(name = "生日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 客户企业简称
     */
    // @ApiModelProperty("客户企业简称")
    // @Excel(name = "客户企业简称")
    private String corpName;

    /**
     * 客户企业全称
     */
    // @ApiModelProperty("客户企业全称")
    // @Excel(name = "客户企业全称")
    private String corpFullName;

    /**
     * 客户职位
     */
    // @ApiModelProperty("客户职位")
    // @Excel(name = "客户职位")
    private String position;

    /**
     * 企业对外部联系人打的标签
     */
    // @ApiModelProperty("企业对外部联系人打的标签")
    // @Excel(name = "企业对外部联系人打的标签")
    private String tag;

    private String qq;
    private String address;
    private String email;

    /**
     * 状态（0正常 1删除）
     */
    // @ApiModelProperty("状态")
    // @Excel(name = "状态", readConverterExp = "0=正常,1=删除")
    private String status;

    /**
     * 企业id
     */
    // @ApiModelProperty("企业id")
    // @Excel(name = "企业id")
    private String corpId;

    // @ApiModelProperty("客户来源")
    private String source;

    // @ApiModelProperty("首次添加客户的成员ID")
    private String firstAddUserId;

    // @ApiModelProperty("首次添加客户的成员ID")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstAddDate;

    // @ApiModelProperty("手机号码")
    // @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String mobiles;

    // 备注手机号
    @TableField(exist = false)
    private String remarkMobiles;

    /**
     * 是否认证
     */
    private Boolean isAuth;

    /**
     * 客户号
     */
    private String custno;

    /**
     * 真实姓名
     */
    private String realName;
}
