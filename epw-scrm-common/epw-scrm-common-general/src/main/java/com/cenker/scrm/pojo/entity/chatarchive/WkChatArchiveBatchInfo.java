package com.cenker.scrm.pojo.entity.chatarchive;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.util.SnowflakeIdUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 会话记录拉取批次任务对象 wk_chat_archive_batch_info
 * 
 * <AUTHOR>
 * @date 2024-01-24
 */
@Data
@TableName(value = "wk_chat_archive_batch_info",autoResultMap = true)
public class WkChatArchiveBatchInfo implements Serializable {

    private static final long serialVersionUID = 7467834811707801349L;
    /** 主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 企业ID */
    private String corpId;

    /** 拉取索引 */
    private Long startSeq;

    /** 最后拉取索引 */
    private Long lastSeq;

    /** 一次拉取条数 */
    private Long pullLimit;

    /** 状态，-1表示未开始，0表示完成，1表示进行中，2表示未完成 */
    private Integer status;

    /** 重试次数 */
    private Integer restartTimes;

    /** 最后批次Id */
    private Long lastBatch;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    public static WkChatArchiveBatchInfo buildBatchInfo(String corpId, long startSeq, long limit, Integer status) {
        WkChatArchiveBatchInfo wxChatArchiveBatchInfo = new WkChatArchiveBatchInfo();
        wxChatArchiveBatchInfo.setId(SnowflakeIdUtil.getSnowId());
        wxChatArchiveBatchInfo.setCorpId(corpId);
        wxChatArchiveBatchInfo.setStartSeq(startSeq);
        wxChatArchiveBatchInfo.setLastSeq(0L);
        wxChatArchiveBatchInfo.setPullLimit(limit);
        wxChatArchiveBatchInfo.setStatus(status);
        wxChatArchiveBatchInfo.setStartTime(new Date());
        return wxChatArchiveBatchInfo;
    }
}
