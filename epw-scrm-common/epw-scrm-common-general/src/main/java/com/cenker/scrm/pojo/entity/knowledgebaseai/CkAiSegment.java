package com.cenker.scrm.pojo.entity.knowledgebaseai;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName(value = "ck_ai_segment",autoResultMap = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CkAiSegment {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long uploadFileInfoId;
    private String segmentContent;
    private String segmentSourceUrl;
    private String segmentKey;
    private String remark;
    /**
     * 逻辑删除 0 表示未删除，1 表示删除
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;
    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;
}
