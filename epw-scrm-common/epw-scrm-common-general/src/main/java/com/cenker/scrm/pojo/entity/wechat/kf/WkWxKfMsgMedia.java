package com.cenker.scrm.pojo.entity.wechat.kf;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgListResp;

import static com.cenker.scrm.enums.MediaType.*;


/**
 * 微信客服 聊天记录 media 文件 对象 tb_wx_kf_msg
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("wk_wx_kf_msg_media")
public class WkWxKfMsgMedia extends WkWxKfMsgItemCommonResp {

    private String msgType;
    private String mediaId;
    private String mediaUrl;

    @Override
    public WkWxKfMsgMedia init(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        super.init(msgItem);
        this.msgType = msgItem.getMsgType();
        return setMediaId(msgItem);
    }

    private WkWxKfMsgMedia setMediaId(WxCpKfMsgListResp.WxCpKfMsgItem msgItem) {
        if (IMAGE.getMediaType().equals(this.msgType)) {
            this.mediaId = msgItem.getImage().getMediaId();
            this.mediaUrl = msgItem.getImage().getMediaUrl();
        }
        if (VOICE.getMediaType().equals(this.msgType)) {
            this.mediaId = msgItem.getVoice().getMediaId();
            this.mediaUrl = msgItem.getVoice().getMediaUrl();
        }
        if (VIDEO.getMediaType().equals(this.msgType)) {
            this.mediaId = msgItem.getVideo().getMediaId();
            this.mediaUrl = msgItem.getVideo().getMediaUrl();
        }
        if (FILE.getMediaType().equals(this.msgType)) {
            this.mediaId = msgItem.getFile().getMediaId();
            this.mediaUrl = msgItem.getFile().getMediaUrl();
        }
        return this;
    }

}
