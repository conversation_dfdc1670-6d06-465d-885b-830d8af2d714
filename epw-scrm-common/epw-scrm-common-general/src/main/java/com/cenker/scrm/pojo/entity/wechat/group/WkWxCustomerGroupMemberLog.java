package com.cenker.scrm.pojo.entity.wechat.group;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cenker.scrm.model.base.BaseDbEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 群成员状态记录表
 * <AUTHOR> @Date 2023-08-28 
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler", "fieldHandler"}, ignoreUnknown = true)
@TableName("wk_wx_customer_group_member_log")
public class WkWxCustomerGroupMemberLog extends BaseDbEntity implements Serializable {
	private static final long serialVersionUID =  3516039261653997169L;

	/**
	 * 主键id
	 */
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 客户群id
	 */
	private String groupId;

	/**
	 * 群成员id
	 */
	private String userId;

	/**
	 * 状态变化日期
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
	private Date changeDay;

	/**
	 * 状态变化时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	private Date changeTime;

	/**
	 * 变化类型 1 进群 2 退群 3 解散退群
	 */
	private Integer changeType;

	/**
	 * 企业自定义的state参数，用于区分不同的入群渠道
	 */
	private String state;

	/**
	 * 群昵称
	 */
	private String groupNickname;

	/**
	 * 成员类型 1 - 企业成员 2 - 外部联系人
	 */
	private String type;

	/**
	 * 入群方式1 - 由群成员邀请入群（直接邀请入群）2 - 由群成员邀请入群（通过邀请链接入群）3 - 通过扫描群二维码入群
	 */
	private String joinScene;

	/**
	 * 退群方式0 自己退群 1 群主/群管理员移出
	 */
	private String quitScene;

	/**
	 * 外部联系人在微信开放平台的唯一身份标识（微信unionid）
	 */
	private String unionId;
}

