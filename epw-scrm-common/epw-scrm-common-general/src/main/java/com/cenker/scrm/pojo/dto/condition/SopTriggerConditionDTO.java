package com.cenker.scrm.pojo.dto.condition;

import com.cenker.scrm.pojo.valid.InsertGroup;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/7/7
 * @Description sop触发条件
 */
@Data
public class SopTriggerConditionDTO extends SopConditionDTO {
    /**
     * 触发条件或者的集合
     */
    @NotNull(message = "请选择具体的触发条件",groups = {InsertGroup.class})
    @Size(min = 1,message = "请选择具体的触发条件",groups = {InsertGroup.class})
    @Valid
    private List<SopTriggerOrConditionDTO> triggerList;
}
