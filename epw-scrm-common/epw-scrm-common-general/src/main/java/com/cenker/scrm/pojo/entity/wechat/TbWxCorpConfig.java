package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cenker.scrm.model.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 企业微信相关配置对象 tb_wx_corp_config
 *
 * <AUTHOR>
 * @date 2021-01-19
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxCorpConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    // @ApiModelProperty("企业配置ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 企业id（登录企业微信管理后台 —— 我的企业 —— 企业ID）
     */
    // @ApiModelProperty("企业id")
    // @Excel(name = "企业id", readConverterExp = "登=录企业微信管理后台,—=—,我=的企业,—=—,企=业ID")
    private String corpId;

    /**
     * 企业名称
     */
    // @ApiModelProperty("企业名称")
    // @Excel(name = "企业名称")
    private String companyName;

    /**
     * 通讯私钥（登录企业微信管理后台 —— 管理工具 —— 通讯录 —— 密钥）
     */
    // @ApiModelProperty("通讯私钥")
    // @Excel(name = "通讯私钥", readConverterExp = "登=录企业微信管理后台,—=—,管=理工具,—=—,通=讯录,—=—,密=钥")
    private String providerSecret;

    /**
     * 外部联系人密钥
     */
    // @ApiModelProperty("外部联系人密钥")
    // @Excel(name = "外部联系人密钥")
    private String contactSecret;

    /**
     * 会话存档密钥
     */
    // @ApiModelProperty("会话存档密钥")
    // @Excel(name = "会话存档密钥")
    private String chatSecret;

    /**
     * 公钥版本
     */
    // @ApiModelProperty("公钥版本")
    // @Excel(name = "公钥版本")
    private String pubKey;

    /**
     * 私钥版本
     */
    // @ApiModelProperty("私钥版本")
    // @Excel(name = "私钥版本")
    private String priKey;

    /**
     * 腾讯云secretid
     */
    // @ApiModelProperty("腾讯云secretid")
    // @Excel(name = "腾讯云secretid")
    private String tencentCloudSecretId;

    /**
     * 腾讯云secretkey
     */
    // @ApiModelProperty("腾讯云secretkey")
    // @Excel(name = "腾讯云secretkey")
    private String tencentCloudSecretSecretKey;

    /**
     * 聊天工具栏应用ID
     */
    // @ApiModelProperty("聊天工具栏应用ID")
    // @Excel(name = "聊天工具栏应用ID")
    private String toolAppId;

    /**
     * 聊天工具栏应用私钥
     */
    // @ApiModelProperty("聊天工具栏应用私钥")
    // @Excel(name = "聊天工具栏应用私钥")
    private String toolAppSecret;

    /**
     * 小程序应用ID
     */
    // @ApiModelProperty("小程序应用ID")
    // @Excel(name = "小程序应用ID")
    private String appletId;

    /**
     * 小程序应用密钥
     */
    // @ApiModelProperty("小程序应用密钥")
    // @Excel(name = "小程序应用密钥")
    private String appletSecret;

    /**
     * 企微扫码应用ID
     */
    // @ApiModelProperty("企微扫码应用ID")
    // @Excel(name = "企微扫码应用ID")
    private String qrAppId;

    /**
     * 企微扫码登录url
     */
    // @ApiModelProperty("企微扫码登录url")
    // @Excel(name = "企微扫码登录url")
    private String qrLoginRedirectUri;

    /**
     * 通讯录回调URL
     */
    // @ApiModelProperty("通讯录回调URL")
    // @Excel(name = "通讯录回调URL")
    private String addBookCallBackUrl;

    /**
     * 通讯录回调Token
     */
    // @ApiModelProperty("通讯录回调Token")
    // @Excel(name = "通讯录回调Token")
    private String addBookCallBackToken;

    /**
     * 通讯录回调私钥
     */
    // @ApiModelProperty("通讯录回调私钥")
    // @Excel(name = "通讯录回调私钥")
    private String addBookCallBackSecret;

    /**
     * 客户消息回调URL
     */
    // @ApiModelProperty("客户消息回调URL")
    // @Excel(name = "客户消息回调URL")
    private String custMessageCallBackUrl;

    /**
     * 客户消息回调Token
     */
    // @ApiModelProperty("客户消息回调Token")
    // @Excel(name = "客户消息回调Token")
    private String custMessageCallBackToken;

    /**
     * 客户消息回调私钥
     */
    // @ApiModelProperty("客户消息回调私钥")
    // @Excel(name = "客户消息回调私钥")
    private String custMessageCallBackSecret;

    /**
     * 帐号状态（0正常 1停用)
     */
    // @ApiModelProperty("帐号状态")
    // @Excel(name = "帐号状态", readConverterExp = "帐号状态（0正常 1停用)")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    // @ApiModelProperty("删除标志")
    private String delFlag;

    /**
     * 客户流失通知开关 0:关闭 1:开启
     */
    // @ApiModelProperty("客户流失通知开关")
    // @Excel(name = "客户流失通知开关 0:关闭 1:开启")
    private String customerChurnNoticeSwitch;

    /**
     * 企业账号后缀
     */
    private String accountSuffix;

    /**
     * 账号后缀修改状态 0 未修改 1 已修改
     */
    private Integer editSuffix;

    /**
     * 企微加密企业id
     */
    private String openCorpId;

    /**
     * 企业头像
     */
    private String corpAvatar;

    /**
     *  商城开通状态
     */
    private Boolean storeStatus;

    /**
     * 话术id
     */
    @TableField(exist = false)
    private Long quickReplyId;

    /**
     * 版本 1 专业版 2 高级版 3 旗舰版
     */
    private Integer systemVersion;
    /**
     * 内容审核部门id，多个部门用逗号隔开
      */
    private String contentAuditDepart;
    @TableField(exist = false)
    private List<AuditDepart> contentAuditDeparts;
}
