package com.cenker.scrm.pojo.entity.wechat;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cenker.scrm.pojo.dto.WechatUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 企业微信组织架构对象 tb_wx_department
 *
 * <AUTHOR>
 * @date 2021-01-21
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxDepartment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部门Id
     */
    private String id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 父级部门Id
     */
    private String parentId;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 子部门
     */
    @TableField(exist = false)
    private List<TbWxDepartment> children = new ArrayList<>();

    /**
     * 部门下的成员
     */
    @TableField(exist = false)
    private List<WechatUser> userList = new ArrayList<>();

    @TableField(exist = false)
    private boolean dimissFlag = false;

    @TableField(exist = false)
    private String userMainDeptId;
    @TableField(exist = false)
    private String corpUserId;
    @TableField(exist = false)
    private String[] ids;
}
