package com.cenker.scrm.pojo.entity.wechat;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxMomentInteract {
  @TableId(type = IdType.ASSIGN_ID)
  private Long id;
  private Long momentTaskId;
  private String userId;
  private String externalUserId;
  /**
   * 1 点赞 2 评论
   */
  private Integer type;
  @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private Date interactTime;
  private String corpId;

}
