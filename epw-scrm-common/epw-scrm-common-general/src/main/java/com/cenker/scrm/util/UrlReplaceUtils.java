package com.cenker.scrm.util;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 替换Url前缀，针对内网环境使用
 */
public class UrlReplaceUtils {

    /**
     * 替换Url的集合
     */
    private static Map<String, String> urlMaps = Maps.newHashMap();

    /**
     * 替换开关
     */
    private static final boolean isOpen = false;

    // 昆山农商行POC环境-Dmz代理服务器
    private static final String TARGET_URL = "http://10.195.72.3:7828";

    static {
        // 企业微信API接口
        urlMaps.put("https://qyapi.weixin.qq.com", TARGET_URL + "/qyapi");
        // 微信API接口
        urlMaps.put("https://api.weixin.qq.com", TARGET_URL + "/wxapi");
        // 公众号域名接口
        urlMaps.put("https://mp.weixin.qq.com", TARGET_URL + "/mpapi");
        // 微信开放域名接口
        urlMaps.put("https://open.weixin.qq.com", TARGET_URL + "/openwx");
        // 企业微信域名接口
        urlMaps.put("https://open.work.weixin.qq.com", TARGET_URL + "/openwork");
        // 企业微信域名接口
        urlMaps.put("https://res.wx.qq.com", TARGET_URL + "/reswx");
        // 微信域名接口
        urlMaps.put("http://wx.qlogo.cn", TARGET_URL + "/qlogowx");
        // 微信域名接口
        urlMaps.put("https://wework.qpic.cn", TARGET_URL + "/wework");

        // 对象存储，特殊处理
        urlMaps.put("http://poc-buckets.aimiscrm.cn", "http://127.0.0.1:7900");
    }


    /**
     * @param sourceUrl
     * @return
     */
    public static String urlReplace(String sourceUrl){
        if(!isOpen){
           return sourceUrl;
        }
        String targetUrl = sourceUrl;
        for(Map.Entry<String, String> entry : urlMaps.entrySet()){
            if(sourceUrl.contains(entry.getKey())){
                targetUrl = sourceUrl.replace(entry.getKey(), entry.getValue());
                break;
            }
        }
        return targetUrl;
    }

    public static void main(String[] args){
        System.out.println(UrlReplaceUtils.urlReplace("http://poc-buckets.aimiscrm.cn/pocbucktes/ssssss.jpb"));
    }
}
