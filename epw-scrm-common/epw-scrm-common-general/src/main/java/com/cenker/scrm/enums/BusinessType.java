package com.cenker.scrm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 业务操作类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessType {
    /**
     * 其它
     */
    OTHER(0, "其它"),

    /**
     * 新增
     */
    INSERT(1, "新增"),

    /**
     * 修改
     */
    UPDATE(2, "修改"),

    /**
     * 删除
     */
    DELETE(3, "删除"),

    /**
     * 查询
     */
    QUERY(4, "查询"),

    /**
     * 导入
     */
    IMPORT(5, "导入"),

    /**
     * 导出/下载
     */
    EXPORT(6, "导出/下载"),

    /**
     * 同步
     */
    SYNCHRONIZATION(7, "同步"),

    /**
     * 强退
     */
    FORCE(8, "强退"),

    /**
     * 清空数据
     */
    CLEAN(9, "清空"),

    /**
     * 授权
     */
    GRANT(10, "授权"),

    /**
     * 更新 统计数据
     */
    UPDATE_DATA(11, "更新"),

    /**
     * 审核
     */
     APPROVAL(12, " 审核"),

    /**
     * 撤回
     */
     REVOKED(13, "撤回");

    private Integer code;

    private String desc;


    /**
     * 根据code获取desc
     * @param code
     * @return
     */
    public static String getDesc(Integer code) {
        BusinessType businessType = getByCode(code);

        return businessType.getDesc();
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static BusinessType getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return BusinessType.OTHER;
        }

        for (BusinessType businessType : BusinessType.values()) {
            if (businessType.getCode().equals(code)) {
                return businessType;
            }
        }
        return BusinessType.OTHER;
    }

}
