package com.cenker.scrm.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2022/6/29
 * @Description 全局错误码
 */
@Getter
public enum ErrCodeEnum {


    /**
     * 系统
     */
    SYSTEM_ERROR(10000,"系统繁忙，请稍后再试"),

    DATA_DIFFERENT(20000,"数据已更新，请刷新重试"),
    DATA_NOT_EXIST(20001,"数据不存在或已被删除"),
    DATA_REPEAT(20002,"数据已存在"),
    DATA_FORMAT_ERROR(20003,"数据格式错误"),
    DATA_OCCUPIED(20004,"数据被占用"),


    /**
     * 限制
     */
    DATE_QUERY_LIMIT_90(30000,"不允许查询天数间隔超过90天"),

    /**
     * 参数问题
     */
    PARAM_ERROR(40000,"参数错误"),

    /**
     * 业务 客户旅程
     */
    EXTERNAL_JOURNEY_SET_STAGE_ERROR(50000,"设置阶段错误，请检查数据后重试"),
    EXTERNAL_JOURNEY_EXIST_STAGE_ERROR(50001,"已经有客户在阶段中啦，请检查数据后重试"),
    EXTERNAL_JOURNEY_NOT_ALLOW_ERROR(50002,"自动化旅程不允许操作"),
    EXTERNAL_JOURNEY_STAGE_NAME_REPEAT_ERROR(50003,"阶段名称重复"),

    /**
     * 业务 客户数据同步
     */
    EXTERNAL_CUSTOMER_SYNC_ERROR(50100,"客户数据同步失败"),
    /**
     * 群发
     */
    MASS_MESSAGE_CANCEL_TASK_FAIL_ERROR(51000,"取消任务失败，该任务已无法取消"),
    MASS_MESSAGE_SEND_FAIL_NO_CUSTOMER_ERROR(51001,"无法发送群发，不存在有效客户"),

    /**
     * sop
     */
    SOP_IS_RUNNING_MUST_DELETE(52000,"请先停用该sop"),
    SOP_CONTENT_SORT_ERROR(52001,"内容序列排序错误，请修改后再提交"),
    SOP_REPEAT_START_ERROR(52002,"该sop已启动，请勿重复启动该sop"),
    SOP_REPEAT_STOP_ERROR(52003,"该sop已停止，请勿重复停止该sop"),
    SOP_CONTENT_EMPTY_ERROR(52004,"内容序列为空"),
    SOP_START_ERROR(52005,"启用sop发生错误，启用失败"),
    SOP_JOB_QUERY_ERROR(52006,"查询sop任务发生错误"),
    SOP_CONTENT_NOT_EXIST_ERROR(52007,"该内容任务不存在或已被删除"),
    SOP_CONTENT_EXIST_REPEAT_ERROR(52008,"内容序列存在同一天的任务，请修改后再提交"),
    SOP_TYPE_ERROR(52009,"sop类型错误"),
    SOP_REPEAT_EXPIRE_NOT_SELECT_ERROR(52010,"请选择结束重复类型"),
    SOP_END_TIME_NOT_SELECT_ERROR(52011,"请选择结束时间"),
    SOP_REMOVE_CUSTOMER_ERROR(52012,"请至少保留一位可发送客户"),
    SOP_MASS_NOT_CUSTOMER_ERROR(52013,"未找到符合条件的客户，请检查条件项后重新提交"),
    SOP_IS_APPROVAL_MUST_DELETE(52014,"请先撤回该sop"),
    SOP_STATUS_START_ERROR(52015,"状态异常，不能启动该sop"),
    SOP_STATUS_STOP_ERROR(52016,"状态异常，不能停止该sop"),
    SOP_NOT_EXIST_ERROR(52017,"该SOP不存在或已被删除"),

    /**
     * 社群活码
     */

    GROUP_CODE_GENERATE_ERROR(53000,"生成社群活码失败"),

    /**
     * 城市活码
     */
    CITY_CONTACT_AREA_STATUS_ERROR(70001,"城市状态错误"),
    CITY_CONTACT_AREA_NOT_OPEN_ERROR(70002,"该城市未启用"),
    CITY_CONTACT_STORE_NOT_EXIST_ERROR(70003,"门店数据不存在或已被停用"),
    CITY_CONTACT_STORE_CAN_NOT_DELETE_ERROR(70004,"请先停用该门店"),
    CITY_CONTACT_STORE_CAN_NOT_DELETE_EXIST_USER_ERROR(70005,"该门店下存在配送员，请分配到其他门店后再试"),
    CITY_CONTACT_DELIVERY_USER_NOT_EXIST_ERROR(70006,"该配送员不存在或已被删除"),
    CITY_CONTACT_DELIVERY_USER_DISABLE_ERROR(70007,"该配送员已被停用"),
    CITY_CONTACT_CITY_HAS_EXIST_ERROR(70008,"该城市已存在活码"),
    CITY_CONTACT_STORE_DISABLE_ERROR(70009,"门店已被停用"),
    CITY_CONTACT_CITY_NOT_EXIST_ERROR(70010,"站点活码不存在或已被删除"),
    CITY_CONTACT_CITY_GENERATE_ERROR(70011,"站点活码存在问题，请联系管理员"),
    CITY_CONTACT_STORE_NAME_REPEAT_ERROR(70012,"该门店已存在，请不要重复添加"),
    CITY_CONTACT_SITE_NAME_REPEAT_ERROR(70013,"站点名称重复"),
    CITY_CONTACT_SITE_NOT_EXIST_ERROR(70014,"站点数据不存在或已被删除"),
    CITY_CONTACT_SITE_CAN_NOT_DELETE_EXIST_STORE_ERROR(70015,"该站点下存在门店，请先将门店迁移至其他站点"),

    /**
     * 内容缓存
     */
    CONTENT_TYPE_ERROR_OR_NOT_SUPPORT_TYPE(80001,"内容类型有误或该不支持缓存!"),

    /**
     * 素材信息上传
     */
    IMAGE_FORMAT_ERROR(40123,"上传临时图片素材，图片格式非法，只支持jpg/png格式"),
    IMAGE_RESOLUTION_ERROR(41081,"附件资源的图片分辨率超过限制"),
    VIDEO_LENGTH_EXCEEDS_LIMIT(41082,"视频时长超过限制"),
    VIDEO_FORMAT_INVALID(41090,"视频格式不合法"),
    FILE_SIZE_EXCEEDS_LIMIT(45001,"文件大小超过限制"),
    VOICE_PLAYBACK_EXCEEDS_LIMIT(45007,"语音播放时间超过限制"),
    FILE_FORMAT_ERROR(844001,"文件格式错误"),
    MADIA_NOT_EXIST(844002,"上传临时素材，素材不存在"),

    /**
     * 朋友圈
     */
    INVALID_USERID(40003,"【发朋友圈】获取互动数据列表失败，无效的UserID"),
    FINISH_NOT_CANCLE_MOMENTS(40004,"已完成状态的朋友圈不能取消！"),
    USER_NOT_POSTED_MOMENTS(41062,"【发朋友圈】获取互动数据列表失败，用户未发表朋友圈"),
    FAILED_OBTAIN_INTERACTIVE_DATA(301026,"【发朋友圈】获取互动数据列表失败"),

    FAILED_WX_CUST_LINK_INTERFACE(90787,"调用获客链接接口失败"),
    ;



    Integer code;
    String message;

    ErrCodeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
