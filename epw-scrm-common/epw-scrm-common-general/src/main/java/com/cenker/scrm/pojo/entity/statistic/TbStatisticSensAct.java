package com.cenker.scrm.pojo.entity.statistic;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.cenker.scrm.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 数据统计-敏感行为
 *
 * <AUTHOR>
 * @since 2024-04-16 17:08
 */
@Data
@TableName("tb_statistic_sens_act")
public class TbStatisticSensAct {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", dateFormat = "yyyy-MM-dd", sort = 1)
    private Date statisticDate;

    /**
     * 敏感行为类型
     */
    private String actType;

    /**
     * 敏感行为类型名称
     */
    @Excel(name = "敏感行为", sort = 2)
    private String actTypeName;

    /**
     * 规则id
     */
    private String ruleId;

    /**
     * 规则名称
     */
    @Excel(name = "敏感规则", sort = 3)
    private String ruleName;

    /**
     * 员工UserID
     */
    private String userid;

    /**
     * 员工名称
     */
    @Excel(name = "员工名称", sort = 4)
    private String userName;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    @Excel(name = "员工部门", sort = 5)
    private String deptName;

    /**
     * 员工触发次数
     */
    @Excel(name = "触发次数", sort = 6)
    private Integer triggerTimes;

    /**
     * 创建时间
     */
    private Date createTime;

}
