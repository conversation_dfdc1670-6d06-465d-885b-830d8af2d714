package com.cenker.scrm.pojo;

import com.cenker.scrm.pojo.dto.WechatUser;
import com.cenker.scrm.pojo.entity.system.SysDept;
import com.cenker.scrm.pojo.entity.system.SysMenu;
import com.cenker.scrm.pojo.entity.wechat.TbWxCategory;
import com.cenker.scrm.pojo.entity.wechat.TbWxCategoryInfo;
import com.cenker.scrm.pojo.entity.wechat.TbWxDepartment;
import com.cenker.scrm.pojo.vo.open.store.AppCategoryVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
@Data
public class TreeSelect implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private String id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 子节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;

    /**
     * 部门下的成员
     */
    private List<WechatUser> userList = new ArrayList<>();

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 类型（M目录 C菜单 F按钮）
     */
    private String menuType;

    /**
     * 组件路径
     */
    private String component;
    /**
     * 所属终端，01:PC，02：企微
     */
    private String belongTerminal;

    public TreeSelect() {

    }

    public TreeSelect(SysDept dept) {
        this.id = dept.getDeptId();
        this.label = dept.getDeptName();
        this.children = dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
        this.corpId = dept.getCorpId();
    }

    public TreeSelect(TbWxDepartment dept) {
        this.id = dept.getId();
        this.label = dept.getName();
        this.children = dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
        this.userList = dept.getUserList();
        this.corpId = dept.getCorpId();
    }

    public TreeSelect(SysMenu menu) {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.component = menu.getComponent();
        this.menuType = menu.getMenuType();
        this.children = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
        this.belongTerminal = menu.getBelongTerminal();
    }

    public TreeSelect(TbWxCategory category) {
        this.id = category.getId();
        this.label = category.getName();
        this.children = category.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(TbWxCategoryInfo categoryInfo) {
        this.id = categoryInfo.getId() + "";
        this.label = categoryInfo.getName();
        this.children = categoryInfo.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(AppCategoryVO categoryInfo) {
        this.id = categoryInfo.getId() + "";
        this.label = categoryInfo.getCategoryName();
        this.children = categoryInfo.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }
}
