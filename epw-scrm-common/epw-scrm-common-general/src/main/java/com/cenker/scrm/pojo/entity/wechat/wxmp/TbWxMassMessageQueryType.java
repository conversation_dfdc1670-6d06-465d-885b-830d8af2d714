package com.cenker.scrm.pojo.entity.wechat.wxmp;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TbWxMassMessageQueryType implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String groupName;// '分组名称',
    private String qryType;// '查询条件类型： "USER_TAG","JOURNERY","USER_ID","ADD_DATE","BUSINESS_DATA" ,"USER_DEFINED"',
    private String qryTypeName;//'条件名称',
    private String qryTypeIcon;//'条件图标',
    private String showFlag;//'显示标记:1 是0否',
    private String displayOrder;// '排序',
    private String createBy;//'创建人ID',
    private String createTime;// '创建时间',
    private String updateBy;//'修改人ID',
    private String updateTime;//
}