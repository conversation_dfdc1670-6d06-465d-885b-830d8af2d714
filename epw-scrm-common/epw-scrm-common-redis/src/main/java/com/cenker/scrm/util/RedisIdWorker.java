package com.cenker.scrm.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @Date 2023/07/14
 * @Description 基于 redis获取按时间排序自增长 id
 */
@Component
public class RedisIdWorker {
    /**
     * 开始时间戳: 2023-01-01 00:00:00 ms
     */
    private static final long BEGIN_TIMESTAMP = 1672502400L;
    /**
     * 序列号的位数
     */
    private static final int COUNT_BITS = 16;

    @Autowired
    private StringRedisTemplate stringredisTemplate;

    public long nextId(String keyPrefix) {
        // 生成时间戳
        LocalDateTime now = LocalDateTime.now();
        long nowSecond = now.toEpochSecond(ZoneOffset.UTC);
        long timestamp = nowSecond - BEGIN_TIMESTAMP;

        // 生成序列号
        // 获取当前日期, 精准到天
        String date = now.format(DateTimeFormatter.ofPattern("yyyy:MM:dd"));
        // 自增长
        long count = stringredisTemplate.opsForValue().increment("icr:" + keyPrefix + ":" + date);
        // 拼接并返回
        return timestamp << COUNT_BITS | count;
    }
}
