package com.cenker.scrm.config;

import com.cenker.scrm.constants.RabbitMqQueuesConstant;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * mq配置
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(RabbitMQProperties.class)
public class RabbitMQAutoConfig {

    private CachingConnectionFactory connectionFactory;

    /**
     * 连接工厂使用自定义配置
     *
     * @param connectionFactory  connectionFactory
     * @param slodonMqProperties slodonMqProperties
     */
    public RabbitMQAutoConfig(CachingConnectionFactory connectionFactory, RabbitMQProperties slodonMqProperties) {
        connectionFactory.setHost(slodonMqProperties.getHost());
        connectionFactory.setPort(slodonMqProperties.getPort());
        connectionFactory.setUsername(slodonMqProperties.getUsername());
        connectionFactory.setPassword(slodonMqProperties.getPassword());
        connectionFactory.setVirtualHost(slodonMqProperties.getVirtualHost());
        connectionFactory.afterPropertiesSet();
        connectionFactory.setAddresses(slodonMqProperties.getHost() + ":" + slodonMqProperties.getPort());
        connectionFactory.setRequestedHeartBeat(0);
        this.connectionFactory = connectionFactory;
    }

    // 在配置类中添加如下声明，防止重复声明冲突
    @Bean
    public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
        RabbitAdmin admin = new RabbitAdmin(connectionFactory);
        admin.setAutoStartup(true);
        admin.setIgnoreDeclarationExceptions(true); // 添加这一行
        return admin;
    }
    @Bean
    public MessageConverter stringMessageConverter() {
        return new MessageConverter() {
            @Override
            public Message toMessage(Object object, MessageProperties messagePropertiesConverter) {
                // 实现将String转换为Message的逻辑（如果需要）
                return null;
            }

            @Override
            public Object fromMessage(Message message) {
                byte[] body = message.getBody();
                return new String(body, StandardCharsets.UTF_8);
            }
        };
    }

    @Bean(RabbitMqQueuesConstant.MQ_FACTORY_NAME_SINGLE_PASS_ERR)
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(MessageConverter stringMessageConverter) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(1);
        factory.setPrefetchCount(1);
        factory.setBatchSize(1);
        factory.setMessageConverter(stringMessageConverter);
        //跳过异常
        factory.setAcknowledgeMode(AcknowledgeMode.NONE);
        return factory;
    }

    /** 交换机 start */

    /**
     * 指定形式
     * @return
     */
    @Bean
    public DirectExchange directExchange() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new DirectExchange(RabbitMqQueuesConstant.MQ_EXCHANGE_DIRECT, true, false, arguments);
    }

    /**
     * 广播形式
     * @return
     */
    @Bean
    public FanoutExchange fanoutExchange() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new FanoutExchange(RabbitMqQueuesConstant.MQ_EXCHANGE_FANOUT, true, false, arguments);
    }

    // 配置延迟交换机
    @Bean
    public CustomExchange delayedExchange() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange(RabbitMqQueuesConstant.DELAYED_EXCHANGE_NAME, "x-delayed-message", true, false, args);
    }

    /** 交换机 end */


    /** 队列 start */
    /**
     * 声明延迟队列
     */
    @Bean
    public Queue delayQueue() {
        return new Queue(RabbitMqQueuesConstant.DELAYED_QUEUE_NAME, true);
    }
    /**
     * 发送欢迎语队列
     * @return Queue
     */
    @Bean
    public Queue sendWelcomeMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.SEND_WELCOME_MESSAGE, true, false, false, arguments);
    }

    /**
     * 活码打标签队列
     * @return Queue
     */
    @Bean
    public Queue contactAddTagMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.CONTACT_ADD_TAG, true, false, false, arguments);
    }

    /**
     * 客户打标签队列
     * @return
     */
    @Bean
    public Queue customerAddTagMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.CUSTOMER_ADD_TAG, true, false, false, arguments);
    }

    /**
     * 客户移除标签队列
     * @return
     */
    @Bean
    public Queue customerRemoveTagMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.CUSTOMER_DEL_TAG, true, false, false, arguments);
    }
    /**
     * 客户移除标签队列
     * @return
     */
    @Bean
    public Queue customerOperTrackMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.CUSTOMER_OPER_TRACK, true, false, false, arguments);
    }

    /**
     * 发送应用消息队列
     * @return Queue
     */
    @Bean
    public Queue sendAgentMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.SEND_AGENT_MESSAGE, true, false, false, arguments);
    }

    /**
     * 取消群发任务队列
     * @return Queue
     */
    @Bean
    public Queue cancelGroupMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.CANCEL_GROUP_SEND, true, false, false, arguments);
    }

    /**
     * 会话存档-拉取会话内容队列
     * @return Queue
     */
    @Bean
    public Queue chatArchivePullMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.CHAT_ARCHIVE_PULL_MESSAGE, true, false, false, arguments);
    }

    /**
     * 会话存档-保存会话内容队列
     * @return Queue
     */
    @Bean
    public Queue chatArchiveSaveMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.CHAT_ARCHIVE_SAVE_MESSAGE, true, false, false, arguments);
    }
    /**
     * 会话存档-会话内容产生事件消息
     * @return Queue
     */
    @Bean
    public Queue chatArchiveAuditNotifyMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.CHAT_ARCHIVE_MSG_AUDIT_NOTIFY, true, false, false, arguments);
    }
    /**
     * 员工同步客户数据事件消息
     * @return Queue
     */
    @Bean
    public Queue userSyncCustomerMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.USER_SYNC_CUSTOMER, true, false, false, arguments);
    }
    /**
     * 员工同步客户群数据事件消息
     * @return Queue
     */
    @Bean
    public Queue userSyncCustomerGroupMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.USER_SYNC_CUSTOMER_GROUP, true, false, false, arguments);
    }
    /**
     * 系统消息通知事件消息
     * @return Queue
     */
    @Bean
    public Queue sysMsgNotifyQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.SYS_MSG_NOTICE, true, false, false, arguments);
    }
    /**
     * 系统消息撤回事件消息
     * @return Queue
     */
    @Bean
    public Queue sysMsgRevocationQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.SYS_MSG_AGENT_REVOCATION, true, false, false, arguments);
    }

    /**
     * 系统消息同步标签事件消息队列
     * @return Queue
     */
    @Bean
    public Queue sysMsgSysnTagMsgQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.SYS_MSG_SYSN_TAG, true, false, false, arguments);
    }

    /**
     * 系统消息同步标签事件消息路由
     * @return Binding
     */
    @Bean
    public Binding sysMsgSysnTagMsgBinding(Queue sysMsgSysnTagMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(sysMsgSysnTagMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.SYS_MSG_SYSN_TAG);
    }

    /**
     * 系统消息撤回事件消息
     * @return Queue
     */
    @Bean
    public Queue sysMsgSendQueue() {
        Map<String, Object> arguments = new HashMap<>(4);
        return new Queue(RabbitMqQueuesConstant.SYS_MSG_NOTICE_SEND, true, false, false, arguments);
    }

    /** 队列 end */

    /** 路由 start */
    /**
     * 绑定队列到交换机
     */
    @Bean
    public Binding delayQueueBinding(Queue delayQueue, CustomExchange delayedExchange) {
        return BindingBuilder.bind(delayQueue).to(delayedExchange).with(RabbitMqQueuesConstant.DELAYED_ROUTING_KEY).noargs();
    }

    /**
     * 会员消息队列路由
     *
     * @return Binding
     */
    @Bean
    public Binding sendWelcomeMsgBinding(Queue sendWelcomeMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(sendWelcomeMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.SEND_WELCOME_MESSAGE);
    }

    /**
     * 活码打标签队列
     * @return Queue
     */
    @Bean
    public Binding contactAddTagMsgBinding(Queue contactAddTagMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(contactAddTagMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.CONTACT_ADD_TAG);

    }

    @Bean
    public Binding customerAddTagMsgBinding(Queue customerAddTagMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(customerAddTagMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.CUSTOMER_ADD_TAG);

    }

    @Bean
    public Binding customerRemoveTagMsgBinding(Queue customerRemoveTagMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(customerRemoveTagMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.CUSTOMER_DEL_TAG);

    }

    @Bean
    public Binding customerOperTrackMsgBinding(Queue customerOperTrackMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(customerOperTrackMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.CUSTOMER_OPER_TRACK);

    }

    /**
     * 发送应用消息队列
     * @return Queue
     */
    @Bean
    public Binding sendAgentMsgBinding(Queue sendAgentMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(sendAgentMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.SEND_AGENT_MESSAGE);
    }

    /**
     * 取消群发任务队列
     * @return Queue
     */
    @Bean
    public Binding cancelGroupMsgBinding(Queue cancelGroupMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(cancelGroupMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.CANCEL_GROUP_SEND);
    }

    /**
     * 会话存档-拉取会话内容队列
     * @return Queue
     */
    @Bean
    public Binding chatArchivePullMsgBinding(Queue chatArchivePullMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(chatArchivePullMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.CHAT_ARCHIVE_PULL_MESSAGE);
    }

    /**
     * 会话存档-保存会话内容队列
     * @return Queue
     */
    @Bean
    public Binding chatArchiveSaveMsgBinding(Queue chatArchiveSaveMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(chatArchiveSaveMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.CHAT_ARCHIVE_SAVE_MESSAGE);
    }
    /**
     * 会话存档-会话内容产生事件
     * @return Queue
     */
    @Bean
    public Binding chatArchiveAuditNotifyMsgBinding(Queue chatArchiveAuditNotifyMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(chatArchiveAuditNotifyMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.CHAT_ARCHIVE_MSG_AUDIT_NOTIFY);
    }
    /**
     * 会话存档-同步员工的客户数据事件
     * @return Queue
     */
    @Bean
    public Binding userSyncCustomerMsgBinding(Queue userSyncCustomerMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(userSyncCustomerMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.USER_SYNC_CUSTOMER);
    }
    /**
     * 会话存档-同步员工的客户群数据事件
     * @return Queue
     */
    @Bean
    public Binding userSyncCustomerGroupMsgBinding(Queue userSyncCustomerGroupMsgQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(userSyncCustomerGroupMsgQueue).to(directExchange).with(RabbitMqQueuesConstant.USER_SYNC_CUSTOMER);
    }
    /**
     * 系统消息通知事件
     * @return Queue
     */
    @Bean
    public Binding sysMsgNotifyBinding(Queue sysMsgNotifyQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(sysMsgNotifyQueue).to(directExchange).with(RabbitMqQueuesConstant.SYS_MSG_NOTICE);
    }
    /**
     * 系统消息撤回事件消息
     * @return Queue
     */
    @Bean
    public Binding sysMsgRevocationBinding(Queue sysMsgRevocationQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(sysMsgRevocationQueue).to(directExchange).with(RabbitMqQueuesConstant.SYS_MSG_AGENT_REVOCATION);
    }
    /**
     * 系统消息撤回事件消息
     * @return Queue
     */
    @Bean
    public Binding sysMsgSendQueueBinding(Queue sysMsgSendQueue, DirectExchange directExchange) {
        return BindingBuilder.bind(sysMsgSendQueue).to(directExchange).with(RabbitMqQueuesConstant.SYS_MSG_NOTICE_SEND);
    }

    /** 路由 end */
}
