package com.cenker.scrm.constants;

/**
 * <AUTHOR>
 * @Date 2023/6/7
 * @Description mq常量 主题标签前缀 用于标识不同消息事件的具体类型
 */
public interface RabbitMqQueuesConstant {

    /**
     * rabbitMq 名称前缀（交换机、队列名称）
     */
    String MQ_NAME_PREFIX = "cenker_scrm";
    /**
     * rabbitMq 交换机Direct Exchange
     */
    String MQ_EXCHANGE_DIRECT = MQ_NAME_PREFIX + "_exchange";
    /**
     * rabbitMq 交换机Fanout Exchange
     */
    String MQ_EXCHANGE_FANOUT = MQ_NAME_PREFIX + "_fanout_exchange";
    /**
     * rabbitMq 交换机Delay Exchange
     */
    String DELAYED_EXCHANGE_NAME = MQ_NAME_PREFIX + "_delay_exchange_1";

    /**
     * 发送欢迎语事件
     */
    String SEND_WELCOME_MESSAGE = MQ_NAME_PREFIX + "send-welcome-message";
    /**
     * 活码添加标签
     */
    String CONTACT_ADD_TAG = MQ_NAME_PREFIX + "contact-add-tag";

    /**
     * 为客户添加标签
     */
    String CUSTOMER_ADD_TAG = MQ_NAME_PREFIX + "customer-add-tag";
    /**
     * 为客户移除标签
     */
    String CUSTOMER_DEL_TAG = MQ_NAME_PREFIX + "customer-del-tag";
    /**
     * 客户操作轨迹
     */
    String CUSTOMER_OPER_TRACK = MQ_NAME_PREFIX + "customer-oper-track";

    /**
     * 发送应用消息
     */
    String SEND_AGENT_MESSAGE = MQ_NAME_PREFIX + "send-agent-message";

    /**
     * 取消群发任务
     */
    String CANCEL_GROUP_SEND = MQ_NAME_PREFIX + "cancel_group_send";

    /**
     * 员工同步客户数据消息
     */
    String USER_SYNC_CUSTOMER = MQ_NAME_PREFIX + "user_sync_customer";

    /**
     * 员工同步客户群数据消息
     */
    String USER_SYNC_CUSTOMER_GROUP = MQ_NAME_PREFIX + "user_sync_customer_group";
    /**
     * 会话存档-产生会话回调事件消息
     */
    String CHAT_ARCHIVE_MSG_AUDIT_NOTIFY = MQ_NAME_PREFIX + "msg_audit_notify";

    /**
     * 会话存档-拉取会话内容
     */
    String CHAT_ARCHIVE_PULL_MESSAGE = MQ_NAME_PREFIX + "chat_archive_pull";
    /**
     * 会话存档-保存会话内容
     */
    String CHAT_ARCHIVE_SAVE_MESSAGE = MQ_NAME_PREFIX + "chat_archive_save";
    /**
     * 系统公告-发送应用消息通知
     */
    String SYS_MSG_NOTICE = MQ_NAME_PREFIX + "_sys_mgs_notice";
    /**
     * 系统消息-应用通知-撤回
     */
    String SYS_MSG_AGENT_REVOCATION = MQ_NAME_PREFIX + "_sys_mgs_agent_revocation";
    /**
     * 系统消息通知
     */
    String SYS_MSG_NOTICE_SEND = MQ_NAME_PREFIX + "_sys_mgs_notice_send";
    /**
     * 同步标签 从SCRM同步到企微
     */
    String SYS_MSG_SYSN_TAG = MQ_NAME_PREFIX + "_sys_mgs_sync_tag";

    /**
     * mq连接工厂名称
     * 连接工厂，单一消费者，发生异常丢弃消息
     */
    String MQ_FACTORY_NAME_SINGLE_PASS_ERR = MQ_NAME_PREFIX + "_factory_single_pass_err";

    /**
     * 审核预警队列
     */
    String DELAYED_QUEUE_NAME = MQ_NAME_PREFIX + "_audit_warning_queue_1";
    String DELAYED_ROUTING_KEY = MQ_NAME_PREFIX + "_dalay_queue_1";

}
