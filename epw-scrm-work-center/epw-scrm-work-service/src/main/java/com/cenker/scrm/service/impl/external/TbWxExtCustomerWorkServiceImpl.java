package com.cenker.scrm.service.impl.external;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cenker.scrm.biz.MqSendMessageManager;
import com.cenker.scrm.constants.Constants;
import com.cenker.scrm.constants.StatusConstants;
import com.cenker.scrm.constants.TypeConstants;
import com.cenker.scrm.entity.MpWxCustomerAuthRecord;
import com.cenker.scrm.enums.TagSource;
import com.cenker.scrm.enums.TrackEventTypeEnum;
import com.cenker.scrm.enums.UserStatus;
import com.cenker.scrm.event.RecordCustomerTrajectoryEvent;
import com.cenker.scrm.handler.DefaultBuOperTrackHandler;
import com.cenker.scrm.mapper.auth.MpWxCustomerAuthRecordWorkMapper;
import com.cenker.scrm.mapper.auth.TbWxCustomerAuthRecordWorkMapper;
import com.cenker.scrm.mapper.external.TbWxExtCustomerWorkMapper;
import com.cenker.scrm.model.login.MpWxUser;
import com.cenker.scrm.pojo.dto.CustomerAddTagMsgDto;
import com.cenker.scrm.pojo.dto.CustomerRemoveTagMsgDto;
import com.cenker.scrm.pojo.dto.bu.CustomerOperTrackMsgDto;
import com.cenker.scrm.pojo.entity.TbWxExtFollowUserTag;
import com.cenker.scrm.pojo.entity.bu.BuOperTrack;
import com.cenker.scrm.pojo.entity.wechat.TbWxCustomerAuthRecord;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtCustomer;
import com.cenker.scrm.pojo.entity.wechat.TbWxExtFollowUser;
import com.cenker.scrm.pojo.entity.wechat.TbWxUser;
import com.cenker.scrm.pojo.vo.TagVO;
import com.cenker.scrm.pojo.vo.bu.OperTrackParams;
import com.cenker.scrm.pojo.vo.external.CustomerFollowVO;
import com.cenker.scrm.pojo.vo.external.CustomerPortraitVo;
import com.cenker.scrm.pojo.vo.external.CustomerTrajectoryContentVO;
import com.cenker.scrm.pojo.vo.tag.ExternalUserTagVO;
import com.cenker.scrm.service.contact.ITbWxUserWorkService;
import com.cenker.scrm.service.external.ITbWxCustomerTrajectoryWorkService;
import com.cenker.scrm.service.external.ITbWxExtCustomerWorkService;
import com.cenker.scrm.service.external.ITbWxExtFollowUserWorkService;
import com.cenker.scrm.service.mp.IMpWxUserWorkService;
import com.cenker.scrm.service.tag.ITbWxCorpTagGroupWorkService;
import com.cenker.scrm.service.tag.ITbWxCorpTagWorkService;
import com.cenker.scrm.service.tag.ITbWxCustomerTagLogWorkService;
import com.cenker.scrm.service.tag.ITbWxExtFollowUserTagWorkService;
import com.cenker.scrm.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.external.contact.ExternalContact;
import me.chanjar.weixin.cp.bean.external.contact.FollowedUser;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/6/1
 * @Description
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class TbWxExtCustomerWorkServiceImpl extends ServiceImpl<TbWxExtCustomerWorkMapper, TbWxExtCustomer> implements ITbWxExtCustomerWorkService {

    private final ITbWxExtFollowUserTagWorkService tbWxExtFollowUserTagService;
    private final ITbWxCustomerTagLogWorkService tbWxCustomerTagLogService;
    private final ITbWxExtFollowUserWorkService tbWxExtFollowUserService;
    private final ITbWxCorpTagWorkService tbWxCorpTagService;
    private final ITbWxCorpTagGroupWorkService tagGroupService;
    private final ITbWxUserWorkService userService;
    private final ITbWxCustomerTrajectoryWorkService trajectoryService;
    private final IMpWxUserWorkService mpWxUserWorkService;
    private final MpWxCustomerAuthRecordWorkMapper mpWxCustomerAuthRecordWorkMapper;
    private final TbWxCustomerAuthRecordWorkMapper tbWxCustomerAuthRecordWorkMapper;

    private final ApplicationEventPublisher applicationEventPublisher;
    private final MqSendMessageManager mqSendMessageManager;
    @Value("${sync.esb.customer:false}")
    private boolean syncEsbCustomer;

    /**
     * 保存客户信息
     *
     * @param externalContactInfo
     * @param corpId
     * @param userId
     */
    public void saveOrUpdateCustomerInfo(WxCpExternalContactInfo externalContactInfo,
                                         FollowedUser followedUser,
                                         String corpId, String userId) {
        // 3.1.4 根据unionId更新MpWxUser表的昵称和头像
        ExternalContact externalContact = externalContactInfo.getExternalContact();
        updateMpWxUser(externalContact.getUnionId(), externalContact.getName(), externalContact.getAvatar());

        String externalUserId = externalContactInfo.getExternalContact().getExternalUserId();

        TbWxExtCustomer existsCustomer = this.lambdaQuery()
                .select(TbWxExtCustomer::getName, TbWxExtCustomer::getExternalUserId,
                        TbWxExtCustomer::getGender)
                .eq(TbWxExtCustomer::getExternalUserId, externalUserId)
                .eq(TbWxExtCustomer::getCorpId, corpId).one();

        // 客户信息已存在，则更新客户状态
        if (existsCustomer != null) {
            update(new LambdaUpdateWrapper<TbWxExtCustomer>()
                    .set(TbWxExtCustomer::getName, externalContactInfo.getExternalContact().getName())
                    .set(TbWxExtCustomer::getAvatar, externalContactInfo.getExternalContact().getAvatar())
                    .set(TbWxExtCustomer::getGender, externalContactInfo.getExternalContact().getGender())
                    .set(TbWxExtCustomer::getStatus, UserStatus.OK.getCode())
                    .eq(TbWxExtCustomer::getExternalUserId, externalUserId)
                    .eq(TbWxExtCustomer::getCorpId, corpId)
            );
            if (!Objects.equals(existsCustomer.getName(), externalContactInfo.getExternalContact().getName())) {
                DefaultBuOperTrackHandler handler = DefaultBuOperTrackHandler.getEventHandler(TrackEventTypeEnum.CUSTOMER_INFO_EDIT.getSubEventType());
                CustomerPortraitVo weCustomerPortrait = new CustomerPortraitVo();
                weCustomerPortrait.setFieldName("name");
                weCustomerPortrait.setName(externalContactInfo.getExternalContact().getName());
                OperTrackParams operTrackParams = OperTrackParams.builder()
                        .externalUserId(existsCustomer.getExternalUserId())
                        .userId(userId)
                        .corpId(corpId)
                        .customer(existsCustomer)
                        .weCustomerPortrait(weCustomerPortrait)
                        .build();
                BuOperTrack buOperTrack = handler.genBuOperTrack(operTrackParams);
                mqSendMessageManager.sendCustomerOperTrackMessage(new CustomerOperTrackMsgDto(buOperTrack));
                log.info("{} 发送MQ消息成功", handler.eventType.getTitle());
            }
            if (!Objects.equals(existsCustomer.getGender(), externalContactInfo.getExternalContact().getGender())) {
                DefaultBuOperTrackHandler handler = DefaultBuOperTrackHandler.getEventHandler(TrackEventTypeEnum.CUSTOMER_INFO_EDIT.getSubEventType());
                CustomerPortraitVo weCustomerPortrait = new CustomerPortraitVo();
                weCustomerPortrait.setFieldName("gender");
                weCustomerPortrait.setGender(externalContactInfo.getExternalContact().getGender());
                OperTrackParams operTrackParams = OperTrackParams.builder()
                        .externalUserId(existsCustomer.getExternalUserId())
                        .userId(userId)
                        .corpId(corpId)
                        .customer(existsCustomer)
                        .weCustomerPortrait(weCustomerPortrait)
                        .build();
                BuOperTrack buOperTrack = handler.genBuOperTrack(operTrackParams);
                mqSendMessageManager.sendCustomerOperTrackMessage(new CustomerOperTrackMsgDto(buOperTrack));
                log.info("{} 发送MQ消息成功", handler.eventType.getTitle());
            }
            return;
        }

        TbWxExtCustomer tbWxExtCustomer = new TbWxExtCustomer();
        BeanUtils.copyProperties(externalContactInfo.getExternalContact(), tbWxExtCustomer);
        tbWxExtCustomer.setStatus(UserStatus.OK.getCode());
        tbWxExtCustomer.setCorpId(corpId);
        tbWxExtCustomer.setCreateBy(Constants.DEFAULT_USER);
        tbWxExtCustomer.setSource(Constants.SOURCE_CP);
        // 更新首次添加客户的员工，如果以前添加过员工的话，这里可能不一定准确，因此在同步客户全量数据时，需要修改这个字段
        tbWxExtCustomer.setFirstAddUserId(userId);
        // 更新首次添加客户时间，如果以前添加过员工的话，这里可能不一定准确，因此在同步客户全量数据时，需要修改这个字段
        Date addTime = DateUtil.date(followedUser.getCreateTime() * 1000);
        tbWxExtCustomer.setFirstAddDate(addTime);
        // 更新手机号，这里可能不一定准确，因此在同步客户全量数据时，需要修改这个字段
        tbWxExtCustomer.setMobiles(StrUtil.join(StrUtil.COMMA, followedUser.getRemarkMobiles()));

        // 可能存在同名标签，这里可能不一定准确，因此在同步客户全量数据时，需要修改这个字段
        if (Objects.nonNull(followedUser.getTags()) && followedUser.getTags().length > 0) {
            List<String> tagNames = Arrays.asList(followedUser.getTags()).stream().map(FollowedUser.Tag::getTagName).collect(Collectors.toList());
            tbWxExtCustomer.setTag(JSON.toJSONString(tagNames));
        }

        this.save(tbWxExtCustomer);
        if (syncEsbCustomer) {
            // 同步ESB 客户认证状态 查询 mp_wx_customer_auth_record 表
            syncCusstomerAuthStatus(tbWxExtCustomer.getUnionId());
        }
    }

    /**
     * 同步客户绑定状态
     *
     * @param unionId
     * @return
     */
    private void syncCusstomerAuthStatus(String unionId) {
        if (StrUtil.isBlank(unionId)) {
            return;
        }
        // 根据unionId查询最新的认证记录
        MpWxCustomerAuthRecord mpWxCustomerAuthRecord = mpWxCustomerAuthRecordWorkMapper.selectLatestByUnionId(unionId);
        if (mpWxCustomerAuthRecord != null && StringUtils.isNotEmpty(mpWxCustomerAuthRecord.getCustNo()) && Objects.equals(mpWxCustomerAuthRecord.getIsAuth(), "1")) {
            // 更新客户表
            baseMapper.update(null, new UpdateWrapper<TbWxExtCustomer>()
                    .eq("union_id", unionId)
                    .set("is_auth", 1)
                    .set("custno", mpWxCustomerAuthRecord.getCustNo())
                    .set("real_name", mpWxCustomerAuthRecord.getRealName())
                    .set("update_time", new Date()));
            // 保存认证记录
            TbWxCustomerAuthRecord record = new TbWxCustomerAuthRecord(unionId, mpWxCustomerAuthRecord.getCustNo());
            tbWxCustomerAuthRecordWorkMapper.insert(record);
            log.info("【易服务绑定状态检查】客户【unionId={}】已绑定易服务！", unionId);
        }
    }


    /**
     * 新增员工客户关系
     *
     * @param followedUser
     * @param corpId
     * @param externalUserId
     */
    public void saveFollowUser(FollowedUser followedUser, String corpId, String externalUserId) {
        TbWxExtFollowUser extFollowUser = new TbWxExtFollowUser();

        extFollowUser.setCorpId(corpId);
        extFollowUser.setCreateBy(Constants.DEFAULT_USER);
        extFollowUser.setStatus(UserStatus.OK.getCode());
        extFollowUser.setExternalUserId(externalUserId);
        extFollowUser.setUserId(followedUser.getUserId());
        extFollowUser.setRemark(followedUser.getRemark());
        extFollowUser.setDescription(followedUser.getDescription());
        extFollowUser.setState(followedUser.getState());
        extFollowUser.setRemarkMobiles(StrUtil.join(StrUtil.COMMA, followedUser.getRemarkMobiles()));
        extFollowUser.setRemarkCorpName(followedUser.getRemarkCorpName());
        extFollowUser.setAddWay(followedUser.getAddWay());
        extFollowUser.setOperatorUserId(followedUser.getOperatorUserId());

//        if (Objects.nonNull(followedUser.getTags()) && followedUser.getTags().length > 0) {
//            List<String> tagNames = Arrays.asList(followedUser.getTags()).stream().map(FollowedUser.Tag::getTagName).collect(Collectors.toList());
//            extFollowUser.setTag(StrUtil.join(StrUtil.COMMA, tagNames));
//        }

        Date addTime = DateUtil.date(followedUser.getCreateTime() * 1000);

        Integer count = tbWxExtFollowUserService.lambdaQuery()
                .eq(TbWxExtFollowUser::getCorpId, corpId)
                .eq(TbWxExtFollowUser::getExternalUserId, externalUserId)
                .eq(TbWxExtFollowUser::getUserId, followedUser.getUserId()).count();
        // 客户与员工之前存在过关系，再次添加时，返回的添加时间不会变，因此这里需要单独处理，直接使用当前时间
        if (count > 0) {
            addTime = DateUtil.date();
        }

        extFollowUser.setCreateTime(addTime);
        extFollowUser.setUpdateTime(addTime);

        tbWxExtFollowUserService.save(extFollowUser);
    }

    @Override
    public void updateFollowUser(FollowedUser followedUser, String corpId, String externalUserId) {
        Integer count = tbWxExtFollowUserService.lambdaQuery()
                .eq(TbWxExtFollowUser::getUserId, followedUser.getUserId())
                .eq(TbWxExtFollowUser::getExternalUserId, externalUserId)
                .eq(TbWxExtFollowUser::getStatus, UserStatus.OK.getCode())
                .count();
        if (count > 0) {
            tbWxExtFollowUserService.lambdaUpdate()
                    .set(TbWxExtFollowUser::getRemark, followedUser.getRemark())
                    .set(TbWxExtFollowUser::getDescription, followedUser.getDescription())
                    .set(TbWxExtFollowUser::getRemarkMobiles, StrUtil.join(StrUtil.COMMA, followedUser.getRemarkMobiles()))
                    .set(TbWxExtFollowUser::getRemarkCorpName, followedUser.getRemarkCorpName())
                    .eq(TbWxExtFollowUser::getUserId, followedUser.getUserId())
                    .eq(TbWxExtFollowUser::getExternalUserId, externalUserId)
                    .eq(TbWxExtFollowUser::getStatus, UserStatus.OK.getCode())
                    .update();
        } else {
            this.saveFollowUser(followedUser, corpId, externalUserId);
        }
    }

    /**
     * 保存客户的企业标签数据
     *
     * @param followedUser
     * @param corpId
     * @param externalUserId
     */
    public void saveFollowUserTag(FollowedUser followedUser, String corpId, String externalUserId) {
        String userId = followedUser.getUserId();
        List<FollowedUser.Tag> followUserTags = Objects.nonNull(followedUser.getTags())? Arrays.asList(followedUser.getTags()) : Lists.newArrayList();
        List<String> followUserTagIds = followUserTags.stream().map(FollowedUser.Tag::getTagId).collect(Collectors.toList());

        // 查询员工名称、头像
        TbWxUser user = this.getUserByUserId(corpId, userId);

        List<TbWxExtFollowUserTag> existFollowUserTags = tbWxExtFollowUserTagService.lambdaQuery()
                .eq(TbWxExtFollowUserTag::getCorpId, corpId)
                .eq(TbWxExtFollowUserTag::getType, TypeConstants.CORP_TAG_TYPE)
                .eq(TbWxExtFollowUserTag::getExternalUserId, externalUserId)
                .eq(TbWxExtFollowUserTag::getUserId, userId).list();

        // 删除已移除的标签
        List<TagVO> removeIds = existFollowUserTags.stream()
                // 已同步的标签数据
                .filter(tag -> StatusConstants.CUSTOMER_TAG_SYNC_TRUE.equals(tag.getSyncStatus()))
                // 企微上不存在
                .filter(tag -> !followUserTagIds.contains(tag.getTagId()))
                .map(tag -> {
                    return TagVO.builder().tagId(tag.getTagId()).tagName(tag.getTag()).groupName(tag.getGroupName()).build();
                }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(removeIds)) {
            CustomerRemoveTagMsgDto customerRemoveTagMsgDto = CustomerRemoveTagMsgDto.builder()
                    .corpId(corpId)
                    .externalUserIds(Lists.newArrayList(externalUserId))
                    .tagList(removeIds)
                    .tagSource(TagSource.OTHER.name())
                    .isRetry(false)
                    .eventTypeEnum(TrackEventTypeEnum.CORPORATE_TAG_EDIT_REMOVE)
                    .userId(userId)
                    .fromCallback(true)
                    .nickName(user.getName())
                    .build();
            mqSendMessageManager.sendCustomerRemoveTagMessage(customerRemoveTagMsgDto);
            log.info("企微回调事件：发送客户移除标签MQ消息成功");
        }
        List<String> existTagIds = existFollowUserTags.stream().map(TbWxExtFollowUserTag::getTagId).collect(Collectors.toList());
        // 新增的标签
        List<TagVO> newFollowUserTags = followUserTags.stream()
                .filter(tag -> !existTagIds.contains(tag.getTagId())).map(tag -> {
                    return TagVO.builder().tagId(tag.getTagId()).tagName(tag.getTagName()).groupName(tag.getGroupName()).build();
                }).collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(newFollowUserTags)) {
            CustomerAddTagMsgDto customerAddTagMsgDto = CustomerAddTagMsgDto.builder()
                    .eventTypeEnum(TrackEventTypeEnum.CORPORATE_TAG_EDIT_ADD)
                    .corpId(corpId)
                    .externalUserIds(Lists.newArrayList(externalUserId))
                    .userId(userId)
                    .tagList(newFollowUserTags)
                    .tagSource(TagSource.OTHER.name())
                    .fromCallback(true)
                    .isRetry(false)
                    .nickName(user.getName())
                    .build();
            // 通过MQ消息统一处理
            mqSendMessageManager.sendCustomerAddTagMessage(customerAddTagMsgDto);
            log.info("企微回调事件：发送客户添加标签MQ消息成功");
        }
    }

    private TbWxUser getUserByUserId(String corpId, String userId) {
        TbWxUser user = userService.lambdaQuery()
                .select(TbWxUser::getName, TbWxUser::getAvatar)
                .eq(TbWxUser::getUserid, userId)
                .last("LIMIT 1").one();

        if (user == null) {
            user = new TbWxUser();
        }

        user.setCorpId(corpId);
        user.setUserid(userId);
        return user;
    }

    /**
     * 记录客户标签变更的轨迹信息
     * @param user
     * @param externalUserId
     */
    private void recordCustomerTagChangeTrajectory(TbWxUser user, String externalUserId, List<String> addTagNames, List<String> removeTagNames) {
        CustomerTrajectoryContentVO vo = CustomerTrajectoryContentVO.builder().build();
        vo.setActionType(TypeConstants.CUSTOMER_TRAJECTORY_STAFF_ACTION);
        vo.setStaffAction("编辑了企业标签");
        vo.setAddTagList(addTagNames);
        vo.setRemoveTagList(removeTagNames);
        vo.setStaffHeadImg(user.getAvatar());
        vo.setStaffName(user.getName());

        applicationEventPublisher.publishEvent(new RecordCustomerTrajectoryEvent(vo, user.getCorpId(), user.getUserid(), externalUserId, TypeConstants.MESSAGE_DYNAMIC, TypeConstants.CUSTOMER_TRAJECTORY_INFORMATION_TAG));
    }

    @Override
    public void updateMpWxUser(String unionId, String name, String avatar) {
        if (StrUtil.isNotEmpty(unionId)) {
            MpWxUser mpWxUser = mpWxUserWorkService.getOne(new LambdaQueryWrapper<MpWxUser>().eq(MpWxUser::getUnionId, unionId));
            if (ObjectUtil.isNotNull(mpWxUser)) {
                if (StrUtil.isNotEmpty(name)) {
                    mpWxUser.setNickName(name);
                }
                if (StrUtil.isNotEmpty(avatar)) {
                    mpWxUser.setHeadImgUrl(avatar);
                }
                mpWxUserWorkService.updateById(mpWxUser);
            }
        }
    }

    @Override
    public void updateTbWxExtCustomer(String unionId, String name, String avatar) {
        if (StrUtil.isNotEmpty(unionId)) {
            TbWxExtCustomer tbWxExtCustomer = this.getOne(new LambdaQueryWrapper<TbWxExtCustomer>().eq(TbWxExtCustomer::getUnionId, unionId));
            if (ObjectUtil.isNotNull(tbWxExtCustomer)) {
                if (StrUtil.isNotEmpty(name)) {
                    tbWxExtCustomer.setName(name);
                }
                if (StrUtil.isNotEmpty(avatar)) {
                    tbWxExtCustomer.setAvatar(avatar);
                }
                this.updateById(tbWxExtCustomer);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveExtCustomer(WxCpExternalContactInfo externalContactInfo, String corpId, String userId) {
        TbWxExtCustomer tbWxExtCustomer = new TbWxExtCustomer();
        BeanUtils.copyProperties(externalContactInfo.getExternalContact(), tbWxExtCustomer);
        tbWxExtCustomer.setStatus(UserStatus.OK.getCode());
        tbWxExtCustomer.setCorpId(corpId);
        tbWxExtCustomer.setCreateBy(Constants.DEFAULT_USER);
        tbWxExtCustomer.setSource(Constants.SOURCE_CP);

        List<TbWxExtFollowUser> followUsers = new ArrayList<>();
        List<TbWxExtFollowUserTag> extFollowUserTags = new ArrayList<>();
        // 处理客户信息，提取跟进员工列表、跟进员工标签列表数据，记录更新客户首次添加员工、首次添加时间、手机号码
        handlerFollowUserAndUserTag(corpId, userId, externalContactInfo, tbWxExtCustomer, followUsers, extFollowUserTags);

        // 判断followUser是否存在表中
        // QueryWrapper wrapper = new QueryWrapper();
        List<String> extUserIds = Lists.newArrayList(tbWxExtCustomer.getExternalUserId());
        List<CustomerFollowVO> existUserFollows = tbWxExtFollowUserService.queryUserIdByCorpIdAndExtUserId(extUserIds, corpId);
        List<TbWxExtFollowUser> addFollowUserList;
        Map<String, CustomerFollowVO> existMap = new HashMap<>(6);
        if (existUserFollows.size() > 0) {
            existMap = existUserFollows.stream().collect(
                    Collectors.toMap(x -> x.getUserId() + x.getExtUserId(), Function.identity(), (x, y) -> y));
            /**
             * 客户删除员工 followUser还会返回该员工信息 导致同步等操作时系统会误以为这是正常的数据将该已流失的数据重新返回为正常
             * 处理方式是在有数据的情况下不新增followUser
             * 然后新增客户回调是重新拉取数据  会和这个逻辑冲突 所以这里排除当前添加人组合
             */
            existMap.put(userId + tbWxExtCustomer.getExternalUserId(), null);
        }
        Map<String, CustomerFollowVO> finalExistMap = existMap;
        log.info("【外部联系人】添加外部联系人，数据库followUser,{}", finalExistMap);
        addFollowUserList = followUsers.stream().filter(tbWxExtFollowUser ->
                finalExistMap.get(tbWxExtFollowUser.getUserId() + tbWxExtFollowUser.getExternalUserId()) == null).collect(Collectors.toList());
        // 查询表中数据存在该客户信息
        if (baseMapper.selectOne(new LambdaQueryWrapper<TbWxExtCustomer>().eq(TbWxExtCustomer::getExternalUserId, tbWxExtCustomer.getExternalUserId())) == null) {
            tbWxExtCustomer.setCreateTime(new Date());
            save(tbWxExtCustomer);
        }
        /**
         * 客户画像0.3 客户存在系统时为协同逻辑 新的员工添加客户不应改动原先的协同数据 2022-03-24
         * 完全流失状态需要更新 只更新状态 2022-04-15
         */
        // else {
        //     wrapper.clear();
        //     wrapper.eq("external_user_id", tbWxExtCustomer.getExternalUserId());
        //     update(tbWxExtCustomer, wrapper);
        // }
        else {
            update(new LambdaUpdateWrapper<TbWxExtCustomer>()
                    .set(TbWxExtCustomer::getStatus, UserStatus.OK.getCode())
                    .eq(TbWxExtCustomer::getExternalUserId, tbWxExtCustomer.getExternalUserId())
                    .eq(TbWxExtCustomer::getCorpId, tbWxExtCustomer.getCorpId())
            );
        }
        if (addFollowUserList.size() > 0) {
            // tbWxExtFollowUserService.saveBatch(followUsers);
            // 直接添加企微返回的follerUser会导致多条记录 只添加变更的 2022-02-21
            tbWxExtFollowUserService.saveBatch(addFollowUserList);
        }
        if (extFollowUserTags.size() > 0) {
            LambdaQueryWrapper<TbWxExtFollowUserTag> warpper = new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                    .eq(TbWxExtFollowUserTag::getType, TypeConstants.CORP_TAG_TYPE)
                    .eq(TbWxExtFollowUserTag::getExternalUserId, tbWxExtCustomer.getExternalUserId())
                    .eq(TbWxExtFollowUserTag::getCorpId, corpId)
                    .eq(TbWxExtFollowUserTag::getUserId, extFollowUserTags.stream().map(TbWxExtFollowUserTag::getUserId).collect(Collectors.toList()));
            tbWxExtFollowUserTagService.removeByWrapperWithLog(warpper);
            tbWxExtFollowUserTagService.saveBatchWithLog(extFollowUserTags);
        }
    }

    /**
     * 处理客户信息，提取跟进员工列表、跟进员工标签列表数据
     * @param corpId
     * @param userId
     * @param externalContactInfo
     * @param tbWxExtCustomer
     * @param followUsers
     * @param extFollowUserTags
     */
    private void handlerFollowUserAndUserTag(String corpId, String userId, WxCpExternalContactInfo externalContactInfo, TbWxExtCustomer tbWxExtCustomer,
                                             List<TbWxExtFollowUser> followUsers, List<TbWxExtFollowUserTag> extFollowUserTags){
        // 客户详情2.0 标签同名加标签名
        List<ExternalUserTagVO> tagVos = new ArrayList<>();
        // 客户详情2.0 共同维护手机号
        String mobiles = "";

        List<FollowedUser> sortFollowerUsers = externalContactInfo.getFollowedUsers();
        sortFollowerUsers.sort((x, y) -> x.getCreateTime() > y.getCreateTime() ? 0 : 1);

        for (FollowedUser user : sortFollowerUsers) {
            TbWxExtFollowUser extFollowUser = new TbWxExtFollowUser();
            BeanUtils.copyProperties(user, extFollowUser);

            extFollowUser.setCorpId(corpId);
            extFollowUser.setCreateBy(Constants.DEFAULT_USER);
            extFollowUser.setStatus(UserStatus.OK.getCode());
            extFollowUser.setCreateTime(new Date(user.getCreateTime() * 1000));
            extFollowUser.setExternalUserId(externalContactInfo.getExternalContact().getExternalUserId());

            /**
             * 由于存在客户删除员工，重新添加获取的addWay会为0，因此在存在数据的情况下，修改当前获取的addWay为上次流失的 2022-04-20
             */
            if ("0".equals(extFollowUser.getAddWay()) && userId.equals(extFollowUser.getUserId())) {
                TbWxExtFollowUser tbWxExtFollowUser = tbWxExtFollowUserService.getOne(new LambdaQueryWrapper<TbWxExtFollowUser>()
                        .eq(TbWxExtFollowUser::getCorpId, corpId)
                        .eq(TbWxExtFollowUser::getUserId, userId)
                        .eq(TbWxExtFollowUser::getExternalUserId, tbWxExtCustomer.getExternalUserId())
                        .select(TbWxExtFollowUser::getAddWay)
                        .orderByDesc(TbWxExtFollowUser::getCreateTime)
                        .last("limit 1")
                );
                if (tbWxExtFollowUser != null) {
                    extFollowUser.setAddWay(tbWxExtFollowUser.getAddWay());
                }
            }

            followUsers.add(extFollowUser);
//            StringBuilder followUserTag = new StringBuilder();
//            StringBuilder followUserSelfTag = new StringBuilder();
            // 记录客户标签
            if (user.getTags() != null && user.getTags().length > 0) {
                for (FollowedUser.Tag tag1 : user.getTags()) {
                    ExternalUserTagVO externalUserTagVo = new ExternalUserTagVO();
                    BeanUtils.copyProperties(tag1, externalUserTagVo);
                    // tag.append(tag1.getTagName() + ",");
                    //type 为1 表示企业标签、为2 表示个人标签
                    if (tag1.getType() == 1) {
//                        followUserTag.append(tag1.getTagName()).append(",");
                        // 只添加企业标签 来做统计
                        tagVos.add(externalUserTagVo);
                    } else {
//                        followUserSelfTag.append(tag1.getTagName()).append(",");
                    }
                    buildCorpUserTag(corpId, tbWxExtCustomer, extFollowUserTags, user, tag1);
                }
            }

            // 记录用户手机号码
            if (user.getRemarkMobiles() != null && user.getRemarkMobiles().length > 0) {
                StringBuilder mobilesBud = new StringBuilder();
                Sets.newHashSet(user.getRemarkMobiles()).forEach(new Consumer<String>() {
                    @Override
                    public void accept(String value) {
                        mobilesBud.append(value).append(",");
                    }
                });
                String remarkmobiles = mobilesBud.toString();
                remarkmobiles = remarkmobiles.substring(0, remarkmobiles.lastIndexOf(","));
                //remarkmobiles = remarkmobiles.substring(0, remarkmobiles.lastIndexOf(",")-1);
                extFollowUser.setRemarkMobiles(remarkmobiles);
                if (StringUtils.isEmpty(mobiles)) {
                    mobiles = remarkmobiles;
                } else {
                    mobiles = mobiles + "," + remarkmobiles;
                }
            }

//            extFollowUser.setTag(followUserTag.toString());
//            extFollowUser.setSelfTag(followUserSelfTag.toString());
        }

        // 更新首次添加客户的员工
        tbWxExtCustomer.setFirstAddUserId(sortFollowerUsers.get(0).getUserId());
        // 更新首次添加客户时间
        Date firstDate = new Date(sortFollowerUsers.get(0).getCreateTime() * 1000);
        tbWxExtCustomer.setFirstAddDate(firstDate);


        log.info("【外部联系人】添加外部联系人，企微接口返回followUser,{}", followUsers);
        // 客户详情2.0 所有员工修改手机号会同步到一张表内 去重 2022-03-03
        if (StringUtils.isNotEmpty(mobiles)) {
            mobiles = Arrays.stream(mobiles.split(",")).distinct().collect(Collectors.joining(","));
        }
        tbWxExtCustomer.setMobiles(mobiles);
        // 客户详情2.0 标签同名加标签名
        List<String> tag = setTagVos(tagVos);
        if (CollectionUtils.isNotEmpty(tag)) {
            tbWxExtCustomer.setTag(JSON.toJSONString(tag));
        }
    }

    private void buildCorpUserTag(String corpId, TbWxExtCustomer tbWxExtCustomer, List<TbWxExtFollowUserTag> extFollowUserTags,
                                  FollowedUser user, FollowedUser.Tag tag1) {
        TbWxExtFollowUserTag userTag = new TbWxExtFollowUserTag();
        userTag.setCorpId(corpId);
        userTag.setTag(tag1.getTagName());
        userTag.setTagId(tag1.getTagId());
        userTag.setType("" + tag1.getType());
        userTag.setUserId(user.getUserId());
        userTag.setExternalUserId(tbWxExtCustomer.getExternalUserId());
        userTag.setGroupName(tag1.getGroupName());
        userTag.setCreateBy(Constants.DEFAULT_USER);
        userTag.setCreateTime(new Date());
        extFollowUserTags.add(userTag);
    }

    private List<String> setTagVos(List<ExternalUserTagVO> tagVos) {
        List<String> tags = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tagVos)) {
            // 标签id 去重
            List<ExternalUserTagVO> tagList = tagVos.stream()
                    .collect(Collectors.collectingAndThen
                            (Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ExternalUserTagVO::getTagId))), ArrayList::new
                            ));
            List<ExternalUserTagVO> sameTagNameList = tagList.stream().filter(distinctByKey(ExternalUserTagVO::getTagName)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sameTagNameList)) {
                List<String> sameTagNameStrList = sameTagNameList.stream().map(ExternalUserTagVO::getTagName).collect(Collectors.toList());
                for (ExternalUserTagVO externalUserTagVo : tagList) {
                    if (sameTagNameStrList.contains(externalUserTagVo.getTagName())) {
                        externalUserTagVo.setTagName(externalUserTagVo.getGroupName() + "：" + externalUserTagVo.getTagName());
                    }
                }
            }
            return tagList.stream().map(ExternalUserTagVO::getTagName).collect(Collectors.toList());
        }
        return tags;
    }

    /**
     * 对比数据库 发生改变记录轨迹信息
     *
     * @param tbWxExtCustomer 客户
     * @param user            员工
     */
    private void recordCustomerTagTrajectory(TbWxExtCustomer tbWxExtCustomer, FollowedUser user) {
        String corpId = tbWxExtCustomer.getCorpId();
        String userId = user.getUserId();
        String externalUserId = tbWxExtCustomer.getExternalUserId();
        log.info("【外部联系人回调】开始比对标签信息，corpId:【{}】，userId:【{}】，externalUserId:【{}】", corpId, userId, externalUserId);
        FollowedUser.Tag[] tags = user.getTags();
        List<TbWxExtFollowUserTag> dataTagList = tbWxExtFollowUserTagService.list(new LambdaQueryWrapper<TbWxExtFollowUserTag>()
                .eq(TbWxExtFollowUserTag::getUserId, userId)
                .eq(TbWxExtFollowUserTag::getExternalUserId, externalUserId)
                .eq(TbWxExtFollowUserTag::getCorpId, corpId)
                // 企业标签
                .eq(TbWxExtFollowUserTag::getType, TypeConstants.CORP_TAG_TYPE)
        );
        List<String> tagIdList = null;
        List<String> notTagIdList = null;
        if (tags != null && tags.length > 0) {
            tagIdList = new ArrayList<>(Arrays.asList(tags)).stream().map(FollowedUser.Tag::getTagId).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(dataTagList)) {
            notTagIdList = dataTagList.stream().map(TbWxExtFollowUserTag::getTagId).collect(Collectors.toList());
        }
        // 由于同步数据接口返回的只是tagId 所以这里用tagId来判断 而不是tagName -- 如果员工离职 不做记录
        TbWxUser tbWxUser = userService.selectTbWxUserById(corpId, userId);
        if (tbWxUser != null && UserStatus.DISABLE.getCode().equals(tbWxUser.getDelFlag())) {
            log.info("【外部联系回调】记录员工编辑标签事件");
            String content = getTagContentString(corpId, notTagIdList, tagIdList, tbWxUser);
            if (content != null) {
                trajectoryService.informationNews2(userId, externalUserId, corpId, TypeConstants.MESSAGE_DYNAMIC, TypeConstants.CUSTOMER_TRAJECTORY_INFORMATION_TAG, content);
            }
        }
    }

    private String getTagContentString(String corpId, List<String> notTagIdList, List<String> tagIdList, TbWxUser tbWxUser) {
        CustomerTrajectoryContentVO vo = new CustomerTrajectoryContentVO();
        /**
         * 通过对比添加删除二者区别 来判断是添加还是删除
         */
        if (CollectionUtils.isNotEmpty(tagIdList) && CollectionUtils.isEmpty(notTagIdList)) {
            log.info("【客户轨迹】添加企微客户标签");
            // 仅添加
            String[] strings = new ArrayList<>(tagIdList).toArray(new String[]{});
            vo.setAddTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
        } else if (CollectionUtils.isNotEmpty(notTagIdList) && CollectionUtils.isEmpty(tagIdList)) {
            log.info("【客户轨迹】删除企微客户标签");
            // 仅删除
            String[] strings = new ArrayList<>(notTagIdList).toArray(new String[]{});
            vo.setRemoveTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
        } else if (CollectionUtils.isNotEmpty(tagIdList) && CollectionUtils.isNotEmpty(notTagIdList)) {
            Set<String> list = Sets.newHashSet();
            list.addAll(tagIdList);
            list.addAll(notTagIdList);
            log.info("【客户轨迹】既添加又删除企微客户标签，{}", list);

            // 抽取二者不同的标签
            List<String> addTagIdList = list.stream().filter(tagId -> !notTagIdList.contains(tagId)).collect(Collectors.toList());
            List<String> removeTagIdList = list.stream().filter(tagId -> !tagIdList.contains(tagId)).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(addTagIdList) && CollectionUtils.isNotEmpty(removeTagIdList)) {
                log.info("【客户轨迹】同时新增了客户标签，{}", addTagIdList);
                // 新增了客户标签
                String[] strings = new ArrayList<>(addTagIdList).toArray(new String[]{});
                vo.setAddTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
                // 删除了客户标签
                strings = new ArrayList<>(removeTagIdList).toArray(new String[]{});
                vo.setRemoveTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
            } else if (CollectionUtils.isNotEmpty(addTagIdList) && CollectionUtils.isEmpty(removeTagIdList)) {
                // 新增了客户标签
                String[] strings = new ArrayList<>(addTagIdList).toArray(new String[]{});
                vo.setAddTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
            } else {
                // 删除了客户标签
                if (CollectionUtils.isNotEmpty(removeTagIdList)) {
                    String[] strings = new ArrayList<>(removeTagIdList).toArray(new String[]{});
                    vo.setRemoveTagList(tbWxCorpTagService.selectTbWxCorpTagNameList(corpId, strings));
                } else {
                    // 没有新增删除操作不做处理
                    return null;
                }
            }
        }
        if (CollectionUtils.isEmpty(vo.getRemoveTagList()) && CollectionUtils.isEmpty(vo.getAddTagList())) {
            return null;
        }
        vo.setStaffAction("编辑了企业标签");
        vo.setActionType(TypeConstants.CUSTOMER_TRAJECTORY_STAFF_ACTION);
        vo.setStaffHeadImg(tbWxUser.getAvatar());
        vo.setStaffName(tbWxUser.getName());
        return JSON.toJSONString(vo);
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.FALSE) != null;
    }
}
