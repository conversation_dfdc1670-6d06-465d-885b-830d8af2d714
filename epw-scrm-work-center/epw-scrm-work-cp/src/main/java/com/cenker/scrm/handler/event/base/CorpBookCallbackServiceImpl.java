package com.cenker.scrm.handler.event.base;

import com.cenker.scrm.handler.message.adapter.CorpEventAdapter;
import com.cenker.scrm.service.ICorpBookCallbackService;
import com.cenker.scrm.service.ICorpCustomerCallbackService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/13
 * @Description 企业事件处理器-通讯录联系处理
 */
@Slf4j
public class CorpBookCallbackServiceImpl extends CorpEventAdapter implements ICorpBookCallbackService {


    @Override
    public void handlerCreateUser(WxCpXmlMessage wxCpXmlMessage, Map<String, Object> map) {

    }

    @Override
    public void handlerUpdateUser(WxCpXmlMessage wxCpXmlMessage, Map<String, Object> map) {

    }

    @Override
    public void handlerDeleteUser(WxCpXmlMessage wxCpXmlMessage, Map<String, Object> map) {

    }

    @Override
    public void handlerCreateDepart(WxCpXmlMessage wxCpXmlMessage, Map<String, Object> map) {

    }

    @Override
    public void handlerUpdateDepart(WxCpXmlMessage wxCpXmlMessage, Map<String, Object> map) {

    }

    @Override
    public void handlerDeleteDepart(WxCpXmlMessage wxCpXmlMessage, Map<String, Object> map) {

    }
}
